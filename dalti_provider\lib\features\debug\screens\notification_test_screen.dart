import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/notification_provider.dart';
import '../../auth/providers/auth_provider.dart';

/// Debug screen for testing Firebase notifications
class NotificationTestScreen extends ConsumerStatefulWidget {
  const NotificationTestScreen({super.key});

  @override
  ConsumerState<NotificationTestScreen> createState() =>
      _NotificationTestScreenState();
}

class _NotificationTestScreenState
    extends ConsumerState<NotificationTestScreen> {
  String? _deviceToken;
  bool _permissionsGranted = false;
  final Set<String> _subscribedTopics = {};

  @override
  void initState() {
    super.initState();
    _loadNotificationStatus();
  }

  Future<void> _loadNotificationStatus() async {
    try {
      // Get device token
      final token = await ref.read(deviceTokenProvider.future);

      // Check permissions
      final permissions = await ref.read(
        notificationPermissionsProvider.future,
      );

      // Get subscribed topics
      final topics = ref.read(notificationSubscriptionsProvider);

      setState(() {
        _deviceToken = token;
        _permissionsGranted = permissions;
        _subscribedTopics.clear();
        _subscribedTopics.addAll(topics);
      });
    } catch (e) {
      print('[NotificationTestScreen] Error loading status: $e');
    }
  }

  Future<void> _requestPermissions() async {
    try {
      final notificationSubscriptions = ref.read(
        notificationSubscriptionsProvider.notifier,
      );

      // Get current user ID if available
      final authData = ref.read(authNotifierProvider);
      final userId = authData.user?.id.toString();

      final granted = await notificationSubscriptions.requestPermissions(
        userId: userId,
      );

      setState(() {
        _permissionsGranted = granted;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              granted ? 'Permissions granted!' : 'Permissions denied',
            ),
            backgroundColor: granted ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      print('[NotificationTestScreen] Error requesting permissions: $e');
    }
  }

  Future<void> _subscribeToTopic(String topic) async {
    try {
      final notificationSubscriptions = ref.read(
        notificationSubscriptionsProvider.notifier,
      );
      await notificationSubscriptions.subscribeToTopic(topic);

      setState(() {
        _subscribedTopics.add(topic);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Subscribed to $topic'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('[NotificationTestScreen] Error subscribing to topic: $e');
    }
  }

  Future<void> _unsubscribeFromTopic(String topic) async {
    try {
      final notificationSubscriptions = ref.read(
        notificationSubscriptionsProvider.notifier,
      );
      await notificationSubscriptions.unsubscribeFromTopic(topic);

      setState(() {
        _subscribedTopics.remove(topic);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unsubscribed from $topic'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      print('[NotificationTestScreen] Error unsubscribing from topic: $e');
    }
  }

  void _copyTokenToClipboard() {
    if (_deviceToken != null) {
      Clipboard.setData(ClipboardData(text: _deviceToken!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Device token copied to clipboard'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Test'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotificationStatus,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permissions Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notification Permissions',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _permissionsGranted
                              ? Icons.check_circle
                              : Icons.cancel,
                          color:
                              _permissionsGranted ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(_permissionsGranted ? 'Granted' : 'Not granted'),
                        const Spacer(),
                        if (!_permissionsGranted)
                          ElevatedButton(
                            onPressed: _requestPermissions,
                            child: const Text('Request'),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Device Token Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Device Token',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_deviceToken != null) ...[
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _deviceToken!,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: _copyTokenToClipboard,
                        icon: const Icon(Icons.copy),
                        label: const Text('Copy Token'),
                      ),
                    ] else
                      const Text('No token available'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Subscribed Topics Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Subscribed Topics',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    if (_subscribedTopics.isEmpty)
                      const Text('No topics subscribed')
                    else
                      ...(_subscribedTopics.map(
                        (topic) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Expanded(child: Text(topic)),
                              IconButton(
                                icon: const Icon(
                                  Icons.remove_circle,
                                  color: Colors.red,
                                ),
                                onPressed: () => _unsubscribeFromTopic(topic),
                              ),
                            ],
                          ),
                        ),
                      )),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Topics Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Topics',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Subscribe to test topics to receive notifications:',
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children:
                          [
                                'test',
                                'general',
                                'appointments',
                                'messages',
                                'bookings',
                              ]
                              .map(
                                (topic) => ElevatedButton(
                                  onPressed:
                                      _subscribedTopics.contains(topic)
                                          ? null
                                          : () => _subscribeToTopic(topic),
                                  child: Text(topic),
                                ),
                              )
                              .toList(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
