// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'profile_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ProfileCategory _$ProfileCategoryFromJson(Map<String, dynamic> json) {
  return _ProfileCategory.fromJson(json);
}

/// @nodoc
mixin _$ProfileCategory {
  int get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;

  /// Serializes this ProfileCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileCategoryCopyWith<ProfileCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileCategoryCopyWith<$Res> {
  factory $ProfileCategoryCopyWith(
          ProfileCategory value, $Res Function(ProfileCategory) then) =
      _$ProfileCategoryCopyWithImpl<$Res, ProfileCategory>;
  @useResult
  $Res call({int id, String title});
}

/// @nodoc
class _$ProfileCategoryCopyWithImpl<$Res, $Val extends ProfileCategory>
    implements $ProfileCategoryCopyWith<$Res> {
  _$ProfileCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileCategoryImplCopyWith<$Res>
    implements $ProfileCategoryCopyWith<$Res> {
  factory _$$ProfileCategoryImplCopyWith(_$ProfileCategoryImpl value,
          $Res Function(_$ProfileCategoryImpl) then) =
      __$$ProfileCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int id, String title});
}

/// @nodoc
class __$$ProfileCategoryImplCopyWithImpl<$Res>
    extends _$ProfileCategoryCopyWithImpl<$Res, _$ProfileCategoryImpl>
    implements _$$ProfileCategoryImplCopyWith<$Res> {
  __$$ProfileCategoryImplCopyWithImpl(
      _$ProfileCategoryImpl _value, $Res Function(_$ProfileCategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
  }) {
    return _then(_$ProfileCategoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileCategoryImpl implements _ProfileCategory {
  const _$ProfileCategoryImpl({required this.id, required this.title});

  factory _$ProfileCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileCategoryImplFromJson(json);

  @override
  final int id;
  @override
  final String title;

  @override
  String toString() {
    return 'ProfileCategory(id: $id, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileCategoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title);

  /// Create a copy of ProfileCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileCategoryImplCopyWith<_$ProfileCategoryImpl> get copyWith =>
      __$$ProfileCategoryImplCopyWithImpl<_$ProfileCategoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileCategoryImplToJson(
      this,
    );
  }
}

abstract class _ProfileCategory implements ProfileCategory {
  const factory _ProfileCategory(
      {required final int id,
      required final String title}) = _$ProfileCategoryImpl;

  factory _ProfileCategory.fromJson(Map<String, dynamic> json) =
      _$ProfileCategoryImpl.fromJson;

  @override
  int get id;
  @override
  String get title;

  /// Create a copy of ProfileCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileCategoryImplCopyWith<_$ProfileCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileData _$ProfileDataFromJson(Map<String, dynamic> json) {
  return _ProfileData.fromJson(json);
}

/// @nodoc
mixin _$ProfileData {
  int get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get presentation => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  bool get isSetupComplete => throw _privateConstructorUsedError;
  ProfileCategory? get category => throw _privateConstructorUsedError;
  double? get averageRating => throw _privateConstructorUsedError;
  int get totalReviews => throw _privateConstructorUsedError;
  String? get profilePictureUrl => throw _privateConstructorUsedError;
  String? get logoUrl => throw _privateConstructorUsedError;

  /// Serializes this ProfileData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileDataCopyWith<ProfileData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileDataCopyWith<$Res> {
  factory $ProfileDataCopyWith(
          ProfileData value, $Res Function(ProfileData) then) =
      _$ProfileDataCopyWithImpl<$Res, ProfileData>;
  @useResult
  $Res call(
      {int id,
      String userId,
      String? title,
      String? phone,
      String? presentation,
      bool isVerified,
      bool isSetupComplete,
      ProfileCategory? category,
      double? averageRating,
      int totalReviews,
      String? profilePictureUrl,
      String? logoUrl});

  $ProfileCategoryCopyWith<$Res>? get category;
}

/// @nodoc
class _$ProfileDataCopyWithImpl<$Res, $Val extends ProfileData>
    implements $ProfileDataCopyWith<$Res> {
  _$ProfileDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = freezed,
    Object? phone = freezed,
    Object? presentation = freezed,
    Object? isVerified = null,
    Object? isSetupComplete = null,
    Object? category = freezed,
    Object? averageRating = freezed,
    Object? totalReviews = null,
    Object? profilePictureUrl = freezed,
    Object? logoUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      presentation: freezed == presentation
          ? _value.presentation
          : presentation // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      isSetupComplete: null == isSetupComplete
          ? _value.isSetupComplete
          : isSetupComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ProfileCategory?,
      averageRating: freezed == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double?,
      totalReviews: null == totalReviews
          ? _value.totalReviews
          : totalReviews // ignore: cast_nullable_to_non_nullable
              as int,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logoUrl: freezed == logoUrl
          ? _value.logoUrl
          : logoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileCategoryCopyWith<$Res>? get category {
    if (_value.category == null) {
      return null;
    }

    return $ProfileCategoryCopyWith<$Res>(_value.category!, (value) {
      return _then(_value.copyWith(category: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProfileDataImplCopyWith<$Res>
    implements $ProfileDataCopyWith<$Res> {
  factory _$$ProfileDataImplCopyWith(
          _$ProfileDataImpl value, $Res Function(_$ProfileDataImpl) then) =
      __$$ProfileDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String userId,
      String? title,
      String? phone,
      String? presentation,
      bool isVerified,
      bool isSetupComplete,
      ProfileCategory? category,
      double? averageRating,
      int totalReviews,
      String? profilePictureUrl,
      String? logoUrl});

  @override
  $ProfileCategoryCopyWith<$Res>? get category;
}

/// @nodoc
class __$$ProfileDataImplCopyWithImpl<$Res>
    extends _$ProfileDataCopyWithImpl<$Res, _$ProfileDataImpl>
    implements _$$ProfileDataImplCopyWith<$Res> {
  __$$ProfileDataImplCopyWithImpl(
      _$ProfileDataImpl _value, $Res Function(_$ProfileDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? title = freezed,
    Object? phone = freezed,
    Object? presentation = freezed,
    Object? isVerified = null,
    Object? isSetupComplete = null,
    Object? category = freezed,
    Object? averageRating = freezed,
    Object? totalReviews = null,
    Object? profilePictureUrl = freezed,
    Object? logoUrl = freezed,
  }) {
    return _then(_$ProfileDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      presentation: freezed == presentation
          ? _value.presentation
          : presentation // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      isSetupComplete: null == isSetupComplete
          ? _value.isSetupComplete
          : isSetupComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ProfileCategory?,
      averageRating: freezed == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double?,
      totalReviews: null == totalReviews
          ? _value.totalReviews
          : totalReviews // ignore: cast_nullable_to_non_nullable
              as int,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      logoUrl: freezed == logoUrl
          ? _value.logoUrl
          : logoUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileDataImpl extends _ProfileData {
  const _$ProfileDataImpl(
      {required this.id,
      required this.userId,
      this.title,
      this.phone,
      this.presentation,
      required this.isVerified,
      required this.isSetupComplete,
      this.category,
      this.averageRating,
      required this.totalReviews,
      this.profilePictureUrl,
      this.logoUrl})
      : super._();

  factory _$ProfileDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileDataImplFromJson(json);

  @override
  final int id;
  @override
  final String userId;
  @override
  final String? title;
  @override
  final String? phone;
  @override
  final String? presentation;
  @override
  final bool isVerified;
  @override
  final bool isSetupComplete;
  @override
  final ProfileCategory? category;
  @override
  final double? averageRating;
  @override
  final int totalReviews;
  @override
  final String? profilePictureUrl;
  @override
  final String? logoUrl;

  @override
  String toString() {
    return 'ProfileData(id: $id, userId: $userId, title: $title, phone: $phone, presentation: $presentation, isVerified: $isVerified, isSetupComplete: $isSetupComplete, category: $category, averageRating: $averageRating, totalReviews: $totalReviews, profilePictureUrl: $profilePictureUrl, logoUrl: $logoUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.presentation, presentation) ||
                other.presentation == presentation) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isSetupComplete, isSetupComplete) ||
                other.isSetupComplete == isSetupComplete) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.averageRating, averageRating) ||
                other.averageRating == averageRating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.logoUrl, logoUrl) || other.logoUrl == logoUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      title,
      phone,
      presentation,
      isVerified,
      isSetupComplete,
      category,
      averageRating,
      totalReviews,
      profilePictureUrl,
      logoUrl);

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileDataImplCopyWith<_$ProfileDataImpl> get copyWith =>
      __$$ProfileDataImplCopyWithImpl<_$ProfileDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileDataImplToJson(
      this,
    );
  }
}

abstract class _ProfileData extends ProfileData {
  const factory _ProfileData(
      {required final int id,
      required final String userId,
      final String? title,
      final String? phone,
      final String? presentation,
      required final bool isVerified,
      required final bool isSetupComplete,
      final ProfileCategory? category,
      final double? averageRating,
      required final int totalReviews,
      final String? profilePictureUrl,
      final String? logoUrl}) = _$ProfileDataImpl;
  const _ProfileData._() : super._();

  factory _ProfileData.fromJson(Map<String, dynamic> json) =
      _$ProfileDataImpl.fromJson;

  @override
  int get id;
  @override
  String get userId;
  @override
  String? get title;
  @override
  String? get phone;
  @override
  String? get presentation;
  @override
  bool get isVerified;
  @override
  bool get isSetupComplete;
  @override
  ProfileCategory? get category;
  @override
  double? get averageRating;
  @override
  int get totalReviews;
  @override
  String? get profilePictureUrl;
  @override
  String? get logoUrl;

  /// Create a copy of ProfileData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileDataImplCopyWith<_$ProfileDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileUpdateRequest _$ProfileUpdateRequestFromJson(Map<String, dynamic> json) {
  return _ProfileUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$ProfileUpdateRequest {
  String? get title => throw _privateConstructorUsedError;
  String? get phone => throw _privateConstructorUsedError;
  String? get presentation => throw _privateConstructorUsedError;
  int? get providerCategoryId => throw _privateConstructorUsedError;

  /// Serializes this ProfileUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileUpdateRequestCopyWith<ProfileUpdateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileUpdateRequestCopyWith<$Res> {
  factory $ProfileUpdateRequestCopyWith(ProfileUpdateRequest value,
          $Res Function(ProfileUpdateRequest) then) =
      _$ProfileUpdateRequestCopyWithImpl<$Res, ProfileUpdateRequest>;
  @useResult
  $Res call(
      {String? title,
      String? phone,
      String? presentation,
      int? providerCategoryId});
}

/// @nodoc
class _$ProfileUpdateRequestCopyWithImpl<$Res,
        $Val extends ProfileUpdateRequest>
    implements $ProfileUpdateRequestCopyWith<$Res> {
  _$ProfileUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? phone = freezed,
    Object? presentation = freezed,
    Object? providerCategoryId = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      presentation: freezed == presentation
          ? _value.presentation
          : presentation // ignore: cast_nullable_to_non_nullable
              as String?,
      providerCategoryId: freezed == providerCategoryId
          ? _value.providerCategoryId
          : providerCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileUpdateRequestImplCopyWith<$Res>
    implements $ProfileUpdateRequestCopyWith<$Res> {
  factory _$$ProfileUpdateRequestImplCopyWith(_$ProfileUpdateRequestImpl value,
          $Res Function(_$ProfileUpdateRequestImpl) then) =
      __$$ProfileUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? phone,
      String? presentation,
      int? providerCategoryId});
}

/// @nodoc
class __$$ProfileUpdateRequestImplCopyWithImpl<$Res>
    extends _$ProfileUpdateRequestCopyWithImpl<$Res, _$ProfileUpdateRequestImpl>
    implements _$$ProfileUpdateRequestImplCopyWith<$Res> {
  __$$ProfileUpdateRequestImplCopyWithImpl(_$ProfileUpdateRequestImpl _value,
      $Res Function(_$ProfileUpdateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? phone = freezed,
    Object? presentation = freezed,
    Object? providerCategoryId = freezed,
  }) {
    return _then(_$ProfileUpdateRequestImpl(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      phone: freezed == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String?,
      presentation: freezed == presentation
          ? _value.presentation
          : presentation // ignore: cast_nullable_to_non_nullable
              as String?,
      providerCategoryId: freezed == providerCategoryId
          ? _value.providerCategoryId
          : providerCategoryId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileUpdateRequestImpl implements _ProfileUpdateRequest {
  const _$ProfileUpdateRequestImpl(
      {this.title, this.phone, this.presentation, this.providerCategoryId});

  factory _$ProfileUpdateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileUpdateRequestImplFromJson(json);

  @override
  final String? title;
  @override
  final String? phone;
  @override
  final String? presentation;
  @override
  final int? providerCategoryId;

  @override
  String toString() {
    return 'ProfileUpdateRequest(title: $title, phone: $phone, presentation: $presentation, providerCategoryId: $providerCategoryId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileUpdateRequestImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.presentation, presentation) ||
                other.presentation == presentation) &&
            (identical(other.providerCategoryId, providerCategoryId) ||
                other.providerCategoryId == providerCategoryId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, title, phone, presentation, providerCategoryId);

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileUpdateRequestImplCopyWith<_$ProfileUpdateRequestImpl>
      get copyWith =>
          __$$ProfileUpdateRequestImplCopyWithImpl<_$ProfileUpdateRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileUpdateRequestImplToJson(
      this,
    );
  }
}

abstract class _ProfileUpdateRequest implements ProfileUpdateRequest {
  const factory _ProfileUpdateRequest(
      {final String? title,
      final String? phone,
      final String? presentation,
      final int? providerCategoryId}) = _$ProfileUpdateRequestImpl;

  factory _ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$ProfileUpdateRequestImpl.fromJson;

  @override
  String? get title;
  @override
  String? get phone;
  @override
  String? get presentation;
  @override
  int? get providerCategoryId;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileUpdateRequestImplCopyWith<_$ProfileUpdateRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProfileResponse _$ProfileResponseFromJson(Map<String, dynamic> json) {
  return _ProfileResponse.fromJson(json);
}

/// @nodoc
mixin _$ProfileResponse {
  bool get success => throw _privateConstructorUsedError;
  ProfileData? get data => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this ProfileResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileResponseCopyWith<ProfileResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileResponseCopyWith<$Res> {
  factory $ProfileResponseCopyWith(
          ProfileResponse value, $Res Function(ProfileResponse) then) =
      _$ProfileResponseCopyWithImpl<$Res, ProfileResponse>;
  @useResult
  $Res call({bool success, ProfileData? data, String? message});

  $ProfileDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$ProfileResponseCopyWithImpl<$Res, $Val extends ProfileResponse>
    implements $ProfileResponseCopyWith<$Res> {
  _$ProfileResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ProfileData?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $ProfileDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProfileResponseImplCopyWith<$Res>
    implements $ProfileResponseCopyWith<$Res> {
  factory _$$ProfileResponseImplCopyWith(_$ProfileResponseImpl value,
          $Res Function(_$ProfileResponseImpl) then) =
      __$$ProfileResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, ProfileData? data, String? message});

  @override
  $ProfileDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$ProfileResponseImplCopyWithImpl<$Res>
    extends _$ProfileResponseCopyWithImpl<$Res, _$ProfileResponseImpl>
    implements _$$ProfileResponseImplCopyWith<$Res> {
  __$$ProfileResponseImplCopyWithImpl(
      _$ProfileResponseImpl _value, $Res Function(_$ProfileResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? message = freezed,
  }) {
    return _then(_$ProfileResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ProfileData?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileResponseImpl implements _ProfileResponse {
  const _$ProfileResponseImpl({required this.success, this.data, this.message});

  factory _$ProfileResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final ProfileData? data;
  @override
  final String? message;

  @override
  String toString() {
    return 'ProfileResponse(success: $success, data: $data, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, message);

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileResponseImplCopyWith<_$ProfileResponseImpl> get copyWith =>
      __$$ProfileResponseImplCopyWithImpl<_$ProfileResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileResponseImplToJson(
      this,
    );
  }
}

abstract class _ProfileResponse implements ProfileResponse {
  const factory _ProfileResponse(
      {required final bool success,
      final ProfileData? data,
      final String? message}) = _$ProfileResponseImpl;

  factory _ProfileResponse.fromJson(Map<String, dynamic> json) =
      _$ProfileResponseImpl.fromJson;

  @override
  bool get success;
  @override
  ProfileData? get data;
  @override
  String? get message;

  /// Create a copy of ProfileResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileResponseImplCopyWith<_$ProfileResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileError _$ProfileErrorFromJson(Map<String, dynamic> json) {
  return _ProfileError.fromJson(json);
}

/// @nodoc
mixin _$ProfileError {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;
  List<String>? get validationErrors => throw _privateConstructorUsedError;

  /// Serializes this ProfileError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileErrorCopyWith<ProfileError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileErrorCopyWith<$Res> {
  factory $ProfileErrorCopyWith(
          ProfileError value, $Res Function(ProfileError) then) =
      _$ProfileErrorCopyWithImpl<$Res, ProfileError>;
  @useResult
  $Res call(
      {String code,
      String message,
      String? details,
      List<String>? validationErrors});
}

/// @nodoc
class _$ProfileErrorCopyWithImpl<$Res, $Val extends ProfileError>
    implements $ProfileErrorCopyWith<$Res> {
  _$ProfileErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
    Object? validationErrors = freezed,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      validationErrors: freezed == validationErrors
          ? _value.validationErrors
          : validationErrors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileErrorImplCopyWith<$Res>
    implements $ProfileErrorCopyWith<$Res> {
  factory _$$ProfileErrorImplCopyWith(
          _$ProfileErrorImpl value, $Res Function(_$ProfileErrorImpl) then) =
      __$$ProfileErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String code,
      String message,
      String? details,
      List<String>? validationErrors});
}

/// @nodoc
class __$$ProfileErrorImplCopyWithImpl<$Res>
    extends _$ProfileErrorCopyWithImpl<$Res, _$ProfileErrorImpl>
    implements _$$ProfileErrorImplCopyWith<$Res> {
  __$$ProfileErrorImplCopyWithImpl(
      _$ProfileErrorImpl _value, $Res Function(_$ProfileErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
    Object? validationErrors = freezed,
  }) {
    return _then(_$ProfileErrorImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      validationErrors: freezed == validationErrors
          ? _value._validationErrors
          : validationErrors // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileErrorImpl implements _ProfileError {
  const _$ProfileErrorImpl(
      {required this.code,
      required this.message,
      this.details,
      final List<String>? validationErrors})
      : _validationErrors = validationErrors;

  factory _$ProfileErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileErrorImplFromJson(json);

  @override
  final String code;
  @override
  final String message;
  @override
  final String? details;
  final List<String>? _validationErrors;
  @override
  List<String>? get validationErrors {
    final value = _validationErrors;
    if (value == null) return null;
    if (_validationErrors is EqualUnmodifiableListView)
      return _validationErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProfileError(code: $code, message: $message, details: $details, validationErrors: $validationErrors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileErrorImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details) &&
            const DeepCollectionEquality()
                .equals(other._validationErrors, _validationErrors));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message, details,
      const DeepCollectionEquality().hash(_validationErrors));

  /// Create a copy of ProfileError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileErrorImplCopyWith<_$ProfileErrorImpl> get copyWith =>
      __$$ProfileErrorImplCopyWithImpl<_$ProfileErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileErrorImplToJson(
      this,
    );
  }
}

abstract class _ProfileError implements ProfileError {
  const factory _ProfileError(
      {required final String code,
      required final String message,
      final String? details,
      final List<String>? validationErrors}) = _$ProfileErrorImpl;

  factory _ProfileError.fromJson(Map<String, dynamic> json) =
      _$ProfileErrorImpl.fromJson;

  @override
  String get code;
  @override
  String get message;
  @override
  String? get details;
  @override
  List<String>? get validationErrors;

  /// Create a copy of ProfileError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileErrorImplCopyWith<_$ProfileErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ProfileResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ProfileData data) success,
    required TResult Function(ProfileError error) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ProfileData data)? success,
    TResult? Function(ProfileError error)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ProfileData data)? success,
    TResult Function(ProfileError error)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileSuccess value) success,
    required TResult Function(ProfileFailure value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileSuccess value)? success,
    TResult? Function(ProfileFailure value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileSuccess value)? success,
    TResult Function(ProfileFailure value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileResultCopyWith<$Res> {
  factory $ProfileResultCopyWith(
          ProfileResult value, $Res Function(ProfileResult) then) =
      _$ProfileResultCopyWithImpl<$Res, ProfileResult>;
}

/// @nodoc
class _$ProfileResultCopyWithImpl<$Res, $Val extends ProfileResult>
    implements $ProfileResultCopyWith<$Res> {
  _$ProfileResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ProfileSuccessImplCopyWith<$Res> {
  factory _$$ProfileSuccessImplCopyWith(_$ProfileSuccessImpl value,
          $Res Function(_$ProfileSuccessImpl) then) =
      __$$ProfileSuccessImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ProfileData data});

  $ProfileDataCopyWith<$Res> get data;
}

/// @nodoc
class __$$ProfileSuccessImplCopyWithImpl<$Res>
    extends _$ProfileResultCopyWithImpl<$Res, _$ProfileSuccessImpl>
    implements _$$ProfileSuccessImplCopyWith<$Res> {
  __$$ProfileSuccessImplCopyWithImpl(
      _$ProfileSuccessImpl _value, $Res Function(_$ProfileSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$ProfileSuccessImpl(
      null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ProfileData,
    ));
  }

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileDataCopyWith<$Res> get data {
    return $ProfileDataCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value));
    });
  }
}

/// @nodoc

class _$ProfileSuccessImpl implements ProfileSuccess {
  const _$ProfileSuccessImpl(this.data);

  @override
  final ProfileData data;

  @override
  String toString() {
    return 'ProfileResult.success(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileSuccessImpl &&
            (identical(other.data, data) || other.data == data));
  }

  @override
  int get hashCode => Object.hash(runtimeType, data);

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileSuccessImplCopyWith<_$ProfileSuccessImpl> get copyWith =>
      __$$ProfileSuccessImplCopyWithImpl<_$ProfileSuccessImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ProfileData data) success,
    required TResult Function(ProfileError error) error,
  }) {
    return success(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ProfileData data)? success,
    TResult? Function(ProfileError error)? error,
  }) {
    return success?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ProfileData data)? success,
    TResult Function(ProfileError error)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileSuccess value) success,
    required TResult Function(ProfileFailure value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileSuccess value)? success,
    TResult? Function(ProfileFailure value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileSuccess value)? success,
    TResult Function(ProfileFailure value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class ProfileSuccess implements ProfileResult {
  const factory ProfileSuccess(final ProfileData data) = _$ProfileSuccessImpl;

  ProfileData get data;

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileSuccessImplCopyWith<_$ProfileSuccessImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProfileFailureImplCopyWith<$Res> {
  factory _$$ProfileFailureImplCopyWith(_$ProfileFailureImpl value,
          $Res Function(_$ProfileFailureImpl) then) =
      __$$ProfileFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ProfileError error});

  $ProfileErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$ProfileFailureImplCopyWithImpl<$Res>
    extends _$ProfileResultCopyWithImpl<$Res, _$ProfileFailureImpl>
    implements _$$ProfileFailureImplCopyWith<$Res> {
  __$$ProfileFailureImplCopyWithImpl(
      _$ProfileFailureImpl _value, $Res Function(_$ProfileFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$ProfileFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as ProfileError,
    ));
  }

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfileErrorCopyWith<$Res> get error {
    return $ProfileErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$ProfileFailureImpl implements ProfileFailure {
  const _$ProfileFailureImpl(this.error);

  @override
  final ProfileError error;

  @override
  String toString() {
    return 'ProfileResult.error(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileFailureImplCopyWith<_$ProfileFailureImpl> get copyWith =>
      __$$ProfileFailureImplCopyWithImpl<_$ProfileFailureImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(ProfileData data) success,
    required TResult Function(ProfileError error) error,
  }) {
    return error(this.error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(ProfileData data)? success,
    TResult? Function(ProfileError error)? error,
  }) {
    return error?.call(this.error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(ProfileData data)? success,
    TResult Function(ProfileError error)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this.error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileSuccess value) success,
    required TResult Function(ProfileFailure value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileSuccess value)? success,
    TResult? Function(ProfileFailure value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileSuccess value)? success,
    TResult Function(ProfileFailure value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class ProfileFailure implements ProfileResult {
  const factory ProfileFailure(final ProfileError error) = _$ProfileFailureImpl;

  ProfileError get error;

  /// Create a copy of ProfileResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileFailureImplCopyWith<_$ProfileFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfilePictureUploadResponse _$ProfilePictureUploadResponseFromJson(
    Map<String, dynamic> json) {
  return _ProfilePictureUploadResponse.fromJson(json);
}

/// @nodoc
mixin _$ProfilePictureUploadResponse {
  String get uploadUrl => throw _privateConstructorUsedError;
  Map<String, dynamic> get uploadFields => throw _privateConstructorUsedError;
  UploadFile get file => throw _privateConstructorUsedError;
  UploadUser get user => throw _privateConstructorUsedError;

  /// Serializes this ProfilePictureUploadResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfilePictureUploadResponseCopyWith<ProfilePictureUploadResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfilePictureUploadResponseCopyWith<$Res> {
  factory $ProfilePictureUploadResponseCopyWith(
          ProfilePictureUploadResponse value,
          $Res Function(ProfilePictureUploadResponse) then) =
      _$ProfilePictureUploadResponseCopyWithImpl<$Res,
          ProfilePictureUploadResponse>;
  @useResult
  $Res call(
      {String uploadUrl,
      Map<String, dynamic> uploadFields,
      UploadFile file,
      UploadUser user});

  $UploadFileCopyWith<$Res> get file;
  $UploadUserCopyWith<$Res> get user;
}

/// @nodoc
class _$ProfilePictureUploadResponseCopyWithImpl<$Res,
        $Val extends ProfilePictureUploadResponse>
    implements $ProfilePictureUploadResponseCopyWith<$Res> {
  _$ProfilePictureUploadResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadUrl = null,
    Object? uploadFields = null,
    Object? file = null,
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      uploadUrl: null == uploadUrl
          ? _value.uploadUrl
          : uploadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      uploadFields: null == uploadFields
          ? _value.uploadFields
          : uploadFields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as UploadFile,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UploadUser,
    ) as $Val);
  }

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UploadFileCopyWith<$Res> get file {
    return $UploadFileCopyWith<$Res>(_value.file, (value) {
      return _then(_value.copyWith(file: value) as $Val);
    });
  }

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UploadUserCopyWith<$Res> get user {
    return $UploadUserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProfilePictureUploadResponseImplCopyWith<$Res>
    implements $ProfilePictureUploadResponseCopyWith<$Res> {
  factory _$$ProfilePictureUploadResponseImplCopyWith(
          _$ProfilePictureUploadResponseImpl value,
          $Res Function(_$ProfilePictureUploadResponseImpl) then) =
      __$$ProfilePictureUploadResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String uploadUrl,
      Map<String, dynamic> uploadFields,
      UploadFile file,
      UploadUser user});

  @override
  $UploadFileCopyWith<$Res> get file;
  @override
  $UploadUserCopyWith<$Res> get user;
}

/// @nodoc
class __$$ProfilePictureUploadResponseImplCopyWithImpl<$Res>
    extends _$ProfilePictureUploadResponseCopyWithImpl<$Res,
        _$ProfilePictureUploadResponseImpl>
    implements _$$ProfilePictureUploadResponseImplCopyWith<$Res> {
  __$$ProfilePictureUploadResponseImplCopyWithImpl(
      _$ProfilePictureUploadResponseImpl _value,
      $Res Function(_$ProfilePictureUploadResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uploadUrl = null,
    Object? uploadFields = null,
    Object? file = null,
    Object? user = null,
  }) {
    return _then(_$ProfilePictureUploadResponseImpl(
      uploadUrl: null == uploadUrl
          ? _value.uploadUrl
          : uploadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      uploadFields: null == uploadFields
          ? _value._uploadFields
          : uploadFields // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as UploadFile,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as UploadUser,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfilePictureUploadResponseImpl
    implements _ProfilePictureUploadResponse {
  const _$ProfilePictureUploadResponseImpl(
      {required this.uploadUrl,
      required final Map<String, dynamic> uploadFields,
      required this.file,
      required this.user})
      : _uploadFields = uploadFields;

  factory _$ProfilePictureUploadResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$ProfilePictureUploadResponseImplFromJson(json);

  @override
  final String uploadUrl;
  final Map<String, dynamic> _uploadFields;
  @override
  Map<String, dynamic> get uploadFields {
    if (_uploadFields is EqualUnmodifiableMapView) return _uploadFields;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_uploadFields);
  }

  @override
  final UploadFile file;
  @override
  final UploadUser user;

  @override
  String toString() {
    return 'ProfilePictureUploadResponse(uploadUrl: $uploadUrl, uploadFields: $uploadFields, file: $file, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfilePictureUploadResponseImpl &&
            (identical(other.uploadUrl, uploadUrl) ||
                other.uploadUrl == uploadUrl) &&
            const DeepCollectionEquality()
                .equals(other._uploadFields, _uploadFields) &&
            (identical(other.file, file) || other.file == file) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, uploadUrl,
      const DeepCollectionEquality().hash(_uploadFields), file, user);

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfilePictureUploadResponseImplCopyWith<
          _$ProfilePictureUploadResponseImpl>
      get copyWith => __$$ProfilePictureUploadResponseImplCopyWithImpl<
          _$ProfilePictureUploadResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfilePictureUploadResponseImplToJson(
      this,
    );
  }
}

abstract class _ProfilePictureUploadResponse
    implements ProfilePictureUploadResponse {
  const factory _ProfilePictureUploadResponse(
      {required final String uploadUrl,
      required final Map<String, dynamic> uploadFields,
      required final UploadFile file,
      required final UploadUser user}) = _$ProfilePictureUploadResponseImpl;

  factory _ProfilePictureUploadResponse.fromJson(Map<String, dynamic> json) =
      _$ProfilePictureUploadResponseImpl.fromJson;

  @override
  String get uploadUrl;
  @override
  Map<String, dynamic> get uploadFields;
  @override
  UploadFile get file;
  @override
  UploadUser get user;

  /// Create a copy of ProfilePictureUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfilePictureUploadResponseImplCopyWith<
          _$ProfilePictureUploadResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UploadFile _$UploadFileFromJson(Map<String, dynamic> json) {
  return _UploadFile.fromJson(json);
}

/// @nodoc
mixin _$UploadFile {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;

  /// Serializes this UploadFile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UploadFile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UploadFileCopyWith<UploadFile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadFileCopyWith<$Res> {
  factory $UploadFileCopyWith(
          UploadFile value, $Res Function(UploadFile) then) =
      _$UploadFileCopyWithImpl<$Res, UploadFile>;
  @useResult
  $Res call({String id, String name, String type, String key});
}

/// @nodoc
class _$UploadFileCopyWithImpl<$Res, $Val extends UploadFile>
    implements $UploadFileCopyWith<$Res> {
  _$UploadFileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UploadFile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? key = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadFileImplCopyWith<$Res>
    implements $UploadFileCopyWith<$Res> {
  factory _$$UploadFileImplCopyWith(
          _$UploadFileImpl value, $Res Function(_$UploadFileImpl) then) =
      __$$UploadFileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String name, String type, String key});
}

/// @nodoc
class __$$UploadFileImplCopyWithImpl<$Res>
    extends _$UploadFileCopyWithImpl<$Res, _$UploadFileImpl>
    implements _$$UploadFileImplCopyWith<$Res> {
  __$$UploadFileImplCopyWithImpl(
      _$UploadFileImpl _value, $Res Function(_$UploadFileImpl) _then)
      : super(_value, _then);

  /// Create a copy of UploadFile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? key = null,
  }) {
    return _then(_$UploadFileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UploadFileImpl implements _UploadFile {
  const _$UploadFileImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.key});

  factory _$UploadFileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UploadFileImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String type;
  @override
  final String key;

  @override
  String toString() {
    return 'UploadFile(id: $id, name: $name, type: $type, key: $key)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadFileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.key, key) || other.key == key));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, type, key);

  /// Create a copy of UploadFile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadFileImplCopyWith<_$UploadFileImpl> get copyWith =>
      __$$UploadFileImplCopyWithImpl<_$UploadFileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UploadFileImplToJson(
      this,
    );
  }
}

abstract class _UploadFile implements UploadFile {
  const factory _UploadFile(
      {required final String id,
      required final String name,
      required final String type,
      required final String key}) = _$UploadFileImpl;

  factory _UploadFile.fromJson(Map<String, dynamic> json) =
      _$UploadFileImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get type;
  @override
  String get key;

  /// Create a copy of UploadFile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UploadFileImplCopyWith<_$UploadFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UploadUser _$UploadUserFromJson(Map<String, dynamic> json) {
  return _UploadUser.fromJson(json);
}

/// @nodoc
mixin _$UploadUser {
  String get id => throw _privateConstructorUsedError;
  String get profilePictureId => throw _privateConstructorUsedError;

  /// Serializes this UploadUser to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UploadUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UploadUserCopyWith<UploadUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UploadUserCopyWith<$Res> {
  factory $UploadUserCopyWith(
          UploadUser value, $Res Function(UploadUser) then) =
      _$UploadUserCopyWithImpl<$Res, UploadUser>;
  @useResult
  $Res call({String id, String profilePictureId});
}

/// @nodoc
class _$UploadUserCopyWithImpl<$Res, $Val extends UploadUser>
    implements $UploadUserCopyWith<$Res> {
  _$UploadUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UploadUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? profilePictureId = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      profilePictureId: null == profilePictureId
          ? _value.profilePictureId
          : profilePictureId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UploadUserImplCopyWith<$Res>
    implements $UploadUserCopyWith<$Res> {
  factory _$$UploadUserImplCopyWith(
          _$UploadUserImpl value, $Res Function(_$UploadUserImpl) then) =
      __$$UploadUserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, String profilePictureId});
}

/// @nodoc
class __$$UploadUserImplCopyWithImpl<$Res>
    extends _$UploadUserCopyWithImpl<$Res, _$UploadUserImpl>
    implements _$$UploadUserImplCopyWith<$Res> {
  __$$UploadUserImplCopyWithImpl(
      _$UploadUserImpl _value, $Res Function(_$UploadUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of UploadUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? profilePictureId = null,
  }) {
    return _then(_$UploadUserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      profilePictureId: null == profilePictureId
          ? _value.profilePictureId
          : profilePictureId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UploadUserImpl implements _UploadUser {
  const _$UploadUserImpl({required this.id, required this.profilePictureId});

  factory _$UploadUserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UploadUserImplFromJson(json);

  @override
  final String id;
  @override
  final String profilePictureId;

  @override
  String toString() {
    return 'UploadUser(id: $id, profilePictureId: $profilePictureId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadUserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.profilePictureId, profilePictureId) ||
                other.profilePictureId == profilePictureId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, profilePictureId);

  /// Create a copy of UploadUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadUserImplCopyWith<_$UploadUserImpl> get copyWith =>
      __$$UploadUserImplCopyWithImpl<_$UploadUserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UploadUserImplToJson(
      this,
    );
  }
}

abstract class _UploadUser implements UploadUser {
  const factory _UploadUser(
      {required final String id,
      required final String profilePictureId}) = _$UploadUserImpl;

  factory _UploadUser.fromJson(Map<String, dynamic> json) =
      _$UploadUserImpl.fromJson;

  @override
  String get id;
  @override
  String get profilePictureId;

  /// Create a copy of UploadUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UploadUserImplCopyWith<_$UploadUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfilePictureDetails _$ProfilePictureDetailsFromJson(
    Map<String, dynamic> json) {
  return _ProfilePictureDetails.fromJson(json);
}

/// @nodoc
mixin _$ProfilePictureDetails {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get type => throw _privateConstructorUsedError;
  String get key => throw _privateConstructorUsedError;
  String get downloadUrl => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;

  /// Serializes this ProfilePictureDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfilePictureDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfilePictureDetailsCopyWith<ProfilePictureDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfilePictureDetailsCopyWith<$Res> {
  factory $ProfilePictureDetailsCopyWith(ProfilePictureDetails value,
          $Res Function(ProfilePictureDetails) then) =
      _$ProfilePictureDetailsCopyWithImpl<$Res, ProfilePictureDetails>;
  @useResult
  $Res call(
      {String id,
      String name,
      String type,
      String key,
      String downloadUrl,
      DateTime createdAt});
}

/// @nodoc
class _$ProfilePictureDetailsCopyWithImpl<$Res,
        $Val extends ProfilePictureDetails>
    implements $ProfilePictureDetailsCopyWith<$Res> {
  _$ProfilePictureDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfilePictureDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? key = null,
    Object? downloadUrl = null,
    Object? createdAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfilePictureDetailsImplCopyWith<$Res>
    implements $ProfilePictureDetailsCopyWith<$Res> {
  factory _$$ProfilePictureDetailsImplCopyWith(
          _$ProfilePictureDetailsImpl value,
          $Res Function(_$ProfilePictureDetailsImpl) then) =
      __$$ProfilePictureDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String type,
      String key,
      String downloadUrl,
      DateTime createdAt});
}

/// @nodoc
class __$$ProfilePictureDetailsImplCopyWithImpl<$Res>
    extends _$ProfilePictureDetailsCopyWithImpl<$Res,
        _$ProfilePictureDetailsImpl>
    implements _$$ProfilePictureDetailsImplCopyWith<$Res> {
  __$$ProfilePictureDetailsImplCopyWithImpl(_$ProfilePictureDetailsImpl _value,
      $Res Function(_$ProfilePictureDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfilePictureDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? type = null,
    Object? key = null,
    Object? downloadUrl = null,
    Object? createdAt = null,
  }) {
    return _then(_$ProfilePictureDetailsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      key: null == key
          ? _value.key
          : key // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfilePictureDetailsImpl implements _ProfilePictureDetails {
  const _$ProfilePictureDetailsImpl(
      {required this.id,
      required this.name,
      required this.type,
      required this.key,
      required this.downloadUrl,
      required this.createdAt});

  factory _$ProfilePictureDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfilePictureDetailsImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String type;
  @override
  final String key;
  @override
  final String downloadUrl;
  @override
  final DateTime createdAt;

  @override
  String toString() {
    return 'ProfilePictureDetails(id: $id, name: $name, type: $type, key: $key, downloadUrl: $downloadUrl, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfilePictureDetailsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.key, key) || other.key == key) &&
            (identical(other.downloadUrl, downloadUrl) ||
                other.downloadUrl == downloadUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, type, key, downloadUrl, createdAt);

  /// Create a copy of ProfilePictureDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfilePictureDetailsImplCopyWith<_$ProfilePictureDetailsImpl>
      get copyWith => __$$ProfilePictureDetailsImplCopyWithImpl<
          _$ProfilePictureDetailsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfilePictureDetailsImplToJson(
      this,
    );
  }
}

abstract class _ProfilePictureDetails implements ProfilePictureDetails {
  const factory _ProfilePictureDetails(
      {required final String id,
      required final String name,
      required final String type,
      required final String key,
      required final String downloadUrl,
      required final DateTime createdAt}) = _$ProfilePictureDetailsImpl;

  factory _ProfilePictureDetails.fromJson(Map<String, dynamic> json) =
      _$ProfilePictureDetailsImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get type;
  @override
  String get key;
  @override
  String get downloadUrl;
  @override
  DateTime get createdAt;

  /// Create a copy of ProfilePictureDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfilePictureDetailsImplCopyWith<_$ProfilePictureDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ProfilePictureInfo _$ProfilePictureInfoFromJson(Map<String, dynamic> json) {
  return _ProfilePictureInfo.fromJson(json);
}

/// @nodoc
mixin _$ProfilePictureInfo {
  bool get hasProfilePicture => throw _privateConstructorUsedError;
  ProfilePictureDetails? get profilePicture =>
      throw _privateConstructorUsedError;

  /// Serializes this ProfilePictureInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfilePictureInfoCopyWith<ProfilePictureInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfilePictureInfoCopyWith<$Res> {
  factory $ProfilePictureInfoCopyWith(
          ProfilePictureInfo value, $Res Function(ProfilePictureInfo) then) =
      _$ProfilePictureInfoCopyWithImpl<$Res, ProfilePictureInfo>;
  @useResult
  $Res call({bool hasProfilePicture, ProfilePictureDetails? profilePicture});

  $ProfilePictureDetailsCopyWith<$Res>? get profilePicture;
}

/// @nodoc
class _$ProfilePictureInfoCopyWithImpl<$Res, $Val extends ProfilePictureInfo>
    implements $ProfilePictureInfoCopyWith<$Res> {
  _$ProfilePictureInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasProfilePicture = null,
    Object? profilePicture = freezed,
  }) {
    return _then(_value.copyWith(
      hasProfilePicture: null == hasProfilePicture
          ? _value.hasProfilePicture
          : hasProfilePicture // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePicture: freezed == profilePicture
          ? _value.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as ProfilePictureDetails?,
    ) as $Val);
  }

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProfilePictureDetailsCopyWith<$Res>? get profilePicture {
    if (_value.profilePicture == null) {
      return null;
    }

    return $ProfilePictureDetailsCopyWith<$Res>(_value.profilePicture!,
        (value) {
      return _then(_value.copyWith(profilePicture: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProfilePictureInfoImplCopyWith<$Res>
    implements $ProfilePictureInfoCopyWith<$Res> {
  factory _$$ProfilePictureInfoImplCopyWith(_$ProfilePictureInfoImpl value,
          $Res Function(_$ProfilePictureInfoImpl) then) =
      __$$ProfilePictureInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool hasProfilePicture, ProfilePictureDetails? profilePicture});

  @override
  $ProfilePictureDetailsCopyWith<$Res>? get profilePicture;
}

/// @nodoc
class __$$ProfilePictureInfoImplCopyWithImpl<$Res>
    extends _$ProfilePictureInfoCopyWithImpl<$Res, _$ProfilePictureInfoImpl>
    implements _$$ProfilePictureInfoImplCopyWith<$Res> {
  __$$ProfilePictureInfoImplCopyWithImpl(_$ProfilePictureInfoImpl _value,
      $Res Function(_$ProfilePictureInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasProfilePicture = null,
    Object? profilePicture = freezed,
  }) {
    return _then(_$ProfilePictureInfoImpl(
      hasProfilePicture: null == hasProfilePicture
          ? _value.hasProfilePicture
          : hasProfilePicture // ignore: cast_nullable_to_non_nullable
              as bool,
      profilePicture: freezed == profilePicture
          ? _value.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as ProfilePictureDetails?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfilePictureInfoImpl implements _ProfilePictureInfo {
  const _$ProfilePictureInfoImpl(
      {required this.hasProfilePicture, this.profilePicture});

  factory _$ProfilePictureInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfilePictureInfoImplFromJson(json);

  @override
  final bool hasProfilePicture;
  @override
  final ProfilePictureDetails? profilePicture;

  @override
  String toString() {
    return 'ProfilePictureInfo(hasProfilePicture: $hasProfilePicture, profilePicture: $profilePicture)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfilePictureInfoImpl &&
            (identical(other.hasProfilePicture, hasProfilePicture) ||
                other.hasProfilePicture == hasProfilePicture) &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, hasProfilePicture, profilePicture);

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfilePictureInfoImplCopyWith<_$ProfilePictureInfoImpl> get copyWith =>
      __$$ProfilePictureInfoImplCopyWithImpl<_$ProfilePictureInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfilePictureInfoImplToJson(
      this,
    );
  }
}

abstract class _ProfilePictureInfo implements ProfilePictureInfo {
  const factory _ProfilePictureInfo(
      {required final bool hasProfilePicture,
      final ProfilePictureDetails? profilePicture}) = _$ProfilePictureInfoImpl;

  factory _ProfilePictureInfo.fromJson(Map<String, dynamic> json) =
      _$ProfilePictureInfoImpl.fromJson;

  @override
  bool get hasProfilePicture;
  @override
  ProfilePictureDetails? get profilePicture;

  /// Create a copy of ProfilePictureInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfilePictureInfoImplCopyWith<_$ProfilePictureInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
