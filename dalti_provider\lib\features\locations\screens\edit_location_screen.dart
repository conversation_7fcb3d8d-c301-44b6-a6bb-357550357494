import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/location_models.dart';
import '../providers/location_provider.dart';
import '../constants/algerian_cities.dart';
import '../services/location_service.dart';
import '../../onboarding/widgets/opening_hours_widget.dart';
import '../../schedules/models/opening_hours_models.dart' as schedule_models;
import '../widgets/location_form_fields.dart';

class EditLocationScreen extends ConsumerStatefulWidget {
  final String locationId;

  const EditLocationScreen({super.key, required this.locationId});

  @override
  ConsumerState<EditLocationScreen> createState() => _EditLocationScreenState();
}

class _EditLocationScreenState extends ConsumerState<EditLocationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _shortNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _countryController = TextEditingController(text: 'Algeria');
  final _postalCodeController = TextEditingController();
  final _mobileController = TextEditingController();
  final _faxController = TextEditingController();
  final _floorController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  final _timezoneController = TextEditingController(text: 'Africa/Algiers');

  bool _parking = false;
  bool _elevator = false;
  bool _handicapAccess = false;
  bool _isLoading = true;
  bool _isGettingLocation = false;
  Location? _originalLocation;

  // Opening hours
  Map<String, List<TimeSlot>> _openingHours = {};

  @override
  void initState() {
    super.initState();
    _loadLocation();

    // Initialize opening hours - all days closed by default
    final daysOfWeek = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    for (String day in daysOfWeek) {
      _openingHours[day] = [];
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _shortNameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _postalCodeController.dispose();
    _mobileController.dispose();
    _faxController.dispose();
    _floorController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    _timezoneController.dispose();
    super.dispose();
  }

  Future<void> _loadLocation() async {
    final locationId = int.tryParse(widget.locationId);
    if (locationId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid location ID'),
            backgroundColor: Colors.red,
          ),
        );
        context.pop();
      }
      return;
    }

    final location = await ref
        .read(locationNotifierProvider.notifier)
        .getLocationById(locationId);

    if (location != null && mounted) {
      setState(() {
        _originalLocation = location;
        _nameController.text = location.name;
        _shortNameController.text = location.shortName ?? '';
        _addressController.text = location.address;
        _cityController.text = location.city;
        _countryController.text = 'Algeria'; // Always Algeria
        _timezoneController.text = location.timezone ?? 'Africa/Algiers';
        _postalCodeController.text = location.postalCode ?? '';
        _mobileController.text = location.mobile ?? '';
        _faxController.text = location.fax ?? '';
        _floorController.text = location.floor ?? '';
        _latitudeController.text = location.latitude?.toString() ?? '';
        _longitudeController.text = location.longitude?.toString() ?? '';
        _parking = location.parking;
        _elevator = location.elevator;
        _handicapAccess = location.handicapAccess;

        // Load opening hours if available
        if (location.openingHours != null) {
          _loadOpeningHours(location.openingHours!);
        }

        _isLoading = false;
      });
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location not found'),
          backgroundColor: Colors.red,
        ),
      );
      context.pop();
    }
  }

  void _loadOpeningHours(
    List<schedule_models.DayOpeningHours> apiOpeningHours,
  ) {
    for (final dayHours in apiOpeningHours) {
      if (dayHours.isActive && dayHours.hours.isNotEmpty) {
        _openingHours[dayHours.dayOfWeek] =
            dayHours.hours.map((apiSlot) {
              return TimeSlot(
                from: _parseTimeOfDay(apiSlot.timeFrom),
                to: _parseTimeOfDay(apiSlot.timeTo),
              );
            }).toList();
      }
    }
  }

  TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate() || _originalLocation == null) {
      return;
    }

    // Create update request with only changed fields
    final request = UpdateLocationRequest(
      name:
          _nameController.text.trim() != _originalLocation!.name
              ? _nameController.text.trim()
              : null,
      shortName:
          _shortNameController.text.trim() !=
                  (_originalLocation!.shortName ?? '')
              ? _shortNameController.text.trim().isNotEmpty
                  ? _shortNameController.text.trim()
                  : null
              : null,
      address:
          _addressController.text.trim() != _originalLocation!.address
              ? _addressController.text.trim()
              : null,
      city:
          _cityController.text.trim() != _originalLocation!.city
              ? _cityController.text.trim()
              : null,
      country: 'Algeria', // Always Algeria
      postalCode:
          _postalCodeController.text.trim() !=
                  (_originalLocation!.postalCode ?? '')
              ? _postalCodeController.text.trim().isNotEmpty
                  ? _postalCodeController.text.trim()
                  : null
              : null,
      timezone: _timezoneController.text.trim(),
      mobile:
          _mobileController.text.trim() != (_originalLocation!.mobile ?? '')
              ? _mobileController.text.trim().isNotEmpty
                  ? _mobileController.text.trim()
                  : null
              : null,
      fax:
          _faxController.text.trim() != (_originalLocation!.fax ?? '')
              ? _faxController.text.trim().isNotEmpty
                  ? _faxController.text.trim()
                  : null
              : null,
      floor:
          _floorController.text.trim() != (_originalLocation!.floor ?? '')
              ? _floorController.text.trim().isNotEmpty
                  ? _floorController.text.trim()
                  : null
              : null,
      latitude:
          _latitudeController.text.trim().isNotEmpty
              ? double.tryParse(_latitudeController.text.trim())
              : null,
      longitude:
          _longitudeController.text.trim().isNotEmpty
              ? double.tryParse(_longitudeController.text.trim())
              : null,
      parking: _parking != _originalLocation!.parking ? _parking : null,
      elevator: _elevator != _originalLocation!.elevator ? _elevator : null,
      handicapAccess:
          _handicapAccess != _originalLocation!.handicapAccess
              ? _handicapAccess
              : null,
      openingHours: _convertOpeningHoursToApiFormat(),
    );

    final success = await ref
        .read(locationNotifierProvider.notifier)
        .updateLocation(_originalLocation!.id, request);

    if (mounted) {
      if (success) {
        // Refresh location data in provider
        await ref
            .read(locationNotifierProvider.notifier)
            .getLocationById(_originalLocation!.id);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Location "${_nameController.text.trim()}" updated successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
        context.pop(); // Go back to location details
      } else {
        final locationData = ref.read(locationNotifierProvider);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(locationData.error ?? 'Failed to update location'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleCancel() {
    context.pop();
  }

  /// Handle opening hours changes
  void _onOpeningHoursChanged(Map<String, List<TimeSlot>> openingHours) {
    setState(() {
      _openingHours = openingHours;
    });
  }

  /// Convert opening hours from widget format to API format
  List<Map<String, dynamic>> _convertOpeningHoursToApiFormat() {
    final daysOfWeek = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];

    return daysOfWeek.map((day) {
      final daySlots = _openingHours[day] ?? [];
      return {
        'dayOfWeek': day,
        'isActive': daySlots.isNotEmpty,
        'hours':
            daySlots
                .map(
                  (slot) => {
                    'timeFrom': slot.formatTimeOfDay(slot.from),
                    'timeTo': slot.formatTimeOfDay(slot.to),
                  },
                )
                .toList(),
      };
    }).toList();
  }

  /// Get current device location
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      final result = await LocationService.getCurrentLocation();

      if (result.isSuccess) {
        setState(() {
          _latitudeController.text = result.latitude!.toString();
          _longitudeController.text = result.longitude!.toString();
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Location updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'Failed to get location'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final locationData = ref.watch(locationNotifierProvider);
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edit Location')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Location'),
        actions: [
          TextButton(
            onPressed: locationData.isLoading ? null : _handleSave,
            child:
                locationData.isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Location Name Section
              _buildSectionHeader('Location Information'),
              const SizedBox(height: 16),

              TextFormField(
                controller: _nameController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Location Name *',
                  hintText: 'e.g., Main Office, Branch Downtown',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.business),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Location name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                  }
                  if (value.trim().length > 100) {
                    return 'Name must be less than 100 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Short Name field
              TextFormField(
                controller: _shortNameController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Short Name',
                  hintText: 'e.g., Main Office',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.title),
                ),
                validator: (value) {
                  if (value != null && value.trim().length > 100) {
                    return 'Short name must be less than 100 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Country field - Fixed to Algeria and disabled
              TextFormField(
                controller: _countryController,
                enabled: false,
                decoration: const InputDecoration(
                  labelText: 'Country *',
                  hintText: 'Algeria',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.public),
                  suffixIcon: Icon(Icons.lock, color: Colors.grey),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Country is required';
                  }
                  if (value.trim() != 'Algeria') {
                    return 'Currently only available in Algeria';
                  }
                  return null;
                },
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 16),

              // City dropdown with Algerian cities
              DropdownButtonFormField<String>(
                value:
                    _cityController.text.isNotEmpty &&
                            AlgerianCities.isValidCity(_cityController.text)
                        ? _cityController.text
                        : null,
                decoration: const InputDecoration(
                  labelText: 'City *',
                  hintText: 'Select an Algerian city',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_city),
                ),
                items:
                    AlgerianCities.sortedCities.map((String city) {
                      return DropdownMenuItem<String>(
                        value: city,
                        child: Text(city),
                      );
                    }).toList(),
                onChanged:
                    locationData.isLoading
                        ? null
                        : (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _cityController.text = newValue;
                            });
                          }
                        },
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'City is required';
                  }
                  if (!AlgerianCities.isValidCity(value)) {
                    return 'Please select a valid Algerian city';
                  }
                  return null;
                },
                isExpanded: true,
              ),

              const SizedBox(height: 16),

              // Timezone field - Fixed to Africa/Algiers and disabled
              TextFormField(
                controller: _timezoneController,
                enabled: false,
                decoration: const InputDecoration(
                  labelText: 'Timezone *',
                  hintText: 'Africa/Algiers',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.schedule),
                  suffixIcon: Icon(Icons.lock, color: Colors.grey),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Timezone is required';
                  }
                  return null;
                },
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 16),

              // Street address field
              TextFormField(
                controller: _addressController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Address *',
                  hintText: 'e.g., 123 Main Street, Building A',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Address is required';
                  }
                  if (value.trim().length < 5) {
                    return 'Please enter a complete address';
                  }
                  if (value.trim().length > 200) {
                    return 'Address must be less than 200 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
                maxLines: 2,
              ),

              const SizedBox(height: 16),

              // Postal Code field (optional)
              TextFormField(
                controller: _postalCodeController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Postal Code',
                  hintText: 'e.g., 16000 (optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.markunread_mailbox),
                ),
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    if (value.trim().length < 4 || value.trim().length > 10) {
                      return 'Postal code must be between 4-10 characters';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              // Location coordinates section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.my_location,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Location Coordinates',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Update coordinates if the location has moved to help customers find you easily.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Latitude and Longitude fields
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _latitudeController,
                            enabled: false,
                            decoration: const InputDecoration(
                              labelText: 'Latitude *',
                              hintText: 'Current latitude',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.place),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please get current location';
                              }
                              final lat = double.tryParse(value);
                              if (lat == null) {
                                return 'Invalid latitude';
                              }
                              if (lat < -90 || lat > 90) {
                                return 'Latitude must be between -90 and 90';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _longitudeController,
                            enabled: false,
                            decoration: const InputDecoration(
                              labelText: 'Longitude *',
                              hintText: 'Current longitude',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.place),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Please get current location';
                              }
                              final lng = double.tryParse(value);
                              if (lng == null) {
                                return 'Invalid longitude';
                              }
                              if (lng < -180 || lng > 180) {
                                return 'Longitude must be between -180 and 180';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Get location button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            (_isGettingLocation || locationData.isLoading)
                                ? null
                                : _getCurrentLocation,
                        icon:
                            _isGettingLocation
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.my_location),
                        label: Text(
                          _isGettingLocation
                              ? 'Getting Location...'
                              : 'Update Current Location',
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Mobile field
              TextFormField(
                controller: _mobileController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Mobile',
                  hintText: 'e.g., +213 555-123456',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                validator: (value) {
                  if (value != null && value.trim().length > 20) {
                    return 'Mobile must be less than 20 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Fax field
              TextFormField(
                controller: _faxController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Fax',
                  hintText: 'e.g., +213 555-123456',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.fax),
                ),
                validator: (value) {
                  if (value != null && value.trim().length > 20) {
                    return 'Fax must be less than 20 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Floor field
              TextFormField(
                controller: _floorController,
                enabled: !locationData.isLoading,
                decoration: const InputDecoration(
                  labelText: 'Floor',
                  hintText: 'e.g., 5th Floor',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.layers),
                ),
                validator: (value) {
                  if (value != null && value.trim().length > 50) {
                    return 'Floor must be less than 50 characters';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 32),

              // Amenities Section
              _buildSectionHeader('Amenities'),
              const SizedBox(height: 16),

              LocationFormFields.amenitiesSection(
                parking: _parking,
                elevator: _elevator,
                handicapAccess: _handicapAccess,
                enabled: !locationData.isLoading,
                onParkingChanged: (value) => setState(() => _parking = value),
                onElevatorChanged: (value) => setState(() => _elevator = value),
                onHandicapAccessChanged:
                    (value) => setState(() => _handicapAccess = value),
              ),

              const SizedBox(height: 32),

              // Opening Hours Section
              _buildSectionHeader('Opening Hours'),
              const SizedBox(height: 16),

              OpeningHoursWidget(
                initialData: _openingHours,
                onChanged: _onOpeningHoursChanged,
                title: 'Business Hours',
                subtitle: 'Set your operating hours for each day of the week',
                showHeader: false, // We're showing our own header
              ),

              const SizedBox(height: 32),

              // Info card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Changes to this location will be updated across all your business services and queues.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: locationData.isLoading ? null : _handleCancel,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: locationData.isLoading ? null : _handleSave,
                      child:
                          locationData.isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Text('Update Location'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
    );
  }
}
