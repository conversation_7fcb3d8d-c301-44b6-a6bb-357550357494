import 'package:hive_flutter/hive_flutter.dart';

/// Service for managing local storage using Hive
class StorageService {
  static const String _settingsBox = 'settings';
  static const String _cacheBox = 'cache';
  static const String _authBox = 'auth';

  // Box instances
  static Box? _settings;
  static Box? _cache;
  static Box? _auth;

  /// Initialize storage service
  static Future<void> init() async {
    await Hive.initFlutter();

    _settings = await Hive.openBox(_settingsBox);
    _cache = await Hive.openBox(_cacheBox);
    _auth = await Hive.openBox(_authBox);
  }

  /// Initialize storage service for testing
  static Future<void> initForTesting() async {
    // Don't call Hive.initFlutter() in tests, just open boxes
    _settings = await Hive.openBox(_settingsBox);
    _cache = await Hive.openBox(_cacheBox);
    _auth = await Hive.openBox(_authBox);
  }

  /// Settings box operations
  static Box get settings {
    if (_settings == null || !_settings!.isOpen) {
      throw Exception('Settings box not initialized');
    }
    return _settings!;
  }

  /// Cache box operations
  static Box get cache {
    if (_cache == null || !_cache!.isOpen) {
      throw Exception('Cache box not initialized');
    }
    return _cache!;
  }

  /// Auth box operations
  static Box get auth {
    if (_auth == null || !_auth!.isOpen) {
      throw Exception('Auth box not initialized');
    }
    return _auth!;
  }

  /// Save data to settings
  static Future<void> saveSetting(String key, dynamic value) async {
    await settings.put(key, value);
  }

  /// Get data from settings
  static T? getSetting<T>(String key, {T? defaultValue}) {
    return settings.get(key, defaultValue: defaultValue) as T?;
  }

  /// Save data to cache
  static Future<void> saveCache(String key, dynamic value) async {
    await cache.put(key, value);
  }

  /// Get data from cache
  static T? getCache<T>(String key, {T? defaultValue}) {
    return cache.get(key, defaultValue: defaultValue) as T?;
  }

  /// Save auth data
  static Future<void> saveAuth(String key, dynamic value) async {
    await auth.put(key, value);
  }

  /// Get auth data
  static T? getAuth<T>(String key, {T? defaultValue}) {
    return auth.get(key, defaultValue: defaultValue) as T?;
  }

  /// Remove specific auth data
  static Future<void> removeAuth(String key) async {
    await auth.delete(key);
  }

  /// Clear all auth data
  static Future<void> clearAuth() async {
    await auth.clear();
  }

  /// Clear all cache data
  static Future<void> clearCache() async {
    await cache.clear();
  }

  /// Clear all settings data
  static Future<void> clearSettings() async {
    await settings.clear();
  }

  /// Clear all data
  static Future<void> clearAll() async {
    await Future.wait([
      clearAuth(),
      clearCache(),
      clearSettings(),
    ]);
  }

  /// Close all boxes
  static Future<void> close() async {
    await Future.wait([
      _settings?.close() ?? Future.value(),
      _cache?.close() ?? Future.value(),
      _auth?.close() ?? Future.value(),
    ]);
  }
}
