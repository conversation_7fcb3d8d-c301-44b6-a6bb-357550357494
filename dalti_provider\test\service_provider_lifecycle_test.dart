import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:dalti_provider/features/services/screens/edit_service_screen.dart';
import 'package:dalti_provider/features/services/screens/service_details_screen.dart';
import 'package:dalti_provider/features/services/providers/service_provider.dart';
import 'package:dalti_provider/features/services/models/service_models.dart';

void main() {
  group('Service Provider Lifecycle Tests', () {
    late GoRouter router;

    setUp(() {
      router = GoRouter(
        initialLocation: '/services/1/edit',
        routes: [
          GoRoute(
            path: '/services/:id/edit',
            builder: (context, state) {
              final serviceId = state.pathParameters['id'] ?? '';
              return EditServiceScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/services/:id',
            builder: (context, state) {
              final serviceId = state.pathParameters['id'] ?? '';
              return ServiceDetailsScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/services',
            builder: (context, state) => const Scaffold(
              body: Text('Services List'),
            ),
          ),
        ],
      );
    });

    testWidgets('EditServiceScreen should not modify provider during initState', (WidgetTester tester) async {
      // This test verifies that the EditServiceScreen doesn't trigger the
      // "Tried to modify a provider while the widget tree was building" error
      
      bool providerModificationError = false;
      
      // Override the error handler to catch provider modification errors
      final originalOnError = FlutterError.onError;
      FlutterError.onError = (FlutterErrorDetails details) {
        if (details.exception.toString().contains('Tried to modify a provider while the widget tree was building')) {
          providerModificationError = true;
        }
        originalOnError?.call(details);
      };

      try {
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp.router(
              routerConfig: router,
            ),
          ),
        );

        // Wait for the widget to build and any post-frame callbacks to execute
        await tester.pumpAndSettle();

        // Verify that no provider modification error occurred
        expect(providerModificationError, isFalse, 
            reason: 'EditServiceScreen should not modify provider during widget tree building');

        // Verify that the screen is displayed (even if service loading fails)
        expect(find.byType(EditServiceScreen), findsOneWidget);

      } finally {
        // Restore the original error handler
        FlutterError.onError = originalOnError;
      }
    });

    testWidgets('ServiceDetailsScreen should not modify provider during initState', (WidgetTester tester) async {
      // This test verifies that the ServiceDetailsScreen doesn't trigger the
      // "Tried to modify a provider while the widget tree was building" error
      
      bool providerModificationError = false;
      
      // Create a router for the details screen
      final detailsRouter = GoRouter(
        initialLocation: '/services/1',
        routes: [
          GoRoute(
            path: '/services/:id',
            builder: (context, state) {
              final serviceId = state.pathParameters['id'] ?? '';
              return ServiceDetailsScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/services/:id/edit',
            builder: (context, state) {
              final serviceId = state.pathParameters['id'] ?? '';
              return EditServiceScreen(serviceId: serviceId);
            },
          ),
          GoRoute(
            path: '/services',
            builder: (context, state) => const Scaffold(
              body: Text('Services List'),
            ),
          ),
        ],
      );
      
      // Override the error handler to catch provider modification errors
      final originalOnError = FlutterError.onError;
      FlutterError.onError = (FlutterErrorDetails details) {
        if (details.exception.toString().contains('Tried to modify a provider while the widget tree was building')) {
          providerModificationError = true;
        }
        originalOnError?.call(details);
      };

      try {
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp.router(
              routerConfig: detailsRouter,
            ),
          ),
        );

        // Wait for the widget to build and any post-frame callbacks to execute
        await tester.pumpAndSettle();

        // Verify that no provider modification error occurred
        expect(providerModificationError, isFalse, 
            reason: 'ServiceDetailsScreen should not modify provider during widget tree building');

        // Verify that the screen is displayed (even if service loading fails)
        expect(find.byType(ServiceDetailsScreen), findsOneWidget);

      } finally {
        // Restore the original error handler
        FlutterError.onError = originalOnError;
      }
    });

    testWidgets('EditServiceScreen should use addPostFrameCallback for provider modification', (WidgetTester tester) async {
      // This test verifies that the provider modification happens after the widget tree is built
      
      int postFrameCallbackCount = 0;
      
      // Mock WidgetsBinding to count addPostFrameCallback calls
      final originalBinding = WidgetsBinding.instance;
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for initial build
      await tester.pump();
      
      // Verify that the screen is created
      expect(find.byType(EditServiceScreen), findsOneWidget);
      
      // Wait for post-frame callbacks to execute
      await tester.pumpAndSettle();
      
      // The test passes if no exceptions are thrown during the pump cycle
      expect(true, isTrue);
    });

    test('Service model validation works correctly', () {
      // Test that service models work correctly for the edit/details screens
      final service = Service(
        id: 1,
        title: 'Test Service',
        duration: 60,
        price: 50.0,
        acceptOnline: true,
        acceptNew: true,
        notificationOn: true,
      );

      expect(service.id, equals(1));
      expect(service.title, equals('Test Service'));
      expect(service.formattedDuration, equals('1h'));
      expect(service.formattedPrice, equals('\$50.00'));
    });

    test('UpdateServiceRequest handles partial updates correctly', () {
      // Test that update requests work for the edit screen
      final updateRequest = UpdateServiceRequest(
        title: 'Updated Service',
        price: 75.0,
      );

      expect(updateRequest.title, equals('Updated Service'));
      expect(updateRequest.price, equals(75.0));
      expect(updateRequest.duration, isNull); // Should be null for partial update
      expect(updateRequest.hasUpdates, isTrue);

      final json = updateRequest.toJson();
      expect(json['title'], equals('Updated Service'));
      expect(json['price'], equals(75.0));
      expect(json.containsKey('duration'), isFalse); // Should not include null fields
    });
  });
}
