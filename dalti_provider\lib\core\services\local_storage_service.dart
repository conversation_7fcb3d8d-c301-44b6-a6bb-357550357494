import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing local storage operations
class LocalStorageService {
  static const String _onboardingSkippedKey = 'onboarding_skipped';
  static const String _setupCompletedKey = 'setup_completed';

  /// Check if onboarding was skipped
  static Future<bool> get isOnboardingSkipped async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_onboardingSkippedKey) ?? false;
  }

  /// Set onboarding skipped status
  static Future<void> setOnboardingSkipped(bool skipped) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingSkippedKey, skipped);
  }

  /// Check if setup is completed
  static Future<bool> get isSetupCompleted async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_setupCompletedKey) ?? false;
  }

  /// Set setup completed status
  static Future<void> setSetupCompleted(bool completed) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_setupCompletedKey, completed);
  }

  /// Clear all onboarding related data
  static Future<void> clearOnboardingData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_onboardingSkippedKey);
    await prefs.remove(_setupCompletedKey);
  }

  /// Check if user needs onboarding (not skipped and not completed)
  static Future<bool> get needsOnboarding async {
    final skipped = await isOnboardingSkipped;
    final completed = await isSetupCompleted;
    return !skipped && !completed;
  }
}
