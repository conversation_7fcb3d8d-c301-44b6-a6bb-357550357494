import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';
import '../providers/customer_provider.dart';
import '../widgets/customer_form.dart';

/// Screen for editing an existing customer
class EditCustomerScreen extends ConsumerStatefulWidget {
  final String customerId;

  const EditCustomerScreen({super.key, required this.customerId});

  @override
  ConsumerState<EditCustomerScreen> createState() => _EditCustomerScreenState();
}

class _EditCustomerScreenState extends ConsumerState<EditCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isInitialized = false;

  // Form controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalIdController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Load customer data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(singleCustomerNotifierProvider(widget.customerId).notifier)
          .loadCustomer();
    });
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _nationalIdController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeForm(Customer customer) {
    if (_isInitialized) return;

    _firstNameController.text = customer.firstName;
    _lastNameController.text = customer.lastName;
    _emailController.text = customer.email ?? '';

    // Handle phone number format - remove +213 prefix if present
    String phoneNumber = customer.phoneNumber ?? '';
    if (phoneNumber.startsWith('+213')) {
      phoneNumber = phoneNumber.substring(4);
    }
    _phoneController.text = phoneNumber;

    _nationalIdController.text = ''; // TODO: Add nationalId to Customer model
    _notesController.text = customer.notes ?? '';

    _isInitialized = true;
  }

  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CustomerRequest(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        mobileNumber: '+213${_phoneController.text.trim()}', // Add country code
        email:
            _emailController.text.trim().isEmpty
                ? null
                : _emailController.text.trim(),
        nationalId:
            _nationalIdController.text.trim().isEmpty
                ? null
                : _nationalIdController.text.trim(),
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
      );

      final repository = ref.read(customerRepositoryProvider);
      final updatedCustomer = await repository.updateCustomer(
        widget.customerId,
        request,
      );

      // Update customer in providers
      ref
          .read(singleCustomerNotifierProvider(widget.customerId).notifier)
          .updateCustomer(updatedCustomer);
      ref
          .read(customerNotifierProvider.notifier)
          .updateCustomer(updatedCustomer);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Customer ${updatedCustomer.firstName} ${updatedCustomer.lastName} updated successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update customer: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final customer = ref.watch(
      singleCustomerNotifierProvider(widget.customerId),
    );

    if (customer == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edit Customer')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // Initialize form with customer data
    _initializeForm(customer);

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit ${customer.firstName} ${customer.lastName}'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveCustomer,
            child:
                _isLoading
                    ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    )
                    : const Text('Save'),
          ),
        ],
      ),
      body: CustomerForm(
        formKey: _formKey,
        firstNameController: _firstNameController,
        lastNameController: _lastNameController,
        emailController: _emailController,
        phoneController: _phoneController,
        nationalIdController: _nationalIdController,
        notesController: _notesController,
      ),
    );
  }
}
