import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:dalti_provider/core/routing/app_routes.dart';
import 'package:dalti_provider/features/auth/screens/forgot_password_screen.dart';
import 'package:dalti_provider/features/auth/screens/verify_reset_otp_screen.dart';
import 'package:dalti_provider/features/auth/screens/reset_password_screen.dart';
import 'package:dalti_provider/features/auth/models/auth_models.dart';
import 'package:dalti_provider/core/network/api_service.dart';
import 'package:dalti_provider/core/providers/app_providers.dart';

// Mock API service for testing
class MockAuthApiService extends AuthApiService {
  MockAuthApiService() : super(null as dynamic);

  bool shouldSucceed = true;
  String mockResetToken = 'mock-reset-token-123';

  @override
  Future<PasswordResetResponse> requestPasswordResetOtp(PasswordResetRequest request) async {
    await Future.delayed(const Duration(milliseconds: 100)); // Simulate network delay
    
    if (shouldSucceed) {
      return const PasswordResetResponse(
        success: true,
        message: 'Password reset OTP sent successfully to your email address.',
      );
    } else {
      return const PasswordResetResponse(
        success: false,
        message: 'No account found with this email address.',
      );
    }
  }

  @override
  Future<PasswordResetOtpVerificationResponse> verifyPasswordResetOtp(PasswordResetOtpVerificationRequest request) async {
    await Future.delayed(const Duration(milliseconds: 100)); // Simulate network delay
    
    if (shouldSucceed && request.otp == '123456') {
      return PasswordResetOtpVerificationResponse(
        success: true,
        message: 'OTP verified successfully. Use the reset token to set your new password.',
        resetToken: mockResetToken,
      );
    } else {
      return const PasswordResetOtpVerificationResponse(
        success: false,
        message: 'Invalid OTP.',
      );
    }
  }

  @override
  Future<PasswordResetConfirmResponse> resetPassword(PasswordResetConfirmRequest request) async {
    await Future.delayed(const Duration(milliseconds: 100)); // Simulate network delay
    
    if (shouldSucceed && request.resetToken == mockResetToken) {
      return const PasswordResetConfirmResponse(
        success: true,
        message: 'Password reset successfully. You can now log in with your new password.',
      );
    } else {
      return const PasswordResetConfirmResponse(
        success: false,
        message: 'Invalid or expired reset token.',
      );
    }
  }
}

void main() {
  group('Password Reset Flow Tests', () {
    late MockAuthApiService mockApiService;

    setUp(() {
      mockApiService = MockAuthApiService();
    });

    Widget createTestApp(Widget child) {
      return ProviderScope(
        overrides: [
          authApiServiceProvider.overrideWithValue(mockApiService),
        ],
        child: MaterialApp(
          home: child,
        ),
      );
    }

    testWidgets('ForgotPasswordScreen - successful OTP request', (WidgetTester tester) async {
      mockApiService.shouldSucceed = true;

      await tester.pumpWidget(createTestApp(const ForgotPasswordScreen()));

      // Verify initial UI elements
      expect(find.text('Reset Your Password'), findsOneWidget);
      expect(find.text('Send Reset Code'), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);

      // Enter email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.pump();

      // Tap send button
      await tester.tap(find.text('Send Reset Code'));
      await tester.pump();

      // Verify loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for API call to complete
      await tester.pumpAndSettle();

      // Verify success state
      expect(find.text('Reset code sent!'), findsOneWidget);
      expect(find.text('Continue to Verification'), findsOneWidget);
      expect(find.text('We sent a verification <NAME_EMAIL>'), findsOneWidget);
    });

    testWidgets('ForgotPasswordScreen - failed OTP request', (WidgetTester tester) async {
      mockApiService.shouldSucceed = false;

      await tester.pumpWidget(createTestApp(const ForgotPasswordScreen()));

      // Enter email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.pump();

      // Tap send button
      await tester.tap(find.text('Send Reset Code'));
      await tester.pumpAndSettle();

      // Verify error message is shown
      expect(find.text('No account found with this email address.'), findsOneWidget);
    });

    testWidgets('ForgotPasswordScreen - email validation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp(const ForgotPasswordScreen()));

      // Try to submit without email
      await tester.tap(find.text('Send Reset Code'));
      await tester.pump();

      // Verify validation error
      expect(find.text('Please enter your email address'), findsOneWidget);

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Code'));
      await tester.pump();

      // Verify validation error
      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('VerifyResetOtpScreen - successful OTP verification', (WidgetTester tester) async {
      mockApiService.shouldSucceed = true;

      await tester.pumpWidget(createTestApp(const VerifyResetOtpScreen(email: '<EMAIL>')));

      // Verify initial UI elements
      expect(find.text('Enter Verification Code'), findsOneWidget);
      expect(find.text('We sent a 6-digit code to\<EMAIL>'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(6)); // 6 OTP input fields

      // Enter OTP
      final otpFields = find.byType(TextFormField);
      await tester.enterText(otpFields.at(0), '1');
      await tester.enterText(otpFields.at(1), '2');
      await tester.enterText(otpFields.at(2), '3');
      await tester.enterText(otpFields.at(3), '4');
      await tester.enterText(otpFields.at(4), '5');
      await tester.enterText(otpFields.at(5), '6');
      await tester.pump();

      // Wait for auto-verification
      await tester.pumpAndSettle();

      // Verify success message
      expect(find.text('OTP verified successfully. Use the reset token to set your new password.'), findsOneWidget);
    });

    testWidgets('VerifyResetOtpScreen - invalid OTP', (WidgetTester tester) async {
      mockApiService.shouldSucceed = true;

      await tester.pumpWidget(createTestApp(const VerifyResetOtpScreen(email: '<EMAIL>')));

      // Enter invalid OTP
      final otpFields = find.byType(TextFormField);
      await tester.enterText(otpFields.at(0), '9');
      await tester.enterText(otpFields.at(1), '9');
      await tester.enterText(otpFields.at(2), '9');
      await tester.enterText(otpFields.at(3), '9');
      await tester.enterText(otpFields.at(4), '9');
      await tester.enterText(otpFields.at(5), '9');
      await tester.pump();

      // Wait for auto-verification
      await tester.pumpAndSettle();

      // Verify error message
      expect(find.text('Invalid OTP.'), findsOneWidget);
    });

    testWidgets('ResetPasswordScreen - successful password reset', (WidgetTester tester) async {
      mockApiService.shouldSucceed = true;

      await tester.pumpWidget(createTestApp(ResetPasswordScreen(resetToken: mockApiService.mockResetToken)));

      // Verify initial UI elements
      expect(find.text('Create New Password'), findsOneWidget);
      expect(find.text('Password Requirements:'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Password and confirm password fields

      // Enter valid password
      final passwordFields = find.byType(TextFormField);
      await tester.enterText(passwordFields.at(0), 'NewPassword123!');
      await tester.pump();

      // Verify password strength indicator appears
      expect(find.text('Password strength: '), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);

      // Enter confirm password
      await tester.enterText(passwordFields.at(1), 'NewPassword123!');
      await tester.pump();

      // Tap reset password button
      await tester.tap(find.text('Reset Password'));
      await tester.pump();

      // Verify loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for API call to complete
      await tester.pumpAndSettle();

      // Verify success dialog appears
      expect(find.text('Password Reset Successful'), findsOneWidget);
      expect(find.text('Go to Login'), findsOneWidget);
    });

    testWidgets('ResetPasswordScreen - password validation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestApp(ResetPasswordScreen(resetToken: mockApiService.mockResetToken)));

      final passwordFields = find.byType(TextFormField);

      // Test weak password
      await tester.enterText(passwordFields.at(0), 'weak');
      await tester.pump();

      // Verify requirements are not met
      expect(find.text('At least 8 characters'), findsOneWidget);
      expect(find.byIcon(Icons.radio_button_unchecked), findsWidgets);

      // Test strong password
      await tester.enterText(passwordFields.at(0), 'StrongPassword123!');
      await tester.pump();

      // Verify all requirements are met
      expect(find.byIcon(Icons.check_circle), findsNWidgets(5)); // All 5 requirements met

      // Test password mismatch
      await tester.enterText(passwordFields.at(1), 'DifferentPassword');
      await tester.tap(find.text('Reset Password'));
      await tester.pump();

      // Verify validation error
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    test('Password Reset Models - JSON serialization', () {
      // Test PasswordResetRequest
      final resetRequest = PasswordResetRequest(email: '<EMAIL>');
      expect(resetRequest.toJson(), {'email': '<EMAIL>'});

      // Test PasswordResetOtpVerificationRequest
      final otpRequest = PasswordResetOtpVerificationRequest(
        email: '<EMAIL>',
        otp: '123456',
      );
      expect(otpRequest.toJson(), {'email': '<EMAIL>', 'otp': '123456'});

      // Test PasswordResetConfirmRequest
      final confirmRequest = PasswordResetConfirmRequest(
        resetToken: 'token123',
        newPassword: 'newpass123',
      );
      expect(confirmRequest.toJson(), {'resetToken': 'token123', 'newPassword': 'newpass123'});

      // Test response models
      final resetResponse = PasswordResetResponse.fromJson({
        'message': 'OTP sent successfully'
      });
      expect(resetResponse.success, true);
      expect(resetResponse.message, 'OTP sent successfully');

      final otpResponse = PasswordResetOtpVerificationResponse.fromJson({
        'resetToken': 'token123',
        'message': 'OTP verified'
      });
      expect(otpResponse.success, true);
      expect(otpResponse.resetToken, 'token123');

      final confirmResponse = PasswordResetConfirmResponse.fromJson({
        'message': 'Password reset successful'
      });
      expect(confirmResponse.success, true);
      expect(confirmResponse.message, 'Password reset successful');
    });
  });
}
