import 'package:flutter/material.dart';

/// Calendar configuration dialog for settings like time slot intervals
class CalendarConfigDialog extends StatefulWidget {
  final int currentTimeSlotInterval;
  final Function(int) onTimeSlotIntervalChanged;

  const CalendarConfigDialog({
    super.key,
    required this.currentTimeSlotInterval,
    required this.onTimeSlotIntervalChanged,
  });

  @override
  State<CalendarConfigDialog> createState() => _CalendarConfigDialogState();
}

class _CalendarConfigDialogState extends State<CalendarConfigDialog> {
  late int _selectedInterval;

  // Available time slot intervals in minutes
  static const List<int> _availableIntervals = [5, 10, 15, 20, 30, 60];

  @override
  void initState() {
    super.initState();
    _selectedInterval = widget.currentTimeSlotInterval;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Calendar Settings'),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Time Slot Interval Section
            Text(
              'Time Slot Interval:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            // Dropdown for time slot interval
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<int>(
                  value: _selectedInterval,
                  isExpanded: true,
                  items: _availableIntervals.map((interval) {
                    return DropdownMenuItem<int>(
                      value: interval,
                      child: Text('$interval minutes'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedInterval = value;
                      });
                    }
                  },
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Description
            Text(
              'This setting determines how many time slots each hour is divided into. For example, 5 minutes will create 12 slots per hour.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
      actions: [
        // Cancel button
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        
        // Apply button
        FilledButton(
          onPressed: () {
            widget.onTimeSlotIntervalChanged(_selectedInterval);
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}

/// Show calendar configuration dialog
Future<void> showCalendarConfigDialog({
  required BuildContext context,
  required int currentTimeSlotInterval,
  required Function(int) onTimeSlotIntervalChanged,
}) {
  return showDialog<void>(
    context: context,
    builder: (context) => CalendarConfigDialog(
      currentTimeSlotInterval: currentTimeSlotInterval,
      onTimeSlotIntervalChanged: onTimeSlotIntervalChanged,
    ),
  );
}
