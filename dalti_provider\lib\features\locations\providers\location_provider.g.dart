// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'location_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$locationApiServiceHash() =>
    r'23ef110eb74f1c36b12959decd4d46922c971003';

/// Location API service provider
///
/// Copied from [locationApiService].
@ProviderFor(locationApiService)
final locationApiServiceProvider =
    AutoDisposeProvider<LocationApiService>.internal(
  locationApiService,
  name: r'locationApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocationApiServiceRef = AutoDisposeProviderRef<LocationApiService>;
String _$locationRepositoryHash() =>
    r'01f255693ea1def06e2193498c280901cd85aec0';

/// Location repository provider
///
/// Copied from [locationRepository].
@ProviderFor(locationRepository)
final locationRepositoryProvider =
    AutoDisposeProvider<LocationRepository>.internal(
  locationRepository,
  name: r'locationRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocationRepositoryRef = AutoDisposeProviderRef<LocationRepository>;
String _$locationIntegrationServiceHash() =>
    r'76974c7126b83175452ffe6e70a2503e4e442150';

/// Location integration service provider
///
/// Copied from [locationIntegrationService].
@ProviderFor(locationIntegrationService)
final locationIntegrationServiceProvider =
    AutoDisposeProvider<LocationIntegrationService>.internal(
  locationIntegrationService,
  name: r'locationIntegrationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationIntegrationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef LocationIntegrationServiceRef
    = AutoDisposeProviderRef<LocationIntegrationService>;
String _$locationNotifierHash() => r'c6b36c573c9fcd2d0b089a2dd655d23258c0d7e3';

/// Location provider for managing location state
///
/// Copied from [LocationNotifier].
@ProviderFor(LocationNotifier)
final locationNotifierProvider =
    AutoDisposeNotifierProvider<LocationNotifier, LocationData>.internal(
  LocationNotifier.new,
  name: r'locationNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LocationNotifier = AutoDisposeNotifier<LocationData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
