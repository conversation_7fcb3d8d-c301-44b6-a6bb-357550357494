import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';

/// Reusable customer form widget for creating and editing customers
class CustomerForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final TextEditingController emailController;
  final TextEditingController phoneController;
  final TextEditingController nationalIdController;
  final TextEditingController notesController;

  const CustomerForm({
    super.key,
    required this.formKey,
    required this.firstNameController,
    required this.lastNameController,
    required this.emailController,
    required this.phoneController,
    required this.nationalIdController,
    required this.notesController,
  });

  @override
  State<CustomerForm> createState() => _CustomerFormState();
}

class _CustomerFormState extends State<CustomerForm> {

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.form<PERSON><PERSON>,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Personal Information Section
            _buildSectionHeader('Personal Information'),
            const SizedBox(height: 8),


            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: widget.firstNameController,
                    decoration: const InputDecoration(
                      labelText: 'First Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return 'First name is required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: widget.lastNameController,
                    decoration: const InputDecoration(
                      labelText: 'Last Name *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value?.trim().isEmpty ?? true) {
                        return 'Last name is required';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: widget.emailController,
              decoration: const InputDecoration(
                labelText: 'Email (Optional)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                // Email is optional, but if provided, must be valid
                if (value?.trim().isNotEmpty == true) {
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                    return 'Please enter a valid email';
                  }
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: widget.phoneController,
              decoration: const InputDecoration(
                labelText: 'Mobile Number *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone_android),
                prefixText: '+213 ',
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value?.trim().isEmpty ?? true) {
                  return 'Mobile number is required';
                }
                if (!RegExp(r'^\d{9}$').hasMatch(value!.trim())) {
                  return 'Please enter a valid 9-digit mobile number';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // National ID
            TextFormField(
              controller: widget.nationalIdController,
              decoration: const InputDecoration(
                labelText: 'National ID (Optional)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.badge),
              ),
              keyboardType: TextInputType.text,
              validator: (value) {
                // National ID is optional, but if provided, should be valid format
                if (value?.trim().isNotEmpty == true) {
                  if (!RegExp(r'^\d{18}$').hasMatch(value!.trim())) {
                    return 'Please enter a valid 18-digit national ID';
                  }
                }
                return null;
              },
            ),

            const SizedBox(height: 24),
            
            // Notes Section
            _buildSectionHeader('Notes'),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: widget.notesController,
              decoration: const InputDecoration(
                labelText: 'Additional notes',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }


}
