import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';

/// Widget displaying customer statistics in a card format
class CustomerStatsCard extends StatelessWidget {
  final Customer customer;

  const CustomerStatsCard({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Overview Stats
        _buildStatsGrid(),
        
        const SizedBox(height: 16),
        
        // Appointment History Chart (placeholder)
        _buildAppointmentHistoryCard(),
        
        const SizedBox(height: 16),
        
        // Spending Analysis
        _buildSpendingAnalysisCard(),
        
        const SizedBox(height: 16),
        
        // Customer Loyalty
        _buildLoyaltyCard(),
      ],
    );
  }

  Widget _buildStatsGrid() {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Customer Overview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildStatItem(
                  icon: Icons.event,
                  title: 'Total Appointments',
                  value: customer.totalAppointments.toString(),
                  color: Colors.blue,
                ),
                _buildStatItem(
                  icon: Icons.attach_money,
                  title: 'Total Spent',
                  value: _formatCurrency(customer.totalSpent),
                  color: Colors.green,
                ),
                _buildStatItem(
                  icon: Icons.schedule,
                  title: 'Customer Since',
                  value: _formatDuration(customer.createdAt),
                  color: Colors.orange,
                ),
                _buildStatItem(
                  icon: Icons.trending_up,
                  title: 'Avg per Visit',
                  value: _formatCurrency(_calculateAverageSpent()),
                  color: Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentHistoryCard() {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Appointment History',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.bar_chart,
                      size: 32,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Chart visualization coming soon',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpendingAnalysisCard() {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Spending Analysis',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSpendingMetric(
                    'Average per Visit',
                    _formatCurrency(_calculateAverageSpent()),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSpendingMetric(
                    'Highest Visit',
                    _formatCurrency(_calculateHighestSpent()),
                    Icons.arrow_upward,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSpendingMetric(
                    'Monthly Average',
                    _formatCurrency(_calculateMonthlyAverage()),
                    Icons.calendar_month,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSpendingMetric(
                    'Loyalty Score',
                    '${_calculateLoyaltyScore()}%',
                    Icons.star,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpendingMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoyaltyCard() {
    final loyaltyScore = _calculateLoyaltyScore();
    final loyaltyLevel = _getLoyaltyLevel(loyaltyScore);
    
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.loyalty,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Customer Loyalty',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Loyalty Level',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        loyaltyLevel['name']!,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: loyaltyLevel['color'],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: loyaltyLevel['color']!.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    loyaltyLevel['icon'],
                    color: loyaltyLevel['color'],
                    size: 24,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: loyaltyScore / 100,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(loyaltyLevel['color']!),
            ),
            const SizedBox(height: 8),
            Text(
              '$loyaltyScore% loyalty score',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateAverageSpent() {
    if (customer.totalAppointments == 0) return 0.0;
    return customer.totalSpent / customer.totalAppointments;
  }

  double _calculateHighestSpent() {
    // This would typically come from appointment history
    // For now, we'll estimate based on average
    return _calculateAverageSpent() * 1.5;
  }

  double _calculateMonthlyAverage() {
    final monthsSinceCreated = DateTime.now().difference(customer.createdAt).inDays / 30;
    if (monthsSinceCreated == 0) return customer.totalSpent;
    return customer.totalSpent / monthsSinceCreated;
  }

  int _calculateLoyaltyScore() {
    // Simple loyalty calculation based on appointments and spending
    final appointmentScore = (customer.totalAppointments * 5).clamp(0, 50);
    final spendingScore = (customer.totalSpent / 100).clamp(0, 30);
    final timeScore = DateTime.now().difference(customer.createdAt).inDays > 365 ? 20 : 10;
    
    return (appointmentScore + spendingScore + timeScore).round().clamp(0, 100);
  }

  Map<String, dynamic> _getLoyaltyLevel(int score) {
    if (score >= 80) {
      return {
        'name': 'VIP Customer',
        'color': Colors.purple,
        'icon': Icons.diamond,
      };
    } else if (score >= 60) {
      return {
        'name': 'Loyal Customer',
        'color': Colors.green,
        'icon': Icons.star,
      };
    } else if (score >= 40) {
      return {
        'name': 'Regular Customer',
        'color': Colors.blue,
        'icon': Icons.person,
      };
    } else {
      return {
        'name': 'New Customer',
        'color': Colors.orange,
        'icon': Icons.person_outline,
      };
    }
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'DA ', decimalDigits: 0);
    return formatter.format(amount);
  }

  String _formatDuration(DateTime date) {
    final difference = DateTime.now().difference(date);
    final days = difference.inDays;
    
    if (days < 30) {
      return '${days}d';
    } else if (days < 365) {
      return '${(days / 30).floor()}m';
    } else {
      return '${(days / 365).floor()}y';
    }
  }
}
