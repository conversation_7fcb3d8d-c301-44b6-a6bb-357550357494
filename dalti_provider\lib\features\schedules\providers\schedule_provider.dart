import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/schedule_models.dart';
import '../repository/schedule_repository.dart';
import '../repository/schedule_repository_impl.dart';
import '../services/schedule_api_service.dart';

part 'schedule_provider.g.dart';

/// Schedule API service provider
@riverpod
ScheduleApiService scheduleApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return ScheduleApiService(httpClient);
}

/// Schedule repository provider
@riverpod
ScheduleRepository scheduleRepository(Ref ref) {
  final apiService = ref.watch(scheduleApiServiceProvider);
  return ScheduleRepositoryImpl(apiService: apiService);
}

/// Schedule provider for managing schedule state
@riverpod
class ScheduleNotifier extends _$ScheduleNotifier {
  @override
  ScheduleData build() {
    return const ScheduleData(state: ScheduleState.initial);
  }

  /// Load all schedules
  Future<void> loadSchedules({
    int? locationId,
    int? dayOfWeek,
    bool? isActive,
    bool forceRefresh = false,
  }) async {
    try {
      // Don't reload if already loaded and not forcing refresh
      if (state.state == ScheduleState.loaded && !forceRefresh) {
        return;
      }

      state = state.copyWith(
        state: ScheduleState.loading,
        isLoading: true,
        clearError: true,
      );

      print('[ScheduleProvider] Loading schedules (locationId: $locationId, dayOfWeek: $dayOfWeek, isActive: $isActive)');

      final repository = ref.read(scheduleRepositoryProvider);
      final schedules = await repository.getSchedules(
        locationId: locationId,
        dayOfWeek: dayOfWeek,
        isActive: isActive,
      );

      // Group schedules by location and day
      final schedulesByLocation = <int, List<Schedule>>{};
      final schedulesByDay = <int, List<Schedule>>{};

      for (final schedule in schedules) {
        schedulesByLocation.putIfAbsent(schedule.locationId, () => []).add(schedule);
        schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
      }

      state = state.copyWith(
        state: ScheduleState.loaded,
        schedules: schedules,
        schedulesByLocation: schedulesByLocation,
        schedulesByDay: schedulesByDay,
        isLoading: false,
      );

      print('[ScheduleProvider] Loaded ${schedules.length} schedules');
    } catch (e) {
      print('[ScheduleProvider] Error loading schedules: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// Load weekly schedule for a specific location
  Future<void> loadWeeklySchedule(int locationId, {bool? isActive}) async {
    try {
      state = state.copyWith(
        state: ScheduleState.loading,
        isLoading: true,
        clearError: true,
      );

      print('[ScheduleProvider] Loading weekly schedule for location: $locationId');

      final repository = ref.read(scheduleRepositoryProvider);
      final weeklySchedule = await repository.getWeeklySchedule(locationId, isActive: isActive);

      // Flatten the weekly schedule into a list
      final schedules = <Schedule>[];
      final schedulesByDay = <int, List<Schedule>>{};

      for (final entry in weeklySchedule.entries) {
        schedules.addAll(entry.value);
        schedulesByDay[entry.key] = entry.value;
      }

      // Update schedules by location
      final schedulesByLocation = Map<int, List<Schedule>>.from(state.schedulesByLocation);
      schedulesByLocation[locationId] = schedules;

      state = state.copyWith(
        state: ScheduleState.loaded,
        schedules: schedules,
        schedulesByLocation: schedulesByLocation,
        schedulesByDay: schedulesByDay,
        isLoading: false,
      );

      print('[ScheduleProvider] Loaded weekly schedule with ${schedules.length} schedules');
    } catch (e) {
      print('[ScheduleProvider] Error loading weekly schedule: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// Create a new schedule
  Future<Schedule?> createSchedule(CreateScheduleRequest request) async {
    try {
      print('[ScheduleProvider] Creating schedule: ${request.toJson()}');

      final repository = ref.read(scheduleRepositoryProvider);
      final schedule = await repository.createSchedule(request);

      // Add to current state
      final updatedSchedules = [...state.schedules, schedule];
      
      // Update grouped data
      final schedulesByLocation = Map<int, List<Schedule>>.from(state.schedulesByLocation);
      schedulesByLocation.putIfAbsent(schedule.locationId, () => []).add(schedule);
      
      final schedulesByDay = Map<int, List<Schedule>>.from(state.schedulesByDay);
      schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);

      state = state.copyWith(
        schedules: updatedSchedules,
        schedulesByLocation: schedulesByLocation,
        schedulesByDay: schedulesByDay,
      );

      print('[ScheduleProvider] Created schedule: ${schedule.id}');
      return schedule;
    } catch (e) {
      print('[ScheduleProvider] Error creating schedule: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Update an existing schedule
  Future<Schedule?> updateSchedule(int id, UpdateScheduleRequest request) async {
    try {
      print('[ScheduleProvider] Updating schedule $id: ${request.toJson()}');

      final repository = ref.read(scheduleRepositoryProvider);
      final updatedSchedule = await repository.updateSchedule(id, request);

      // Update in current state
      final updatedSchedules = state.schedules.map((schedule) {
        return schedule.id == id ? updatedSchedule : schedule;
      }).toList();

      // Rebuild grouped data
      final schedulesByLocation = <int, List<Schedule>>{};
      final schedulesByDay = <int, List<Schedule>>{};

      for (final schedule in updatedSchedules) {
        schedulesByLocation.putIfAbsent(schedule.locationId, () => []).add(schedule);
        schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
      }

      state = state.copyWith(
        schedules: updatedSchedules,
        schedulesByLocation: schedulesByLocation,
        schedulesByDay: schedulesByDay,
      );

      print('[ScheduleProvider] Updated schedule: ${updatedSchedule.id}');
      return updatedSchedule;
    } catch (e) {
      print('[ScheduleProvider] Error updating schedule: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
      );
      return null;
    }
  }

  /// Delete a schedule
  Future<bool> deleteSchedule(int id) async {
    try {
      print('[ScheduleProvider] Deleting schedule: $id');

      final repository = ref.read(scheduleRepositoryProvider);
      final success = await repository.deleteSchedule(id);

      if (success) {
        // Remove from current state
        final updatedSchedules = state.schedules.where((schedule) => schedule.id != id).toList();

        // Rebuild grouped data
        final schedulesByLocation = <int, List<Schedule>>{};
        final schedulesByDay = <int, List<Schedule>>{};

        for (final schedule in updatedSchedules) {
          schedulesByLocation.putIfAbsent(schedule.locationId, () => []).add(schedule);
          schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
        }

        state = state.copyWith(
          schedules: updatedSchedules,
          schedulesByLocation: schedulesByLocation,
          schedulesByDay: schedulesByDay,
        );

        print('[ScheduleProvider] Deleted schedule: $id');
      }

      return success;
    } catch (e) {
      print('[ScheduleProvider] Error deleting schedule: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Create multiple schedules (bulk operation)
  Future<List<Schedule>> createBulkSchedules(List<CreateScheduleRequest> requests) async {
    try {
      print('[ScheduleProvider] Creating ${requests.length} schedules in bulk');

      final repository = ref.read(scheduleRepositoryProvider);
      final createdSchedules = await repository.createBulkSchedules(requests);

      // Add to current state
      final updatedSchedules = [...state.schedules, ...createdSchedules];

      // Rebuild grouped data
      final schedulesByLocation = <int, List<Schedule>>{};
      final schedulesByDay = <int, List<Schedule>>{};

      for (final schedule in updatedSchedules) {
        schedulesByLocation.putIfAbsent(schedule.locationId, () => []).add(schedule);
        schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
      }

      state = state.copyWith(
        schedules: updatedSchedules,
        schedulesByLocation: schedulesByLocation,
        schedulesByDay: schedulesByDay,
      );

      print('[ScheduleProvider] Created ${createdSchedules.length} schedules in bulk');
      return createdSchedules;
    } catch (e) {
      print('[ScheduleProvider] Error creating bulk schedules: $e');
      state = state.copyWith(
        state: ScheduleState.error,
        error: e.toString(),
      );
      return [];
    }
  }

  /// Check for schedule conflicts
  Future<List<Schedule>> checkConflicts({
    required int dayOfWeek,
    required String startTime,
    required String endTime,
    required int locationId,
    int? excludeScheduleId,
  }) async {
    try {
      final repository = ref.read(scheduleRepositoryProvider);
      return await repository.checkScheduleConflicts(
        dayOfWeek: dayOfWeek,
        startTime: startTime,
        endTime: endTime,
        locationId: locationId,
        excludeScheduleId: excludeScheduleId,
      );
    } catch (e) {
      print('[ScheduleProvider] Error checking conflicts: $e');
      return [];
    }
  }

  /// Get schedules for a specific location
  List<Schedule> getSchedulesForLocation(int locationId) {
    return state.schedulesByLocation[locationId] ?? [];
  }

  /// Get schedules for a specific day
  List<Schedule> getSchedulesForDay(int dayOfWeek) {
    return state.schedulesByDay[dayOfWeek] ?? [];
  }

  /// Get today's schedules
  List<Schedule> getTodaySchedules() {
    final today = DateTime.now();
    final dayOfWeek = today.weekday % 7; // Convert to 0-6 format
    return getSchedulesForDay(dayOfWeek);
  }

  /// Clear all schedule data
  void clearSchedules() {
    state = const ScheduleData(state: ScheduleState.initial);
  }

  /// Refresh schedules
  Future<void> refreshSchedules() async {
    await loadSchedules(forceRefresh: true);
  }
}
