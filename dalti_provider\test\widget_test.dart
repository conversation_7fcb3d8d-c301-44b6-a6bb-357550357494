// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';

import 'package:dalti_provider/main.dart';
import 'package:dalti_provider/core/storage/storage_service.dart';
import 'package:dalti_provider/core/providers/app_providers.dart';

void main() {
  setUpAll(() async {
    // Initialize Hive for testing with a temporary directory
    Hive.init('./test/hive_test_db');

    // Initialize StorageService properly for testing
    await StorageService.initForTesting();
  });

  tearDownAll(() async {
    // Close storage service
    await StorageService.close();
  });

  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: Dalti<PERSON>roviderApp()));

    // Wait for the app to settle
    await tester.pumpAndSettle();

    // Verify that the app loads with login screen (unauthenticated state)
    expect(find.text('Login'), findsOneWidget);
    expect(find.text('Welcome to Dalti Provider'), findsOneWidget);
  });

  test('HTTP client configuration', () {
    // Test that HTTP client can be created
    final container = ProviderContainer();
    final httpClient = container.read(httpClientProvider);

    expect(httpClient, isNotNull);
    expect(httpClient.dio.options.baseUrl, contains('dapi'));

    container.dispose();
  });

  test('Router configuration', () {
    // Test that router can be created
    final container = ProviderContainer();
    final router = container.read(routerProvider);

    expect(router, isNotNull);
    expect(router.routerDelegate, isNotNull);

    container.dispose();
  });

  test('JWT service configuration', () {
    // Test that JWT service can be created
    final container = ProviderContainer();
    final jwtService = container.read(jwtServiceProvider);

    expect(jwtService, isNotNull);
    expect(jwtService.isAuthenticated, isFalse);
    expect(jwtService.currentToken, isNull);

    container.dispose();
  });

  test('Auth API service configuration', () {
    // Test that auth API service can be created
    final container = ProviderContainer();
    final authService = container.read(authApiServiceProvider);

    expect(authService, isNotNull);

    container.dispose();
  });
}
