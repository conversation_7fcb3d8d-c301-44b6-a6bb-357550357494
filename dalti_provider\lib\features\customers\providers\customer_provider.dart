import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/customer_models.dart';
import '../services/customer_api_service.dart';
import '../services/customer_service.dart';
import '../repository/customer_repository.dart';
import '../repository/customer_repository_impl.dart';

part 'customer_provider.g.dart';

/// Customer state for managing customer data
class CustomerState {
  final List<Customer> customers;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final CustomerSearchFilters? currentFilters;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final bool hasMore;

  const CustomerState({
    this.customers = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.currentFilters,
    this.currentPage = 1,
    this.totalPages = 0,
    this.totalCount = 0,
    this.hasMore = false,
  });

  CustomerState copyWith({
    List<Customer>? customers,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    CustomerSearchFilters? currentFilters,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    bool? hasMore,
  }) {
    return CustomerState(
      customers: customers ?? this.customers,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error,
      currentFilters: currentFilters ?? this.currentFilters,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  bool get hasData => customers.isNotEmpty;
  bool get hasError => error != null;
}

/// Provider for customer API service
@riverpod
CustomerApiService customerApiService(CustomerApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return CustomerApiService(httpClient);
}

/// Provider for customer repository
@riverpod
CustomerRepository customerRepository(CustomerRepositoryRef ref) {
  final apiService = ref.watch(customerApiServiceProvider);
  return CustomerRepositoryImpl(apiService);
}

/// Provider for customer service
@riverpod
CustomerService customerService(CustomerServiceRef ref) {
  final repository = ref.watch(customerRepositoryProvider);
  return CustomerService(repository);
}

/// Provider for customer state management
@riverpod
class CustomerNotifier extends _$CustomerNotifier {
  @override
  CustomerState build() {
    return const CustomerState();
  }

  /// Load customers with optional filters
  Future<void> loadCustomers({
    CustomerSearchFilters? filters,
    bool refresh = false,
  }) async {
    final repository = ref.read(customerRepositoryProvider);

    try {
      if (refresh || !state.hasData) {
        state = state.copyWith(
          isLoading: !state.hasData,
          isRefreshing: state.hasData,
          error: null,
        );
      }

      print('[CustomerNotifier] Loading customers with filters: ${filters?.searchQuery}');

      final response = await repository.getCustomers(
        filters: filters,
        page: 1,
        pageSize: 20,
      );

      state = state.copyWith(
        customers: response,
        isLoading: false,
        isRefreshing: false,
        currentFilters: filters,
        currentPage: 1,
        totalCount: response.length,
        hasMore: response.length >= 20,
        error: null,
      );

      print('[CustomerNotifier] Customers loaded successfully: ${response.length} customers');
    } catch (e) {
      print('[CustomerNotifier] Error loading customers: $e');
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Load more customers (pagination)
  Future<void> loadMoreCustomers() async {
    if (state.isLoading || !state.hasMore) return;

    final repository = ref.read(customerRepositoryProvider);

    try {
      state = state.copyWith(isLoading: true);

      final nextPage = state.currentPage + 1;
      print('[CustomerNotifier] Loading more customers - page: $nextPage');

      final response = await repository.getCustomers(
        filters: state.currentFilters,
        page: nextPage,
        pageSize: 20,
      );

      final updatedCustomers = [...state.customers, ...response];

      state = state.copyWith(
        customers: updatedCustomers,
        isLoading: false,
        currentPage: nextPage,
        hasMore: response.length >= 20,
      );

      print('[CustomerNotifier] More customers loaded: ${response.length} new customers');
    } catch (e) {
      print('[CustomerNotifier] Error loading more customers: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Search customers
  Future<void> searchCustomers(String query) async {
    if (query.trim().isEmpty) {
      await loadCustomers(refresh: true);
      return;
    }

    final filters = CustomerSearchFilters(searchQuery: query.trim());
    await loadCustomers(filters: filters, refresh: true);
  }

  /// Filter customers
  Future<void> filterCustomers(CustomerSearchFilters filters) async {
    await loadCustomers(filters: filters, refresh: true);
  }

  /// Refresh customers
  Future<void> refreshCustomers() async {
    await loadCustomers(filters: state.currentFilters, refresh: true);
  }

  /// Clear search/filters
  Future<void> clearFilters() async {
    await loadCustomers(refresh: true);
  }

  /// Add a new customer to the list
  void addCustomer(Customer customer) {
    final updatedCustomers = [customer, ...state.customers];
    state = state.copyWith(
      customers: updatedCustomers,
      totalCount: state.totalCount + 1,
    );
  }

  /// Update a customer in the list
  void updateCustomer(Customer customer) {
    final updatedCustomers = state.customers.map((c) {
      return c.id == customer.id ? customer : c;
    }).toList();

    state = state.copyWith(customers: updatedCustomers);
  }

  /// Remove a customer from the list
  void removeCustomer(String customerId) {
    final updatedCustomers = state.customers.where((c) => c.id != customerId).toList();
    state = state.copyWith(
      customers: updatedCustomers,
      totalCount: state.totalCount - 1,
    );
  }
}

/// Provider for a single customer
@riverpod
class SingleCustomerNotifier extends _$SingleCustomerNotifier {
  @override
  Customer? build(String customerId) {
    return null;
  }

  /// Load customer details
  Future<void> loadCustomer() async {
    final repository = ref.read(customerRepositoryProvider);

    try {
      print('[SingleCustomerNotifier] Loading customer: $customerId');
      final customer = await repository.getCustomer(customerId);
      state = customer;
      print('[SingleCustomerNotifier] Customer loaded successfully');
    } catch (e) {
      print('[SingleCustomerNotifier] Error loading customer: $e');
      // Keep current state on error
    }
  }

  /// Update customer
  void updateCustomer(Customer customer) {
    state = customer;
  }
}

/// Provider for customer appointments
@riverpod
class CustomerAppointmentsNotifier extends _$CustomerAppointmentsNotifier {
  @override
  List<CustomerAppointment> build(String customerId) {
    return [];
  }

  /// Load customer appointments
  Future<void> loadAppointments() async {
    final repository = ref.read(customerRepositoryProvider);

    try {
      print('[CustomerAppointmentsNotifier] Loading appointments for customer: $customerId');
      final appointments = await repository.getCustomerAppointments(customerId);
      state = appointments;
      print('[CustomerAppointmentsNotifier] Appointments loaded: ${appointments.length}');
    } catch (e) {
      print('[CustomerAppointmentsNotifier] Error loading appointments: $e');
      // Keep current state on error
    }
  }
}

/// Provider for customer statistics
@riverpod
class CustomerStatsNotifier extends _$CustomerStatsNotifier {
  @override
  CustomerStats build() {
    return const CustomerStats();
  }

  /// Load customer statistics
  Future<void> loadStats() async {
    final repository = ref.read(customerRepositoryProvider);

    try {
      print('[CustomerStatsNotifier] Loading customer stats');
      final stats = await repository.getCustomerStats();
      state = stats;
      print('[CustomerStatsNotifier] Stats loaded successfully');
    } catch (e) {
      print('[CustomerStatsNotifier] Error loading stats: $e');
      // Keep current state on error
    }
  }
}
