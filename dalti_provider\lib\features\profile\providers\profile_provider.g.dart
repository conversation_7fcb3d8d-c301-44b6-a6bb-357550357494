// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'profile_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileApiServiceHash() => r'496268fe181a6980439aad645bd2320cd5de8bae';

/// Profile API service provider
///
/// Copied from [profileApiService].
@ProviderFor(profileApiService)
final profileApiServiceProvider =
    AutoDisposeProvider<ProfileApiService>.internal(
  profileApiService,
  name: r'profileApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProfileApiServiceRef = AutoDisposeProviderRef<ProfileApiService>;
String _$profileNotifierHash() => r'622496988435fcc20d69d9481c708f3b587bd3d5';

/// Legacy StateNotifier provider for backward compatibility
///
/// Copied from [profileNotifier].
@ProviderFor(profileNotifier)
final profileNotifierProvider = AutoDisposeProvider<ProfileNotifier>.internal(
  profileNotifier,
  name: r'profileNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProfileNotifierRef = AutoDisposeProviderRef<ProfileNotifier>;
String _$profileStateHash() => r'de4c945be532037909788ec7a9308d368914a45a';

/// Profile state provider using StateNotifier
///
/// Copied from [profileState].
@ProviderFor(profileState)
final profileStateProvider =
    AutoDisposeProvider<AsyncValue<ProfileData>>.internal(
  profileState,
  name: r'profileStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$profileStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProfileStateRef = AutoDisposeProviderRef<AsyncValue<ProfileData>>;
String _$profileCategoriesHash() => r'dda2d5b64a78e4c2094df202ac1b76bccc397e4c';

/// Provider categories provider
///
/// Copied from [profileCategories].
@ProviderFor(profileCategories)
final profileCategoriesProvider =
    AutoDisposeFutureProvider<List<ProfileCategory>>.internal(
  profileCategories,
  name: r'profileCategoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileCategoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProfileCategoriesRef
    = AutoDisposeFutureProviderRef<List<ProfileCategory>>;
String _$profileHash() => r'2f9d6522a5819894cb0d995b8fe0247956282758';

/// Profile provider
///
/// Copied from [Profile].
@ProviderFor(Profile)
final profileProvider =
    AutoDisposeNotifierProvider<Profile, AsyncValue<ProfileData>>.internal(
  Profile.new,
  name: r'profileProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$profileHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Profile = AutoDisposeNotifier<AsyncValue<ProfileData>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
