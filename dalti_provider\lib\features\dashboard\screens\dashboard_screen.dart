import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dashboard_provider.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/business_overview_card.dart';
import '../widgets/today_schedule_card.dart';
import '../widgets/notifications_panel.dart';
import '../widgets/profile_completion_widget.dart';
import '../widgets/quick_actions_panel.dart';
import '../widgets/in_progress_appointments_card.dart';
import '../widgets/pending_appointments_card.dart';
import '../providers/profile_completion_provider.dart';
import '../../notifications/providers/notifications_provider.dart';
import '../../appointments/providers/appointment_provider.dart';

/// Main dashboard screen for authenticated providers
class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  bool _isNotificationsExpanded = false;

  @override
  void initState() {
    super.initState();
    // Load notifications when dashboard opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationsProvider.notifier).loadNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardNotifierProvider);
    final profileCompletionState = ref.watch(profileCompletionNotifierProvider);
    final theme = Theme.of(context);

    print(
      '[DashboardScreen] Building dashboard with state: ${dashboardState.hasData ? "has data" : "loading"}',
    );

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            print('[DashboardScreen] Pull-to-refresh triggered');
            await Future.wait<void>([
              ref
                  .read(dashboardNotifierProvider.notifier)
                  .refreshDashboardData(),
              ref.read(notificationsProvider.notifier).refreshNotifications(),
              // Refresh today's appointments for the Today's Schedule widget
              ref
                  .read(todaysAppointmentsNotifierProvider.notifier)
                  .refreshTodaysAppointments(),
            ]);
          },
          color: theme.colorScheme.primary,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // Dashboard Header
              SliverToBoxAdapter(
                child: DashboardHeader(
                  isLoading: dashboardState.isLoading,
                  lastUpdated: dashboardState.lastUpdated,
                ),
              ),

              // Main Content
              if (dashboardState.isLoading && !dashboardState.hasData)
                // Initial loading state
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Loading dashboard...',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else if (dashboardState.hasError && !dashboardState.hasData)
                // Error state without cached data
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: theme.colorScheme.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Failed to load dashboard',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          dashboardState.error ?? 'Unknown error occurred',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        FilledButton.icon(
                          onPressed: () {
                            ref
                                .read(dashboardNotifierProvider.notifier)
                                .refreshDashboardData();
                          },
                          icon: const Icon(Icons.refresh),
                          label: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                )
              else
                // Dashboard content
                SliverPadding(
                  padding: const EdgeInsets.all(16.0),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      // Error banner (if error with cached data)
                      if (dashboardState.hasError)
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.errorContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color: theme.colorScheme.onErrorContainer,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Unable to refresh data. Showing cached information.',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onErrorContainer,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Profile Completion Widget
                      if (profileCompletionState.data?.overallPercentage != 100)
                        const ProfileCompletionWidget(),

                      const SizedBox(height: 16),

                      // Pending Appointments
                      const PendingAppointmentsCard(),

                      const SizedBox(height: 16),

                      // InProgress Appointments Quick Access
                      const InProgressAppointmentsCard(),

                      const SizedBox(height: 16),

                      // Business Overview Card
                      BusinessOverviewCard(
                        businessMetrics: dashboardState.data?.businessMetrics,
                        isLoading: dashboardState.isRefreshing,
                      ),

                      const SizedBox(height: 16),

                      // Today's Schedule Card
                      const TodayScheduleCard(),

                      const SizedBox(height: 16),

                      // Notifications Panel
                      NotificationsPanel(
                        isExpanded: _isNotificationsExpanded,
                        onToggle: () {
                          setState(() {
                            _isNotificationsExpanded =
                                !_isNotificationsExpanded;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // Quick Actions Panel
                      const QuickActionsPanel(),

                      // Bottom spacing for better scrolling
                      const SizedBox(height: 24),
                    ]),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Quick stat item widget for compact display
class _QuickStatItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _QuickStatItem({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
