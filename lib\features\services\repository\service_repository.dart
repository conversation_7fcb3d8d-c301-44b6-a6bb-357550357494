import '../models/service_models.dart';

/// Abstract repository interface for service management operations
abstract class ServiceRepository {
  /// Get all services for the current provider
  /// 
  /// [isActive] - Filter by active status (optional)
  /// [categoryId] - Filter by category ID (optional)
  /// [search] - Search term for service title (optional)
  /// 
  /// Returns list of services
  Future<List<Service>> getServices({
    bool? isActive,
    int? categoryId,
    String? search,
  });

  /// Get a specific service by ID
  /// 
  /// [id] - Service ID
  /// 
  /// Returns service details
  Future<Service> getServiceById(int id);

  /// Create a new service
  /// 
  /// [request] - Service creation request with all required details
  /// 
  /// Returns the created service
  Future<Service> createService(CreateServiceRequest request);

  /// Update an existing service
  /// 
  /// [id] - Service ID to update
  /// [request] - Update request with modified fields
  /// 
  /// Returns the updated service
  Future<Service> updateService(int id, UpdateServiceRequest request);

  /// Delete a service
  /// 
  /// [id] - Service ID to delete
  /// 
  /// Returns success status
  /// Throws exception if service has dependencies (active appointments, queue assignments, etc.)
  Future<bool> deleteService(int id);

  /// Check if a service can be deleted
  /// 
  /// [id] - Service ID to check
  /// 
  /// Returns true if service can be safely deleted
  Future<bool> canDeleteService(int id);

  /// Get services with their associated queues and appointments
  /// 
  /// Returns services with extended information for management screens
  Future<List<Service>> getServicesWithDetails();

  /// Search services by title or description
  /// 
  /// [query] - Search query
  /// [limit] - Maximum number of results (optional)
  /// 
  /// Returns matching services
  Future<List<Service>> searchServices(String query, {int? limit});

  /// Get service statistics
  /// 
  /// [id] - Service ID
  /// 
  /// Returns statistics like number of appointments, revenue, etc.
  Future<Map<String, dynamic>> getServiceStats(int id);

  /// Get services by category
  /// 
  /// [categoryId] - Category ID to filter by
  /// [includeInactive] - Whether to include inactive services
  /// 
  /// Returns services in the specified category
  Future<List<Service>> getServicesByCategory(int categoryId, {bool includeInactive = false});

  /// Get popular services based on appointment frequency
  /// 
  /// [limit] - Maximum number of services to return
  /// [timeRange] - Time range for popularity calculation (days)
  /// 
  /// Returns list of popular services
  Future<List<Service>> getPopularServices({int limit = 10, int timeRange = 30});

  /// Get services available for online booking
  /// 
  /// Returns services that accept online bookings
  Future<List<Service>> getOnlineBookableServices();

  /// Get services that accept new customers
  /// 
  /// Returns services that accept new customers
  Future<List<Service>> getNewCustomerServices();

  /// Bulk update service availability
  /// 
  /// [serviceIds] - List of service IDs to update
  /// [isActive] - New active status
  /// 
  /// Returns success status
  Future<bool> bulkUpdateServiceStatus(List<int> serviceIds, bool isActive);

  /// Get service availability by location
  /// 
  /// [locationId] - Location ID to check availability for
  /// 
  /// Returns services available at the specified location
  Future<List<Service>> getServicesByLocation(int locationId);

  /// Get service pricing information
  /// 
  /// [serviceIds] - List of service IDs to get pricing for
  /// 
  /// Returns map of service ID to pricing information
  Future<Map<int, double>> getServicePricing(List<int> serviceIds);

  /// Update service pricing
  /// 
  /// [serviceId] - Service ID to update
  /// [newPrice] - New price for the service
  /// 
  /// Returns updated service
  Future<Service> updateServicePrice(int serviceId, double newPrice);

  /// Get services requiring specific points
  /// 
  /// [minPoints] - Minimum points required
  /// 
  /// Returns services that require at least the specified points
  Future<List<Service>> getServicesByPointsRequirement(int minPoints);

  /// Clone a service with new title
  /// 
  /// [serviceId] - Service ID to clone
  /// [newTitle] - Title for the cloned service
  /// 
  /// Returns the newly created service
  Future<Service> cloneService(int serviceId, String newTitle);
}
