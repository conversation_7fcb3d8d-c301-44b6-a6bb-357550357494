// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'appointment_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Appointment _$AppointmentFromJson(Map<String, dynamic> json) {
  return _Appointment.fromJson(json);
}

/// @nodoc
mixin _$Appointment {
  String get id => throw _privateConstructorUsedError;
  String get customerId => throw _privateConstructorUsedError;
  String get customerName => throw _privateConstructorUsedError;
  String? get customerPhone => throw _privateConstructorUsedError;
  String? get customerEmail => throw _privateConstructorUsedError;
  String get serviceId => throw _privateConstructorUsedError;
  String get serviceName => throw _privateConstructorUsedError;
  DateTime get scheduledTime => throw _privateConstructorUsedError;
  DateTime? get expectedEndTime =>
      throw _privateConstructorUsedError; // Expected end time from API
  int get duration => throw _privateConstructorUsedError; // in minutes
  AppointmentStatus get status => throw _privateConstructorUsedError;
  String? get locationId => throw _privateConstructorUsedError;
  String? get locationName => throw _privateConstructorUsedError;
  String? get queueId => throw _privateConstructorUsedError;
  String? get queueName => throw _privateConstructorUsedError;
  String? get providerId => throw _privateConstructorUsedError;
  String? get providerName => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  String? get cancellationReason => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;
  DateTime? get realAppointmentStartTime =>
      throw _privateConstructorUsedError; // When service actually started
  DateTime? get realAppointmentEndTime =>
      throw _privateConstructorUsedError; // When service actually ended
// Additional fields for appointment management
  String? get reminderSent => throw _privateConstructorUsedError;
  bool? get isRecurring => throw _privateConstructorUsedError;
  String? get recurringPattern => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;

  /// Serializes this Appointment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Appointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentCopyWith<Appointment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentCopyWith<$Res> {
  factory $AppointmentCopyWith(
          Appointment value, $Res Function(Appointment) then) =
      _$AppointmentCopyWithImpl<$Res, Appointment>;
  @useResult
  $Res call(
      {String id,
      String customerId,
      String customerName,
      String? customerPhone,
      String? customerEmail,
      String serviceId,
      String serviceName,
      DateTime scheduledTime,
      DateTime? expectedEndTime,
      int duration,
      AppointmentStatus status,
      String? locationId,
      String? locationName,
      String? queueId,
      String? queueName,
      String? providerId,
      String? providerName,
      double? price,
      String? notes,
      String? cancellationReason,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? completedAt,
      DateTime? realAppointmentStartTime,
      DateTime? realAppointmentEndTime,
      String? reminderSent,
      bool? isRecurring,
      String? recurringPattern,
      List<String>? tags});
}

/// @nodoc
class _$AppointmentCopyWithImpl<$Res, $Val extends Appointment>
    implements $AppointmentCopyWith<$Res> {
  _$AppointmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Appointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerId = null,
    Object? customerName = null,
    Object? customerPhone = freezed,
    Object? customerEmail = freezed,
    Object? serviceId = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? expectedEndTime = freezed,
    Object? duration = null,
    Object? status = null,
    Object? locationId = freezed,
    Object? locationName = freezed,
    Object? queueId = freezed,
    Object? queueName = freezed,
    Object? providerId = freezed,
    Object? providerName = freezed,
    Object? price = freezed,
    Object? notes = freezed,
    Object? cancellationReason = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? completedAt = freezed,
    Object? realAppointmentStartTime = freezed,
    Object? realAppointmentEndTime = freezed,
    Object? reminderSent = freezed,
    Object? isRecurring = freezed,
    Object? recurringPattern = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      customerEmail: freezed == customerEmail
          ? _value.customerEmail
          : customerEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expectedEndTime: freezed == expectedEndTime
          ? _value.expectedEndTime
          : expectedEndTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueName: freezed == queueName
          ? _value.queueName
          : queueName // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerName: freezed == providerName
          ? _value.providerName
          : providerName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationReason: freezed == cancellationReason
          ? _value.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      realAppointmentStartTime: freezed == realAppointmentStartTime
          ? _value.realAppointmentStartTime
          : realAppointmentStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      realAppointmentEndTime: freezed == realAppointmentEndTime
          ? _value.realAppointmentEndTime
          : realAppointmentEndTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reminderSent: freezed == reminderSent
          ? _value.reminderSent
          : reminderSent // ignore: cast_nullable_to_non_nullable
              as String?,
      isRecurring: freezed == isRecurring
          ? _value.isRecurring
          : isRecurring // ignore: cast_nullable_to_non_nullable
              as bool?,
      recurringPattern: freezed == recurringPattern
          ? _value.recurringPattern
          : recurringPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppointmentImplCopyWith<$Res>
    implements $AppointmentCopyWith<$Res> {
  factory _$$AppointmentImplCopyWith(
          _$AppointmentImpl value, $Res Function(_$AppointmentImpl) then) =
      __$$AppointmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String customerId,
      String customerName,
      String? customerPhone,
      String? customerEmail,
      String serviceId,
      String serviceName,
      DateTime scheduledTime,
      DateTime? expectedEndTime,
      int duration,
      AppointmentStatus status,
      String? locationId,
      String? locationName,
      String? queueId,
      String? queueName,
      String? providerId,
      String? providerName,
      double? price,
      String? notes,
      String? cancellationReason,
      DateTime? createdAt,
      DateTime? updatedAt,
      DateTime? completedAt,
      DateTime? realAppointmentStartTime,
      DateTime? realAppointmentEndTime,
      String? reminderSent,
      bool? isRecurring,
      String? recurringPattern,
      List<String>? tags});
}

/// @nodoc
class __$$AppointmentImplCopyWithImpl<$Res>
    extends _$AppointmentCopyWithImpl<$Res, _$AppointmentImpl>
    implements _$$AppointmentImplCopyWith<$Res> {
  __$$AppointmentImplCopyWithImpl(
      _$AppointmentImpl _value, $Res Function(_$AppointmentImpl) _then)
      : super(_value, _then);

  /// Create a copy of Appointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerId = null,
    Object? customerName = null,
    Object? customerPhone = freezed,
    Object? customerEmail = freezed,
    Object? serviceId = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? expectedEndTime = freezed,
    Object? duration = null,
    Object? status = null,
    Object? locationId = freezed,
    Object? locationName = freezed,
    Object? queueId = freezed,
    Object? queueName = freezed,
    Object? providerId = freezed,
    Object? providerName = freezed,
    Object? price = freezed,
    Object? notes = freezed,
    Object? cancellationReason = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? completedAt = freezed,
    Object? realAppointmentStartTime = freezed,
    Object? realAppointmentEndTime = freezed,
    Object? reminderSent = freezed,
    Object? isRecurring = freezed,
    Object? recurringPattern = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$AppointmentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      customerPhone: freezed == customerPhone
          ? _value.customerPhone
          : customerPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      customerEmail: freezed == customerEmail
          ? _value.customerEmail
          : customerEmail // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expectedEndTime: freezed == expectedEndTime
          ? _value.expectedEndTime
          : expectedEndTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueName: freezed == queueName
          ? _value.queueName
          : queueName // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerName: freezed == providerName
          ? _value.providerName
          : providerName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      cancellationReason: freezed == cancellationReason
          ? _value.cancellationReason
          : cancellationReason // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      realAppointmentStartTime: freezed == realAppointmentStartTime
          ? _value.realAppointmentStartTime
          : realAppointmentStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      realAppointmentEndTime: freezed == realAppointmentEndTime
          ? _value.realAppointmentEndTime
          : realAppointmentEndTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reminderSent: freezed == reminderSent
          ? _value.reminderSent
          : reminderSent // ignore: cast_nullable_to_non_nullable
              as String?,
      isRecurring: freezed == isRecurring
          ? _value.isRecurring
          : isRecurring // ignore: cast_nullable_to_non_nullable
              as bool?,
      recurringPattern: freezed == recurringPattern
          ? _value.recurringPattern
          : recurringPattern // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentImpl implements _Appointment {
  const _$AppointmentImpl(
      {required this.id,
      required this.customerId,
      required this.customerName,
      this.customerPhone,
      this.customerEmail,
      required this.serviceId,
      required this.serviceName,
      required this.scheduledTime,
      this.expectedEndTime,
      required this.duration,
      required this.status,
      this.locationId,
      this.locationName,
      this.queueId,
      this.queueName,
      this.providerId,
      this.providerName,
      this.price,
      this.notes,
      this.cancellationReason,
      this.createdAt,
      this.updatedAt,
      this.completedAt,
      this.realAppointmentStartTime,
      this.realAppointmentEndTime,
      this.reminderSent,
      this.isRecurring,
      this.recurringPattern,
      final List<String>? tags})
      : _tags = tags;

  factory _$AppointmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentImplFromJson(json);

  @override
  final String id;
  @override
  final String customerId;
  @override
  final String customerName;
  @override
  final String? customerPhone;
  @override
  final String? customerEmail;
  @override
  final String serviceId;
  @override
  final String serviceName;
  @override
  final DateTime scheduledTime;
  @override
  final DateTime? expectedEndTime;
// Expected end time from API
  @override
  final int duration;
// in minutes
  @override
  final AppointmentStatus status;
  @override
  final String? locationId;
  @override
  final String? locationName;
  @override
  final String? queueId;
  @override
  final String? queueName;
  @override
  final String? providerId;
  @override
  final String? providerName;
  @override
  final double? price;
  @override
  final String? notes;
  @override
  final String? cancellationReason;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final DateTime? completedAt;
  @override
  final DateTime? realAppointmentStartTime;
// When service actually started
  @override
  final DateTime? realAppointmentEndTime;
// When service actually ended
// Additional fields for appointment management
  @override
  final String? reminderSent;
  @override
  final bool? isRecurring;
  @override
  final String? recurringPattern;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Appointment(id: $id, customerId: $customerId, customerName: $customerName, customerPhone: $customerPhone, customerEmail: $customerEmail, serviceId: $serviceId, serviceName: $serviceName, scheduledTime: $scheduledTime, expectedEndTime: $expectedEndTime, duration: $duration, status: $status, locationId: $locationId, locationName: $locationName, queueId: $queueId, queueName: $queueName, providerId: $providerId, providerName: $providerName, price: $price, notes: $notes, cancellationReason: $cancellationReason, createdAt: $createdAt, updatedAt: $updatedAt, completedAt: $completedAt, realAppointmentStartTime: $realAppointmentStartTime, realAppointmentEndTime: $realAppointmentEndTime, reminderSent: $reminderSent, isRecurring: $isRecurring, recurringPattern: $recurringPattern, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.customerPhone, customerPhone) ||
                other.customerPhone == customerPhone) &&
            (identical(other.customerEmail, customerEmail) ||
                other.customerEmail == customerEmail) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.serviceName, serviceName) ||
                other.serviceName == serviceName) &&
            (identical(other.scheduledTime, scheduledTime) ||
                other.scheduledTime == scheduledTime) &&
            (identical(other.expectedEndTime, expectedEndTime) ||
                other.expectedEndTime == expectedEndTime) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.locationName, locationName) ||
                other.locationName == locationName) &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.queueName, queueName) ||
                other.queueName == queueName) &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.cancellationReason, cancellationReason) ||
                other.cancellationReason == cancellationReason) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(
                    other.realAppointmentStartTime, realAppointmentStartTime) ||
                other.realAppointmentStartTime == realAppointmentStartTime) &&
            (identical(other.realAppointmentEndTime, realAppointmentEndTime) ||
                other.realAppointmentEndTime == realAppointmentEndTime) &&
            (identical(other.reminderSent, reminderSent) ||
                other.reminderSent == reminderSent) &&
            (identical(other.isRecurring, isRecurring) ||
                other.isRecurring == isRecurring) &&
            (identical(other.recurringPattern, recurringPattern) ||
                other.recurringPattern == recurringPattern) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        serviceId,
        serviceName,
        scheduledTime,
        expectedEndTime,
        duration,
        status,
        locationId,
        locationName,
        queueId,
        queueName,
        providerId,
        providerName,
        price,
        notes,
        cancellationReason,
        createdAt,
        updatedAt,
        completedAt,
        realAppointmentStartTime,
        realAppointmentEndTime,
        reminderSent,
        isRecurring,
        recurringPattern,
        const DeepCollectionEquality().hash(_tags)
      ]);

  /// Create a copy of Appointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentImplCopyWith<_$AppointmentImpl> get copyWith =>
      __$$AppointmentImplCopyWithImpl<_$AppointmentImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentImplToJson(
      this,
    );
  }
}

abstract class _Appointment implements Appointment {
  const factory _Appointment(
      {required final String id,
      required final String customerId,
      required final String customerName,
      final String? customerPhone,
      final String? customerEmail,
      required final String serviceId,
      required final String serviceName,
      required final DateTime scheduledTime,
      final DateTime? expectedEndTime,
      required final int duration,
      required final AppointmentStatus status,
      final String? locationId,
      final String? locationName,
      final String? queueId,
      final String? queueName,
      final String? providerId,
      final String? providerName,
      final double? price,
      final String? notes,
      final String? cancellationReason,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      final DateTime? completedAt,
      final DateTime? realAppointmentStartTime,
      final DateTime? realAppointmentEndTime,
      final String? reminderSent,
      final bool? isRecurring,
      final String? recurringPattern,
      final List<String>? tags}) = _$AppointmentImpl;

  factory _Appointment.fromJson(Map<String, dynamic> json) =
      _$AppointmentImpl.fromJson;

  @override
  String get id;
  @override
  String get customerId;
  @override
  String get customerName;
  @override
  String? get customerPhone;
  @override
  String? get customerEmail;
  @override
  String get serviceId;
  @override
  String get serviceName;
  @override
  DateTime get scheduledTime;
  @override
  DateTime? get expectedEndTime; // Expected end time from API
  @override
  int get duration; // in minutes
  @override
  AppointmentStatus get status;
  @override
  String? get locationId;
  @override
  String? get locationName;
  @override
  String? get queueId;
  @override
  String? get queueName;
  @override
  String? get providerId;
  @override
  String? get providerName;
  @override
  double? get price;
  @override
  String? get notes;
  @override
  String? get cancellationReason;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  DateTime? get completedAt;
  @override
  DateTime? get realAppointmentStartTime; // When service actually started
  @override
  DateTime? get realAppointmentEndTime; // When service actually ended
// Additional fields for appointment management
  @override
  String? get reminderSent;
  @override
  bool? get isRecurring;
  @override
  String? get recurringPattern;
  @override
  List<String>? get tags;

  /// Create a copy of Appointment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentImplCopyWith<_$AppointmentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppointmentRequest _$AppointmentRequestFromJson(Map<String, dynamic> json) {
  return _AppointmentRequest.fromJson(json);
}

/// @nodoc
mixin _$AppointmentRequest {
  String get customerId => throw _privateConstructorUsedError;
  String get serviceId => throw _privateConstructorUsedError;
  DateTime get scheduledTime => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  String? get locationId => throw _privateConstructorUsedError;
  String? get queueId => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;

  /// Serializes this AppointmentRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentRequestCopyWith<AppointmentRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentRequestCopyWith<$Res> {
  factory $AppointmentRequestCopyWith(
          AppointmentRequest value, $Res Function(AppointmentRequest) then) =
      _$AppointmentRequestCopyWithImpl<$Res, AppointmentRequest>;
  @useResult
  $Res call(
      {String customerId,
      String serviceId,
      DateTime scheduledTime,
      int duration,
      String? locationId,
      String? queueId,
      String? notes,
      List<String>? tags});
}

/// @nodoc
class _$AppointmentRequestCopyWithImpl<$Res, $Val extends AppointmentRequest>
    implements $AppointmentRequestCopyWith<$Res> {
  _$AppointmentRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = null,
    Object? serviceId = null,
    Object? scheduledTime = null,
    Object? duration = null,
    Object? locationId = freezed,
    Object? queueId = freezed,
    Object? notes = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppointmentRequestImplCopyWith<$Res>
    implements $AppointmentRequestCopyWith<$Res> {
  factory _$$AppointmentRequestImplCopyWith(_$AppointmentRequestImpl value,
          $Res Function(_$AppointmentRequestImpl) then) =
      __$$AppointmentRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String customerId,
      String serviceId,
      DateTime scheduledTime,
      int duration,
      String? locationId,
      String? queueId,
      String? notes,
      List<String>? tags});
}

/// @nodoc
class __$$AppointmentRequestImplCopyWithImpl<$Res>
    extends _$AppointmentRequestCopyWithImpl<$Res, _$AppointmentRequestImpl>
    implements _$$AppointmentRequestImplCopyWith<$Res> {
  __$$AppointmentRequestImplCopyWithImpl(_$AppointmentRequestImpl _value,
      $Res Function(_$AppointmentRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerId = null,
    Object? serviceId = null,
    Object? scheduledTime = null,
    Object? duration = null,
    Object? locationId = freezed,
    Object? queueId = freezed,
    Object? notes = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$AppointmentRequestImpl(
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentRequestImpl implements _AppointmentRequest {
  const _$AppointmentRequestImpl(
      {required this.customerId,
      required this.serviceId,
      required this.scheduledTime,
      required this.duration,
      this.locationId,
      this.queueId,
      this.notes,
      final List<String>? tags})
      : _tags = tags;

  factory _$AppointmentRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentRequestImplFromJson(json);

  @override
  final String customerId;
  @override
  final String serviceId;
  @override
  final DateTime scheduledTime;
  @override
  final int duration;
  @override
  final String? locationId;
  @override
  final String? queueId;
  @override
  final String? notes;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AppointmentRequest(customerId: $customerId, serviceId: $serviceId, scheduledTime: $scheduledTime, duration: $duration, locationId: $locationId, queueId: $queueId, notes: $notes, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentRequestImpl &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.scheduledTime, scheduledTime) ||
                other.scheduledTime == scheduledTime) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      customerId,
      serviceId,
      scheduledTime,
      duration,
      locationId,
      queueId,
      notes,
      const DeepCollectionEquality().hash(_tags));

  /// Create a copy of AppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentRequestImplCopyWith<_$AppointmentRequestImpl> get copyWith =>
      __$$AppointmentRequestImplCopyWithImpl<_$AppointmentRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentRequestImplToJson(
      this,
    );
  }
}

abstract class _AppointmentRequest implements AppointmentRequest {
  const factory _AppointmentRequest(
      {required final String customerId,
      required final String serviceId,
      required final DateTime scheduledTime,
      required final int duration,
      final String? locationId,
      final String? queueId,
      final String? notes,
      final List<String>? tags}) = _$AppointmentRequestImpl;

  factory _AppointmentRequest.fromJson(Map<String, dynamic> json) =
      _$AppointmentRequestImpl.fromJson;

  @override
  String get customerId;
  @override
  String get serviceId;
  @override
  DateTime get scheduledTime;
  @override
  int get duration;
  @override
  String? get locationId;
  @override
  String? get queueId;
  @override
  String? get notes;
  @override
  List<String>? get tags;

  /// Create a copy of AppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentRequestImplCopyWith<_$AppointmentRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppointmentSearchFilters _$AppointmentSearchFiltersFromJson(
    Map<String, dynamic> json) {
  return _AppointmentSearchFilters.fromJson(json);
}

/// @nodoc
mixin _$AppointmentSearchFilters {
  String? get customerName => throw _privateConstructorUsedError;
  String? get serviceName => throw _privateConstructorUsedError;
  String? get locationId => throw _privateConstructorUsedError;
  String? get queueId => throw _privateConstructorUsedError;
  AppointmentStatus? get status => throw _privateConstructorUsedError;
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;

  /// Serializes this AppointmentSearchFilters to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentSearchFiltersCopyWith<AppointmentSearchFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentSearchFiltersCopyWith<$Res> {
  factory $AppointmentSearchFiltersCopyWith(AppointmentSearchFilters value,
          $Res Function(AppointmentSearchFilters) then) =
      _$AppointmentSearchFiltersCopyWithImpl<$Res, AppointmentSearchFilters>;
  @useResult
  $Res call(
      {String? customerName,
      String? serviceName,
      String? locationId,
      String? queueId,
      AppointmentStatus? status,
      DateTime? startDate,
      DateTime? endDate,
      List<String>? tags});
}

/// @nodoc
class _$AppointmentSearchFiltersCopyWithImpl<$Res,
        $Val extends AppointmentSearchFilters>
    implements $AppointmentSearchFiltersCopyWith<$Res> {
  _$AppointmentSearchFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerName = freezed,
    Object? serviceName = freezed,
    Object? locationId = freezed,
    Object? queueId = freezed,
    Object? status = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? tags = freezed,
  }) {
    return _then(_value.copyWith(
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceName: freezed == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppointmentSearchFiltersImplCopyWith<$Res>
    implements $AppointmentSearchFiltersCopyWith<$Res> {
  factory _$$AppointmentSearchFiltersImplCopyWith(
          _$AppointmentSearchFiltersImpl value,
          $Res Function(_$AppointmentSearchFiltersImpl) then) =
      __$$AppointmentSearchFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? customerName,
      String? serviceName,
      String? locationId,
      String? queueId,
      AppointmentStatus? status,
      DateTime? startDate,
      DateTime? endDate,
      List<String>? tags});
}

/// @nodoc
class __$$AppointmentSearchFiltersImplCopyWithImpl<$Res>
    extends _$AppointmentSearchFiltersCopyWithImpl<$Res,
        _$AppointmentSearchFiltersImpl>
    implements _$$AppointmentSearchFiltersImplCopyWith<$Res> {
  __$$AppointmentSearchFiltersImplCopyWithImpl(
      _$AppointmentSearchFiltersImpl _value,
      $Res Function(_$AppointmentSearchFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerName = freezed,
    Object? serviceName = freezed,
    Object? locationId = freezed,
    Object? queueId = freezed,
    Object? status = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? tags = freezed,
  }) {
    return _then(_$AppointmentSearchFiltersImpl(
      customerName: freezed == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String?,
      serviceName: freezed == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      queueId: freezed == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentSearchFiltersImpl implements _AppointmentSearchFilters {
  const _$AppointmentSearchFiltersImpl(
      {this.customerName,
      this.serviceName,
      this.locationId,
      this.queueId,
      this.status,
      this.startDate,
      this.endDate,
      final List<String>? tags})
      : _tags = tags;

  factory _$AppointmentSearchFiltersImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentSearchFiltersImplFromJson(json);

  @override
  final String? customerName;
  @override
  final String? serviceName;
  @override
  final String? locationId;
  @override
  final String? queueId;
  @override
  final AppointmentStatus? status;
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AppointmentSearchFilters(customerName: $customerName, serviceName: $serviceName, locationId: $locationId, queueId: $queueId, status: $status, startDate: $startDate, endDate: $endDate, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentSearchFiltersImpl &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.serviceName, serviceName) ||
                other.serviceName == serviceName) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      customerName,
      serviceName,
      locationId,
      queueId,
      status,
      startDate,
      endDate,
      const DeepCollectionEquality().hash(_tags));

  /// Create a copy of AppointmentSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentSearchFiltersImplCopyWith<_$AppointmentSearchFiltersImpl>
      get copyWith => __$$AppointmentSearchFiltersImplCopyWithImpl<
          _$AppointmentSearchFiltersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentSearchFiltersImplToJson(
      this,
    );
  }
}

abstract class _AppointmentSearchFilters implements AppointmentSearchFilters {
  const factory _AppointmentSearchFilters(
      {final String? customerName,
      final String? serviceName,
      final String? locationId,
      final String? queueId,
      final AppointmentStatus? status,
      final DateTime? startDate,
      final DateTime? endDate,
      final List<String>? tags}) = _$AppointmentSearchFiltersImpl;

  factory _AppointmentSearchFilters.fromJson(Map<String, dynamic> json) =
      _$AppointmentSearchFiltersImpl.fromJson;

  @override
  String? get customerName;
  @override
  String? get serviceName;
  @override
  String? get locationId;
  @override
  String? get queueId;
  @override
  AppointmentStatus? get status;
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  List<String>? get tags;

  /// Create a copy of AppointmentSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentSearchFiltersImplCopyWith<_$AppointmentSearchFiltersImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AppointmentResponse _$AppointmentResponseFromJson(Map<String, dynamic> json) {
  return _AppointmentResponse.fromJson(json);
}

/// @nodoc
mixin _$AppointmentResponse {
  bool get success => throw _privateConstructorUsedError;
  Appointment? get appointment => throw _privateConstructorUsedError;
  AppointmentError? get error => throw _privateConstructorUsedError;

  /// Serializes this AppointmentResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentResponseCopyWith<AppointmentResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentResponseCopyWith<$Res> {
  factory $AppointmentResponseCopyWith(
          AppointmentResponse value, $Res Function(AppointmentResponse) then) =
      _$AppointmentResponseCopyWithImpl<$Res, AppointmentResponse>;
  @useResult
  $Res call({bool success, Appointment? appointment, AppointmentError? error});

  $AppointmentCopyWith<$Res>? get appointment;
  $AppointmentErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$AppointmentResponseCopyWithImpl<$Res, $Val extends AppointmentResponse>
    implements $AppointmentResponseCopyWith<$Res> {
  _$AppointmentResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointment = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointment: freezed == appointment
          ? _value.appointment
          : appointment // ignore: cast_nullable_to_non_nullable
              as Appointment?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppointmentError?,
    ) as $Val);
  }

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppointmentCopyWith<$Res>? get appointment {
    if (_value.appointment == null) {
      return null;
    }

    return $AppointmentCopyWith<$Res>(_value.appointment!, (value) {
      return _then(_value.copyWith(appointment: value) as $Val);
    });
  }

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppointmentErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppointmentErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppointmentResponseImplCopyWith<$Res>
    implements $AppointmentResponseCopyWith<$Res> {
  factory _$$AppointmentResponseImplCopyWith(_$AppointmentResponseImpl value,
          $Res Function(_$AppointmentResponseImpl) then) =
      __$$AppointmentResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, Appointment? appointment, AppointmentError? error});

  @override
  $AppointmentCopyWith<$Res>? get appointment;
  @override
  $AppointmentErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$AppointmentResponseImplCopyWithImpl<$Res>
    extends _$AppointmentResponseCopyWithImpl<$Res, _$AppointmentResponseImpl>
    implements _$$AppointmentResponseImplCopyWith<$Res> {
  __$$AppointmentResponseImplCopyWithImpl(_$AppointmentResponseImpl _value,
      $Res Function(_$AppointmentResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointment = freezed,
    Object? error = freezed,
  }) {
    return _then(_$AppointmentResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointment: freezed == appointment
          ? _value.appointment
          : appointment // ignore: cast_nullable_to_non_nullable
              as Appointment?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppointmentError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentResponseImpl implements _AppointmentResponse {
  const _$AppointmentResponseImpl(
      {required this.success, this.appointment, this.error});

  factory _$AppointmentResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final Appointment? appointment;
  @override
  final AppointmentError? error;

  @override
  String toString() {
    return 'AppointmentResponse(success: $success, appointment: $appointment, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.appointment, appointment) ||
                other.appointment == appointment) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, appointment, error);

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentResponseImplCopyWith<_$AppointmentResponseImpl> get copyWith =>
      __$$AppointmentResponseImplCopyWithImpl<_$AppointmentResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentResponseImplToJson(
      this,
    );
  }
}

abstract class _AppointmentResponse implements AppointmentResponse {
  const factory _AppointmentResponse(
      {required final bool success,
      final Appointment? appointment,
      final AppointmentError? error}) = _$AppointmentResponseImpl;

  factory _AppointmentResponse.fromJson(Map<String, dynamic> json) =
      _$AppointmentResponseImpl.fromJson;

  @override
  bool get success;
  @override
  Appointment? get appointment;
  @override
  AppointmentError? get error;

  /// Create a copy of AppointmentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentResponseImplCopyWith<_$AppointmentResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppointmentsResponse _$AppointmentsResponseFromJson(Map<String, dynamic> json) {
  return _AppointmentsResponse.fromJson(json);
}

/// @nodoc
mixin _$AppointmentsResponse {
  bool get success => throw _privateConstructorUsedError;
  List<Appointment> get appointments => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  AppointmentError? get error => throw _privateConstructorUsedError;

  /// Serializes this AppointmentsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentsResponseCopyWith<AppointmentsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentsResponseCopyWith<$Res> {
  factory $AppointmentsResponseCopyWith(AppointmentsResponse value,
          $Res Function(AppointmentsResponse) then) =
      _$AppointmentsResponseCopyWithImpl<$Res, AppointmentsResponse>;
  @useResult
  $Res call(
      {bool success,
      List<Appointment> appointments,
      int totalCount,
      int currentPage,
      int pageSize,
      AppointmentError? error});

  $AppointmentErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$AppointmentsResponseCopyWithImpl<$Res,
        $Val extends AppointmentsResponse>
    implements $AppointmentsResponseCopyWith<$Res> {
  _$AppointmentsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointments = null,
    Object? totalCount = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointments: null == appointments
          ? _value.appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<Appointment>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppointmentError?,
    ) as $Val);
  }

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppointmentErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppointmentErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AppointmentsResponseImplCopyWith<$Res>
    implements $AppointmentsResponseCopyWith<$Res> {
  factory _$$AppointmentsResponseImplCopyWith(_$AppointmentsResponseImpl value,
          $Res Function(_$AppointmentsResponseImpl) then) =
      __$$AppointmentsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      List<Appointment> appointments,
      int totalCount,
      int currentPage,
      int pageSize,
      AppointmentError? error});

  @override
  $AppointmentErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$AppointmentsResponseImplCopyWithImpl<$Res>
    extends _$AppointmentsResponseCopyWithImpl<$Res, _$AppointmentsResponseImpl>
    implements _$$AppointmentsResponseImplCopyWith<$Res> {
  __$$AppointmentsResponseImplCopyWithImpl(_$AppointmentsResponseImpl _value,
      $Res Function(_$AppointmentsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointments = null,
    Object? totalCount = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? error = freezed,
  }) {
    return _then(_$AppointmentsResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointments: null == appointments
          ? _value._appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<Appointment>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppointmentError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentsResponseImpl implements _AppointmentsResponse {
  const _$AppointmentsResponseImpl(
      {required this.success,
      final List<Appointment> appointments = const [],
      this.totalCount = 0,
      this.currentPage = 1,
      this.pageSize = 20,
      this.error})
      : _appointments = appointments;

  factory _$AppointmentsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentsResponseImplFromJson(json);

  @override
  final bool success;
  final List<Appointment> _appointments;
  @override
  @JsonKey()
  List<Appointment> get appointments {
    if (_appointments is EqualUnmodifiableListView) return _appointments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_appointments);
  }

  @override
  @JsonKey()
  final int totalCount;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;
  @override
  final AppointmentError? error;

  @override
  String toString() {
    return 'AppointmentsResponse(success: $success, appointments: $appointments, totalCount: $totalCount, currentPage: $currentPage, pageSize: $pageSize, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentsResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality()
                .equals(other._appointments, _appointments) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      const DeepCollectionEquality().hash(_appointments),
      totalCount,
      currentPage,
      pageSize,
      error);

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentsResponseImplCopyWith<_$AppointmentsResponseImpl>
      get copyWith =>
          __$$AppointmentsResponseImplCopyWithImpl<_$AppointmentsResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentsResponseImplToJson(
      this,
    );
  }
}

abstract class _AppointmentsResponse implements AppointmentsResponse {
  const factory _AppointmentsResponse(
      {required final bool success,
      final List<Appointment> appointments,
      final int totalCount,
      final int currentPage,
      final int pageSize,
      final AppointmentError? error}) = _$AppointmentsResponseImpl;

  factory _AppointmentsResponse.fromJson(Map<String, dynamic> json) =
      _$AppointmentsResponseImpl.fromJson;

  @override
  bool get success;
  @override
  List<Appointment> get appointments;
  @override
  int get totalCount;
  @override
  int get currentPage;
  @override
  int get pageSize;
  @override
  AppointmentError? get error;

  /// Create a copy of AppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentsResponseImplCopyWith<_$AppointmentsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AppointmentError _$AppointmentErrorFromJson(Map<String, dynamic> json) {
  return _AppointmentError.fromJson(json);
}

/// @nodoc
mixin _$AppointmentError {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;

  /// Serializes this AppointmentError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentErrorCopyWith<AppointmentError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentErrorCopyWith<$Res> {
  factory $AppointmentErrorCopyWith(
          AppointmentError value, $Res Function(AppointmentError) then) =
      _$AppointmentErrorCopyWithImpl<$Res, AppointmentError>;
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class _$AppointmentErrorCopyWithImpl<$Res, $Val extends AppointmentError>
    implements $AppointmentErrorCopyWith<$Res> {
  _$AppointmentErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppointmentErrorImplCopyWith<$Res>
    implements $AppointmentErrorCopyWith<$Res> {
  factory _$$AppointmentErrorImplCopyWith(_$AppointmentErrorImpl value,
          $Res Function(_$AppointmentErrorImpl) then) =
      __$$AppointmentErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class __$$AppointmentErrorImplCopyWithImpl<$Res>
    extends _$AppointmentErrorCopyWithImpl<$Res, _$AppointmentErrorImpl>
    implements _$$AppointmentErrorImplCopyWith<$Res> {
  __$$AppointmentErrorImplCopyWithImpl(_$AppointmentErrorImpl _value,
      $Res Function(_$AppointmentErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_$AppointmentErrorImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentErrorImpl implements _AppointmentError {
  const _$AppointmentErrorImpl(
      {required this.code, required this.message, this.details});

  factory _$AppointmentErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentErrorImplFromJson(json);

  @override
  final String code;
  @override
  final String message;
  @override
  final String? details;

  @override
  String toString() {
    return 'AppointmentError(code: $code, message: $message, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentErrorImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message, details);

  /// Create a copy of AppointmentError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentErrorImplCopyWith<_$AppointmentErrorImpl> get copyWith =>
      __$$AppointmentErrorImplCopyWithImpl<_$AppointmentErrorImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentErrorImplToJson(
      this,
    );
  }
}

abstract class _AppointmentError implements AppointmentError {
  const factory _AppointmentError(
      {required final String code,
      required final String message,
      final String? details}) = _$AppointmentErrorImpl;

  factory _AppointmentError.fromJson(Map<String, dynamic> json) =
      _$AppointmentErrorImpl.fromJson;

  @override
  String get code;
  @override
  String get message;
  @override
  String? get details;

  /// Create a copy of AppointmentError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentErrorImplCopyWith<_$AppointmentErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CalendarViewData _$CalendarViewDataFromJson(Map<String, dynamic> json) {
  return _CalendarViewData.fromJson(json);
}

/// @nodoc
mixin _$CalendarViewData {
  DateTime get date => throw _privateConstructorUsedError;
  List<Appointment> get appointments => throw _privateConstructorUsedError;
  int get totalAppointments => throw _privateConstructorUsedError;
  int get completedAppointments => throw _privateConstructorUsedError;
  int get cancelledAppointments => throw _privateConstructorUsedError;

  /// Serializes this CalendarViewData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CalendarViewData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CalendarViewDataCopyWith<CalendarViewData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarViewDataCopyWith<$Res> {
  factory $CalendarViewDataCopyWith(
          CalendarViewData value, $Res Function(CalendarViewData) then) =
      _$CalendarViewDataCopyWithImpl<$Res, CalendarViewData>;
  @useResult
  $Res call(
      {DateTime date,
      List<Appointment> appointments,
      int totalAppointments,
      int completedAppointments,
      int cancelledAppointments});
}

/// @nodoc
class _$CalendarViewDataCopyWithImpl<$Res, $Val extends CalendarViewData>
    implements $CalendarViewDataCopyWith<$Res> {
  _$CalendarViewDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CalendarViewData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? appointments = null,
    Object? totalAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      appointments: null == appointments
          ? _value.appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<Appointment>,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CalendarViewDataImplCopyWith<$Res>
    implements $CalendarViewDataCopyWith<$Res> {
  factory _$$CalendarViewDataImplCopyWith(_$CalendarViewDataImpl value,
          $Res Function(_$CalendarViewDataImpl) then) =
      __$$CalendarViewDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime date,
      List<Appointment> appointments,
      int totalAppointments,
      int completedAppointments,
      int cancelledAppointments});
}

/// @nodoc
class __$$CalendarViewDataImplCopyWithImpl<$Res>
    extends _$CalendarViewDataCopyWithImpl<$Res, _$CalendarViewDataImpl>
    implements _$$CalendarViewDataImplCopyWith<$Res> {
  __$$CalendarViewDataImplCopyWithImpl(_$CalendarViewDataImpl _value,
      $Res Function(_$CalendarViewDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarViewData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? appointments = null,
    Object? totalAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
  }) {
    return _then(_$CalendarViewDataImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      appointments: null == appointments
          ? _value._appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<Appointment>,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CalendarViewDataImpl implements _CalendarViewData {
  const _$CalendarViewDataImpl(
      {required this.date,
      final List<Appointment> appointments = const [],
      this.totalAppointments = 0,
      this.completedAppointments = 0,
      this.cancelledAppointments = 0})
      : _appointments = appointments;

  factory _$CalendarViewDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$CalendarViewDataImplFromJson(json);

  @override
  final DateTime date;
  final List<Appointment> _appointments;
  @override
  @JsonKey()
  List<Appointment> get appointments {
    if (_appointments is EqualUnmodifiableListView) return _appointments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_appointments);
  }

  @override
  @JsonKey()
  final int totalAppointments;
  @override
  @JsonKey()
  final int completedAppointments;
  @override
  @JsonKey()
  final int cancelledAppointments;

  @override
  String toString() {
    return 'CalendarViewData(date: $date, appointments: $appointments, totalAppointments: $totalAppointments, completedAppointments: $completedAppointments, cancelledAppointments: $cancelledAppointments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalendarViewDataImpl &&
            (identical(other.date, date) || other.date == date) &&
            const DeepCollectionEquality()
                .equals(other._appointments, _appointments) &&
            (identical(other.totalAppointments, totalAppointments) ||
                other.totalAppointments == totalAppointments) &&
            (identical(other.completedAppointments, completedAppointments) ||
                other.completedAppointments == completedAppointments) &&
            (identical(other.cancelledAppointments, cancelledAppointments) ||
                other.cancelledAppointments == cancelledAppointments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      date,
      const DeepCollectionEquality().hash(_appointments),
      totalAppointments,
      completedAppointments,
      cancelledAppointments);

  /// Create a copy of CalendarViewData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalendarViewDataImplCopyWith<_$CalendarViewDataImpl> get copyWith =>
      __$$CalendarViewDataImplCopyWithImpl<_$CalendarViewDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CalendarViewDataImplToJson(
      this,
    );
  }
}

abstract class _CalendarViewData implements CalendarViewData {
  const factory _CalendarViewData(
      {required final DateTime date,
      final List<Appointment> appointments,
      final int totalAppointments,
      final int completedAppointments,
      final int cancelledAppointments}) = _$CalendarViewDataImpl;

  factory _CalendarViewData.fromJson(Map<String, dynamic> json) =
      _$CalendarViewDataImpl.fromJson;

  @override
  DateTime get date;
  @override
  List<Appointment> get appointments;
  @override
  int get totalAppointments;
  @override
  int get completedAppointments;
  @override
  int get cancelledAppointments;

  /// Create a copy of CalendarViewData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalendarViewDataImplCopyWith<_$CalendarViewDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TimeSlot _$TimeSlotFromJson(Map<String, dynamic> json) {
  return _TimeSlot.fromJson(json);
}

/// @nodoc
mixin _$TimeSlot {
  DateTime get startTime => throw _privateConstructorUsedError;
  DateTime get endTime => throw _privateConstructorUsedError;
  bool get isAvailable => throw _privateConstructorUsedError;
  String? get appointmentId => throw _privateConstructorUsedError;
  String? get reason => throw _privateConstructorUsedError;

  /// Serializes this TimeSlot to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimeSlotCopyWith<TimeSlot> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimeSlotCopyWith<$Res> {
  factory $TimeSlotCopyWith(TimeSlot value, $Res Function(TimeSlot) then) =
      _$TimeSlotCopyWithImpl<$Res, TimeSlot>;
  @useResult
  $Res call(
      {DateTime startTime,
      DateTime endTime,
      bool isAvailable,
      String? appointmentId,
      String? reason});
}

/// @nodoc
class _$TimeSlotCopyWithImpl<$Res, $Val extends TimeSlot>
    implements $TimeSlotCopyWith<$Res> {
  _$TimeSlotCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startTime = null,
    Object? endTime = null,
    Object? isAvailable = null,
    Object? appointmentId = freezed,
    Object? reason = freezed,
  }) {
    return _then(_value.copyWith(
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isAvailable: null == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      appointmentId: freezed == appointmentId
          ? _value.appointmentId
          : appointmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimeSlotImplCopyWith<$Res>
    implements $TimeSlotCopyWith<$Res> {
  factory _$$TimeSlotImplCopyWith(
          _$TimeSlotImpl value, $Res Function(_$TimeSlotImpl) then) =
      __$$TimeSlotImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime startTime,
      DateTime endTime,
      bool isAvailable,
      String? appointmentId,
      String? reason});
}

/// @nodoc
class __$$TimeSlotImplCopyWithImpl<$Res>
    extends _$TimeSlotCopyWithImpl<$Res, _$TimeSlotImpl>
    implements _$$TimeSlotImplCopyWith<$Res> {
  __$$TimeSlotImplCopyWithImpl(
      _$TimeSlotImpl _value, $Res Function(_$TimeSlotImpl) _then)
      : super(_value, _then);

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startTime = null,
    Object? endTime = null,
    Object? isAvailable = null,
    Object? appointmentId = freezed,
    Object? reason = freezed,
  }) {
    return _then(_$TimeSlotImpl(
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isAvailable: null == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      appointmentId: freezed == appointmentId
          ? _value.appointmentId
          : appointmentId // ignore: cast_nullable_to_non_nullable
              as String?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TimeSlotImpl implements _TimeSlot {
  const _$TimeSlotImpl(
      {required this.startTime,
      required this.endTime,
      required this.isAvailable,
      this.appointmentId,
      this.reason});

  factory _$TimeSlotImpl.fromJson(Map<String, dynamic> json) =>
      _$$TimeSlotImplFromJson(json);

  @override
  final DateTime startTime;
  @override
  final DateTime endTime;
  @override
  final bool isAvailable;
  @override
  final String? appointmentId;
  @override
  final String? reason;

  @override
  String toString() {
    return 'TimeSlot(startTime: $startTime, endTime: $endTime, isAvailable: $isAvailable, appointmentId: $appointmentId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeSlotImpl &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.appointmentId, appointmentId) ||
                other.appointmentId == appointmentId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, startTime, endTime, isAvailable, appointmentId, reason);

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeSlotImplCopyWith<_$TimeSlotImpl> get copyWith =>
      __$$TimeSlotImplCopyWithImpl<_$TimeSlotImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TimeSlotImplToJson(
      this,
    );
  }
}

abstract class _TimeSlot implements TimeSlot {
  const factory _TimeSlot(
      {required final DateTime startTime,
      required final DateTime endTime,
      required final bool isAvailable,
      final String? appointmentId,
      final String? reason}) = _$TimeSlotImpl;

  factory _TimeSlot.fromJson(Map<String, dynamic> json) =
      _$TimeSlotImpl.fromJson;

  @override
  DateTime get startTime;
  @override
  DateTime get endTime;
  @override
  bool get isAvailable;
  @override
  String? get appointmentId;
  @override
  String? get reason;

  /// Create a copy of TimeSlot
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeSlotImplCopyWith<_$TimeSlotImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppointmentStats _$AppointmentStatsFromJson(Map<String, dynamic> json) {
  return _AppointmentStats.fromJson(json);
}

/// @nodoc
mixin _$AppointmentStats {
  int get totalAppointments => throw _privateConstructorUsedError;
  int get scheduledAppointments => throw _privateConstructorUsedError;
  int get completedAppointments => throw _privateConstructorUsedError;
  int get cancelledAppointments => throw _privateConstructorUsedError;
  int get noShowAppointments => throw _privateConstructorUsedError;
  double get completionRate => throw _privateConstructorUsedError;
  double get cancellationRate => throw _privateConstructorUsedError;
  double get noShowRate => throw _privateConstructorUsedError;
  double get totalRevenue => throw _privateConstructorUsedError;

  /// Serializes this AppointmentStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppointmentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppointmentStatsCopyWith<AppointmentStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppointmentStatsCopyWith<$Res> {
  factory $AppointmentStatsCopyWith(
          AppointmentStats value, $Res Function(AppointmentStats) then) =
      _$AppointmentStatsCopyWithImpl<$Res, AppointmentStats>;
  @useResult
  $Res call(
      {int totalAppointments,
      int scheduledAppointments,
      int completedAppointments,
      int cancelledAppointments,
      int noShowAppointments,
      double completionRate,
      double cancellationRate,
      double noShowRate,
      double totalRevenue});
}

/// @nodoc
class _$AppointmentStatsCopyWithImpl<$Res, $Val extends AppointmentStats>
    implements $AppointmentStatsCopyWith<$Res> {
  _$AppointmentStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppointmentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalAppointments = null,
    Object? scheduledAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
    Object? noShowAppointments = null,
    Object? completionRate = null,
    Object? cancellationRate = null,
    Object? noShowRate = null,
    Object? totalRevenue = null,
  }) {
    return _then(_value.copyWith(
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      scheduledAppointments: null == scheduledAppointments
          ? _value.scheduledAppointments
          : scheduledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      noShowAppointments: null == noShowAppointments
          ? _value.noShowAppointments
          : noShowAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      cancellationRate: null == cancellationRate
          ? _value.cancellationRate
          : cancellationRate // ignore: cast_nullable_to_non_nullable
              as double,
      noShowRate: null == noShowRate
          ? _value.noShowRate
          : noShowRate // ignore: cast_nullable_to_non_nullable
              as double,
      totalRevenue: null == totalRevenue
          ? _value.totalRevenue
          : totalRevenue // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppointmentStatsImplCopyWith<$Res>
    implements $AppointmentStatsCopyWith<$Res> {
  factory _$$AppointmentStatsImplCopyWith(_$AppointmentStatsImpl value,
          $Res Function(_$AppointmentStatsImpl) then) =
      __$$AppointmentStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalAppointments,
      int scheduledAppointments,
      int completedAppointments,
      int cancelledAppointments,
      int noShowAppointments,
      double completionRate,
      double cancellationRate,
      double noShowRate,
      double totalRevenue});
}

/// @nodoc
class __$$AppointmentStatsImplCopyWithImpl<$Res>
    extends _$AppointmentStatsCopyWithImpl<$Res, _$AppointmentStatsImpl>
    implements _$$AppointmentStatsImplCopyWith<$Res> {
  __$$AppointmentStatsImplCopyWithImpl(_$AppointmentStatsImpl _value,
      $Res Function(_$AppointmentStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppointmentStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalAppointments = null,
    Object? scheduledAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
    Object? noShowAppointments = null,
    Object? completionRate = null,
    Object? cancellationRate = null,
    Object? noShowRate = null,
    Object? totalRevenue = null,
  }) {
    return _then(_$AppointmentStatsImpl(
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      scheduledAppointments: null == scheduledAppointments
          ? _value.scheduledAppointments
          : scheduledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      noShowAppointments: null == noShowAppointments
          ? _value.noShowAppointments
          : noShowAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      cancellationRate: null == cancellationRate
          ? _value.cancellationRate
          : cancellationRate // ignore: cast_nullable_to_non_nullable
              as double,
      noShowRate: null == noShowRate
          ? _value.noShowRate
          : noShowRate // ignore: cast_nullable_to_non_nullable
              as double,
      totalRevenue: null == totalRevenue
          ? _value.totalRevenue
          : totalRevenue // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppointmentStatsImpl implements _AppointmentStats {
  const _$AppointmentStatsImpl(
      {this.totalAppointments = 0,
      this.scheduledAppointments = 0,
      this.completedAppointments = 0,
      this.cancelledAppointments = 0,
      this.noShowAppointments = 0,
      this.completionRate = 0.0,
      this.cancellationRate = 0.0,
      this.noShowRate = 0.0,
      this.totalRevenue = 0.0});

  factory _$AppointmentStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppointmentStatsImplFromJson(json);

  @override
  @JsonKey()
  final int totalAppointments;
  @override
  @JsonKey()
  final int scheduledAppointments;
  @override
  @JsonKey()
  final int completedAppointments;
  @override
  @JsonKey()
  final int cancelledAppointments;
  @override
  @JsonKey()
  final int noShowAppointments;
  @override
  @JsonKey()
  final double completionRate;
  @override
  @JsonKey()
  final double cancellationRate;
  @override
  @JsonKey()
  final double noShowRate;
  @override
  @JsonKey()
  final double totalRevenue;

  @override
  String toString() {
    return 'AppointmentStats(totalAppointments: $totalAppointments, scheduledAppointments: $scheduledAppointments, completedAppointments: $completedAppointments, cancelledAppointments: $cancelledAppointments, noShowAppointments: $noShowAppointments, completionRate: $completionRate, cancellationRate: $cancellationRate, noShowRate: $noShowRate, totalRevenue: $totalRevenue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppointmentStatsImpl &&
            (identical(other.totalAppointments, totalAppointments) ||
                other.totalAppointments == totalAppointments) &&
            (identical(other.scheduledAppointments, scheduledAppointments) ||
                other.scheduledAppointments == scheduledAppointments) &&
            (identical(other.completedAppointments, completedAppointments) ||
                other.completedAppointments == completedAppointments) &&
            (identical(other.cancelledAppointments, cancelledAppointments) ||
                other.cancelledAppointments == cancelledAppointments) &&
            (identical(other.noShowAppointments, noShowAppointments) ||
                other.noShowAppointments == noShowAppointments) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.cancellationRate, cancellationRate) ||
                other.cancellationRate == cancellationRate) &&
            (identical(other.noShowRate, noShowRate) ||
                other.noShowRate == noShowRate) &&
            (identical(other.totalRevenue, totalRevenue) ||
                other.totalRevenue == totalRevenue));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalAppointments,
      scheduledAppointments,
      completedAppointments,
      cancelledAppointments,
      noShowAppointments,
      completionRate,
      cancellationRate,
      noShowRate,
      totalRevenue);

  /// Create a copy of AppointmentStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppointmentStatsImplCopyWith<_$AppointmentStatsImpl> get copyWith =>
      __$$AppointmentStatsImplCopyWithImpl<_$AppointmentStatsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppointmentStatsImplToJson(
      this,
    );
  }
}

abstract class _AppointmentStats implements AppointmentStats {
  const factory _AppointmentStats(
      {final int totalAppointments,
      final int scheduledAppointments,
      final int completedAppointments,
      final int cancelledAppointments,
      final int noShowAppointments,
      final double completionRate,
      final double cancellationRate,
      final double noShowRate,
      final double totalRevenue}) = _$AppointmentStatsImpl;

  factory _AppointmentStats.fromJson(Map<String, dynamic> json) =
      _$AppointmentStatsImpl.fromJson;

  @override
  int get totalAppointments;
  @override
  int get scheduledAppointments;
  @override
  int get completedAppointments;
  @override
  int get cancelledAppointments;
  @override
  int get noShowAppointments;
  @override
  double get completionRate;
  @override
  double get cancellationRate;
  @override
  double get noShowRate;
  @override
  double get totalRevenue;

  /// Create a copy of AppointmentStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppointmentStatsImplCopyWith<_$AppointmentStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateAppointmentRequest _$CreateAppointmentRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateAppointmentRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateAppointmentRequest {
  String get customerUserId =>
      throw _privateConstructorUsedError; // UUID string
  int get serviceId => throw _privateConstructorUsedError;
  int get placeId => throw _privateConstructorUsedError;
  int get queueId => throw _privateConstructorUsedError;
  DateTime get startTime => throw _privateConstructorUsedError;
  DateTime get endTime => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this CreateAppointmentRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateAppointmentRequestCopyWith<CreateAppointmentRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateAppointmentRequestCopyWith<$Res> {
  factory $CreateAppointmentRequestCopyWith(CreateAppointmentRequest value,
          $Res Function(CreateAppointmentRequest) then) =
      _$CreateAppointmentRequestCopyWithImpl<$Res, CreateAppointmentRequest>;
  @useResult
  $Res call(
      {String customerUserId,
      int serviceId,
      int placeId,
      int queueId,
      DateTime startTime,
      DateTime endTime,
      String? notes});
}

/// @nodoc
class _$CreateAppointmentRequestCopyWithImpl<$Res,
        $Val extends CreateAppointmentRequest>
    implements $CreateAppointmentRequestCopyWith<$Res> {
  _$CreateAppointmentRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerUserId = null,
    Object? serviceId = null,
    Object? placeId = null,
    Object? queueId = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      customerUserId: null == customerUserId
          ? _value.customerUserId
          : customerUserId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int,
      placeId: null == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as int,
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateAppointmentRequestImplCopyWith<$Res>
    implements $CreateAppointmentRequestCopyWith<$Res> {
  factory _$$CreateAppointmentRequestImplCopyWith(
          _$CreateAppointmentRequestImpl value,
          $Res Function(_$CreateAppointmentRequestImpl) then) =
      __$$CreateAppointmentRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String customerUserId,
      int serviceId,
      int placeId,
      int queueId,
      DateTime startTime,
      DateTime endTime,
      String? notes});
}

/// @nodoc
class __$$CreateAppointmentRequestImplCopyWithImpl<$Res>
    extends _$CreateAppointmentRequestCopyWithImpl<$Res,
        _$CreateAppointmentRequestImpl>
    implements _$$CreateAppointmentRequestImplCopyWith<$Res> {
  __$$CreateAppointmentRequestImplCopyWithImpl(
      _$CreateAppointmentRequestImpl _value,
      $Res Function(_$CreateAppointmentRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? customerUserId = null,
    Object? serviceId = null,
    Object? placeId = null,
    Object? queueId = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? notes = freezed,
  }) {
    return _then(_$CreateAppointmentRequestImpl(
      customerUserId: null == customerUserId
          ? _value.customerUserId
          : customerUserId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int,
      placeId: null == placeId
          ? _value.placeId
          : placeId // ignore: cast_nullable_to_non_nullable
              as int,
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as int,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateAppointmentRequestImpl implements _CreateAppointmentRequest {
  const _$CreateAppointmentRequestImpl(
      {required this.customerUserId,
      required this.serviceId,
      required this.placeId,
      required this.queueId,
      required this.startTime,
      required this.endTime,
      this.notes});

  factory _$CreateAppointmentRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateAppointmentRequestImplFromJson(json);

  @override
  final String customerUserId;
// UUID string
  @override
  final int serviceId;
  @override
  final int placeId;
  @override
  final int queueId;
  @override
  final DateTime startTime;
  @override
  final DateTime endTime;
  @override
  final String? notes;

  @override
  String toString() {
    return 'CreateAppointmentRequest(customerUserId: $customerUserId, serviceId: $serviceId, placeId: $placeId, queueId: $queueId, startTime: $startTime, endTime: $endTime, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateAppointmentRequestImpl &&
            (identical(other.customerUserId, customerUserId) ||
                other.customerUserId == customerUserId) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.placeId, placeId) || other.placeId == placeId) &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, customerUserId, serviceId,
      placeId, queueId, startTime, endTime, notes);

  /// Create a copy of CreateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateAppointmentRequestImplCopyWith<_$CreateAppointmentRequestImpl>
      get copyWith => __$$CreateAppointmentRequestImplCopyWithImpl<
          _$CreateAppointmentRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateAppointmentRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateAppointmentRequest implements CreateAppointmentRequest {
  const factory _CreateAppointmentRequest(
      {required final String customerUserId,
      required final int serviceId,
      required final int placeId,
      required final int queueId,
      required final DateTime startTime,
      required final DateTime endTime,
      final String? notes}) = _$CreateAppointmentRequestImpl;

  factory _CreateAppointmentRequest.fromJson(Map<String, dynamic> json) =
      _$CreateAppointmentRequestImpl.fromJson;

  @override
  String get customerUserId; // UUID string
  @override
  int get serviceId;
  @override
  int get placeId;
  @override
  int get queueId;
  @override
  DateTime get startTime;
  @override
  DateTime get endTime;
  @override
  String? get notes;

  /// Create a copy of CreateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateAppointmentRequestImplCopyWith<_$CreateAppointmentRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpdateAppointmentRequest _$UpdateAppointmentRequestFromJson(
    Map<String, dynamic> json) {
  return _UpdateAppointmentRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateAppointmentRequest {
  int get serviceId => throw _privateConstructorUsedError;
  int get queueId => throw _privateConstructorUsedError;
  String get expectedStartTime =>
      throw _privateConstructorUsedError; // ISO 8601 date-time string
  String get expectedEndTime =>
      throw _privateConstructorUsedError; // ISO 8601 date-time string
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this UpdateAppointmentRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateAppointmentRequestCopyWith<UpdateAppointmentRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateAppointmentRequestCopyWith<$Res> {
  factory $UpdateAppointmentRequestCopyWith(UpdateAppointmentRequest value,
          $Res Function(UpdateAppointmentRequest) then) =
      _$UpdateAppointmentRequestCopyWithImpl<$Res, UpdateAppointmentRequest>;
  @useResult
  $Res call(
      {int serviceId,
      int queueId,
      String expectedStartTime,
      String expectedEndTime,
      String? notes});
}

/// @nodoc
class _$UpdateAppointmentRequestCopyWithImpl<$Res,
        $Val extends UpdateAppointmentRequest>
    implements $UpdateAppointmentRequestCopyWith<$Res> {
  _$UpdateAppointmentRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? serviceId = null,
    Object? queueId = null,
    Object? expectedStartTime = null,
    Object? expectedEndTime = null,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int,
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as int,
      expectedStartTime: null == expectedStartTime
          ? _value.expectedStartTime
          : expectedStartTime // ignore: cast_nullable_to_non_nullable
              as String,
      expectedEndTime: null == expectedEndTime
          ? _value.expectedEndTime
          : expectedEndTime // ignore: cast_nullable_to_non_nullable
              as String,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateAppointmentRequestImplCopyWith<$Res>
    implements $UpdateAppointmentRequestCopyWith<$Res> {
  factory _$$UpdateAppointmentRequestImplCopyWith(
          _$UpdateAppointmentRequestImpl value,
          $Res Function(_$UpdateAppointmentRequestImpl) then) =
      __$$UpdateAppointmentRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int serviceId,
      int queueId,
      String expectedStartTime,
      String expectedEndTime,
      String? notes});
}

/// @nodoc
class __$$UpdateAppointmentRequestImplCopyWithImpl<$Res>
    extends _$UpdateAppointmentRequestCopyWithImpl<$Res,
        _$UpdateAppointmentRequestImpl>
    implements _$$UpdateAppointmentRequestImplCopyWith<$Res> {
  __$$UpdateAppointmentRequestImplCopyWithImpl(
      _$UpdateAppointmentRequestImpl _value,
      $Res Function(_$UpdateAppointmentRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? serviceId = null,
    Object? queueId = null,
    Object? expectedStartTime = null,
    Object? expectedEndTime = null,
    Object? notes = freezed,
  }) {
    return _then(_$UpdateAppointmentRequestImpl(
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as int,
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as int,
      expectedStartTime: null == expectedStartTime
          ? _value.expectedStartTime
          : expectedStartTime // ignore: cast_nullable_to_non_nullable
              as String,
      expectedEndTime: null == expectedEndTime
          ? _value.expectedEndTime
          : expectedEndTime // ignore: cast_nullable_to_non_nullable
              as String,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateAppointmentRequestImpl implements _UpdateAppointmentRequest {
  const _$UpdateAppointmentRequestImpl(
      {required this.serviceId,
      required this.queueId,
      required this.expectedStartTime,
      required this.expectedEndTime,
      this.notes});

  factory _$UpdateAppointmentRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateAppointmentRequestImplFromJson(json);

  @override
  final int serviceId;
  @override
  final int queueId;
  @override
  final String expectedStartTime;
// ISO 8601 date-time string
  @override
  final String expectedEndTime;
// ISO 8601 date-time string
  @override
  final String? notes;

  @override
  String toString() {
    return 'UpdateAppointmentRequest(serviceId: $serviceId, queueId: $queueId, expectedStartTime: $expectedStartTime, expectedEndTime: $expectedEndTime, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAppointmentRequestImpl &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.expectedStartTime, expectedStartTime) ||
                other.expectedStartTime == expectedStartTime) &&
            (identical(other.expectedEndTime, expectedEndTime) ||
                other.expectedEndTime == expectedEndTime) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, serviceId, queueId,
      expectedStartTime, expectedEndTime, notes);

  /// Create a copy of UpdateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAppointmentRequestImplCopyWith<_$UpdateAppointmentRequestImpl>
      get copyWith => __$$UpdateAppointmentRequestImplCopyWithImpl<
          _$UpdateAppointmentRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateAppointmentRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateAppointmentRequest implements UpdateAppointmentRequest {
  const factory _UpdateAppointmentRequest(
      {required final int serviceId,
      required final int queueId,
      required final String expectedStartTime,
      required final String expectedEndTime,
      final String? notes}) = _$UpdateAppointmentRequestImpl;

  factory _UpdateAppointmentRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateAppointmentRequestImpl.fromJson;

  @override
  int get serviceId;
  @override
  int get queueId;
  @override
  String get expectedStartTime; // ISO 8601 date-time string
  @override
  String get expectedEndTime; // ISO 8601 date-time string
  @override
  String? get notes;

  /// Create a copy of UpdateAppointmentRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAppointmentRequestImplCopyWith<_$UpdateAppointmentRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
