import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:mime/mime.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:math' as math;
import '../../../core/config/app_config.dart';
import '../../../core/auth/jwt_service.dart';

/// Service for uploading provider logos using 3-step S3 upload workflow
class ProviderLogoService {
  final JwtService _jwtService;

  ProviderLogoService(this._jwtService);

  /// Get headers with authentication
  Future<Map<String, String>> get _headers async {
    final authHeader = _jwtService.getAuthorizationHeader();
    return {
      'Authorization': authHeader ?? '',
      'Content-Type': 'application/json',
    };
  }

  /// Complete logo upload workflow using cross-platform file handling
  Future<LogoUploadResult> uploadLogo(PlatformFile file) async {
    try {
      print('[ProviderLogoService] Starting logo upload for: ${file.name}');

      // Validate file
      final validation = _validateFile(file);
      if (!validation.isValid) {
        return LogoUploadResult.error(validation.error!);
      }

      // Step 1: Initialize upload
      final initResult = await _initializeUpload(file);
      if (!initResult.success) {
        return LogoUploadResult.error(
          initResult.error ?? 'Failed to initialize upload',
        );
      }

      // Step 2: Upload to S3
      final s3Result = await _uploadToS3(
        file,
        initResult.uploadUrl!,
        initResult.uploadFields!,
      );
      if (!s3Result.success) {
        return LogoUploadResult.error(
          s3Result.error ?? 'Failed to upload to S3',
        );
      }

      // Step 3: Confirm upload (optional)
      final confirmResult = await _confirmUpload(initResult.fileId!);
      if (!confirmResult.success) {
        // S3 upload succeeded but confirmation failed - this is still a success
        print(
          '[ProviderLogoService] Warning: Upload succeeded but confirmation failed',
        );
      }

      return LogoUploadResult.success(
        fileId: initResult.fileId!,
        fileName: file.name,
        downloadUrl: confirmResult.downloadUrl,
      );
    } catch (e) {
      print('[ProviderLogoService] Upload error: $e');
      return LogoUploadResult.error('Upload failed: ${e.toString()}');
    }
  }

  /// Step 1: Initialize upload with backend
  Future<InitializeUploadResult> _initializeUpload(PlatformFile file) async {
    try {
      print('[ProviderLogoService] Initializing upload...');

      final headers = await _headers;
      final response = await http.post(
        Uri.parse('${AppConfig.apiBaseUrl}/api/auth/providers/mobile/logo'),
        headers: headers,
        body: json.encode({
          'fileName': file.name,
          'fileType': lookupMimeType(file.name) ?? 'application/octet-stream',
          'fileSize': file.size,
        }),
      );

      print(
        '[ProviderLogoService] Initialize response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true && data['data'] != null) {
          final responseData = data['data'];

          return InitializeUploadResult(
            success: true,
            uploadUrl: responseData['instructions']['url'],
            uploadFields: Map<String, dynamic>.from(
              responseData['instructions']['fields'],
            ),
            fileId: responseData['file']['id'],
          );
        } else {
          return InitializeUploadResult(
            success: false,
            error: data['message'] ?? 'Failed to initialize upload',
          );
        }
      } else {
        return InitializeUploadResult(
          success: false,
          error: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[ProviderLogoService] Initialize error: $e');
      return InitializeUploadResult(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Step 2: Upload file directly to S3
  Future<S3UploadResult> _uploadToS3(
    PlatformFile file,
    String uploadUrl,
    Map<String, dynamic> uploadFields,
  ) async {
    try {
      print('[ProviderLogoService] Uploading to S3...');

      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));

      // Add all S3 security fields FIRST (order matters for S3)
      uploadFields.forEach((key, value) {
        request.fields[key] = value.toString();
      });

      // Add the actual file LAST
      if (kIsWeb && file.bytes != null) {
        // Web: use bytes
        request.files.add(
          http.MultipartFile.fromBytes(
            'file',
            file.bytes!,
            filename: file.name,
          ),
        );
      } else if (file.path != null) {
        // Mobile: use file path
        request.files.add(
          await http.MultipartFile.fromPath(
            'file',
            file.path!,
            filename: file.name,
          ),
        );
      } else {
        return S3UploadResult(success: false, error: 'File data not available');
      }

      final response = await request.send();

      print('[ProviderLogoService] S3 response status: ${response.statusCode}');

      // S3 returns 204 for successful uploads
      if (response.statusCode == 204) {
        return S3UploadResult(success: true);
      } else {
        final responseBody = await response.stream.bytesToString();
        return S3UploadResult(
          success: false,
          error: 'S3 upload failed: ${response.statusCode} - $responseBody',
        );
      }
    } catch (e) {
      print('[ProviderLogoService] S3 upload error: $e');
      return S3UploadResult(
        success: false,
        error: 'S3 upload error: ${e.toString()}',
      );
    }
  }

  /// Step 3: Confirm upload with backend (optional)
  Future<ConfirmUploadResult> _confirmUpload(String fileId) async {
    try {
      print('[ProviderLogoService] Confirming upload...');

      final headers = await _headers;
      final response = await http.post(
        Uri.parse(
          '${AppConfig.apiBaseUrl}/api/auth/providers/mobile/logo/confirm',
        ),
        headers: headers,
        body: json.encode({'fileId': fileId}),
      );

      print(
        '[ProviderLogoService] Confirm response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true && data['data'] != null) {
          // Extract downloadUrl from the nested logo object in the response
          final logoData = data['data']['logo'];
          final downloadUrl = logoData?['downloadUrl'];

          return ConfirmUploadResult(success: true, downloadUrl: downloadUrl);
        } else {
          return ConfirmUploadResult(
            success: false,
            error: data['message'] ?? 'Failed to confirm upload',
          );
        }
      } else {
        return ConfirmUploadResult(
          success: false,
          error: 'Server error: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[ProviderLogoService] Confirm error: $e');
      return ConfirmUploadResult(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Validate file before upload
  FileValidationResult _validateFile(PlatformFile file) {
    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return FileValidationResult(
        isValid: false,
        error: 'File size must be less than 5MB',
      );
    }

    // Check file type
    final mimeType = lookupMimeType(file.name);
    final allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (mimeType == null || !allowedTypes.contains(mimeType)) {
      return FileValidationResult(
        isValid: false,
        error: 'Only JPEG, PNG, and WebP images are allowed',
      );
    }

    return FileValidationResult(isValid: true);
  }
}

/// Result classes for different upload steps
class LogoUploadResult {
  final bool success;
  final String? error;
  final String? fileId;
  final String? fileName;
  final String? downloadUrl;

  LogoUploadResult({
    required this.success,
    this.error,
    this.fileId,
    this.fileName,
    this.downloadUrl,
  });

  factory LogoUploadResult.success({
    required String fileId,
    required String fileName,
    String? downloadUrl,
  }) {
    return LogoUploadResult(
      success: true,
      fileId: fileId,
      fileName: fileName,
      downloadUrl: downloadUrl,
    );
  }

  factory LogoUploadResult.error(String error) {
    return LogoUploadResult(success: false, error: error);
  }
}

class InitializeUploadResult {
  final bool success;
  final String? error;
  final String? uploadUrl;
  final Map<String, dynamic>? uploadFields;
  final String? fileId;

  InitializeUploadResult({
    required this.success,
    this.error,
    this.uploadUrl,
    this.uploadFields,
    this.fileId,
  });
}

class S3UploadResult {
  final bool success;
  final String? error;

  S3UploadResult({required this.success, this.error});
}

class ConfirmUploadResult {
  final bool success;
  final String? error;
  final String? downloadUrl;

  ConfirmUploadResult({required this.success, this.error, this.downloadUrl});
}

class FileValidationResult {
  final bool isValid;
  final String? error;

  FileValidationResult({required this.isValid, this.error});
}
