import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/services/category_service.dart';
import 'package:dalti_provider/core/network/api_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';

void main() {
  group('Debug Category Service', () {
    late CategoryService categoryService;

    setUp(() {
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);
      final mockApiService = AuthApiService(httpClient, jwtService);
      categoryService = CategoryService(mockApiService);
    });

    test('Debug what CategoryService.getCategories() returns', () async {
      final categories = await categoryService.getCategories();
      
      print('CategoryService.getCategories() returned ${categories.length} categories:');
      for (final category in categories) {
        print('Category: ${category.name} (ID: ${category.id}, selectable: ${category.isSelectable}, children: ${category.children.length})');
        for (final child in category.children) {
          print('  Child: ${child.name} (ID: ${child.id}, parentId: ${child.parentId}, selectable: ${child.isSelectable})');
        }
      }
      
      // This should be 3 parent categories
      expect(categories.length, equals(3));
    });

    test('Debug what CategoryService.getSelectableCategories() returns', () async {
      final selectableCategories = await categoryService.getSelectableCategories();
      
      print('CategoryService.getSelectableCategories() returned ${selectableCategories.length} categories:');
      for (final category in selectableCategories) {
        print('Selectable: ${category.name} (ID: ${category.id}, parentId: ${category.parentId})');
      }
      
      // This should be 12 selectable categories
      expect(selectableCategories.length, equals(12));
    });
  });
}
