import 'dart:convert';

/// JWT Token model for managing authentication tokens
class JwtToken {
  final String accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final String? tokenType;
  final Map<String, dynamic>? payload;

  const JwtToken({
    required this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.tokenType = 'Bearer',
    this.payload,
  });

  /// Create JwtToken from JSON response
  factory JwtToken.fromJson(Map<String, dynamic> json) {
    final accessToken = json['access_token'] ?? json['token'] ?? '';
    final refreshToken = json['refresh_token'];
    final expiresIn = json['expires_in']; // seconds
    final tokenType = json['token_type'] ?? 'Bearer';

    DateTime? expiresAt;
    if (expiresIn != null) {
      expiresAt = DateTime.now().add(Duration(seconds: expiresIn));
    }

    // Parse JWT payload if possible
    Map<String, dynamic>? payload;
    try {
      payload = _parseJwtPayload(accessToken);
      // If we have payload with exp, use that for expiration
      if (payload != null && payload['exp'] != null) {
        expiresAt = DateTime.fromMillisecondsSinceEpoch(
          payload['exp'] * 1000,
        );
      }
    } catch (e) {
      // Ignore parsing errors, payload will remain null
    }

    return JwtToken(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: expiresAt,
      tokenType: tokenType,
      payload: payload,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt?.toIso8601String(),
      'token_type': tokenType,
      'payload': payload,
    };
  }

  /// Create JwtToken from stored JSON
  factory JwtToken.fromStoredJson(Map<String, dynamic> json) {
    return JwtToken(
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'],
      expiresAt: json['expires_at'] != null
          ? DateTime.parse(json['expires_at'])
          : null,
      tokenType: json['token_type'] ?? 'Bearer',
      payload: json['payload']?.cast<String, dynamic>(),
    );
  }

  /// Check if the token is expired
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if the token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    if (expiresAt == null) return false;
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return expiresAt!.isBefore(fiveMinutesFromNow);
  }

  /// Check if the token is valid (not expired and has access token)
  bool get isValid {
    return accessToken.isNotEmpty && !isExpired;
  }

  /// Get user ID from token payload
  String? get userId {
    return payload?['sub'] ?? payload?['user_id'] ?? payload?['id'];
  }

  /// Get user email from token payload
  String? get userEmail {
    return payload?['email'];
  }

  /// Get user roles from token payload
  List<String> get userRoles {
    final roles = payload?['roles'] ?? payload?['role'];
    if (roles is List) {
      return roles.cast<String>();
    } else if (roles is String) {
      return [roles];
    }
    return [];
  }

  /// Get token expiration time remaining
  Duration? get timeUntilExpiry {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (expiresAt!.isBefore(now)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// Create a copy with updated values
  JwtToken copyWith({
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    String? tokenType,
    Map<String, dynamic>? payload,
  }) {
    return JwtToken(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      tokenType: tokenType ?? this.tokenType,
      payload: payload ?? this.payload,
    );
  }

  @override
  String toString() {
    return 'JwtToken(accessToken: ${accessToken.substring(0, 20)}..., '
        'refreshToken: ${refreshToken?.substring(0, 20)}..., '
        'expiresAt: $expiresAt, '
        'isValid: $isValid)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JwtToken &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.expiresAt == expiresAt &&
        other.tokenType == tokenType;
  }

  @override
  int get hashCode {
    return accessToken.hashCode ^
        refreshToken.hashCode ^
        expiresAt.hashCode ^
        tokenType.hashCode;
  }

  /// Parse JWT payload from token string
  static Map<String, dynamic>? _parseJwtPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      // Add padding if needed
      final normalizedPayload = base64Url.normalize(payload);
      final decoded = base64Url.decode(normalizedPayload);
      final jsonString = utf8.decode(decoded);
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
}
