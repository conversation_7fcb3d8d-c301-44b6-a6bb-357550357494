# Dalti Provider - Backward Compatibility Strategy

## Overview

This document outlines the backward compatibility strategy for the enhanced Dalti Provider onboarding wizard data models. The goal is to ensure that existing code continues to work while providing new enhanced functionality.

## Compatibility Matrix

| Model | Legacy Version | Enhanced Version | Compatibility Status |
|-------|---------------|------------------|---------------------|
| BusinessProfile | ✅ Already Enhanced | ✅ Complete | ✅ Fully Compatible |
| Location | Location | EnhancedLocation | ✅ Conversion Methods |
| Service | Service | Service (Enhanced) | ✅ Optional Fields |
| Queue | Queue | EnhancedQueue | ✅ Conversion Methods |
| OnboardingData | OnboardingData | EnhancedOnboardingData | ✅ Conversion Methods |

## Conversion Strategy

### 1. **BusinessProfile Model** ✅ COMPLETE
- **Status**: Already fully enhanced and backward compatible
- **Fields Added**: mobile, landline, fax, shortName, logoUrl
- **Compatibility**: All new fields are optional (nullable)
- **Migration**: No migration needed - existing data works as-is

### 2. **Location Model** ✅ COMPLETE
- **Legacy**: `Location` class
- **Enhanced**: `EnhancedLocation` class
- **Conversion Methods**:
  - `EnhancedLocation.fromLegacyLocation()` - Convert legacy to enhanced
  - `EnhancedLocation.toLegacyLocation()` - Convert enhanced to legacy
- **New Fields**: shortName, timezone, coordinates, openingHours
- **Compatibility**: All new fields are optional

### 3. **Service Model** ✅ COMPLETE
- **Legacy**: `Service` class (original)
- **Enhanced**: `Service` class (with new optional fields)
- **New Fields**: isPublic, deliveryType, servedRegions
- **Conversion Methods**:
  - `Service.fromCreateRequest()` - Create from CreateServiceRequest
  - `Service.toCreateRequest()` - Convert to CreateServiceRequest
- **Compatibility**: All new fields are optional with sensible defaults

### 4. **Queue Model** ✅ COMPLETE
- **Legacy**: `Queue` class
- **Enhanced**: `EnhancedQueue` class
- **Conversion Methods**:
  - `EnhancedQueue.fromLegacyQueue()` - Convert legacy to enhanced
  - `EnhancedQueue.toLegacyQueue()` - Convert enhanced to legacy
- **New Fields**: openingHours (optional)
- **Compatibility**: Enhanced queue can be converted back to legacy format

### 5. **OnboardingData Model** ✅ COMPLETE
- **Legacy**: `OnboardingData` class
- **Enhanced**: `EnhancedOnboardingData` class
- **Conversion Methods**:
  - `EnhancedOnboardingData.toLegacyOnboardingData()` - Convert to legacy
- **New Features**: Multiple locations, QueueWithLocationLink
- **Compatibility**: Automatic conversion to legacy format for existing APIs

## Request Models Compatibility

### Location Requests
- **Legacy**: `CreateLocationRequest`
- **Enhanced**: `CreateEnhancedLocationRequest`
- **Conversion**: `CreateEnhancedLocationRequest.toLegacyRequest()`

### Queue Requests
- **Legacy**: `CreateQueueRequest`, `UpdateQueueRequest`
- **Enhanced**: `CreateEnhancedQueueRequest`, `UpdateEnhancedQueueRequest`
- **Conversion**: `toLegacyRequest()` and `fromLegacyRequest()` methods

## Migration Strategy

### Phase 1: Gradual Adoption (Current)
1. **Enhanced models coexist** with legacy models
2. **Conversion methods** allow seamless transitions
3. **Existing APIs** continue to work with legacy models
4. **New features** use enhanced models

### Phase 2: Enhanced Integration (Future)
1. **Onboarding wizard** uses enhanced models
2. **Legacy APIs** automatically convert to enhanced models internally
3. **New APIs** expose enhanced model capabilities
4. **Existing data** is gradually migrated

### Phase 3: Full Migration (Future)
1. **Legacy models** become aliases to enhanced models
2. **All APIs** use enhanced models
3. **Data migration** is complete
4. **Legacy conversion methods** remain for external integrations

## Data Migration Examples

### Example 1: Location Migration
```dart
// Legacy location data
final legacyLocation = Location(
  id: 1,
  name: "Main Clinic",
  address: "123 Main St",
  city: "Algiers",
  // ... other fields
);

// Convert to enhanced
final enhancedLocation = EnhancedLocation.fromLegacyLocation(
  legacyLocation,
  shortName: "Main",
  timezone: "Africa/Algiers",
  openingHours: OpeningHours.standardBusiness(),
);

// Convert back to legacy
final backToLegacy = enhancedLocation.toLegacyLocation();
```

### Example 2: Service Migration
```dart
// Legacy service data
final legacyService = Service(
  id: 1,
  title: "Consultation",
  duration: 30,
  // ... other fields
);

// Enhanced service with new fields
final enhancedService = legacyService.copyWith(
  isPublic: true,
  deliveryType: "at_location",
  servedRegions: null, // Not applicable for at_location
);
```

### Example 3: Queue Migration
```dart
// Legacy queue data
final legacyQueue = Queue(
  id: 1,
  title: "General Queue",
  sProvidingPlaceId: 1,
  // ... other fields
);

// Convert to enhanced with custom hours
final enhancedQueue = EnhancedQueue.fromLegacyQueue(
  legacyQueue,
  openingHours: OpeningHours.standardBusiness(),
);

// Convert back to legacy
final backToLegacy = enhancedQueue.toLegacyQueue();
```

## Testing Strategy

### Unit Tests Required
1. **Conversion Method Tests**
   - Legacy to Enhanced conversion
   - Enhanced to Legacy conversion
   - Round-trip conversion (legacy → enhanced → legacy)

2. **Data Integrity Tests**
   - No data loss during conversion
   - Default values applied correctly
   - Optional fields handled properly

3. **API Compatibility Tests**
   - Existing API endpoints work with legacy models
   - New API endpoints work with enhanced models
   - Conversion happens transparently

### Integration Tests Required
1. **Onboarding Flow Tests**
   - Legacy onboarding data can be loaded
   - Enhanced onboarding data can be saved
   - Mixed legacy/enhanced data works correctly

2. **Database Migration Tests**
   - Existing data can be read
   - Enhanced data can be written
   - Schema changes are backward compatible

## Risk Mitigation

### Low Risk ✅
- **BusinessProfile**: Already enhanced and tested
- **Optional Fields**: All new fields are optional
- **Conversion Methods**: Comprehensive conversion coverage

### Medium Risk ⚠️
- **API Changes**: Need to ensure existing API contracts are maintained
- **Database Schema**: Changes must be additive only
- **Third-party Integrations**: External systems may need updates

### High Risk ❌
- **Breaking Changes**: None identified - all changes are additive
- **Data Loss**: Prevented by comprehensive conversion methods
- **Performance Impact**: Minimal - conversion is lightweight

## Rollback Strategy

If issues arise, the rollback strategy is:

1. **Immediate**: Use legacy models exclusively
2. **Short-term**: Disable enhanced features in onboarding wizard
3. **Long-term**: Revert to previous data model versions

The conversion methods ensure that no data is lost during rollback.

## Conclusion

The backward compatibility strategy ensures:
- ✅ **Zero Breaking Changes** - All existing code continues to work
- ✅ **Seamless Migration** - Gradual adoption of enhanced features
- ✅ **Data Integrity** - No data loss during transitions
- ✅ **Future-Proof** - Clear path for full migration

This approach allows the team to adopt enhanced features incrementally while maintaining system stability.
