// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'message_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$messageApiServiceHash() => r'ad39f91d040b47b8dcfe5b1f9ac1bb52e2619277';

/// Message API service provider
///
/// Copied from [messageApiService].
@ProviderFor(messageApiService)
final messageApiServiceProvider =
    AutoDisposeProvider<MessageApiService>.internal(
  messageApiService,
  name: r'messageApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MessageApiServiceRef = AutoDisposeProviderRef<MessageApiService>;
String _$messageRepositoryHash() => r'6d378b0277532c1ad8b40afef79fb187baad5a98';

/// Message repository provider
///
/// Copied from [messageRepository].
@ProviderFor(messageRepository)
final messageRepositoryProvider =
    AutoDisposeProvider<MessageRepository>.internal(
  messageRepository,
  name: r'messageRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MessageRepositoryRef = AutoDisposeProviderRef<MessageRepository>;
String _$customerMessageServiceHash() =>
    r'b96e0b613807ed94f81d90d8e281b78fe29deb7f';

/// Customer message service provider
///
/// Copied from [customerMessageService].
@ProviderFor(customerMessageService)
final customerMessageServiceProvider =
    AutoDisposeProvider<CustomerMessageService>.internal(
  customerMessageService,
  name: r'customerMessageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customerMessageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CustomerMessageServiceRef
    = AutoDisposeProviderRef<CustomerMessageService>;
String _$conversationHash() => r'e6666304a65216ecdccf30a38a79ae75017f20de';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider for getting a specific conversation
///
/// Copied from [conversation].
@ProviderFor(conversation)
const conversationProvider = ConversationFamily();

/// Provider for getting a specific conversation
///
/// Copied from [conversation].
class ConversationFamily extends Family<AsyncValue<Conversation?>> {
  /// Provider for getting a specific conversation
  ///
  /// Copied from [conversation].
  const ConversationFamily();

  /// Provider for getting a specific conversation
  ///
  /// Copied from [conversation].
  ConversationProvider call(
    String conversationId,
  ) {
    return ConversationProvider(
      conversationId,
    );
  }

  @override
  ConversationProvider getProviderOverride(
    covariant ConversationProvider provider,
  ) {
    return call(
      provider.conversationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'conversationProvider';
}

/// Provider for getting a specific conversation
///
/// Copied from [conversation].
class ConversationProvider extends AutoDisposeFutureProvider<Conversation?> {
  /// Provider for getting a specific conversation
  ///
  /// Copied from [conversation].
  ConversationProvider(
    String conversationId,
  ) : this._internal(
          (ref) => conversation(
            ref as ConversationRef,
            conversationId,
          ),
          from: conversationProvider,
          name: r'conversationProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$conversationHash,
          dependencies: ConversationFamily._dependencies,
          allTransitiveDependencies:
              ConversationFamily._allTransitiveDependencies,
          conversationId: conversationId,
        );

  ConversationProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.conversationId,
  }) : super.internal();

  final String conversationId;

  @override
  Override overrideWith(
    FutureOr<Conversation?> Function(ConversationRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ConversationProvider._internal(
        (ref) => create(ref as ConversationRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        conversationId: conversationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Conversation?> createElement() {
    return _ConversationProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ConversationProvider &&
        other.conversationId == conversationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, conversationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ConversationRef on AutoDisposeFutureProviderRef<Conversation?> {
  /// The parameter `conversationId` of this provider.
  String get conversationId;
}

class _ConversationProviderElement
    extends AutoDisposeFutureProviderElement<Conversation?>
    with ConversationRef {
  _ConversationProviderElement(super.provider);

  @override
  String get conversationId => (origin as ConversationProvider).conversationId;
}

String _$conversationsNotifierHash() =>
    r'fe3f6ae110f0136c1e977aac345fae7c95887408';

/// Conversations notifier for managing conversations state
///
/// Copied from [ConversationsNotifier].
@ProviderFor(ConversationsNotifier)
final conversationsNotifierProvider = AutoDisposeNotifierProvider<
    ConversationsNotifier, ConversationsState>.internal(
  ConversationsNotifier.new,
  name: r'conversationsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationsNotifier = AutoDisposeNotifier<ConversationsState>;
String _$messagesNotifierHash() => r'e7fc62aa802dca5f693aae86a846916ae188d33f';

abstract class _$MessagesNotifier
    extends BuildlessAutoDisposeNotifier<MessagesState> {
  late final String conversationId;

  MessagesState build(
    String conversationId,
  );
}

/// Messages notifier for managing messages in a conversation
///
/// Copied from [MessagesNotifier].
@ProviderFor(MessagesNotifier)
const messagesNotifierProvider = MessagesNotifierFamily();

/// Messages notifier for managing messages in a conversation
///
/// Copied from [MessagesNotifier].
class MessagesNotifierFamily extends Family<MessagesState> {
  /// Messages notifier for managing messages in a conversation
  ///
  /// Copied from [MessagesNotifier].
  const MessagesNotifierFamily();

  /// Messages notifier for managing messages in a conversation
  ///
  /// Copied from [MessagesNotifier].
  MessagesNotifierProvider call(
    String conversationId,
  ) {
    return MessagesNotifierProvider(
      conversationId,
    );
  }

  @override
  MessagesNotifierProvider getProviderOverride(
    covariant MessagesNotifierProvider provider,
  ) {
    return call(
      provider.conversationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'messagesNotifierProvider';
}

/// Messages notifier for managing messages in a conversation
///
/// Copied from [MessagesNotifier].
class MessagesNotifierProvider
    extends AutoDisposeNotifierProviderImpl<MessagesNotifier, MessagesState> {
  /// Messages notifier for managing messages in a conversation
  ///
  /// Copied from [MessagesNotifier].
  MessagesNotifierProvider(
    String conversationId,
  ) : this._internal(
          () => MessagesNotifier()..conversationId = conversationId,
          from: messagesNotifierProvider,
          name: r'messagesNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$messagesNotifierHash,
          dependencies: MessagesNotifierFamily._dependencies,
          allTransitiveDependencies:
              MessagesNotifierFamily._allTransitiveDependencies,
          conversationId: conversationId,
        );

  MessagesNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.conversationId,
  }) : super.internal();

  final String conversationId;

  @override
  MessagesState runNotifierBuild(
    covariant MessagesNotifier notifier,
  ) {
    return notifier.build(
      conversationId,
    );
  }

  @override
  Override overrideWith(MessagesNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: MessagesNotifierProvider._internal(
        () => create()..conversationId = conversationId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        conversationId: conversationId,
      ),
    );
  }

  @override
  AutoDisposeNotifierProviderElement<MessagesNotifier, MessagesState>
      createElement() {
    return _MessagesNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MessagesNotifierProvider &&
        other.conversationId == conversationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, conversationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MessagesNotifierRef on AutoDisposeNotifierProviderRef<MessagesState> {
  /// The parameter `conversationId` of this provider.
  String get conversationId;
}

class _MessagesNotifierProviderElement
    extends AutoDisposeNotifierProviderElement<MessagesNotifier, MessagesState>
    with MessagesNotifierRef {
  _MessagesNotifierProviderElement(super.provider);

  @override
  String get conversationId =>
      (origin as MessagesNotifierProvider).conversationId;
}

String _$unreadMessagesCountNotifierHash() =>
    r'8bb3a544de8ccefa5b1053d9bad4ea27de7f7f3d';

/// Unread messages count provider
///
/// Copied from [UnreadMessagesCountNotifier].
@ProviderFor(UnreadMessagesCountNotifier)
final unreadMessagesCountNotifierProvider =
    AutoDisposeNotifierProvider<UnreadMessagesCountNotifier, int>.internal(
  UnreadMessagesCountNotifier.new,
  name: r'unreadMessagesCountNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$unreadMessagesCountNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UnreadMessagesCountNotifier = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
