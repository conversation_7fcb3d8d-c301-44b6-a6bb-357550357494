import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_provider.dart';

/// Welcome step component for onboarding introduction
/// Used as the first step in the onboarding wizard
class WelcomeStepWidget extends ConsumerWidget {
  final VoidCallback? onGetStarted;
  final bool enabled;

  const WelcomeStepWidget({
    super.key,
    this.onGetStarted,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 32),
          
          // Welcome illustration/icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: theme.colors.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.business_center,
              size: 60,
              color: theme.colors.primary,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Welcome title
          Text(
            'Welcome to Dalti Provider!',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colors.primary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Welcome subtitle
          Text(
            'Let\'s set up your business profile and get you ready to serve customers.',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colors.onSurface.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 48),
          
          // Setup steps overview
          _buildSetupStepsCard(theme),
          
          const SizedBox(height: 32),
          
          // Benefits section
          _buildBenefitsSection(theme),
          
          const SizedBox(height: 48),
          
          // Get started button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: enabled ? onGetStarted : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Get Started',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward,
                    size: 20,
                    color: theme.colors.onPrimary,
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Time estimate
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: theme.colors.onSurface.withOpacity(0.6),
              ),
              const SizedBox(width: 4),
              Text(
                'Takes about 5-10 minutes to complete',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colors.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSetupStepsCard(ThemeData theme) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.checklist,
                  color: theme.colors.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Setup Steps',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Step list
            _buildStepItem(
              theme,
              1,
              'Business Information',
              'Tell us about your business and contact details',
              Icons.business,
            ),
            
            _buildStepItem(
              theme,
              2,
              'Location Setup',
              'Add your business locations and opening hours',
              Icons.location_on,
            ),
            
            _buildStepItem(
              theme,
              3,
              'Service Creation',
              'Define the services you offer to customers',
              Icons.medical_services,
            ),
            
            _buildStepItem(
              theme,
              4,
              'Queue Management',
              'Set up queues to organize your services',
              Icons.queue,
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepItem(
    ThemeData theme,
    int stepNumber,
    String title,
    String description,
    IconData icon, {
    bool isLast = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Step number and line
        Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: theme.colors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: theme.colors.primary.withOpacity(0.3),
                ),
              ),
              child: Center(
                child: Text(
                  stepNumber.toString(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colors.primary,
                  ),
                ),
              ),
            ),
            if (!isLast) ...[
              Container(
                width: 2,
                height: 40,
                color: theme.colors.primary.withOpacity(0.2),
              ),
            ],
          ],
        ),
        
        const SizedBox(width: 16),
        
        // Step content
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: theme.colors.primary,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colors.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBenefitsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Why use Dalti Provider?',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Benefits list
        _buildBenefitItem(
          theme,
          Icons.people,
          'Manage Customers',
          'Organize and track your customer appointments efficiently',
        ),
        
        _buildBenefitItem(
          theme,
          Icons.schedule,
          'Smart Scheduling',
          'Automated queue management and appointment scheduling',
        ),
        
        _buildBenefitItem(
          theme,
          Icons.analytics,
          'Business Insights',
          'Track performance and grow your business with analytics',
        ),
        
        _buildBenefitItem(
          theme,
          Icons.mobile_friendly,
          'Mobile Ready',
          'Access your business dashboard anywhere, anytime',
        ),
      ],
    );
  }

  Widget _buildBenefitItem(
    ThemeData theme,
    IconData icon,
    String title,
    String description,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: theme.colors.primary,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
