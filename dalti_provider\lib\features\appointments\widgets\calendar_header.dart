import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Calendar header widget with navigation and view controls
class CalendarHeader extends StatelessWidget {
  final DateTime currentDate;
  final String viewType; // 'day', 'week', 'month'
  final VoidCallback onPreviousPressed;
  final VoidCallback onNextPressed;
  final VoidCallback onTodayPressed;
  final Function(String) onViewChanged;
  final VoidCallback? onAddAppointment;
  final VoidCallback? onConfigPressed;

  const CalendarHeader({
    super.key,
    required this.currentDate,
    required this.viewType,
    required this.onPreviousPressed,
    required this.onNextPressed,
    required this.onTodayPressed,
    required this.onViewChanged,
    this.onAddAppointment,
    this.onConfigPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Title and navigation row
          Row(
            children: [
              // Title
              Expanded(
                child: Text(
                  _getTitle(),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Navigation buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Today button
                  TextButton(
                    onPressed: onTodayPressed,
                    child: Text(
                      'Today',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  // Config button
                  if (onConfigPressed != null)
                    IconButton(
                      onPressed: onConfigPressed,
                      icon: const Icon(Icons.settings),
                      tooltip: 'Calendar Settings',
                      style: IconButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                      ),
                    ),

                  const SizedBox(width: 8),

                  // Previous button
                  IconButton(
                    onPressed: onPreviousPressed,
                    icon: const Icon(Icons.chevron_left),
                    tooltip: 'Previous ${viewType.toLowerCase()}',
                  ),

                  // Next button
                  IconButton(
                    onPressed: onNextPressed,
                    icon: const Icon(Icons.chevron_right),
                    tooltip: 'Next ${viewType.toLowerCase()}',
                  ),

                  const SizedBox(width: 8),

                  // Add appointment button
                  if (onAddAppointment != null)
                    IconButton(
                      onPressed: onAddAppointment,
                      icon: const Icon(Icons.add),
                      tooltip: 'Add appointment',
                      style: IconButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // View type selector
          Row(
            children: [
              Expanded(
                child: SegmentedButton<String>(
                  segments: const [
                    ButtonSegment(
                      value: 'day',
                      label: Text('Day'),
                      icon: Icon(Icons.view_day),
                    ),
                    ButtonSegment(
                      value: 'week',
                      label: Text('Week'),
                      icon: Icon(Icons.view_week),
                    ),
                    ButtonSegment(
                      value: 'month',
                      label: Text('Month'),
                      icon: Icon(Icons.view_module),
                    ),
                  ],
                  selected: {viewType},
                  onSelectionChanged: (Set<String> selection) {
                    if (selection.isNotEmpty) {
                      onViewChanged(selection.first);
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTitle() {
    switch (viewType) {
      case 'day':
        return DateFormat('EEEE, MMMM d, yyyy').format(currentDate);
      case 'week':
        final startOfWeek = currentDate.subtract(
          Duration(days: currentDate.weekday - 1),
        );
        final endOfWeek = startOfWeek.add(const Duration(days: 6));

        if (startOfWeek.month == endOfWeek.month) {
          return '${DateFormat('MMMM d').format(startOfWeek)} - ${DateFormat('d, yyyy').format(endOfWeek)}';
        } else {
          return '${DateFormat('MMM d').format(startOfWeek)} - ${DateFormat('MMM d, yyyy').format(endOfWeek)}';
        }
      case 'month':
        return DateFormat('MMMM yyyy').format(currentDate);
      default:
        return DateFormat('MMMM yyyy').format(currentDate);
    }
  }
}

/// Time indicator widget for current time in calendar views
class TimeIndicator extends StatelessWidget {
  final DateTime currentTime;
  final bool showLine;

  const TimeIndicator({
    super.key,
    required this.currentTime,
    this.showLine = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Time text
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            DateFormat('HH:mm').format(currentTime),
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),

        // Line indicator
        if (showLine) ...[
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              height: 2,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Calendar legend widget showing appointment status colors
class CalendarLegend extends StatelessWidget {
  const CalendarLegend({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 16,
        runSpacing: 8,
        children: [
          _buildLegendItem(
            context,
            color: Theme.of(context).colorScheme.primary,
            label: 'Scheduled',
          ),
          _buildLegendItem(
            context,
            color: Theme.of(context).colorScheme.secondary,
            label: 'Confirmed',
          ),
          _buildLegendItem(
            context,
            color: Theme.of(context).colorScheme.tertiary,
            label: 'In Progress',
          ),
          _buildLegendItem(
            context,
            color: Theme.of(context).colorScheme.outline,
            label: 'Completed',
          ),
          _buildLegendItem(
            context,
            color: Theme.of(context).colorScheme.error,
            label: 'Cancelled',
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(
    BuildContext context, {
    required Color color,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 6),
        Text(label, style: Theme.of(context).textTheme.labelSmall),
      ],
    );
  }
}
