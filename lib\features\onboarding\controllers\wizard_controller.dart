import 'package:flutter/material.dart';
import '../models/onboarding_models.dart';

/// Controller for managing wizard navigation and state
class Wizard<PERSON>ontroll<PERSON> extends ChangeNotifier {
  final List<OnboardingStep> _steps;
  final PageController _pageController;

  OnboardingStep _currentStep;
  bool _canGoBack;
  bool _canProceed;
  bool _isLoading;
  String? _error;

  WizardController({
    required List<OnboardingStep> steps,
    OnboardingStep? initialStep,
  }) : _steps = steps,
       _pageController = PageController(),
       _currentStep = initialStep ?? steps.first,
       _canGoBack = false,
       _canProceed = false,
       _isLoading = false;

  // Getters
  List<OnboardingStep> get steps => _steps;
  OnboardingStep get currentStep => _currentStep;
  bool get canGoBack => _canGoBack;
  bool get canProceed => _canProceed;
  bool get isLoading => _isLoading;
  String? get error => _error;
  PageController get pageController => _pageController;

  int get currentIndex => _steps.indexOf(_currentStep);
  int get totalSteps => _steps.length;
  double get progress => (currentIndex + 1) / totalSteps;
  bool get isFirstStep => currentIndex == 0;
  bool get isLastStep => currentIndex == totalSteps - 1;

  /// Set loading state
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Set error message
  void setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// Clear error
  void clearError() {
    setError(null);
  }

  /// Update navigation state
  void updateNavigationState({bool? canGoBack, bool? canProceed}) {
    bool changed = false;

    if (canGoBack != null && _canGoBack != canGoBack) {
      _canGoBack = canGoBack;
      changed = true;
    }

    if (canProceed != null && _canProceed != canProceed) {
      _canProceed = canProceed;
      changed = true;
    }

    if (changed) {
      notifyListeners();
    }
  }

  /// Go to specific step
  Future<void> goToStep(OnboardingStep step, {bool animate = true}) async {
    final stepIndex = _steps.indexOf(step);
    if (stepIndex == -1) {
      throw ArgumentError('Step $step not found in wizard steps');
    }

    await goToIndex(stepIndex, animate: animate);
  }

  /// Go to specific index
  Future<void> goToIndex(int index, {bool animate = true}) async {
    if (index < 0 || index >= _steps.length) {
      throw ArgumentError('Index $index is out of range');
    }

    if (index == currentIndex) return;

    _currentStep = _steps[index];

    // Update navigation state based on current position
    _canGoBack = index > 0;

    if (animate) {
      await _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _pageController.jumpToPage(index);
    }

    notifyListeners();
  }

  /// Go to next step
  Future<void> nextStep({bool animate = true}) async {
    if (!isLastStep) {
      await goToIndex(currentIndex + 1, animate: animate);
    }
  }

  /// Go to previous step
  Future<void> previousStep({bool animate = true}) async {
    if (!isFirstStep) {
      await goToIndex(currentIndex - 1, animate: animate);
    }
  }

  /// Jump to first step
  Future<void> goToFirst({bool animate = true}) async {
    await goToIndex(0, animate: animate);
  }

  /// Jump to last step
  Future<void> goToLast({bool animate = true}) async {
    await goToIndex(totalSteps - 1, animate: animate);
  }

  /// Reset wizard to initial state
  void reset({OnboardingStep? initialStep}) {
    _currentStep = initialStep ?? _steps.first;
    _canGoBack = false;
    _canProceed = false;
    _isLoading = false;
    _error = null;

    _pageController.jumpToPage(0);
    notifyListeners();
  }

  /// Handle page changed (called by PageView)
  void onPageChanged(int index) {
    if (index >= 0 && index < _steps.length) {
      _currentStep = _steps[index];
      _canGoBack = index > 0;
      notifyListeners();
    }
  }

  /// Validate current step
  Future<bool> validateCurrentStep(Map<String, dynamic> data) async {
    // This should be overridden by the implementing class
    // or connected to a validation service
    return true;
  }

  /// Get step by index
  OnboardingStep? getStepAt(int index) {
    if (index >= 0 && index < _steps.length) {
      return _steps[index];
    }
    return null;
  }

  /// Get index of step
  int getIndexOf(OnboardingStep step) {
    return _steps.indexOf(step);
  }

  /// Check if step is completed
  bool isStepCompleted(OnboardingStep step) {
    // This should be overridden to check actual completion state
    final stepIndex = _steps.indexOf(step);
    return stepIndex >= 0 && stepIndex < currentIndex;
  }

  /// Check if step is accessible
  bool isStepAccessible(OnboardingStep step) {
    // By default, only allow access to current and previous steps
    final stepIndex = _steps.indexOf(step);
    return stepIndex >= 0 && stepIndex <= currentIndex;
  }

  /// Get next step
  OnboardingStep? get nextStep {
    if (!isLastStep) {
      return _steps[currentIndex + 1];
    }
    return null;
  }

  /// Get previous step
  OnboardingStep? get previousStep {
    if (!isFirstStep) {
      return _steps[currentIndex - 1];
    }
    return null;
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

/// Enhanced wizard controller with validation and state management
class OnboardingWizardController extends WizardController {
  final Map<OnboardingStep, bool> _stepCompletionState = {};
  final Map<OnboardingStep, Map<String, dynamic>> _stepData = {};

  OnboardingWizardController({required super.steps, super.initialStep});

  /// Get data for a specific step
  Map<String, dynamic>? getStepData(OnboardingStep step) {
    return _stepData[step];
  }

  /// Set data for a specific step
  void setStepData(OnboardingStep step, Map<String, dynamic> data) {
    _stepData[step] = data;
    notifyListeners();
  }

  /// Mark step as completed
  void markStepCompleted(OnboardingStep step, bool completed) {
    _stepCompletionState[step] = completed;

    // Update navigation state
    updateNavigationState(canProceed: completed);
  }

  @override
  bool isStepCompleted(OnboardingStep step) {
    return _stepCompletionState[step] ?? false;
  }

  @override
  bool isStepAccessible(OnboardingStep step) {
    // Allow access to current step and all completed previous steps
    if (step == currentStep) return true;

    if (step.index < currentIndex) {
      // Check if all steps before this one are completed
      for (int i = 0; i < step.index; i++) {
        if (!isStepCompleted(steps[i])) {
          return false;
        }
      }
      return true;
    }

    return false;
  }

  /// Get completion percentage
  double get completionPercentage {
    final completedSteps =
        _stepCompletionState.values.where((completed) => completed).length;
    return completedSteps / totalSteps;
  }

  /// Check if all steps are completed
  bool get isAllStepsCompleted {
    return _stepCompletionState.length == totalSteps &&
        _stepCompletionState.values.every((completed) => completed);
  }

  /// Get summary of wizard state
  Map<String, dynamic> get summary {
    return {
      'currentStep': currentStep.name,
      'currentIndex': currentIndex,
      'totalSteps': totalSteps,
      'progress': progress,
      'completionPercentage': completionPercentage,
      'isCompleted': isAllStepsCompleted,
      'canGoBack': canGoBack,
      'canProceed': canProceed,
      'isLoading': isLoading,
      'error': error,
      'completedSteps':
          _stepCompletionState.entries
              .where((entry) => entry.value)
              .map((entry) => entry.key.name)
              .toList(),
    };
  }

  @override
  void reset({OnboardingStep? initialStep}) {
    super.reset(initialStep: initialStep);
    _stepCompletionState.clear();
    _stepData.clear();
  }
}
