import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Calendar configuration state
class CalendarConfig {
  final int timeSlotInterval; // in minutes

  const CalendarConfig({
    this.timeSlotInterval = 15, // Default to 15 minutes
  });

  CalendarConfig copyWith({
    int? timeSlotInterval,
  }) {
    return CalendarConfig(
      timeSlotInterval: timeSlotInterval ?? this.timeSlotInterval,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalendarConfig &&
        other.timeSlotInterval == timeSlotInterval;
  }

  @override
  int get hashCode => timeSlotInterval.hashCode;
}

/// Calendar configuration notifier
class CalendarConfigNotifier extends StateNotifier<CalendarConfig> {
  CalendarConfigNotifier() : super(const CalendarConfig()) {
    _loadConfig();
  }

  static const String _timeSlotIntervalKey = 'calendar_time_slot_interval';

  /// Load configuration from shared preferences
  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timeSlotInterval = prefs.getInt(_timeSlotIntervalKey) ?? 15;
      
      state = CalendarConfig(timeSlotInterval: timeSlotInterval);
    } catch (e) {
      // If loading fails, keep default values
      print('Error loading calendar config: $e');
    }
  }

  /// Update time slot interval
  Future<void> setTimeSlotInterval(int interval) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_timeSlotIntervalKey, interval);
      
      state = state.copyWith(timeSlotInterval: interval);
    } catch (e) {
      print('Error saving time slot interval: $e');
    }
  }

  /// Reset to default configuration
  Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_timeSlotIntervalKey);
      
      state = const CalendarConfig();
    } catch (e) {
      print('Error resetting calendar config: $e');
    }
  }
}

/// Calendar configuration provider
final calendarConfigProvider = StateNotifierProvider<CalendarConfigNotifier, CalendarConfig>(
  (ref) => CalendarConfigNotifier(),
);
