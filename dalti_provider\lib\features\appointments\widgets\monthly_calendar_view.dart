import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import 'calendar_header.dart';

/// Monthly calendar view showing appointments for a month
class MonthlyCalendarView extends ConsumerStatefulWidget {
  final DateTime selectedDate;
  final Function(DateTime) onDateChanged;
  final Function(DateTime) onDayTap;
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;

  const MonthlyCalendarView({
    super.key,
    required this.selectedDate,
    required this.onDateChanged,
    required this.onDayTap,
    this.onAppointmentTap,
    this.onAddAppointment,
  });

  @override
  ConsumerState<MonthlyCalendarView> createState() =>
      _MonthlyCalendarViewState();
}

class _MonthlyCalendarViewState extends ConsumerState<MonthlyCalendarView> {
  late DateTime _firstDayOfMonth;
  late DateTime _lastDayOfMonth;
  late DateTime _firstDayOfCalendar;
  late DateTime _lastDayOfCalendar;

  @override
  void initState() {
    super.initState();
    _calculateMonthRange();
  }

  @override
  void didUpdateWidget(MonthlyCalendarView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDate.month != widget.selectedDate.month ||
        oldWidget.selectedDate.year != widget.selectedDate.year) {
      _calculateMonthRange();
    }
  }

  void _calculateMonthRange() {
    _firstDayOfMonth = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month,
      1,
    );
    _lastDayOfMonth = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month + 1,
      0,
    );

    // Calculate first day of calendar (start of week containing first day of month)
    final firstWeekday = _firstDayOfMonth.weekday;
    _firstDayOfCalendar = _firstDayOfMonth.subtract(
      Duration(days: firstWeekday - 1),
    );

    // Calculate last day of calendar (end of week containing last day of month)
    final lastWeekday = _lastDayOfMonth.weekday;
    _lastDayOfCalendar = _lastDayOfMonth.add(Duration(days: 7 - lastWeekday));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Calendar header
        CalendarHeader(
          currentDate: widget.selectedDate,
          viewType: 'month',
          onPreviousPressed: () => _changeMonth(-1),
          onNextPressed: () => _changeMonth(1),
          onTodayPressed: () => widget.onDateChanged(DateTime.now()),
          onViewChanged: (viewType) {
            // Handle view change in parent widget
          },
          onAddAppointment: widget.onAddAppointment,
        ),

        // Month view content
        Expanded(child: _buildMonthView()),
      ],
    );
  }

  Widget _buildMonthView() {
    final appointmentsAsync = ref.watch(appointmentNotifierProvider);

    return appointmentsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load appointments',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed:
                      () =>
                          ref
                              .read(appointmentNotifierProvider.notifier)
                              .refreshAppointments(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
      data: (appointments) {
        // Filter appointments for the month view range
        final monthAppointments =
            appointments
                .where((apt) => _isDateInRange(apt.scheduledTime))
                .toList();

        return _buildMonthGrid(monthAppointments);
      },
    );
  }

  Widget _buildMonthGrid(List<Appointment> appointments) {
    // Group appointments by date
    final Map<DateTime, List<Appointment>> appointmentsByDate = {};
    for (final appointment in appointments) {
      final date = DateTime(
        appointment.scheduledTime.year,
        appointment.scheduledTime.month,
        appointment.scheduledTime.day,
      );
      appointmentsByDate.putIfAbsent(date, () => []).add(appointment);
    }

    return Column(
      children: [
        // Days of week header
        _buildDaysOfWeekHeader(),

        // Calendar grid
        Expanded(child: _buildCalendarGrid(appointmentsByDate)),
      ],
    );
  }

  Widget _buildDaysOfWeekHeader() {
    final daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
      ),
      child: Row(
        children:
            daysOfWeek
                .map(
                  (day) => Expanded(
                    child: Center(
                      child: Text(
                        day,
                        style: Theme.of(
                          context,
                        ).textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ),
                )
                .toList(),
      ),
    );
  }

  Widget _buildCalendarGrid(
    Map<DateTime, List<Appointment>> appointmentsByDate,
  ) {
    final totalDays =
        _lastDayOfCalendar.difference(_firstDayOfCalendar).inDays + 1;
    final weeks = (totalDays / 7).ceil();

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
      ),
      itemCount: totalDays,
      itemBuilder: (context, index) {
        final date = _firstDayOfCalendar.add(Duration(days: index));
        final dayAppointments = appointmentsByDate[date] ?? [];

        return _buildDayCell(date, dayAppointments);
      },
    );
  }

  Widget _buildDayCell(DateTime date, List<Appointment> dayAppointments) {
    final isCurrentMonth = date.month == widget.selectedDate.month;
    final isToday = _isSameDay(date, DateTime.now());
    final isSelected = _isSameDay(date, widget.selectedDate);

    return GestureDetector(
      onTap: () => widget.onDayTap(date),
      child: Container(
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : isToday
                  ? Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withOpacity(0.3)
                  : null,
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Day number
              Text(
                date.day.toString(),
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color:
                      isCurrentMonth
                          ? (isToday || isSelected)
                              ? Theme.of(context).colorScheme.onPrimaryContainer
                              : Theme.of(context).colorScheme.onSurface
                          : Theme.of(
                            context,
                          ).colorScheme.onSurfaceVariant.withOpacity(0.5),
                  fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                ),
              ),

              // Appointment indicators
              if (dayAppointments.isNotEmpty) ...[
                const SizedBox(height: 2),
                Expanded(child: _buildAppointmentIndicators(dayAppointments)),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentIndicators(List<Appointment> appointments) {
    // Sort appointments by time
    appointments.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));

    // Show up to 3 appointments, then show count
    final visibleAppointments = appointments.take(3).toList();
    final remainingCount = appointments.length - visibleAppointments.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Individual appointment indicators
        ...visibleAppointments.map(
          (appointment) => Padding(
            padding: const EdgeInsets.only(bottom: 1),
            child: GestureDetector(
              onTap: () => widget.onAppointmentTap?.call(appointment),
              child: Container(
                height: 12,
                decoration: BoxDecoration(
                  color: _getAppointmentColor(appointment),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Text(
                    DateFormat('HH:mm').format(appointment.scheduledTime),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      fontSize: 8,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ),
        ),

        // Remaining count indicator
        if (remainingCount > 0)
          Container(
            height: 12,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
            child: Center(
              child: Text(
                '+$remainingCount',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontSize: 8,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Color _getAppointmentColor(Appointment appointment) {
    switch (appointment.status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.scheduled:
        return Theme.of(context).colorScheme.primary;
      case AppointmentStatus.confirmed:
        return Theme.of(context).colorScheme.secondary;
      case AppointmentStatus.inProgress:
        return Theme.of(context).colorScheme.tertiary;
      case AppointmentStatus.completed:
        return Theme.of(context).colorScheme.outline;
      case AppointmentStatus.canceled:
        return Theme.of(context).colorScheme.error;
      case AppointmentStatus.noShow:
        return Theme.of(context).colorScheme.error.withValues(alpha: 0.7);
      case AppointmentStatus.rescheduled:
        return Theme.of(context).colorScheme.primary.withValues(alpha: 0.7);
    }
  }

  void _changeMonth(int months) {
    final newDate = DateTime(
      widget.selectedDate.year,
      widget.selectedDate.month + months,
      1,
    );
    widget.onDateChanged(newDate);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isDateInRange(DateTime date) {
    return date.isAfter(
          _firstDayOfCalendar.subtract(const Duration(days: 1)),
        ) &&
        date.isBefore(_lastDayOfCalendar.add(const Duration(days: 1)));
  }
}
