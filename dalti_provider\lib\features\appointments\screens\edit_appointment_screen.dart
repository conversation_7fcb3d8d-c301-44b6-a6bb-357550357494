import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../models/appointment_models.dart' as appointment_models;
import '../providers/appointment_provider.dart';
import '../../services/models/service_models.dart';
import '../../services/providers/service_provider.dart';
import '../../locations/models/location_models.dart';
import '../../locations/providers/location_provider.dart';
import '../../queues/models/queue_models.dart';
import '../../queues/providers/queue_provider.dart';
import '../../dashboard/providers/dashboard_provider.dart';

class EditAppointmentScreen extends ConsumerStatefulWidget {
  final appointment_models.Appointment appointment;

  const EditAppointmentScreen({super.key, required this.appointment});

  @override
  ConsumerState<EditAppointmentScreen> createState() =>
      _EditAppointmentScreenState();
}

class _EditAppointmentScreenState extends ConsumerState<EditAppointmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  Service? _selectedService;
  Location? _selectedLocation;
  Queue? _selectedQueue;
  DateTime? _startTime;
  DateTime? _endTime;

  bool _isLoading = false;
  bool _dataLoaded = false;

  @override
  void initState() {
    super.initState();
    // Initialize with appointment data
    _initializeFields();

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _initializeFields() {
    _startTime = widget.appointment.scheduledTime;
    // Calculate end time from scheduled time and duration
    _endTime = widget.appointment.scheduledTime.add(
      Duration(minutes: widget.appointment.duration),
    );
    _notesController.text = widget.appointment.notes ?? '';
  }

  void _loadData() {
    // Load services, locations, and queues
    ref.read(serviceNotifierProvider.notifier).loadServices();
    ref.read(locationNotifierProvider.notifier).loadLocations();
    ref.read(queueNotifierProvider.notifier).loadQueues();
  }

  void _populateFromLoadedData() {
    if (_dataLoaded) return;

    final serviceState = ref.read(serviceNotifierProvider);
    final locationState = ref.read(locationNotifierProvider);
    final queueState = ref.read(queueNotifierProvider);

    // Find and set the service (Service ID is int, stored as String in appointment)
    if (serviceState.services.isNotEmpty &&
        widget.appointment.serviceId.isNotEmpty) {
      try {
        final serviceId = int.parse(widget.appointment.serviceId);
        _selectedService =
            serviceState.services.where((s) => s.id == serviceId).firstOrNull;
      } catch (e) {
        // Handle parsing error silently
      }
    }

    // Find and set the location (Location ID is int, stored as String in appointment)
    if (locationState.locations.isNotEmpty &&
        widget.appointment.locationId != null) {
      try {
        final locationId = int.parse(widget.appointment.locationId!);
        _selectedLocation =
            locationState.locations
                .where((l) => l.id == locationId)
                .firstOrNull;
      } catch (e) {
        // Handle parsing error silently
      }
    }

    // Find and set the queue (Queue ID is int, stored as String in appointment)
    if (queueState.queues.isNotEmpty && widget.appointment.queueId != null) {
      try {
        final queueId = int.parse(widget.appointment.queueId!);
        _selectedQueue =
            queueState.queues.where((q) => q.id == queueId).firstOrNull;
      } catch (e) {
        // Handle parsing error silently
      }
    }

    _dataLoaded = true;
    setState(() {});
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _onServiceChanged(Service? service) {
    setState(() {
      _selectedService = service;
      // Auto-calculate end time based on service duration
      if (service != null && _startTime != null) {
        _endTime = _startTime!.add(Duration(minutes: service.duration));
      }
    });
  }

  void _onStartTimeChanged(DateTime? startTime) {
    setState(() {
      _startTime = startTime;
      // Auto-calculate end time based on service duration
      if (startTime != null && _selectedService != null) {
        _endTime = startTime.add(Duration(minutes: _selectedService!.duration));
      }
    });
  }

  void _onLocationChanged(Location? location) {
    setState(() {
      _selectedLocation = location;
      // Reset queue when location changes (compare int ID with String ID from appointment)
      if (location?.id.toString() != widget.appointment.locationId) {
        _selectedQueue = null; // Reset queue when location changes
      }
    });
  }

  List<Queue> _getFilteredQueues() {
    final queueState = ref.watch(queueNotifierProvider);
    if (_selectedLocation == null || queueState.queues.isEmpty) {
      return [];
    }
    return queueState.queues
        .where((queue) => queue.locationId == _selectedLocation!.id)
        .toList();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedService == null ||
        _selectedLocation == null ||
        _selectedQueue == null ||
        _startTime == null ||
        _endTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Additional validation for DateTime objects
    if (_startTime!.isAfter(_endTime!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Start time must be before end time'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Ensure DateTime objects have proper precision (include seconds)
      final startTime = DateTime(
        _startTime!.year,
        _startTime!.month,
        _startTime!.day,
        _startTime!.hour,
        _startTime!.minute,
        0, // seconds
        0, // milliseconds
      );

      final endTime = DateTime(
        _endTime!.year,
        _endTime!.month,
        _endTime!.day,
        _endTime!.hour,
        _endTime!.minute,
        0, // seconds
        0, // milliseconds
      );

      // Debug logging
      print(
        '[EditAppointmentScreen] Start time: $startTime (${startTime.toIso8601String()})',
      );
      print(
        '[EditAppointmentScreen] End time: $endTime (${endTime.toIso8601String()})',
      );

      final request = appointment_models.UpdateAppointmentRequest.fromDateTimes(
        serviceId: _selectedService!.id,
        queueId: _selectedQueue!.id,
        expectedStartTime: startTime,
        expectedEndTime: endTime,
        notes:
            _notesController.text.trim().isNotEmpty
                ? _notesController.text.trim()
                : null,
      );

      print(
        '[EditAppointmentScreen] Updating appointment request: ${request.toJson()}',
      );

      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointment(widget.appointment.id, request);

      // Refresh all appointment-related data to ensure we have the latest information
      await Future.wait([
        // Refresh main appointments list
        ref.read(appointmentNotifierProvider.notifier).refreshAppointments(),
        // Refresh today's appointments
        ref
            .read(todaysAppointmentsNotifierProvider.notifier)
            .refreshTodaysAppointments(),
        // Refresh upcoming appointments
        ref
            .read(upcomingAppointmentsNotifierProvider.notifier)
            .refreshUpcomingAppointments(),
        // Refresh dashboard data (includes appointment counts and stats)
        ref.read(dashboardNotifierProvider.notifier).refreshDashboardData(),
      ]);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      print('[EditAppointmentScreen] Error updating appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update appointment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _cancelAppointment() async {
    final TextEditingController reasonController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Appointment'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Are you sure you want to cancel this appointment?'),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: const InputDecoration(
                    labelText: 'Cancellation reason (optional)',
                    border: OutlineInputBorder(),
                    hintText: 'Enter reason for cancellation...',
                  ),
                  maxLines: 3,
                  textCapitalization: TextCapitalization.sentences,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Keep Appointment'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.orange),
                child: const Text('Cancel Appointment'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        final reason = reasonController.text.trim();
        await ref
            .read(appointmentNotifierProvider.notifier)
            .updateAppointmentStatus(
              widget.appointment.id,
              appointment_models.AppointmentStatus.canceled,
              notes: reason.isNotEmpty ? reason : null,
            );

        // Refresh all appointment-related data to ensure we have the latest information
        await Future.wait([
          // Refresh main appointments list
          ref.read(appointmentNotifierProvider.notifier).refreshAppointments(),
          // Refresh today's appointments
          ref
              .read(todaysAppointmentsNotifierProvider.notifier)
              .refreshTodaysAppointments(),
          // Refresh upcoming appointments
          ref
              .read(upcomingAppointmentsNotifierProvider.notifier)
              .refreshUpcomingAppointments(),
          // Refresh dashboard data (includes appointment counts and stats)
          ref.read(dashboardNotifierProvider.notifier).refreshDashboardData(),
        ]);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Appointment cancelled successfully'),
              backgroundColor: Colors.orange,
            ),
          );
          context.pop();
        }
      } catch (e) {
        print('[EditAppointmentScreen] Error cancelling appointment: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to cancel appointment: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }

    reasonController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the providers to populate fields when data loads
    final serviceState = ref.watch(serviceNotifierProvider);
    final locationState = ref.watch(locationNotifierProvider);
    final queueState = ref.watch(queueNotifierProvider);

    // Populate fields when all data is loaded
    if (!serviceState.isLoading &&
        !locationState.isLoading &&
        !queueState.isLoading &&
        !_dataLoaded &&
        serviceState.services.isNotEmpty &&
        locationState.locations.isNotEmpty &&
        queueState.queues.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFromLoadedData();
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Appointment'),
        actions: [
          // Cancel button - only show if appointment is not already cancelled
          if (widget.appointment.status !=
              appointment_models.AppointmentStatus.canceled)
            TextButton(
              onPressed: _isLoading ? null : _cancelAppointment,
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Cancel'),
            ),
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Appointment info card
            _buildAppointmentInfoCard(),
            const SizedBox(height: 16),

            // Service Selection
            _buildServiceDropdown(),
            const SizedBox(height: 16),

            // Location Selection
            _buildLocationDropdown(),
            const SizedBox(height: 16),

            // Queue Selection
            _buildQueueDropdown(),
            const SizedBox(height: 16),

            // Time Selection
            _buildTimeSelection(),
            const SizedBox(height: 16),

            // Notes
            _buildNotesField(),
            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Appointment #${widget.appointment.id}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildStatusChip(widget.appointment.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Customer: ${widget.appointment.customerName}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Service: ${widget.appointment.serviceName}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Original Time: ${DateFormat('MMM dd, yyyy HH:mm').format(widget.appointment.scheduledTime)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(appointment_models.AppointmentStatus status) {
    Color color;
    switch (status) {
      case appointment_models.AppointmentStatus.scheduled:
        color = Colors.blue;
        break;
      case appointment_models.AppointmentStatus.confirmed:
        color = Colors.green;
        break;
      case appointment_models.AppointmentStatus.completed:
        color = Colors.purple;
        break;
      case appointment_models.AppointmentStatus.canceled:
        color = Colors.red;
        break;
      case appointment_models.AppointmentStatus.noShow:
        color = Colors.orange;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status.name,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      padding: EdgeInsets.zero,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildServiceDropdown() {
    final serviceState = ref.watch(serviceNotifierProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.miscellaneous_services,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Service',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            serviceState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : serviceState.hasError
                ? Text(
                  'Error loading services: ${serviceState.error}',
                  style: TextStyle(color: Colors.red),
                )
                : DropdownButtonFormField<Service>(
                  value: _selectedService,
                  decoration: const InputDecoration(
                    labelText: 'Select Service',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a service';
                    }
                    return null;
                  },
                  onChanged: _onServiceChanged,
                  isExpanded:
                      true, // This prevents overflow in the selected value
                  selectedItemBuilder: (BuildContext context) {
                    return serviceState.services.map<Widget>((Service service) {
                      return Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          service.title,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      );
                    }).toList();
                  },
                  items:
                      serviceState.services.map((service) {
                        return DropdownMenuItem<Service>(
                          value: service,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  service.title,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  '${service.formattedDuration} - ${service.formattedPrice}',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(color: Colors.grey[600]),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDropdown() {
    final locationState = ref.watch(locationNotifierProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Location',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            locationState.isLoading
                ? const Center(child: CircularProgressIndicator())
                : locationState.hasError
                ? Text(
                  'Error loading locations: ${locationState.error}',
                  style: TextStyle(color: Colors.red),
                )
                : DropdownButtonFormField<Location>(
                  value: _selectedLocation,
                  decoration: const InputDecoration(
                    labelText: 'Select Location',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a location';
                    }
                    return null;
                  },
                  onChanged: _onLocationChanged,
                  items:
                      locationState.locations.map((location) {
                        return DropdownMenuItem<Location>(
                          value: location,
                          child: Text(location.name),
                        );
                      }).toList(),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildQueueDropdown() {
    final filteredQueues = _getFilteredQueues();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.queue,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Queue',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            filteredQueues.isEmpty
                ? Text(
                  _selectedLocation == null
                      ? 'Please select a location first'
                      : 'No queues available for selected location',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                )
                : DropdownButtonFormField<Queue>(
                  value: _selectedQueue,
                  decoration: const InputDecoration(
                    labelText: 'Select Queue',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null) {
                      return 'Please select a queue';
                    }
                    return null;
                  },
                  onChanged: (queue) {
                    setState(() {
                      _selectedQueue = queue;
                    });
                  },
                  items:
                      filteredQueues.map((queue) {
                        return DropdownMenuItem<Queue>(
                          value: queue,
                          child: Text(queue.title),
                        );
                      }).toList(),
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Time',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Start Time',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () => _selectDateTime(isStartTime: true),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _startTime != null
                                ? DateFormat(
                                  'MMM dd, yyyy HH:mm',
                                ).format(_startTime!)
                                : 'Select start time',
                            style:
                                _startTime != null
                                    ? null
                                    : TextStyle(
                                      color: Theme.of(context).hintColor,
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'End Time',
                        style: Theme.of(context).textTheme.labelMedium,
                      ),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () => _selectDateTime(isStartTime: false),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _endTime != null
                                ? DateFormat(
                                  'MMM dd, yyyy HH:mm',
                                ).format(_endTime!)
                                : 'Select end time',
                            style:
                                _endTime != null
                                    ? null
                                    : TextStyle(
                                      color: Theme.of(context).hintColor,
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_selectedService != null &&
                _startTime != null &&
                _endTime != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Duration: ${_endTime!.difference(_startTime!).inMinutes} minutes',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesField() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.notes,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Appointment notes (optional)',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.pop(),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitForm,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text('Save'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDateTime({required bool isStartTime}) async {
    final now = DateTime.now();
    final initialDate =
        isStartTime
            ? (_startTime ?? now)
            : (_endTime ??
                _startTime?.add(const Duration(hours: 1)) ??
                now.add(const Duration(hours: 1)));

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: now.subtract(const Duration(days: 30)),
      lastDate: now.add(const Duration(days: 365)),
    );

    if (selectedDate == null || !mounted) return;

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDate),
    );

    if (selectedTime == null || !mounted) return;

    final selectedDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      selectedTime.hour,
      selectedTime.minute,
    );

    if (isStartTime) {
      _onStartTimeChanged(selectedDateTime);
    } else {
      setState(() {
        _endTime = selectedDateTime;
      });
    }
  }
}
