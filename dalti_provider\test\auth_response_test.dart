import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/models/auth_models.dart';

void main() {
  group('Auth Response Tests', () {
    test('Login request structure is correct', () {
      final request = LoginRequest(
        identifier: '<EMAIL>',
        password: 'password123',
      );

      final json = request.toJson();
      
      expect(json['identifier'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
    });

    test('Email OTP request structure is correct', () {
      final request = EmailOtpRequest(
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        isProviderRegistration: true,
      );

      final json = request.toJson();
      
      expect(json['email'], equals('<EMAIL>'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['password'], equals('password123'));
      expect(json['isProviderRegistration'], equals(true));
    });

    test('Provider registration request structure is correct', () {
      final request = ProviderRegistrationRequest(
        otp: '123456',
        identifier: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        providerCategoryId: 1,
        businessName: 'Test Business',
        phone: '+**********',
        email: '<EMAIL>',
      );

      final json = request.toJson();
      
      expect(json['otp'], equals('123456'));
      expect(json['identifier'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['providerCategoryId'], equals(1));
      expect(json['businessName'], equals('Test Business'));
      expect(json['phone'], equals('+**********'));
      expect(json['email'], equals('<EMAIL>'));
    });

    test('Auth response parsing works correctly with real API structure', () {
      // This is the actual response structure from the API
      final responseData = {
        'sessionId': 'gfpyz37dllbkyc5n6s2fl7oiqm5upfcxgii65lla',
        'user': {
          'id': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
          'email': '<EMAIL>',
          'firstName': 'dada',
          'lastName': 'nasro',
          'role': 'CUSTOMER',
        },
        'provider': {
          'id': 1,
          'userId': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
          'title': 'dalti clinic',
          'phone': '**********',
          'providerCategoryId': 1,
          'category': {
            'id': 1,
            'title': 'Healthcare',
            'parentId': null,
          }
        }
      };

      final response = AuthResponse.fromJson(responseData);
      
      expect(response.success, isTrue);
      expect(response.message, equals('Login successful'));
      expect(response.accessToken, equals('gfpyz37dllbkyc5n6s2fl7oiqm5upfcxgii65lla'));
      expect(response.user?.id, equals('c318cff7-b668-40eb-a8c8-c1ac05fc2593'));
      expect(response.user?.email, equals('<EMAIL>'));
      expect(response.user?.firstName, equals('dada'));
      expect(response.user?.lastName, equals('nasro'));
      expect(response.user?.fullName, equals('dada nasro'));
      expect(response.user?.role, equals('CUSTOMER'));
      expect(response.provider?.id, equals(1));
      expect(response.provider?.title, equals('dalti clinic'));
      expect(response.provider?.phone, equals('**********'));
      expect(response.provider?.providerCategoryId, equals(1));
      expect(response.provider?.category?.title, equals('Healthcare'));
      expect(response.provider?.category?.id, equals(1));
      expect(response.provider?.category?.parentId, isNull);
    });

    test('Auth response handles missing sessionId correctly', () {
      final responseData = {
        'user': {
          'id': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
          'email': '<EMAIL>',
          'firstName': 'John',
          'lastName': 'Doe',
          'role': 'CUSTOMER',
        }
        // No sessionId - should be treated as failed login
      };

      final response = AuthResponse.fromJson(responseData);
      
      expect(response.success, isTrue); // fromJson always sets success to true
      expect(response.accessToken, isNull); // No sessionId means no accessToken
    });

    test('Provider category data parsing works correctly', () {
      final categoryData = {
        'id': 1,
        'title': 'Healthcare',
        'parentId': null,
      };

      final category = ProviderCategoryData.fromJson(categoryData);
      
      expect(category.id, equals(1));
      expect(category.title, equals('Healthcare'));
      expect(category.parentId, isNull);
    });

    test('Provider data parsing works correctly', () {
      final providerData = {
        'id': 1,
        'userId': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
        'title': 'dalti clinic',
        'phone': '**********',
        'providerCategoryId': 1,
        'isSetupComplete': false,
        'category': {
          'id': 1,
          'title': 'Healthcare',
          'parentId': null,
        }
      };

      final provider = ProviderData.fromJson(providerData);

      expect(provider.id, equals(1));
      expect(provider.userId, equals('c318cff7-b668-40eb-a8c8-c1ac05fc2593'));
      expect(provider.title, equals('dalti clinic'));
      expect(provider.phone, equals('**********'));
      expect(provider.providerCategoryId, equals(1));
      expect(provider.isSetupComplete, equals(false));
      expect(provider.category?.title, equals('Healthcare'));
    });

    test('Registration response parsing works correctly', () {
      // This is the actual registration response structure from the API
      final responseData = {
        'message': 'Provider registered successfully',
        'user': {
          'id': 'f63f6d7f-4fde-46d8-98f6-b35209418f5f',
          'email': '<EMAIL>',
          'firstName': 'ddd',
          'lastName': 'aaa',
          'role': 'CUSTOMER',
        },
        'provider': {
          'id': 2,
          'userId': 'f63f6d7f-4fde-46d8-98f6-b35209418f5f',
          'providerCategoryId': 3,
          'title': 'addd',
          'phone': '**********',
          'isSetupComplete': false,
        },
        'sessionId': 'r62dicmfc7hgto6oqllprwm3oyzt44sjqitax6we',
      };

      final response = AuthResponse.fromJson(responseData);

      expect(response.success, isTrue);
      expect(response.message, equals('Provider registered successfully'));
      expect(response.accessToken, equals('r62dicmfc7hgto6oqllprwm3oyzt44sjqitax6we'));
      expect(response.user?.id, equals('f63f6d7f-4fde-46d8-98f6-b35209418f5f'));
      expect(response.user?.email, equals('<EMAIL>'));
      expect(response.user?.firstName, equals('ddd'));
      expect(response.user?.lastName, equals('aaa'));
      expect(response.user?.role, equals('CUSTOMER'));
      expect(response.provider?.id, equals(2));
      expect(response.provider?.title, equals('addd'));
      expect(response.provider?.phone, equals('**********'));
      expect(response.provider?.providerCategoryId, equals(3));
      expect(response.provider?.isSetupComplete, equals(false));
    });
  });
}
