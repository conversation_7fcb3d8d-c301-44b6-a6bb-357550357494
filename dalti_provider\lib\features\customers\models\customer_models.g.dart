// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'customer_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CustomerImpl _$$CustomerImplFromJson(Map<String, dynamic> json) =>
    _$CustomerImpl(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String?,
      phoneNumber: json['mobileNumber'] as String?,
      nationalId: json['nationalId'] as String?,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      address: json['address'] as String?,
      city: json['city'] as String?,
      wilaya: json['wilaya'] as String?,
      notes: json['notes'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      totalAppointments: (json['appointmentCount'] as num?)?.toInt() ?? 0,
      totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
      lastAppointmentDate: json['lastAppointmentDate'] == null
          ? null
          : DateTime.parse(json['lastAppointmentDate'] as String),
      status: $enumDecodeNullable(_$CustomerStatusEnumMap, json['status']) ??
          CustomerStatus.active,
      preferences: json['preferences'] == null
          ? null
          : CustomerPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerImplToJson(_$CustomerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      if (instance.email case final value?) 'email': value,
      if (instance.phoneNumber case final value?) 'mobileNumber': value,
      if (instance.nationalId case final value?) 'nationalId': value,
      if (instance.dateOfBirth?.toIso8601String() case final value?)
        'dateOfBirth': value,
      if (instance.address case final value?) 'address': value,
      if (instance.city case final value?) 'city': value,
      if (instance.wilaya case final value?) 'wilaya': value,
      if (instance.notes case final value?) 'notes': value,
      'tags': instance.tags,
      'createdAt': instance.createdAt.toIso8601String(),
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
      'appointmentCount': instance.totalAppointments,
      'totalSpent': instance.totalSpent,
      if (instance.lastAppointmentDate?.toIso8601String() case final value?)
        'lastAppointmentDate': value,
      'status': _$CustomerStatusEnumMap[instance.status]!,
      if (instance.preferences?.toJson() case final value?)
        'preferences': value,
    };

const _$CustomerStatusEnumMap = {
  CustomerStatus.active: 'active',
  CustomerStatus.inactive: 'inactive',
  CustomerStatus.blocked: 'blocked',
};

_$CustomerPreferencesImpl _$$CustomerPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerPreferencesImpl(
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      smsNotifications: json['smsNotifications'] as bool? ?? true,
      appointmentReminders: json['appointmentReminders'] as bool? ?? true,
      promotionalMessages: json['promotionalMessages'] as bool? ?? true,
      preferredLanguage: json['preferredLanguage'] as String?,
      preferredContactMethod: json['preferredContactMethod'] as String?,
    );

Map<String, dynamic> _$$CustomerPreferencesImplToJson(
        _$CustomerPreferencesImpl instance) =>
    <String, dynamic>{
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'appointmentReminders': instance.appointmentReminders,
      'promotionalMessages': instance.promotionalMessages,
      if (instance.preferredLanguage case final value?)
        'preferredLanguage': value,
      if (instance.preferredContactMethod case final value?)
        'preferredContactMethod': value,
    };

_$CustomerAppointmentImpl _$$CustomerAppointmentImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerAppointmentImpl(
      id: json['id'] as String,
      customerId: json['customerId'] as String,
      serviceId: json['serviceId'] as String,
      serviceName: json['serviceName'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      status: $enumDecode(_$AppointmentStatusEnumMap, json['status']),
      locationId: json['locationId'] as String?,
      locationName: json['locationName'] as String?,
      providerId: json['providerId'] as String?,
      providerName: json['providerName'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      duration: (json['duration'] as num?)?.toInt(),
      notes: json['notes'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$CustomerAppointmentImplToJson(
        _$CustomerAppointmentImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'customerId': instance.customerId,
      'serviceId': instance.serviceId,
      'serviceName': instance.serviceName,
      'scheduledTime': instance.scheduledTime.toIso8601String(),
      'status': _$AppointmentStatusEnumMap[instance.status]!,
      if (instance.locationId case final value?) 'locationId': value,
      if (instance.locationName case final value?) 'locationName': value,
      if (instance.providerId case final value?) 'providerId': value,
      if (instance.providerName case final value?) 'providerName': value,
      if (instance.price case final value?) 'price': value,
      if (instance.duration case final value?) 'duration': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.createdAt?.toIso8601String() case final value?)
        'createdAt': value,
      if (instance.updatedAt?.toIso8601String() case final value?)
        'updatedAt': value,
    };

const _$AppointmentStatusEnumMap = {
  AppointmentStatus.scheduled: 'scheduled',
  AppointmentStatus.confirmed: 'confirmed',
  AppointmentStatus.inProgress: 'in_progress',
  AppointmentStatus.completed: 'completed',
  AppointmentStatus.canceled: 'canceled',
  AppointmentStatus.noShow: 'no_show',
};

_$CustomerSearchFiltersImpl _$$CustomerSearchFiltersImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerSearchFiltersImpl(
      searchQuery: json['searchQuery'] as String?,
      status: $enumDecodeNullable(_$CustomerStatusEnumMap, json['status']),
      wilaya: json['wilaya'] as String?,
      createdAfter: json['createdAfter'] == null
          ? null
          : DateTime.parse(json['createdAfter'] as String),
      createdBefore: json['createdBefore'] == null
          ? null
          : DateTime.parse(json['createdBefore'] as String),
      minAppointments: (json['minAppointments'] as num?)?.toInt() ?? 0,
      minSpent: (json['minSpent'] as num?)?.toDouble() ?? 0.0,
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      sortBy: json['sortBy'] as String? ?? 'firstName',
      ascending: json['ascending'] as bool? ?? true,
    );

Map<String, dynamic> _$$CustomerSearchFiltersImplToJson(
        _$CustomerSearchFiltersImpl instance) =>
    <String, dynamic>{
      if (instance.searchQuery case final value?) 'searchQuery': value,
      if (_$CustomerStatusEnumMap[instance.status] case final value?)
        'status': value,
      if (instance.wilaya case final value?) 'wilaya': value,
      if (instance.createdAfter?.toIso8601String() case final value?)
        'createdAfter': value,
      if (instance.createdBefore?.toIso8601String() case final value?)
        'createdBefore': value,
      'minAppointments': instance.minAppointments,
      'minSpent': instance.minSpent,
      if (instance.tags case final value?) 'tags': value,
      'sortBy': instance.sortBy,
      'ascending': instance.ascending,
    };

_$CustomersResponseImpl _$$CustomersResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomersResponseImpl(
      success: json['success'] as bool,
      customers: (json['customers'] as List<dynamic>?)
              ?.map((e) => Customer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 20,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 0,
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomersResponseImplToJson(
        _$CustomersResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'customers': instance.customers.map((e) => e.toJson()).toList(),
      'totalCount': instance.totalCount,
      'currentPage': instance.currentPage,
      'pageSize': instance.pageSize,
      'totalPages': instance.totalPages,
      if (instance.error?.toJson() case final value?) 'error': value,
    };

_$CustomerResponseImpl _$$CustomerResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerResponseImpl(
      success: json['success'] as bool,
      customer: json['customer'] == null
          ? null
          : Customer.fromJson(json['customer'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerResponseImplToJson(
        _$CustomerResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      if (instance.customer?.toJson() case final value?) 'customer': value,
      if (instance.error?.toJson() case final value?) 'error': value,
    };

_$CustomerAppointmentsResponseImpl _$$CustomerAppointmentsResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerAppointmentsResponseImpl(
      success: json['success'] as bool,
      appointments: (json['appointments'] as List<dynamic>?)
              ?.map((e) =>
                  CustomerAppointment.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalCount: (json['totalCount'] as num?)?.toInt() ?? 0,
      error: json['error'] == null
          ? null
          : CustomerError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerAppointmentsResponseImplToJson(
        _$CustomerAppointmentsResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      'appointments': instance.appointments.map((e) => e.toJson()).toList(),
      'totalCount': instance.totalCount,
      if (instance.error?.toJson() case final value?) 'error': value,
    };

_$CustomerErrorImpl _$$CustomerErrorImplFromJson(Map<String, dynamic> json) =>
    _$CustomerErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
    );

Map<String, dynamic> _$$CustomerErrorImplToJson(_$CustomerErrorImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      if (instance.details case final value?) 'details': value,
    };

_$CustomerRequestImpl _$$CustomerRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomerRequestImpl(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      mobileNumber: json['mobileNumber'] as String?,
      email: json['email'] as String?,
      nationalId: json['nationalId'] as String?,
      notes: json['notes'] as String?,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      address: json['address'] as String?,
      city: json['city'] as String?,
      wilaya: json['wilaya'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      preferences: json['preferences'] == null
          ? null
          : CustomerPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$CustomerRequestImplToJson(
        _$CustomerRequestImpl instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      if (instance.mobileNumber case final value?) 'mobileNumber': value,
      if (instance.email case final value?) 'email': value,
      if (instance.nationalId case final value?) 'nationalId': value,
      if (instance.notes case final value?) 'notes': value,
      if (instance.dateOfBirth?.toIso8601String() case final value?)
        'dateOfBirth': value,
      if (instance.address case final value?) 'address': value,
      if (instance.city case final value?) 'city': value,
      if (instance.wilaya case final value?) 'wilaya': value,
      'tags': instance.tags,
      if (instance.preferences?.toJson() case final value?)
        'preferences': value,
    };

_$CustomerStatsImpl _$$CustomerStatsImplFromJson(Map<String, dynamic> json) =>
    _$CustomerStatsImpl(
      totalCustomers: (json['totalCustomers'] as num?)?.toInt() ?? 0,
      newCustomersThisMonth:
          (json['newCustomersThisMonth'] as num?)?.toInt() ?? 0,
      activeCustomers: (json['activeCustomers'] as num?)?.toInt() ?? 0,
      averageSpentPerCustomer:
          (json['averageSpentPerCustomer'] as num?)?.toDouble() ?? 0.0,
      customerRetentionRate:
          (json['customerRetentionRate'] as num?)?.toDouble() ?? 0.0,
      customersByWilaya: (json['customersByWilaya'] as List<dynamic>?)
              ?.map(
                  (e) => CustomersByWilaya.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CustomerStatsImplToJson(_$CustomerStatsImpl instance) =>
    <String, dynamic>{
      'totalCustomers': instance.totalCustomers,
      'newCustomersThisMonth': instance.newCustomersThisMonth,
      'activeCustomers': instance.activeCustomers,
      'averageSpentPerCustomer': instance.averageSpentPerCustomer,
      'customerRetentionRate': instance.customerRetentionRate,
      'customersByWilaya':
          instance.customersByWilaya.map((e) => e.toJson()).toList(),
    };

_$CustomersByWilayaImpl _$$CustomersByWilayaImplFromJson(
        Map<String, dynamic> json) =>
    _$CustomersByWilayaImpl(
      wilaya: json['wilaya'] as String,
      count: (json['count'] as num).toInt(),
      percentage: (json['percentage'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$CustomersByWilayaImplToJson(
        _$CustomersByWilayaImpl instance) =>
    <String, dynamic>{
      'wilaya': instance.wilaya,
      'count': instance.count,
      'percentage': instance.percentage,
    };
