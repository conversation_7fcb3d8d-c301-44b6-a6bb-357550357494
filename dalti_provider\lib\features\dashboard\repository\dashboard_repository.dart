import '../models/dashboard_models.dart';

/// Repository interface for dashboard data operations
abstract class DashboardRepository {
  /// Fetch complete dashboard data
  Future<DashboardData> getDashboardData();

  /// Fetch business overview metrics
  Future<BusinessMetrics> getBusinessOverview();

  /// Fetch today's schedule and queue data
  Future<ScheduleData> getTodaySchedule();

  /// Fetch notifications
  Future<List<NotificationItem>> getNotifications({
    int limit = 10,
    bool unreadOnly = false,
  });

  /// Fetch quick stats for real-time updates
  Future<QuickStats> getQuickStats();

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId);

  /// Emergency queue control (pause/resume all queues)
  Future<EmergencyQueueControlResponse> emergencyQueueControl(
    EmergencyQueueControl request,
  );

  /// Get unread notification count
  Future<int> getUnreadNotificationCount();

  /// Refresh all dashboard data
  Future<DashboardData> refreshDashboardData();

  /// Check if dashboard data needs refresh
  bool needsRefresh();

  /// Get cached dashboard data (if available)
  DashboardData? getCachedData();

  /// Clear cached data
  Future<void> clearCache();
}
