import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';

/// Customer list item widget for displaying customer information in a list
class CustomerListItem extends StatelessWidget {
  final Customer customer;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onCall;
  final VoidCallback? onEmail;
  final bool showActions;

  const CustomerListItem({
    super.key,
    required this.customer,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onCall,
    this.onEmail,
    this.showActions = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with name and status
              Row(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                    child: Text(
                      _getInitials(customer.firstName, customer.lastName),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Name and email
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${customer.firstName} ${customer.lastName}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          customer.email ?? 'No email',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status badge
                  _buildStatusBadge(customer.status),
                  
                  // Actions menu
                  if (showActions)
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 20, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              
              // const SizedBox(height: 12),
              
              // // Contact information with action buttons
              // Row(
              //   children: [
              //     InkWell(
              //       onTap: onCall,
              //       child: Row(
              //         mainAxisSize: MainAxisSize.min,
              //         children: [
              //           Icon(
              //             Icons.phone,
              //             size: 16,
              //             color: onCall != null
              //                 ? Theme.of(context).colorScheme.primary
              //                 : Theme.of(context).colorScheme.onSurfaceVariant,
              //           ),
              //           const SizedBox(width: 6),
              //           Text(
              //             customer.phoneNumber,
              //             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              //               color: onCall != null
              //                   ? Theme.of(context).colorScheme.primary
              //                   : Theme.of(context).colorScheme.onSurface,
              //               decoration: onCall != null ? TextDecoration.underline : null,
              //             ),
              //           ),
              //         ],
              //       ),
              //     ),
              //     const SizedBox(width: 16),
              //     InkWell(
              //       onTap: onEmail,
              //       child: Row(
              //         mainAxisSize: MainAxisSize.min,
              //         children: [
              //           Icon(
              //             Icons.email,
              //             size: 16,
              //             color: onEmail != null
              //                 ? Theme.of(context).colorScheme.primary
              //                 : Theme.of(context).colorScheme.onSurfaceVariant,
              //           ),
              //           const SizedBox(width: 6),
              //           Text(
              //             'Email',
              //             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              //               color: onEmail != null
              //                   ? Theme.of(context).colorScheme.primary
              //                   : Theme.of(context).colorScheme.onSurface,
              //               decoration: onEmail != null ? TextDecoration.underline : null,
              //             ),
              //           ),
              //         ],
              //       ),
              //     ),
              //     if (customer.city?.isNotEmpty == true) ...[
              //       const SizedBox(width: 16),
              //       Icon(
              //         Icons.location_on,
              //         size: 16,
              //         color: Theme.of(context).colorScheme.onSurfaceVariant,
              //       ),
              //       const SizedBox(width: 6),
              //       Text(
              //         customer.city!,
              //         style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              //           color: Theme.of(context).colorScheme.onSurface,
              //         ),
              //       ),
              //     ],
              //   ],
              // ),
              
              // const SizedBox(height: 12),
              
              // // Statistics row
              // Row(
              //   children: [
              //     _buildStatItem(
              //       context: context,
              //       icon: Icons.event,
              //       label: 'Appointments',
              //       value: customer.totalAppointments.toString(),
              //     ),
              //     const SizedBox(width: 24),
              //     _buildStatItem(
              //       context: context,
              //       icon: Icons.attach_money,
              //       label: 'Total Spent',
              //       value: _formatCurrency(customer.totalSpent),
              //     ),
              //     const Spacer(),
              //     if (customer.lastAppointmentDate != null)
              //       Text(
              //         'Last visit: ${_formatDate(customer.lastAppointmentDate!)}',
              //         style: Theme.of(context).textTheme.labelSmall?.copyWith(
              //           color: Theme.of(context).colorScheme.onSurfaceVariant,
              //         ),
              //       ),
              //   ],
              // ),
              
              // // Tags
              // if (customer.tags.isNotEmpty) ...[
              //   const SizedBox(height: 12),
              //   Wrap(
              //     spacing: 6,
              //     runSpacing: 4,
              //     children: customer.tags.take(3).map((tag) => _buildTag(context, tag)).toList(),
              //   ),
              // ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(CustomerStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case CustomerStatus.active:
        color = Colors.green;
        text = 'Active';
        break;
      case CustomerStatus.inactive:
        color = Colors.orange;
        text = 'Inactive';
        break;
      case CustomerStatus.blocked:
        color = Colors.red;
        text = 'Blocked';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTag(BuildContext context, String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        tag,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: Theme.of(context).colorScheme.onPrimaryContainer,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _getInitials(String firstName, String lastName) {
    final first = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    final last = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$first$last';
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'DA ', decimalDigits: 0);
    return formatter.format(amount);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference < 7) {
      return '${difference}d ago';
    } else if (difference < 30) {
      return '${(difference / 7).floor()}w ago';
    } else {
      return DateFormat('MMM d, y').format(date);
    }
  }
}
