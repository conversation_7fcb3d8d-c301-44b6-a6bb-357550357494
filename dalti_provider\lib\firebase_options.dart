// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD3z86nxd9i4okjur85vpg4be-um8lQzn8',
    appId: '1:816987655237:web:99b535475db49b632e672c',
    messagingSenderId: '816987655237',
    projectId: 'dalti-3d06b',
    authDomain: 'dalti-3d06b.firebaseapp.com',
    storageBucket: 'dalti-3d06b.firebasestorage.app',
    measurementId: 'G-0BKG02FNLZ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCLV3ESOoycIrloOQ3IxZbOesUcfc8kJpY',
    appId: '1:816987655237:android:5c8650ead20f8cb12e672c',
    messagingSenderId: '816987655237',
    projectId: 'dalti-3d06b',
    storageBucket: 'dalti-3d06b.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBsjCZ7uE8jMtt-aIxIRDweH67Qt8W-aME',
    appId: '1:816987655237:ios:51327ec5337d08a32e672c',
    messagingSenderId: '816987655237',
    projectId: 'dalti-3d06b',
    storageBucket: 'dalti-3d06b.firebasestorage.app',
    iosBundleId: 'com.example.daltiProvider',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBsjCZ7uE8jMtt-aIxIRDweH67Qt8W-aME',
    appId: '1:816987655237:ios:51327ec5337d08a32e672c',
    messagingSenderId: '816987655237',
    projectId: 'dalti-3d06b',
    storageBucket: 'dalti-3d06b.firebasestorage.app',
    iosBundleId: 'com.example.daltiProvider',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD3z86nxd9i4okjur85vpg4be-um8lQzn8',
    appId: '1:816987655237:web:7c7c4a794c7aacfc2e672c',
    messagingSenderId: '816987655237',
    projectId: 'dalti-3d06b',
    authDomain: 'dalti-3d06b.firebaseapp.com',
    storageBucket: 'dalti-3d06b.firebasestorage.app',
    measurementId: 'G-TFYBNKNFCW',
  );
}
