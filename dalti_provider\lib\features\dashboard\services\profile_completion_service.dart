import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/network/http_client.dart';
import '../../../core/providers/app_providers.dart';
import '../models/profile_completion_models.dart';

/// Service for handling profile completion API calls
class ProfileCompletionService {
  final HttpClient _httpClient;

  ProfileCompletionService(this._httpClient);

  /// Fetch profile completion data
  Future<ProfileCompletionData> getProfileCompletion() async {
    try {
      print('[ProfileCompletionService] Fetching profile completion data');

      final response = await _httpClient.get(
        '/api/auth/provider/profile-completion',
      );

      print(
        '[ProfileCompletionService] Profile completion response status: ${response.statusCode}',
      );

      if (response.statusCode == 200 && response.data != null) {
        final profileCompletionResponse = ProfileCompletionResponse.fromJson(
          response.data,
        );

        if (!profileCompletionResponse.success) {
          throw Exception(
            'Failed to fetch profile completion: ${profileCompletionResponse.message}',
          );
        }

        print(
          '[ProfileCompletionService] Profile completion fetched successfully',
        );
        return profileCompletionResponse.data;
      } else {
        print(
          '[ProfileCompletionService] Failed to fetch profile completion with status: ${response.statusCode}',
        );
        throw Exception('Failed to fetch profile completion data');
      }
    } catch (e) {
      print('[ProfileCompletionService] Error fetching profile completion: $e');
      throw Exception('Error fetching profile completion: $e');
    }
  }
}

/// Provider for ProfileCompletionService
final profileCompletionServiceProvider = Provider<ProfileCompletionService>((
  ref,
) {
  final httpClient = ref.watch(httpClientProvider);
  return ProfileCompletionService(httpClient);
});
