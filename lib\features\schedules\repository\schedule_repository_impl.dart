import '../services/schedule_api_service.dart';
import '../models/schedule_models.dart';
import 'schedule_repository.dart';

/// Concrete implementation of ScheduleRepository using API service
class ScheduleRepositoryImpl implements ScheduleRepository {
  final ScheduleApiService _apiService;

  ScheduleRepositoryImpl({
    required ScheduleApiService apiService,
  }) : _apiService = apiService;

  @override
  Future<List<Schedule>> getSchedules({
    int? locationId,
    int? dayOfWeek,
    bool? isActive,
  }) async {
    try {
      print('[ScheduleRepository] Getting schedules (locationId: $locationId, dayOfWeek: $dayOfWeek, isActive: $isActive)');

      final schedules = await _apiService.getSchedules(
        locationId: locationId,
        dayOfWeek: dayOfWeek,
        isActive: isActive,
      );

      print('[ScheduleRepository] Retrieved ${schedules.length} schedules');
      return schedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedules: $e');
      rethrow;
    }
  }

  @override
  Future<Schedule> getScheduleById(int id) async {
    try {
      print('[ScheduleRepository] Getting schedule by ID: $id');

      final schedule = await _apiService.getScheduleById(id);

      print('[ScheduleRepository] Retrieved schedule: ${schedule.id}');
      return schedule;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedule by ID: $e');
      rethrow;
    }
  }

  @override
  Future<Schedule> createSchedule(CreateScheduleRequest request) async {
    try {
      print('[ScheduleRepository] Creating schedule: ${request.toJson()}');

      // Validate schedule time
      final validation = await validateScheduleTime(request.startTime, request.endTime);
      if (!validation.isValid) {
        throw Exception('Invalid schedule time: ${validation.errorMessage}');
      }

      // Check for conflicts
      final conflicts = await checkScheduleConflicts(
        dayOfWeek: request.dayOfWeek,
        startTime: request.startTime,
        endTime: request.endTime,
        locationId: request.locationId,
      );

      if (conflicts.isNotEmpty) {
        throw Exception('Schedule conflicts with existing schedule: ${conflicts.first.timeRange}');
      }

      final schedule = await _apiService.createSchedule(request);

      print('[ScheduleRepository] Created schedule: ${schedule.id}');
      return schedule;
    } catch (e) {
      print('[ScheduleRepository] Error creating schedule: $e');
      rethrow;
    }
  }

  @override
  Future<Schedule> updateSchedule(int id, UpdateScheduleRequest request) async {
    try {
      print('[ScheduleRepository] Updating schedule $id: ${request.toJson()}');

      // Validate schedule time if provided
      if (request.startTime != null && request.endTime != null) {
        final validation = await validateScheduleTime(request.startTime!, request.endTime!);
        if (!validation.isValid) {
          throw Exception('Invalid schedule time: ${validation.errorMessage}');
        }
      }

      // Check for conflicts if time or day is being changed
      if (request.dayOfWeek != null || request.startTime != null || 
          request.endTime != null || request.locationId != null) {
        
        // Get current schedule to fill in missing values
        final currentSchedule = await getScheduleById(id);
        
        final conflicts = await checkScheduleConflicts(
          dayOfWeek: request.dayOfWeek ?? currentSchedule.dayOfWeek,
          startTime: request.startTime ?? currentSchedule.startTime,
          endTime: request.endTime ?? currentSchedule.endTime,
          locationId: request.locationId ?? currentSchedule.locationId,
          excludeScheduleId: id,
        );

        if (conflicts.isNotEmpty) {
          throw Exception('Schedule conflicts with existing schedule: ${conflicts.first.timeRange}');
        }
      }

      final schedule = await _apiService.updateSchedule(id, request);

      print('[ScheduleRepository] Updated schedule: ${schedule.id}');
      return schedule;
    } catch (e) {
      print('[ScheduleRepository] Error updating schedule: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deleteSchedule(int id) async {
    try {
      print('[ScheduleRepository] Deleting schedule: $id');

      final result = await _apiService.deleteSchedule(id);

      print('[ScheduleRepository] Deleted schedule: $id');
      return result;
    } catch (e) {
      print('[ScheduleRepository] Error deleting schedule: $e');
      rethrow;
    }
  }

  @override
  Future<List<Schedule>> getSchedulesByLocation(int locationId, {bool? isActive}) async {
    try {
      print('[ScheduleRepository] Getting schedules for location: $locationId');

      final schedules = await _apiService.getSchedules(
        locationId: locationId,
        isActive: isActive,
      );

      print('[ScheduleRepository] Retrieved ${schedules.length} schedules for location $locationId');
      return schedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedules by location: $e');
      rethrow;
    }
  }

  @override
  Future<List<Schedule>> getSchedulesByDay(int dayOfWeek, {bool? isActive}) async {
    try {
      print('[ScheduleRepository] Getting schedules for day: $dayOfWeek');

      final schedules = await _apiService.getSchedules(
        dayOfWeek: dayOfWeek,
        isActive: isActive,
      );

      print('[ScheduleRepository] Retrieved ${schedules.length} schedules for day $dayOfWeek');
      return schedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedules by day: $e');
      rethrow;
    }
  }

  @override
  Future<Map<int, List<Schedule>>> getWeeklySchedule(int locationId, {bool? isActive}) async {
    try {
      print('[ScheduleRepository] Getting weekly schedule for location: $locationId');

      final schedules = await getSchedulesByLocation(locationId, isActive: isActive);
      
      // Group schedules by day of week
      final weeklySchedule = <int, List<Schedule>>{};
      for (final schedule in schedules) {
        weeklySchedule.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
      }

      // Sort schedules within each day by start time
      for (final daySchedules in weeklySchedule.values) {
        daySchedules.sort((a, b) => a.startTime.compareTo(b.startTime));
      }

      print('[ScheduleRepository] Retrieved weekly schedule with ${weeklySchedule.length} days');
      return weeklySchedule;
    } catch (e) {
      print('[ScheduleRepository] Error getting weekly schedule: $e');
      rethrow;
    }
  }

  @override
  Future<List<Schedule>> checkScheduleConflicts({
    required int dayOfWeek,
    required String startTime,
    required String endTime,
    required int locationId,
    int? excludeScheduleId,
  }) async {
    try {
      print('[ScheduleRepository] Checking schedule conflicts');

      final conflicts = await _apiService.checkScheduleConflicts(
        dayOfWeek: dayOfWeek,
        startTime: startTime,
        endTime: endTime,
        locationId: locationId,
        excludeScheduleId: excludeScheduleId,
      );

      print('[ScheduleRepository] Found ${conflicts.length} conflicts');
      return conflicts;
    } catch (e) {
      print('[ScheduleRepository] Error checking schedule conflicts: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getScheduleStats(int locationId) async {
    try {
      print('[ScheduleRepository] Getting schedule stats for location: $locationId');

      final stats = await _apiService.getScheduleStats(locationId);

      print('[ScheduleRepository] Retrieved schedule stats: $stats');
      return stats;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedule stats: $e');
      return {};
    }
  }

  @override
  Future<List<Schedule>> createBulkSchedules(List<CreateScheduleRequest> requests) async {
    try {
      print('[ScheduleRepository] Creating ${requests.length} schedules in bulk');

      // Validate all schedules first
      for (final request in requests) {
        final validation = await validateScheduleTime(request.startTime, request.endTime);
        if (!validation.isValid) {
          throw Exception('Invalid schedule time for ${request.dayOfWeek}: ${validation.errorMessage}');
        }
      }

      final schedules = await _apiService.createBulkSchedules(requests);

      print('[ScheduleRepository] Created ${schedules.length} schedules in bulk');
      return schedules;
    } catch (e) {
      print('[ScheduleRepository] Error creating bulk schedules: $e');
      rethrow;
    }
  }

  @override
  Future<Map<int, List<Schedule>>> getSchedulesGroupedByLocation({bool? isActive}) async {
    try {
      print('[ScheduleRepository] Getting schedules grouped by location');

      final schedules = await getSchedules(isActive: isActive);
      
      final groupedSchedules = <int, List<Schedule>>{};
      for (final schedule in schedules) {
        groupedSchedules.putIfAbsent(schedule.locationId, () => []).add(schedule);
      }

      print('[ScheduleRepository] Grouped schedules by ${groupedSchedules.length} locations');
      return groupedSchedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedules grouped by location: $e');
      rethrow;
    }
  }

  @override
  Future<Map<int, List<Schedule>>> getSchedulesGroupedByDay({
    int? locationId,
    bool? isActive,
  }) async {
    try {
      print('[ScheduleRepository] Getting schedules grouped by day');

      final schedules = await getSchedules(locationId: locationId, isActive: isActive);
      
      final groupedSchedules = <int, List<Schedule>>{};
      for (final schedule in schedules) {
        groupedSchedules.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
      }

      print('[ScheduleRepository] Grouped schedules by ${groupedSchedules.length} days');
      return groupedSchedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting schedules grouped by day: $e');
      rethrow;
    }
  }

  @override
  Future<Schedule> toggleScheduleStatus(int id, bool isActive) async {
    try {
      print('[ScheduleRepository] Toggling schedule $id status to: $isActive');

      // Note: The API doesn't have a specific toggle endpoint, so we use update
      final request = UpdateScheduleRequest();
      // Since UpdateScheduleRequest doesn't have isActive field in the API schema,
      // we'll need to implement this differently or update the API
      
      // For now, we'll get the current schedule and update it
      final currentSchedule = await getScheduleById(id);
      
      // This is a workaround - in a real implementation, the API should support isActive updates
      throw UnimplementedError('Toggle schedule status not supported by current API');
    } catch (e) {
      print('[ScheduleRepository] Error toggling schedule status: $e');
      rethrow;
    }
  }

  @override
  Future<List<Schedule>> getTodaySchedules() async {
    try {
      print('[ScheduleRepository] Getting today\'s schedules');

      final today = DateTime.now();
      final dayOfWeek = today.weekday % 7; // Convert to 0-6 format

      final schedules = await getSchedulesByDay(dayOfWeek, isActive: true);

      print('[ScheduleRepository] Retrieved ${schedules.length} schedules for today');
      return schedules;
    } catch (e) {
      print('[ScheduleRepository] Error getting today\'s schedules: $e');
      rethrow;
    }
  }

  @override
  Future<ScheduleValidationResult> validateScheduleTime(String startTime, String endTime) async {
    try {
      // Validate time format (HH:MM)
      final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
      
      if (!timeRegex.hasMatch(startTime)) {
        return ScheduleValidationResult.invalid('Invalid start time format. Use HH:MM format.');
      }
      
      if (!timeRegex.hasMatch(endTime)) {
        return ScheduleValidationResult.invalid('Invalid end time format. Use HH:MM format.');
      }

      // Convert to minutes for comparison
      final startMinutes = _timeToMinutes(startTime);
      final endMinutes = _timeToMinutes(endTime);

      if (startMinutes >= endMinutes) {
        return ScheduleValidationResult.invalid('End time must be after start time.');
      }

      // Check for reasonable duration (at least 30 minutes, max 12 hours)
      final durationMinutes = endMinutes - startMinutes;
      if (durationMinutes < 30) {
        return ScheduleValidationResult.invalid('Schedule duration must be at least 30 minutes.');
      }
      
      if (durationMinutes > 720) { // 12 hours
        return ScheduleValidationResult.invalid('Schedule duration cannot exceed 12 hours.');
      }

      return ScheduleValidationResult.valid();
    } catch (e) {
      return ScheduleValidationResult.invalid('Error validating schedule time: $e');
    }
  }

  /// Convert time string (HH:MM) to minutes since midnight
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  }
}
