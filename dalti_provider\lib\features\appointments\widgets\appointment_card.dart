import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/appointment_models.dart';

/// Appointment card widget for displaying appointment information
class AppointmentCard extends StatelessWidget {
  final Appointment appointment;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onCancel;
  final VoidCallback? onComplete;
  final bool isCompact;
  final bool showActions;

  const AppointmentCard({
    super.key,
    required this.appointment,
    this.onTap,
    this.onEdit,
    this.onCancel,
    this.onComplete,
    this.isCompact = false,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: isCompact ? 4 : 8,
        vertical: isCompact ? 2 : 4,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isCompact ? 8 : 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: _getStatusColor(context), width: 2),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with time and status
              Row(
                children: [
                  // Time
                  Icon(
                    Icons.schedule,
                    size: isCompact ? 14 : 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    DateFormat('HH:mm').format(appointment.scheduledTime),
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: isCompact ? 12 : null,
                    ),
                  ),

                  if (!isCompact) ...[
                    const SizedBox(width: 8),
                    Text(
                      '(${appointment.duration}min)',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],

                  const Spacer(),

                  // Status badge
                  _buildStatusBadge(context),
                ],
              ),

              const SizedBox(height: 8),

              // Customer name
              Text(
                appointment.customerName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  fontSize: isCompact ? 14 : null,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // Service name
              Text(
                appointment.serviceName,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontSize: isCompact ? 12 : null,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              // Location (if available and not compact)
              if (!isCompact && appointment.locationName != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        appointment.locationName!,
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],

              // Notes (if available and not compact)
              if (!isCompact &&
                  appointment.notes != null &&
                  appointment.notes!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    appointment.notes!,
                    style: Theme.of(context).textTheme.labelSmall,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],

              // Actions (if enabled and not compact)
              if (showActions && !isCompact && _shouldShowActions()) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    // Edit button
                    if (onEdit != null && _canEdit())
                      TextButton.icon(
                        onPressed: onEdit,
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('Edit'),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          minimumSize: Size.zero,
                        ),
                      ),

                    // Complete button
                    if (onComplete != null && _canComplete()) ...[
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: onComplete,
                        icon: const Icon(Icons.check, size: 16),
                        label: const Text('Complete'),
                        style: TextButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          minimumSize: Size.zero,
                        ),
                      ),
                    ],

                    // Cancel button
                    if (onCancel != null && _canCancel()) ...[
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('Cancel'),
                        style: TextButton.styleFrom(
                          foregroundColor: Theme.of(context).colorScheme.error,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          minimumSize: Size.zero,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    final color = _getStatusColor(context);
    final text = _getStatusText();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
          fontSize: isCompact ? 10 : null,
        ),
      ),
    );
  }

  Color _getStatusColor(BuildContext context) {
    switch (appointment.status) {
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.scheduled:
        return Theme.of(context).colorScheme.primary;
      case AppointmentStatus.confirmed:
        return Theme.of(context).colorScheme.secondary;
      case AppointmentStatus.inProgress:
        return Theme.of(context).colorScheme.tertiary;
      case AppointmentStatus.completed:
        return Theme.of(context).colorScheme.outline;
      case AppointmentStatus.canceled:
        return Theme.of(context).colorScheme.error;
      case AppointmentStatus.noShow:
        return Theme.of(context).colorScheme.error.withValues(alpha: 0.7);
      case AppointmentStatus.rescheduled:
        return Theme.of(context).colorScheme.primary.withValues(alpha: 0.7);
    }
  }

  String _getStatusText() {
    switch (appointment.status) {
      case AppointmentStatus.pending:
        return 'Pending';
      case AppointmentStatus.scheduled:
        return 'Scheduled';
      case AppointmentStatus.confirmed:
        return 'Confirmed';
      case AppointmentStatus.inProgress:
        return 'In Progress';
      case AppointmentStatus.completed:
        return 'Completed';
      case AppointmentStatus.canceled:
        return 'Cancelled';
      case AppointmentStatus.noShow:
        return 'No Show';
      case AppointmentStatus.rescheduled:
        return 'Rescheduled';
    }
  }

  bool _shouldShowActions() {
    return appointment.status != AppointmentStatus.completed &&
        appointment.status != AppointmentStatus.canceled &&
        appointment.status != AppointmentStatus.noShow;
  }

  bool _canEdit() {
    return appointment.status == AppointmentStatus.scheduled ||
        appointment.status == AppointmentStatus.confirmed;
  }

  bool _canComplete() {
    return appointment.status == AppointmentStatus.confirmed ||
        appointment.status == AppointmentStatus.inProgress;
  }

  bool _canCancel() {
    return appointment.status == AppointmentStatus.scheduled ||
        appointment.status == AppointmentStatus.confirmed;
  }
}
