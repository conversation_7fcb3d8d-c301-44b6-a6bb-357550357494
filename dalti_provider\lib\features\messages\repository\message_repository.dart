import '../models/message_models.dart';

/// Abstract repository interface for message and conversation operations
abstract class MessageRepository {
  /// Get conversations with optional filters and pagination
  Future<List<Conversation>> getConversations({
    String? searchQuery,
    int page = 1,
    int pageSize = 20,
  });

  /// Get a single conversation by ID
  Future<Conversation> getConversation(String conversationId);

  /// Create a new conversation with a customer
  Future<Conversation> createConversation(CreateConversationRequest request);

  /// Get messages for a conversation with pagination
  Future<List<Message>> getMessages(
    String conversationId, {
    String? searchQuery,
    int page = 1,
    int pageSize = 50,
  });

  /// Send a new message
  Future<Message> sendMessage(SendMessageRequest request);

  /// Mark messages as read
  Future<bool> markMessagesAsRead(
    String conversationId,
    List<String> messageIds,
  );

  /// Mark a single message as read
  Future<MarkAsReadResponse> markMessageAsRead(MarkAsReadRequest request);

  /// Archive a conversation
  Future<bool> archiveConversation(String conversationId);

  /// Unarchive a conversation
  Future<bool> unarchiveConversation(String conversationId);

  /// Delete a conversation
  Future<bool> deleteConversation(String conversationId);

  /// Get unread messages count
  Future<int> getUnreadMessagesCount();

  /// Search conversations by query
  Future<List<Conversation>> searchConversations(String query);

  /// Search messages by query
  Future<List<Message>> searchMessages(String query, {String? conversationId});

  /// Get conversation statistics
  Future<Map<String, dynamic>> getConversationStats();

  /// Get conversations count
  Future<int> getConversationsCount({String? searchQuery});

  /// Get recent conversations (last 30 days)
  Future<List<Conversation>> getRecentConversations({int limit = 10});

  /// Get conversations with unread messages
  Future<List<Conversation>> getConversationsWithUnreadMessages({
    int limit = 10,
  });

  /// Clear cached conversation data
  Future<void> clearCache();

  /// Refresh conversation data from server
  Future<void> refreshConversations();

  /// Stream of real-time conversation updates
  Stream<List<Conversation>> watchConversations();

  /// Stream of real-time message updates for a conversation
  Stream<List<Message>> watchMessages(String conversationId);

  /// Stream of unread messages count updates
  Stream<int> watchUnreadMessagesCount();
}
