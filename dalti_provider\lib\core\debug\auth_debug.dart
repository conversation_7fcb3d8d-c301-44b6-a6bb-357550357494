import '../auth/jwt_service.dart';
import '../storage/storage_service.dart';

/// Debug utility for checking authentication state
class AuthDebug {
  /// Print detailed authentication state information
  static void printAuthState(JwtService jwtService) {
    print('[] ');
    
    // JWT Service state
    print('[] ');
    print('[] ');
    print('[] ');
    
    if (jwtService.currentToken != null) {
      final token = jwtService.currentToken!;
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
    }
    
    // Authorization header
    final authHeader = jwtService.getAuthorizationHeader();
    print('[] ');
    if (authHeader != null) {
      print('[] ');
    } else {
      print('[] ');
    }
    
    // Storage state
    print('[] ');
    try {
      final storedToken = StorageService.getAuth<Map<String, dynamic>>('jwt_token');
      print('[] ');
      
      final simpleToken = StorageService.getAuth<String>('access_token');
      print('[] ');
      
      final userId = StorageService.getAuth<String>('user_id');
      print('[] ');
    } catch (e) {
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Check if the user should be able to make authenticated requests
  static bool canMakeAuthenticatedRequests(JwtService jwtService) {
    final isAuthenticated = jwtService.isAuthenticated;
    final hasValidToken = jwtService.currentToken?.isValid ?? false;
    final hasAuthHeader = jwtService.getAuthorizationHeader() != null;
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    final canMakeRequests = isAuthenticated && hasValidToken && hasAuthHeader;
    print('[] ');
    
    return canMakeRequests;
  }
  
  /// Simulate what happens when making a location API request
  static void simulateLocationRequest(JwtService jwtService) {
    print('[] ');
    
    // Check if we can make the request
    final canMakeRequest = canMakeAuthenticatedRequests(jwtService);
    
    if (canMakeRequest) {
      final authHeader = jwtService.getAuthorizationHeader();
      print('[] ');
      print('[] ');
      print('[] ');
    } else {
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
    }
    
    print('[] ');
  }
}
