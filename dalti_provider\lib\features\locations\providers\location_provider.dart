import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/location_models.dart';
import '../repository/location_repository.dart';
import '../repository/location_repository_impl.dart';
import '../services/location_api_service.dart';
import '../services/location_integration_service.dart';

part 'location_provider.g.dart';

/// Location state
enum LocationState {
  initial,
  loading,
  loaded,
  error,
}

/// Location data model for state management
class LocationData {
  final LocationState state;
  final List<Location> locations;
  final Location? selectedLocation;
  final String? error;
  final bool isLoading;

  const LocationData({
    required this.state,
    this.locations = const [],
    this.selectedLocation,
    this.error,
    this.isLoading = false,
  });

  LocationData copyWith({
    LocationState? state,
    List<Location>? locations,
    Location? selectedLocation,
    String? error,
    bool? isLoading,
    bool clearSelectedLocation = false,
    bool clearError = false,
  }) {
    return LocationData(
      state: state ?? this.state,
      locations: locations ?? this.locations,
      selectedLocation: clearSelectedLocation ? null : (selectedLocation ?? this.selectedLocation),
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get hasError => state == LocationState.error;
  bool get hasLocations => locations.isNotEmpty;
}

/// Location API service provider
@riverpod
LocationApiService locationApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return LocationApiService(httpClient);
}

/// Location repository provider
@riverpod
LocationRepository locationRepository(Ref ref) {
  final apiService = ref.watch(locationApiServiceProvider);
  return LocationRepositoryImpl(apiService: apiService);
}

/// Location integration service provider
@riverpod
LocationIntegrationService locationIntegrationService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return LocationIntegrationService(httpClient);
}

/// Location provider for managing location state
@riverpod
class LocationNotifier extends _$LocationNotifier {
  @override
  LocationData build() {
    return const LocationData(state: LocationState.initial);
  }

  /// Load all locations
  Future<void> loadLocations({
    bool? isActive,
    String? search,
    bool forceRefresh = false,
  }) async {
    try {
      // Don't reload if already loaded and not forcing refresh
      if (state.state == LocationState.loaded && !forceRefresh && search == null) {
        return;
      }

      state = state.copyWith(
        state: LocationState.loading,
        isLoading: true,
        clearError: true,
      );

      print('[LocationProvider] Loading locations (isActive: $isActive, search: $search)');

      final repository = ref.read(locationRepositoryProvider);
      final locations = await repository.getLocations(
        isActive: isActive,
        search: search,
      );

      state = state.copyWith(
        state: LocationState.loaded,
        locations: locations,
        isLoading: false,
      );

      print(().toString());
    } catch (e) {
      print(().toString());

      state = state.copyWith(
        state: LocationState.error,
        error: 'Failed to load locations: $e',
        isLoading: false,
      );
    }
  }

  /// Get location by ID
  Future<Location?> getLocationById(int id) async {
    try {
      print(().toString());

      final repository = ref.read(locationRepositoryProvider);
      final location = await repository.getLocationById(id);

      // Update selected location
      state = state.copyWith(selectedLocation: location);

      print(().toString());

      return location;
    } catch (e) {
      print(().toString());

      state = state.copyWith(
        error: 'Failed to get location: $e',
      );

      return null;
    }
  }

  /// Create a new location
  Future<bool> createLocation(CreateLocationRequest request) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print(().toString());

      final repository = ref.read(locationRepositoryProvider);
      final location = await repository.createLocation(request);

      // Add to current locations list
      final updatedLocations = [...state.locations, location];
      state = state.copyWith(
        locations: updatedLocations,
        selectedLocation: location,
        isLoading: false,
      );

      print(().toString());

      return true;
    } catch (e) {
      print(().toString());

      state = state.copyWith(
        error: 'Failed to create location: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Update an existing location
  Future<bool> updateLocation(int id, UpdateLocationRequest request) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print(().toString());

      final repository = ref.read(locationRepositoryProvider);
      final updatedLocation = await repository.updateLocation(id, request);

      // Update in current locations list
      final updatedLocations = state.locations.map((location) {
        return location.id == id ? updatedLocation : location;
      }).toList();

      state = state.copyWith(
        locations: updatedLocations,
        selectedLocation: state.selectedLocation?.id == id ? updatedLocation : state.selectedLocation,
        isLoading: false,
      );

      print(().toString());

      return true;
    } catch (e) {
      print(().toString());

      state = state.copyWith(
        error: 'Failed to update location: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Delete a location
  Future<bool> deleteLocation(int id) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print(().toString());

      final repository = ref.read(locationRepositoryProvider);
      final success = await repository.deleteLocation(id);

      if (success) {
        // Remove from current locations list
        final updatedLocations = state.locations.where((location) => location.id != id).toList();
        
        state = state.copyWith(
          locations: updatedLocations,
          clearSelectedLocation: state.selectedLocation?.id == id,
          isLoading: false,
        );

        print(().toString());
      } else {
        state = state.copyWith(
          error: 'Failed to delete location',
          isLoading: false,
        );
      }

      return success;
    } catch (e) {
      print(().toString());

      state = state.copyWith(
        error: 'Failed to delete location: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Check if location can be deleted
  Future<bool> canDeleteLocation(int id) async {
    try {
      // Check both repository and integration dependencies
      final repository = ref.read(locationRepositoryProvider);
      final integrationService = ref.read(locationIntegrationServiceProvider);

      final canDeleteFromRepo = await repository.canDeleteLocation(id);
      final dependencies = await integrationService.getLocationDependencies(id);

      final canDelete = canDeleteFromRepo && dependencies.canDelete;

      print('[LocationProvider] Location $id can delete: $canDelete (repo: $canDeleteFromRepo, dependencies: ${dependencies.canDelete})');

      return canDelete;
    } catch (e) {
      print(().toString());
      return true; // Assume we can try to delete
    }
  }

  /// Search locations
  Future<void> searchLocations(String query) async {
    if (query.isEmpty) {
      // If query is empty, reload all locations
      await loadLocations(forceRefresh: true);
      return;
    }

    await loadLocations(search: query, forceRefresh: true);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Clear selected location
  void clearSelectedLocation() {
    state = state.copyWith(clearSelectedLocation: true);
  }

  /// Refresh locations
  Future<void> refresh() async {
    await loadLocations(forceRefresh: true);
  }
}
