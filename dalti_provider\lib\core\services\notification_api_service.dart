import 'dart:io';
import '../network/http_client.dart';

/// API service for notification-related operations
class NotificationApiService {
  final HttpClient _httpClient;

  NotificationApiService(this._httpClient);

  /// Save FCM token to backend
  /// POST /api/auth/notifications/mobile/save-fcm-token
  Future<bool> saveFcmToken({
    required String fcmToken,
    String? userId,
  }) async {
    try {
      print('[NotificationApiService] Saving FCM token...');

      final data = {
        'fcmToken': fcmToken,
        'platform': Platform.isIOS ? 'ios' : 'android',
      };

      // Add userId if provided
      if (userId != null) {
        data['userId'] = userId;
      }

      final response = await _httpClient.post(
        '/api/auth/notifications/mobile/save-fcm-token',
        data: data,
      );

      print('[NotificationApiService] Save FCM token response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('[NotificationApiService] FCM token saved successfully');
        return true;
      } else {
        print('[NotificationApiService] Failed to save FCM token: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('[NotificationApiService] Error saving FCM token: $e');
      return false;
    }
  }

  /// Remove FCM token from backend
  /// DELETE /api/auth/notifications/mobile/remove-fcm-token (assuming this endpoint exists)
  Future<bool> removeFcmToken({
    required String fcmToken,
    String? userId,
  }) async {
    try {
      print('[NotificationApiService] Removing FCM token...');

      final data = {
        'fcmToken': fcmToken,
        'platform': Platform.isIOS ? 'ios' : 'android',
      };

      // Add userId if provided
      if (userId != null) {
        data['userId'] = userId;
      }

      final response = await _httpClient.delete(
        '/api/auth/notifications/mobile/remove-fcm-token',
        data: data,
      );

      print('[NotificationApiService] Remove FCM token response: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        print('[NotificationApiService] FCM token removed successfully');
        return true;
      } else {
        print('[NotificationApiService] Failed to remove FCM token: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('[NotificationApiService] Error removing FCM token: $e');
      return false;
    }
  }

  /// Update notification preferences
  /// PUT /api/auth/notifications/mobile/preferences (assuming this endpoint exists)
  Future<bool> updateNotificationPreferences({
    required Map<String, bool> preferences,
  }) async {
    try {
      print('[NotificationApiService] Updating notification preferences...');

      final response = await _httpClient.put(
        '/api/auth/notifications/mobile/preferences',
        data: {'preferences': preferences},
      );

      print('[NotificationApiService] Update preferences response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[NotificationApiService] Notification preferences updated successfully');
        return true;
      } else {
        print('[NotificationApiService] Failed to update notification preferences: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('[NotificationApiService] Error updating notification preferences: $e');
      return false;
    }
  }

  /// Get notification preferences
  /// GET /api/auth/notifications/mobile/preferences (assuming this endpoint exists)
  Future<Map<String, bool>?> getNotificationPreferences() async {
    try {
      print('[NotificationApiService] Getting notification preferences...');

      final response = await _httpClient.get('/api/auth/notifications/mobile/preferences');

      print('[NotificationApiService] Get preferences response: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final preferences = data['preferences'] as Map<String, dynamic>?;
        
        if (preferences != null) {
          return preferences.map((key, value) => MapEntry(key, value as bool));
        }
      }

      return null;
    } catch (e) {
      print('[NotificationApiService] Error getting notification preferences: $e');
      return null;
    }
  }
}
