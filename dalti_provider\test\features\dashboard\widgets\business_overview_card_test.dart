import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/dashboard/widgets/business_overview_card.dart';
import 'package:dalti_provider/features/dashboard/models/dashboard_models.dart';

void main() {
  group('BusinessOverviewCard Widget Tests', () {
    testWidgets('should display loading state when businessMetrics is null', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: null,
              isLoading: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Business Overview'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsWidgets);
      expect(find.byIcon(Icons.business_center), findsOneWidget);
    });

    testWidgets('should display business metrics when data is provided', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 1250.50,
        todayAppointments: 15,
        completedAppointments: 12,
        activeQueues: 3,
        totalCustomersToday: 45,
        customerSatisfaction: 4.5,
        revenueChange: 12.5,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Business Overview'), findsOneWidget);
      expect(find.text('\$1250'), findsOneWidget);
      expect(find.text('15'), findsOneWidget);
      expect(find.text('3'), findsOneWidget);
      expect(find.text('4.5'), findsOneWidget);
      expect(find.text('Today\'s Revenue'), findsOneWidget);
      expect(find.text('Appointments'), findsOneWidget);
      expect(find.text('Active Queues'), findsOneWidget);
      expect(find.text('Satisfaction'), findsOneWidget);
    });

    testWidgets('should display trend indicators when revenueChange is provided', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 1000.0,
        todayAppointments: 10,
        completedAppointments: 8,
        activeQueues: 2,
        totalCustomersToday: 30,
        customerSatisfaction: 4.2,
        revenueChange: 15.3,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('15.3%'), findsOneWidget);
      expect(find.byIcon(Icons.trending_up), findsOneWidget);
    });

    testWidgets('should display negative trend indicator for negative change', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 800.0,
        todayAppointments: 8,
        completedAppointments: 6,
        activeQueues: 1,
        totalCustomersToday: 20,
        customerSatisfaction: 4.0,
        revenueChange: -5.2,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('5.2%'), findsOneWidget);
      expect(find.byIcon(Icons.trending_down), findsOneWidget);
    });

    testWidgets('should display loading indicator when isLoading is true', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 1000.0,
        todayAppointments: 10,
        completedAppointments: 8,
        activeQueues: 2,
        totalCustomersToday: 30,
        customerSatisfaction: 4.2,
        revenueChange: 5.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsWidgets);
    });

    testWidgets('should have proper semantic labels for accessibility', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 1000.0,
        todayAppointments: 10,
        completedAppointments: 8,
        activeQueues: 2,
        totalCustomersToday: 30,
        customerSatisfaction: 4.2,
        revenueChange: 5.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.bySemanticsLabel('Business Overview Card'), findsOneWidget);
    });

    testWidgets('should display all metric items in grid layout', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 2000.0,
        todayAppointments: 20,
        completedAppointments: 18,
        activeQueues: 4,
        totalCustomersToday: 60,
        customerSatisfaction: 4.8,
        revenueChange: 8.5,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      // Check that all 4 metric items are displayed
      expect(find.byIcon(Icons.attach_money), findsOneWidget);
      expect(find.byIcon(Icons.event), findsOneWidget);
      expect(find.byIcon(Icons.queue), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);

      // Check metric values
      expect(find.text('\$2000'), findsOneWidget);
      expect(find.text('20'), findsOneWidget);
      expect(find.text('4'), findsOneWidget);
      expect(find.text('4.8'), findsOneWidget);

      // Check subtitles
      expect(find.text('18 completed'), findsOneWidget);
      expect(find.text('60 customers'), findsOneWidget);
      expect(find.text('Average rating'), findsOneWidget);
    });

    testWidgets('should handle zero values correctly', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 0.0,
        todayAppointments: 0,
        completedAppointments: 0,
        activeQueues: 0,
        totalCustomersToday: 0,
        customerSatisfaction: 0.0,
        revenueChange: 0.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('\$0'), findsOneWidget);
      expect(find.text('0'), findsWidgets);
      expect(find.text('0.0'), findsOneWidget);
    });

    testWidgets('should use RepaintBoundary for performance optimization', (tester) async {
      // Arrange
      final businessMetrics = BusinessMetrics(
        todayRevenue: 1000.0,
        todayAppointments: 10,
        completedAppointments: 8,
        activeQueues: 2,
        totalCustomersToday: 30,
        customerSatisfaction: 4.2,
        revenueChange: 5.0,
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessOverviewCard(
              businessMetrics: businessMetrics,
              isLoading: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(RepaintBoundary), findsOneWidget);
    });
  });
}
