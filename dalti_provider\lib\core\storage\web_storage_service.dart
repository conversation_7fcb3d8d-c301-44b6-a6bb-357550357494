import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Enhanced storage service that uses SharedPreferences for web and Hive for other platforms
class WebStorageService {
  static const String _settingsBox = 'settings';
  static const String _cacheBox = 'cache';
  static const String _authBox = 'auth';

  // SharedPreferences instance for web
  static SharedPreferences? _prefs;
  
  // Hive boxes for non-web platforms
  static Box? _settings;
  static Box? _cache;
  static Box? _auth;

  /// Initialize storage service
  static Future<void> init() async {
    if (kIsWeb) {
      // Use SharedPreferences for web
      _prefs = await SharedPreferences.getInstance();
      print('[WebStorageService] Initialized with SharedPreferences for web');
    } else {
      // Use Hive for other platforms
      await Hive.initFlutter();
      _settings = await Hive.openBox(_settingsBox);
      _cache = await Hive.openBox(_cacheBox);
      _auth = await Hive.openBox(_authBox);
      print('[WebStorageService] Initialized with Hive for non-web platforms');
    }
  }

  /// Initialize storage service for testing
  static Future<void> initForTesting() async {
    if (kIsWeb) {
      _prefs = await SharedPreferences.getInstance();
    } else {
      _settings = await Hive.openBox(_settingsBox);
      _cache = await Hive.openBox(_cacheBox);
      _auth = await Hive.openBox(_authBox);
    }
  }

  /// Save data to settings
  static Future<void> saveSetting(String key, dynamic value) async {
    if (kIsWeb) {
      await _saveToPrefs('${_settingsBox}_$key', value);
    } else {
      await _settings!.put(key, value);
    }
  }

  /// Get data from settings
  static T? getSetting<T>(String key, {T? defaultValue}) {
    if (kIsWeb) {
      return _getFromPrefs<T>('${_settingsBox}_$key', defaultValue: defaultValue);
    } else {
      return _settings!.get(key, defaultValue: defaultValue) as T?;
    }
  }

  /// Save data to cache
  static Future<void> saveCache(String key, dynamic value) async {
    if (kIsWeb) {
      await _saveToPrefs('${_cacheBox}_$key', value);
    } else {
      await _cache!.put(key, value);
    }
  }

  /// Get data from cache
  static T? getCache<T>(String key, {T? defaultValue}) {
    if (kIsWeb) {
      return _getFromPrefs<T>('${_cacheBox}_$key', defaultValue: defaultValue);
    } else {
      return _cache!.get(key, defaultValue: defaultValue) as T?;
    }
  }

  /// Remove specific cache data
  static Future<void> removeCache(String key) async {
    if (kIsWeb) {
      await _prefs!.remove('${_cacheBox}_$key');
    } else {
      await _cache!.delete(key);
    }
  }

  /// Save auth data
  static Future<void> saveAuth(String key, dynamic value) async {
    print('[WebStorageService] Saving auth data: $key');
    if (kIsWeb) {
      await _saveToPrefs('${_authBox}_$key', value);
      print('[WebStorageService] Saved to SharedPreferences: ${_authBox}_$key');
    } else {
      await _auth!.put(key, value);
      print('[WebStorageService] Saved to Hive: $key');
    }
  }

  /// Get auth data
  static T? getAuth<T>(String key, {T? defaultValue}) {
    if (kIsWeb) {
      final result = _getFromPrefs<T>('${_authBox}_$key', defaultValue: defaultValue);
      print('[WebStorageService] Retrieved from SharedPreferences: ${_authBox}_$key = ${result != null ? "EXISTS" : "NULL"}');
      return result;
    } else {
      final result = _auth!.get(key, defaultValue: defaultValue) as T?;
      print('[WebStorageService] Retrieved from Hive: $key = ${result != null ? "EXISTS" : "NULL"}');
      return result;
    }
  }

  /// Remove specific auth data
  static Future<void> removeAuth(String key) async {
    print('[WebStorageService] Removing auth data: $key');
    if (kIsWeb) {
      await _prefs!.remove('${_authBox}_$key');
    } else {
      await _auth!.delete(key);
    }
  }

  /// Clear all auth data
  static Future<void> clearAuth() async {
    print('[WebStorageService] Clearing all auth data');
    if (kIsWeb) {
      final keys = _prefs!.getKeys().where((key) => key.startsWith('${_authBox}_')).toList();
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    } else {
      await _auth!.clear();
    }
  }

  /// Clear all cache data
  static Future<void> clearCache() async {
    if (kIsWeb) {
      final keys = _prefs!.getKeys().where((key) => key.startsWith('${_cacheBox}_')).toList();
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    } else {
      await _cache!.clear();
    }
  }

  /// Clear all settings data
  static Future<void> clearSettings() async {
    if (kIsWeb) {
      final keys = _prefs!.getKeys().where((key) => key.startsWith('${_settingsBox}_')).toList();
      for (final key in keys) {
        await _prefs!.remove(key);
      }
    } else {
      await _settings!.clear();
    }
  }

  /// Clear all data
  static Future<void> clearAll() async {
    await Future.wait([
      clearAuth(),
      clearCache(),
      clearSettings(),
    ]);
  }

  /// Get all auth keys (for debugging)
  static List<String> getAuthKeys() {
    if (kIsWeb) {
      return _prefs!.getKeys().where((key) => key.startsWith('${_authBox}_')).toList();
    } else {
      return _auth!.keys.cast<String>().toList();
    }
  }

  /// Helper method to save to SharedPreferences
  static Future<void> _saveToPrefs(String key, dynamic value) async {
    if (value == null) {
      await _prefs!.remove(key);
      return;
    }

    if (value is String) {
      await _prefs!.setString(key, value);
    } else if (value is int) {
      await _prefs!.setInt(key, value);
    } else if (value is double) {
      await _prefs!.setDouble(key, value);
    } else if (value is bool) {
      await _prefs!.setBool(key, value);
    } else if (value is List<String>) {
      await _prefs!.setStringList(key, value);
    } else {
      // For complex objects, serialize to JSON
      final jsonString = json.encode(value);
      await _prefs!.setString(key, jsonString);
    }
  }

  /// Helper method to get from SharedPreferences
  static T? _getFromPrefs<T>(String key, {T? defaultValue}) {
    if (!_prefs!.containsKey(key)) {
      return defaultValue;
    }

    final value = _prefs!.get(key);
    
    if (value == null) {
      return defaultValue;
    }

    // If it's a string and we expect a complex object, try to decode JSON
    if (value is String && T != String) {
      try {
        final decoded = json.decode(value);
        return decoded as T;
      } catch (e) {
        // If JSON decode fails, return the string value or default
        return value as T? ?? defaultValue;
      }
    }

    return value as T? ?? defaultValue;
  }

  /// Close all storage (for cleanup)
  static Future<void> close() async {
    if (!kIsWeb) {
      await Future.wait([
        _settings?.close() ?? Future.value(),
        _cache?.close() ?? Future.value(),
        _auth?.close() ?? Future.value(),
      ]);
    }
    // SharedPreferences doesn't need explicit closing
  }
}
