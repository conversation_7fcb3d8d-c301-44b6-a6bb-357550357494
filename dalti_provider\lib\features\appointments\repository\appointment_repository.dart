import '../models/appointment_models.dart';

/// Abstract repository interface for appointment operations
abstract class AppointmentRepository {
  /// Get appointments with optional filters and pagination
  Future<List<Appointment>> getAppointments({
    AppointmentSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  });

  /// Get appointments for a specific date range
  Future<List<Appointment>> getAppointmentsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
    String? queueId,
  });

  /// Get appointments for a specific day
  Future<List<Appointment>> getAppointmentsByDate(DateTime date);

  /// Get a single appointment by ID
  Future<Appointment> getAppointment(String appointmentId);

  /// Create a new appointment
  Future<Appointment> createAppointment(AppointmentRequest request);

  /// Update an existing appointment
  Future<Appointment> updateAppointment(
    String appointmentId,
    UpdateAppointmentRequest request,
  );

  /// Update appointment status
  Future<Appointment> updateAppointmentStatus(
    String appointmentId,
    AppointmentStatus status, {
    String? notes,
  });

  /// Cancel an appointment
  Future<void> cancelAppointment(String appointmentId, String reason);

  /// Delete an appointment
  Future<void> deleteAppointment(String appointmentId);

  /// Get available time slots for a service on a specific date
  Future<List<TimeSlot>> getAvailableTimeSlots({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  });

  /// Get appointment statistics for a date range
  Future<AppointmentStats> getAppointmentStats({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  });

  /// Search appointments by customer name or service
  Future<List<Appointment>> searchAppointments(String query);

  /// Get upcoming appointments (next 7 days)
  Future<List<Appointment>> getUpcomingAppointments();

  /// Get today's appointments
  Future<List<Appointment>> getTodaysAppointments();

  /// Reschedule an appointment
  Future<Appointment> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDateTime,
    String? reason,
  });

  /// Mark appointment as completed
  Future<Appointment> completeAppointment(
    String appointmentId, {
    String? notes,
  });

  /// Mark appointment as no-show
  Future<Appointment> markNoShow(String appointmentId, {String? notes});

  /// Extend appointment duration
  Future<Appointment> extendAppointment(
    String appointmentId,
    int extensionMinutes,
  );

  /// Get calendar view data for a month
  Future<List<CalendarViewData>> getCalendarViewData({
    required DateTime startDate,
    required DateTime endDate,
  });
}
