import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/locations/models/location_models.dart';
import 'package:dalti_provider/features/services/models/service_models.dart';
import 'package:dalti_provider/features/queues/models/queue_models.dart';
import 'package:dalti_provider/features/schedules/models/opening_hours_models.dart';
import 'package:dalti_provider/features/onboarding/models/onboarding_models.dart';

void main() {
  group('Backward Compatibility Tests', () {
    group('Location Model Conversion', () {
      test('Legacy Location to Enhanced Location conversion', () {
        // Create legacy location
        final legacyLocation = Location(
          id: 1,
          name: 'Main Clinic',
          address: '123 Main St',
          city: 'Algiers',
          isMobileHidden: false,
          parking: true,
          elevator: false,
          handicapAccess: true,
        );

        // Convert to enhanced
        final enhancedLocation = EnhancedLocation.fromLegacyLocation(
          legacyLocation,
          shortName: 'Main',
          timezone: 'Africa/Algiers',
          latitude: 36.7538,
          longitude: 3.0588,
          openingHours: OpeningHours.standardBusiness(),
        );

        // Verify conversion
        expect(enhancedLocation.id, equals(legacyLocation.id));
        expect(enhancedLocation.name, equals(legacyLocation.name));
        expect(enhancedLocation.address, equals(legacyLocation.address));
        expect(enhancedLocation.city, equals(legacyLocation.city));
        expect(enhancedLocation.parking, equals(legacyLocation.parking));
        expect(enhancedLocation.elevator, equals(legacyLocation.elevator));
        expect(enhancedLocation.handicapAccess, equals(legacyLocation.handicapAccess));
        expect(enhancedLocation.shortName, equals('Main'));
        expect(enhancedLocation.timezone, equals('Africa/Algiers'));
        expect(enhancedLocation.hasCoordinates, isTrue);
        expect(enhancedLocation.hasOpeningHours, isTrue);
      });

      test('Enhanced Location to Legacy Location conversion', () {
        // Create enhanced location
        final enhancedLocation = EnhancedLocation(
          id: 1,
          name: 'Main Clinic',
          shortName: 'Main',
          address: '123 Main St',
          city: 'Algiers',
          country: 'Algeria',
          timezone: 'Africa/Algiers',
          latitude: 36.7538,
          longitude: 3.0588,
          parking: true,
          elevator: false,
          handicapAccess: true,
          openingHours: OpeningHours.standardBusiness(),
          isMobileHidden: false,
        );

        // Convert to legacy
        final legacyLocation = enhancedLocation.toLegacyLocation();

        // Verify conversion
        expect(legacyLocation.id, equals(enhancedLocation.id));
        expect(legacyLocation.name, equals(enhancedLocation.name));
        expect(legacyLocation.address, equals(enhancedLocation.address));
        expect(legacyLocation.city, equals(enhancedLocation.city));
        expect(legacyLocation.parking, equals(enhancedLocation.parking));
        expect(legacyLocation.elevator, equals(enhancedLocation.elevator));
        expect(legacyLocation.handicapAccess, equals(enhancedLocation.handicapAccess));
        expect(legacyLocation.isMobileHidden, equals(enhancedLocation.isMobileHidden));
      });

      test('Round-trip conversion preserves data', () {
        // Create legacy location
        final originalLegacy = Location(
          id: 1,
          name: 'Test Clinic',
          address: '456 Test Ave',
          city: 'Oran',
          isMobileHidden: true,
          parking: false,
          elevator: true,
          handicapAccess: false,
        );

        // Convert to enhanced and back to legacy
        final enhanced = EnhancedLocation.fromLegacyLocation(originalLegacy);
        final backToLegacy = enhanced.toLegacyLocation();

        // Verify data integrity
        expect(backToLegacy.id, equals(originalLegacy.id));
        expect(backToLegacy.name, equals(originalLegacy.name));
        expect(backToLegacy.address, equals(originalLegacy.address));
        expect(backToLegacy.city, equals(originalLegacy.city));
        expect(backToLegacy.isMobileHidden, equals(originalLegacy.isMobileHidden));
        expect(backToLegacy.parking, equals(originalLegacy.parking));
        expect(backToLegacy.elevator, equals(originalLegacy.elevator));
        expect(backToLegacy.handicapAccess, equals(originalLegacy.handicapAccess));
      });
    });

    group('Service Model Compatibility', () {
      test('Service with new optional fields maintains compatibility', () {
        // Create service with new fields
        final service = Service(
          id: 1,
          title: 'Consultation',
          duration: 30,
          color: '#FF5722',
          acceptOnline: true,
          acceptNew: true,
          notificationOn: true,
          pointsRequirements: 5,
          description: 'Medical consultation',
          price: 50.0,
          isActive: true,
          categoryId: 1,
          // New fields
          isPublic: true,
          deliveryType: 'at_location',
          servedRegions: null,
        );

        // Verify new field accessors work
        expect(service.isServicePublic, isTrue);
        expect(service.serviceDeliveryType, equals('at_location'));
        expect(service.isAtLocation, isTrue);
        expect(service.isAtCustomer, isFalse);
        expect(service.hasServedRegions, isFalse);
      });

      test('Service without new fields uses defaults', () {
        // Create service without new fields (legacy style)
        final service = Service(
          id: 1,
          title: 'Consultation',
          duration: 30,
          // New fields are null
        );

        // Verify defaults are applied
        expect(service.isServicePublic, isTrue); // Default to true
        expect(service.serviceDeliveryType, equals('at_location')); // Default
        expect(service.isAtLocation, isTrue);
        expect(service.isAtCustomer, isFalse);
        expect(service.hasServedRegions, isFalse);
      });

      test('Service conversion to/from CreateServiceRequest', () {
        // Create service
        final service = Service(
          id: 1,
          title: 'Home Visit',
          duration: 60,
          pointsRequirements: 10,
          isPublic: false,
          deliveryType: 'at_customer',
          servedRegions: ['Algiers', 'Oran'],
        );

        // Convert to CreateServiceRequest
        final createRequest = service.toCreateRequest();
        expect(createRequest.title, equals(service.title));
        expect(createRequest.duration, equals(service.duration));
        expect(createRequest.isPublic, equals(service.isServicePublic));
        expect(createRequest.deliveryType, equals(service.serviceDeliveryType));
        expect(createRequest.servedRegions, equals(service.servedRegions));

        // Convert back to Service
        final newService = Service.fromCreateRequest(createRequest, id: 2);
        expect(newService.title, equals(service.title));
        expect(newService.duration, equals(service.duration));
        expect(newService.isServicePublic, equals(service.isServicePublic));
        expect(newService.serviceDeliveryType, equals(service.serviceDeliveryType));
        expect(newService.servedRegions, equals(service.servedRegions));
      });
    });

    group('Queue Model Conversion', () {
      test('Legacy Queue to Enhanced Queue conversion', () {
        // Create legacy queue
        final legacyQueue = Queue(
          id: 1,
          title: 'General Queue',
          isActive: true,
          sProvidingPlaceId: 1,
          services: [
            QueueService(queueId: 1, serviceId: 1),
            QueueService(queueId: 1, serviceId: 2),
          ],
        );

        // Convert to enhanced
        final enhancedQueue = EnhancedQueue.fromLegacyQueue(
          legacyQueue,
          openingHours: OpeningHours.standardBusiness(),
        );

        // Verify conversion
        expect(enhancedQueue.id, equals(legacyQueue.id));
        expect(enhancedQueue.title, equals(legacyQueue.title));
        expect(enhancedQueue.isActive, equals(legacyQueue.isActive));
        expect(enhancedQueue.locationId, equals(legacyQueue.sProvidingPlaceId));
        expect(enhancedQueue.services.length, equals(legacyQueue.services.length));
        expect(enhancedQueue.hasCustomOpeningHours, isTrue);
        expect(enhancedQueue.inheritsOpeningHours, isFalse);
      });

      test('Enhanced Queue to Legacy Queue conversion', () {
        // Create enhanced queue
        final enhancedQueue = EnhancedQueue(
          id: 1,
          title: 'VIP Queue',
          isActive: true,
          locationId: 2,
          services: [
            QueueService(queueId: 1, serviceId: 3),
          ],
          openingHours: OpeningHours.allClosed(),
        );

        // Convert to legacy
        final legacyQueue = enhancedQueue.toLegacyQueue();

        // Verify conversion
        expect(legacyQueue.id, equals(enhancedQueue.id));
        expect(legacyQueue.title, equals(enhancedQueue.title));
        expect(legacyQueue.isActive, equals(enhancedQueue.isActive));
        expect(legacyQueue.sProvidingPlaceId, equals(enhancedQueue.locationId));
        expect(legacyQueue.services.length, equals(enhancedQueue.services.length));
      });
    });

    group('Opening Hours Model', () {
      test('OpeningHours legacy format conversion', () {
        // Create opening hours
        final openingHours = OpeningHours.standardBusiness();

        // Convert to legacy format
        final legacyFormat = openingHours.toLegacyFormat();

        // Verify legacy format
        expect(legacyFormat['monday_open'], equals('09:00'));
        expect(legacyFormat['monday_close'], equals('17:00'));
        expect(legacyFormat['friday_open'], equals('09:00'));
        expect(legacyFormat['friday_close'], equals('17:00'));
        expect(legacyFormat.containsKey('sunday_open'), isFalse);
        expect(legacyFormat.containsKey('saturday_open'), isFalse);

        // Convert back from legacy format
        final backToOpeningHours = OpeningHours.fromLegacyFormat(legacyFormat);
        expect(backToOpeningHours.isOpenOn('Monday'), isTrue);
        expect(backToOpeningHours.isOpenOn('Friday'), isTrue);
        expect(backToOpeningHours.isOpenOn('Sunday'), isFalse);
        expect(backToOpeningHours.isOpenOn('Saturday'), isFalse);
      });
    });
  });
}
