import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/algerian_cities.dart';
import '../services/location_service.dart';

class LocationFormFields {
  // Private constructor to prevent instantiation
  LocationFormFields._();

  /// Name field with validation
  static Widget nameField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      decoration: const InputDecoration(
        labelText: 'Location Name *',
        hintText: 'e.g., Main Clinic, Downtown Office',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.business),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Location name is required';
        }
        if (value.trim().length < 2) {
          return 'Location name must be at least 2 characters';
        }
        if (value.trim().length > 100) {
          return 'Location name must be less than 100 characters';
        }
        return null;
      },
      textCapitalization: TextCapitalization.words,
    );
  }

  /// Address field with validation
  static Widget addressField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      decoration: const InputDecoration(
        labelText: 'Street Address *',
        hintText: 'e.g., 123 Main Street',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_on),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Street address is required';
        }
        if (value.trim().length < 5) {
          return 'Please enter a complete address';
        }
        if (value.trim().length > 200) {
          return 'Address must be less than 200 characters';
        }
        return null;
      },
      textCapitalization: TextCapitalization.words,
      maxLines: 2,
    );
  }

  /// City field with Algerian cities dropdown
  static Widget cityField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return DropdownButtonFormField<String>(
      value: controller.text.isNotEmpty && AlgerianCities.isValidCity(controller.text)
          ? controller.text
          : null,
      decoration: const InputDecoration(
        labelText: 'City *',
        hintText: 'Select an Algerian city',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_city),
      ),
      items: AlgerianCities.sortedCities.map((String city) {
        return DropdownMenuItem<String>(
          value: city,
          child: Text(city),
        );
      }).toList(),
      onChanged: enabled ? (String? newValue) {
        if (newValue != null) {
          controller.text = newValue;
        }
      } : null,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'City is required';
        }
        if (!AlgerianCities.isValidCity(value)) {
          return 'Please select a valid Algerian city';
        }
        return null;
      },
      isExpanded: true,
    );
  }

  /// Country field - Fixed to Algeria and disabled
  static Widget countryField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    // Ensure controller always has Algeria as value
    if (controller.text != 'Algeria') {
      controller.text = 'Algeria';
    }

    return TextFormField(
      controller: controller,
      enabled: false, // Always disabled since we only work in Algeria
      decoration: const InputDecoration(
        labelText: 'Country *',
        hintText: 'Algeria',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.public),
        suffixIcon: Icon(Icons.lock, color: Colors.grey),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Country is required';
        }
        if (value.trim() != 'Algeria') {
          return 'Currently only available in Algeria';
        }
        return null;
      },
      style: TextStyle(
        color: Colors.grey.shade600,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Postal code field with validation
  static Widget postalCodeField({
    required TextEditingController controller,
    bool enabled = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      decoration: const InputDecoration(
        labelText: 'Postal Code *',
        hintText: 'e.g., M5V 3A8',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.markunread_mailbox),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Postal code is required';
        }
        if (value.trim().length < 3) {
          return 'Please enter a valid postal code';
        }
        if (value.trim().length > 10) {
          return 'Postal code must be less than 10 characters';
        }
        return null;
      },
      textCapitalization: TextCapitalization.characters,
    );
  }

  /// Latitude field with validation
  static Widget latitudeField({
    required TextEditingController controller,
    bool enabled = true,
    bool required = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      decoration: InputDecoration(
        labelText: required ? 'Latitude *' : 'Latitude',
        hintText: 'e.g., 43.6532',
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.my_location),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
      ],
      validator: (value) {
        if (required && (value == null || value.trim().isEmpty)) {
          return 'Latitude is required';
        }
        if (value != null && value.trim().isNotEmpty) {
          final latitude = double.tryParse(value.trim());
          if (latitude == null) {
            return 'Please enter a valid latitude';
          }
          if (latitude < -90 || latitude > 90) {
            return 'Latitude must be between -90 and 90';
          }
        }
        return null;
      },
    );
  }

  /// Longitude field with validation
  static Widget longitudeField({
    required TextEditingController controller,
    bool enabled = true,
    bool required = true,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      decoration: InputDecoration(
        labelText: required ? 'Longitude *' : 'Longitude',
        hintText: 'e.g., -79.3832',
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.place),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
      ],
      validator: (value) {
        if (required && (value == null || value.trim().isEmpty)) {
          return 'Longitude is required';
        }
        if (value != null && value.trim().isNotEmpty) {
          final longitude = double.tryParse(value.trim());
          if (longitude == null) {
            return 'Please enter a valid longitude';
          }
          if (longitude < -180 || longitude > 180) {
            return 'Longitude must be between -180 and 180';
          }
        }
        return null;
      },
    );
  }

  /// Amenities section with checkboxes
  static Widget amenitiesSection({
    required bool parking,
    required bool elevator,
    required bool handicapAccess,
    required bool enabled,
    required ValueChanged<bool> onParkingChanged,
    required ValueChanged<bool> onElevatorChanged,
    required ValueChanged<bool> onHandicapAccessChanged,
  }) {
    return Column(
      children: [
        CheckboxListTile(
          title: const Text('Parking Available'),
          subtitle: const Text('On-site parking for customers'),
          value: parking,
          onChanged: enabled ? (value) => onParkingChanged(value ?? false) : null,
          secondary: const Icon(Icons.local_parking),
        ),
        CheckboxListTile(
          title: const Text('Elevator Access'),
          subtitle: const Text('Building has elevator access'),
          value: elevator,
          onChanged: enabled ? (value) => onElevatorChanged(value ?? false) : null,
          secondary: const Icon(Icons.elevator),
        ),
        CheckboxListTile(
          title: const Text('Wheelchair Accessible'),
          subtitle: const Text('Accessible for people with disabilities'),
          value: handicapAccess,
          onChanged: enabled ? (value) => onHandicapAccessChanged(value ?? false) : null,
          secondary: const Icon(Icons.accessible),
        ),
      ],
    );
  }

  /// Coordinates widget with current location acquisition
  static Widget coordinatesWidget({
    required TextEditingController latitudeController,
    required TextEditingController longitudeController,
    required VoidCallback onLocationChanged,
    bool enabled = true,
  }) {
    return StatefulBuilder(
      builder: (context, setState) {
        bool isGettingLocation = false;
        String? locationStatus;

        Future<void> getCurrentLocation() async {
          setState(() {
            isGettingLocation = true;
            locationStatus = 'Getting current location...';
          });

          try {
            final result = await LocationService.getCurrentLocation();

            if (result.isSuccess) {
              latitudeController.text = result.latitude!.toStringAsFixed(6);
              longitudeController.text = result.longitude!.toStringAsFixed(6);
              setState(() {
                locationStatus = 'Location acquired successfully';
                isGettingLocation = false;
              });
              onLocationChanged();

              // Clear success message after 3 seconds
              Future.delayed(const Duration(seconds: 3), () {
                if (context.mounted) {
                  setState(() {
                    locationStatus = null;
                  });
                }
              });
            } else {
              setState(() {
                locationStatus = result.error;
                isGettingLocation = false;
              });
            }
          } catch (e) {
            setState(() {
              locationStatus = 'Error: ${e.toString()}';
              isGettingLocation = false;
            });
          }
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current location button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: enabled && !isGettingLocation ? getCurrentLocation : null,
                icon: isGettingLocation
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.my_location),
                label: Text(isGettingLocation
                    ? 'Getting Location...'
                    : 'Use Current Location'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            if (locationStatus != null) ...[
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: locationStatus!.contains('Error') || locationStatus!.contains('denied') || locationStatus!.contains('disabled')
                      ? Colors.red.shade50
                      : Colors.green.shade50,
                  border: Border.all(
                    color: locationStatus!.contains('Error') || locationStatus!.contains('denied') || locationStatus!.contains('disabled')
                        ? Colors.red.shade200
                        : Colors.green.shade200,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      locationStatus!.contains('Error') || locationStatus!.contains('denied') || locationStatus!.contains('disabled')
                          ? Icons.error_outline
                          : Icons.check_circle_outline,
                      color: locationStatus!.contains('Error') || locationStatus!.contains('denied') || locationStatus!.contains('disabled')
                          ? Colors.red.shade700
                          : Colors.green.shade700,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        locationStatus!,
                        style: TextStyle(
                          color: locationStatus!.contains('Error') || locationStatus!.contains('denied') || locationStatus!.contains('disabled')
                              ? Colors.red.shade700
                              : Colors.green.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Manual coordinate inputs (read-only, showing acquired coordinates)
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: latitudeController,
                    enabled: false, // Read-only, populated by location service
                    decoration: const InputDecoration(
                      labelText: 'Latitude *',
                      hintText: 'Will be filled automatically',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.my_location),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please get current location';
                      }
                      final lat = double.tryParse(value);
                      if (lat == null) {
                        return 'Invalid latitude';
                      }
                      if (!LocationService.isValidLatitude(lat)) {
                        return 'Latitude must be between -90 and 90';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: longitudeController,
                    enabled: false, // Read-only, populated by location service
                    decoration: const InputDecoration(
                      labelText: 'Longitude *',
                      hintText: 'Will be filled automatically',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.place),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please get current location';
                      }
                      final lng = double.tryParse(value);
                      if (lng == null) {
                        return 'Invalid longitude';
                      }
                      if (!LocationService.isValidLongitude(lng)) {
                        return 'Longitude must be between -180 and 180';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),

            if (latitudeController.text.isNotEmpty && longitudeController.text.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade700, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Coordinates: ${LocationService.formatCoordinates(
                          double.parse(latitudeController.text),
                          double.parse(longitudeController.text),
                        )}',
                        style: TextStyle(
                          color: Colors.blue.shade700,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}
