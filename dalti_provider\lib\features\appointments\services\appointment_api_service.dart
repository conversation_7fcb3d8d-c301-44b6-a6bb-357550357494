import '../../../core/network/http_client.dart';
import '../models/appointment_models.dart';

/// API service for appointment operations
class AppointmentApiService {
  final HttpClient _httpClient;

  AppointmentApiService(this._httpClient);

  /// Convert UTC time string to local time string
  String? _convertUtcToLocal(String utcTimeString) {
    if (utcTimeString.isEmpty) return null;

    try {
      // Parse the UTC time
      final utcDateTime = DateTime.parse(utcTimeString);

      // Convert to local time
      final localDateTime = utcDateTime.toLocal();

      // Debug logging for timezone conversion
      print(
        '[AppointmentApiService] UTC: $utcDateTime -> Local: $localDateTime',
      );

      // Return as ISO string (which will be parsed correctly by the model)
      return localDateTime.toIso8601String();
    } catch (e) {
      print('[AppointmentApiService] Error converting UTC time: $e');
      return null; // Return null if conversion fails
    }
  }

  /// Format DateTime to YYYY-MM-DD string for API
  String _formatDateForApi(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Format DateTime to YYYY-MM-DD THH:mm:ss string for API with time
  String _formatDateTimeForApi(DateTime dateTime) {
    return '${dateTime.year.toString().padLeft(4, '0')}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} T${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// Transform API response format to match Appointment model expectations
  Map<String, dynamic> _transformAppointmentData(Map<String, dynamic> apiData) {
    try {
      print(
        '[AppointmentApiService] Transforming appointment data: ${apiData['id']}',
      );

      final customer = apiData['customer'] as Map<String, dynamic>?;
      final service = apiData['service'] as Map<String, dynamic>?;
      final place = apiData['place'] as Map<String, dynamic>?;
      final queue = apiData['queue'] as Map<String, dynamic>?;

      // Map API status to our enum values
      String mapStatus(String? apiStatus) {
        print('[AppointmentApiService] Mapping status: "$apiStatus"');
        switch (apiStatus?.toLowerCase()) {
          case 'canceled':
            return 'canceled'; // API uses 'canceled', we use 'canceled'
          case 'in_progress':
          case 'inprogress':
            return 'InProgress'; // Return the exact JsonValue expected by our enum
          case 'no_show':
          case 'noshow':
            return 'no_show';
          default:
            // If the API returns 'InProgress' directly, keep it as is
            if (apiStatus == 'InProgress') {
              return 'InProgress';
            }
            return apiStatus?.toLowerCase() ?? 'scheduled';
        }
      }

      print('[AppointmentApiService] Mapping appointment data...');
      print('[AppointmentApiService] API data keys: ${apiData.keys.toList()}');
      print(
        '[AppointmentApiService] realAppointmentStartTime from API: ${apiData['realAppointmentStartTime']}',
      );

      final transformedData = {
        'id': apiData['id']?.toString() ?? '',
        'customerId': customer?['id']?.toString() ?? '',
        'customerName':
            '${customer?['firstName'] ?? ''} ${customer?['lastName'] ?? ''}'
                .trim(),
        'customerPhone': customer?['phone']?.toString(),
        'customerEmail': customer?['email']?.toString(),
        'serviceId': service?['id']?.toString() ?? '',
        'serviceName': service?['title']?.toString() ?? '',
        'scheduledTime':
            _convertUtcToLocal(
              apiData['expectedAppointmentStartTime']?.toString() ?? '',
            ) ??
            apiData['expectedAppointmentStartTime']?.toString() ??
            DateTime.now()
                .toIso8601String(), // Fallback to current time if all else fails
        'expectedEndTime':
            _convertUtcToLocal(
              apiData['expectedAppointmentEndTime']?.toString() ?? '',
            ) ??
            apiData['expectedAppointmentEndTime']?.toString(),
        'duration': service?['duration'] ?? 30, // Default to 30 minutes
        'status': mapStatus(apiData['status']?.toString()),
        'locationId': place?['id']?.toString(),
        'locationName': place?['name']?.toString(),
        'queueId': queue?['id']?.toString(),
        'queueName': queue?['title']?.toString() ?? queue?['name']?.toString(),
        'price': service?['price']?.toDouble(),
        'notes': apiData['notes']?.toString(),
        'createdAt':
            _convertUtcToLocal(apiData['createdAt']?.toString() ?? '') ??
            apiData['createdAt']?.toString(),
        'updatedAt':
            _convertUtcToLocal(apiData['updatedAt']?.toString() ?? '') ??
            apiData['updatedAt']?.toString(),
        'completedAt':
            _convertUtcToLocal(apiData['completedAt']?.toString() ?? '') ??
            apiData['completedAt']?.toString(),
        'realAppointmentStartTime':
            apiData['realAppointmentStartTime'] != null
                ? (_convertUtcToLocal(
                      apiData['realAppointmentStartTime']?.toString() ?? '',
                    ) ??
                    apiData['realAppointmentStartTime']?.toString())
                : null,
        'realAppointmentEndTime':
            apiData['realAppointmentEndTime'] != null
                ? (_convertUtcToLocal(
                      apiData['realAppointmentEndTime']?.toString() ?? '',
                    ) ??
                    apiData['realAppointmentEndTime']?.toString())
                : null,
      };

      print(
        '[AppointmentApiService] Transformed data: ${transformedData['customerName']} - ${transformedData['serviceName']} - Status: ${transformedData['status']}',
      );
      print(
        '[AppointmentApiService] Mapped realAppointmentStartTime: ${transformedData['realAppointmentStartTime']}',
      );
      return transformedData;
    } catch (e) {
      print('[AppointmentApiService] Error transforming appointment data: $e');
      print('[AppointmentApiService] Original data: $apiData');
      rethrow;
    }
  }

  /// Get appointments with filters and pagination
  Future<AppointmentsResponse> getAppointments({
    AppointmentSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print(
        '[AppointmentApiService] Fetching appointments - page: $page, pageSize: $pageSize',
      );

      final queryParams = <String, dynamic>{'page': page, 'pageSize': pageSize};

      // Add filters to query parameters
      if (filters != null) {
        if (filters.customerName != null) {
          queryParams['customerName'] = filters.customerName;
        }
        if (filters.serviceName != null) {
          queryParams['serviceName'] = filters.serviceName;
        }
        if (filters.locationId != null) {
          queryParams['locationId'] = filters.locationId;
        }
        if (filters.queueId != null) {
          queryParams['queueId'] = filters.queueId;
        }
        if (filters.status != null) {
          queryParams['status'] = filters.status!.name;
        }
        if (filters.startDate != null) {
          // API expects YYYY-MM-DD THH:mm:ss format with time
          final startDateFormatted = _formatDateTimeForApi(filters.startDate!);
          queryParams['startDate'] = startDateFormatted;
          print(
            '[AppointmentApiService] Formatted startDate: $startDateFormatted',
          );
        }
        if (filters.endDate != null) {
          // API expects YYYY-MM-DD THH:mm:ss format with time
          final endDateFormatted = _formatDateTimeForApi(filters.endDate!);
          queryParams['endDate'] = endDateFormatted;
          print('[AppointmentApiService] Formatted endDate: $endDateFormatted');
        }
        if (filters.tags != null && filters.tags!.isNotEmpty) {
          queryParams['tags'] = filters.tags!.join(',');
        }
      }

      final response = await _httpClient.get(
        '/api/auth/providers/appointments',
        queryParameters: queryParams,
      );

      print(
        '[AppointmentApiService] Appointments response: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Handle the nested response structure
        final data = responseData['data'] ?? responseData;
        final appointments =
            (data['appointments'] as List?)
                ?.map(
                  (json) =>
                      Appointment.fromJson(_transformAppointmentData(json)),
                )
                .toList() ??
            [];

        // Handle pagination info
        final pagination = data['pagination'] as Map<String, dynamic>?;

        return AppointmentsResponse(
          success: true,
          appointments: appointments,
          totalCount: pagination?['total'] ?? appointments.length,
          currentPage: pagination?['page'] ?? page,
          pageSize: pagination?['limit'] ?? pageSize,
        );
      } else {
        return AppointmentsResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to fetch appointments',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Get appointments error: $e');
      return AppointmentsResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get appointments by date range
  Future<AppointmentsResponse> getAppointmentsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
    String? queueId,
  }) async {
    try {
      print(
        '[AppointmentApiService] Fetching appointments by date range: $startDate - $endDate',
      );

      final queryParams = <String, dynamic>{
        'startDate': _formatDateTimeForApi(startDate),
        'endDate': _formatDateTimeForApi(endDate),
      };

      if (locationId != null) queryParams['locationId'] = locationId;
      if (queueId != null) queryParams['queueId'] = queueId;

      final response = await _httpClient.get(
        '/api/auth/providers/appointments/date-range',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Handle the nested response structure
        final data = responseData['data'] ?? responseData;
        final appointments =
            (data['appointments'] as List?)
                ?.map(
                  (json) =>
                      Appointment.fromJson(_transformAppointmentData(json)),
                )
                .toList() ??
            [];

        return AppointmentsResponse(
          success: true,
          appointments: appointments,
          totalCount: appointments.length,
        );
      } else {
        return AppointmentsResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to fetch appointments by date range',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Get appointments by date range error: $e');
      return AppointmentsResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get single appointment by ID
  Future<AppointmentResponse> getAppointment(String appointmentId) async {
    try {
      print('[AppointmentApiService] Fetching appointment: $appointmentId');

      final response = await _httpClient.get(
        '/api/auth/providers/appointments/$appointmentId',
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to fetch appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Get appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Create new appointment
  Future<AppointmentResponse> createAppointment(
    AppointmentRequest request,
  ) async {
    try {
      print('[AppointmentApiService] Creating appointment');

      final response = await _httpClient.post(
        '/api/auth/providers/appointments',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to create appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Create appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Create new appointment with the new API schema
  Future<AppointmentResponse> createNewAppointment(
    CreateAppointmentRequest request,
  ) async {
    try {
      print('[AppointmentApiService] Creating new appointment with schema');

      // Ensure we send proper ISO 8601 formatted date-time strings
      final startTimeIso = request.startTime.toUtc().toIso8601String();

      // Convert the request to the expected API format according to the API spec
      final requestData = {
        'customerId':
            request.customerUserId, // API expects customerId as string
        'serviceId': request.serviceId,
        'queueId': request.queueId,
        'expectedStartTime': startTimeIso, // API expects expectedStartTime
        if (request.notes != null && request.notes!.isNotEmpty)
          'notes': request.notes,
      };

      // Debug logging to see what we're sending
      print('[AppointmentApiService] Request data: $requestData');
      print(
        '[AppointmentApiService] Start time: ${request.startTime} -> $startTimeIso',
      );

      final response = await _httpClient.post(
        '/api/auth/providers/appointments',
        data: requestData,
      );

      print('[AppointmentApiService] Response status: ${response.statusCode}');
      if (response.statusCode != 201) {
        print('[AppointmentApiService] Response data: ${response.data}');
      }

      if (response.statusCode == 201) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        // Try to extract more detailed error information
        String errorMessage = 'Failed to create appointment';
        String errorDetails = 'Status code: ${response.statusCode}';

        if (response.data != null) {
          if (response.data is Map<String, dynamic>) {
            final data = response.data as Map<String, dynamic>;
            if (data.containsKey('message')) {
              errorMessage = data['message'].toString();
            }
            errorDetails = response.data.toString();
          } else {
            errorDetails = response.data.toString();
          }
        }

        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: errorMessage,
            details: errorDetails,
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Create new appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Update appointment
  Future<AppointmentResponse> updateAppointment(
    String appointmentId,
    UpdateAppointmentRequest request,
  ) async {
    try {
      print('[AppointmentApiService] Updating appointment: $appointmentId');

      final response = await _httpClient.put(
        '/api/auth/providers/appointments/$appointmentId',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to update appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Update appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Update appointment status
  Future<AppointmentResponse> updateAppointmentStatus(
    String appointmentId,
    AppointmentStatus status, {
    String? notes,
  }) async {
    try {
      print(
        '[AppointmentApiService] Updating appointment status: $appointmentId -> ${status.name}',
      );

      // Convert enum to the correct API status value
      String getApiStatusValue(AppointmentStatus status) {
        switch (status) {
          case AppointmentStatus.pending:
            return 'pending';
          case AppointmentStatus.scheduled:
            return 'scheduled';
          case AppointmentStatus.confirmed:
            return 'confirmed';
          case AppointmentStatus.inProgress:
            return 'InProgress'; // API expects 'InProgress' with capital P
          case AppointmentStatus.completed:
            return 'completed';
          case AppointmentStatus.canceled:
            return 'canceled';
          case AppointmentStatus.noShow:
            return 'no_show';
          case AppointmentStatus.rescheduled:
            return 'rescheduled';
        }
      }

      final apiStatusValue = getApiStatusValue(status);
      print('[AppointmentApiService] Sending status value: $apiStatusValue');

      final requestData = <String, dynamic>{'status': apiStatusValue};

      if (notes != null && notes.isNotEmpty) {
        requestData['notes'] = notes;
      }

      final response = await _httpClient.put(
        '/api/auth/providers/appointments/$appointmentId/status',
        data: requestData,
      );

      if (response.statusCode == 200) {
        // Status update endpoint returns limited data, but that's fine
        // The UI will close and refresh the appointments list anyway
        print('[AppointmentApiService] Status update successful');
        return AppointmentResponse(success: true);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to update appointment status',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Update appointment status error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Cancel appointment
  Future<AppointmentResponse> cancelAppointment(
    String appointmentId,
    String reason,
  ) async {
    try {
      print('[AppointmentApiService] Cancelling appointment: $appointmentId');

      final response = await _httpClient.patch(
        '/api/auth/providers/appointments/$appointmentId/cancel',
        data: {'reason': reason},
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to cancel appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Cancel appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Extend appointment duration
  Future<AppointmentResponse> extendAppointment(
    String appointmentId,
    int extensionMinutes,
  ) async {
    try {
      print(
        '[AppointmentApiService] Extending appointment: $appointmentId by $extensionMinutes minutes',
      );

      final requestData = <String, dynamic>{
        'appointmentId': int.parse(appointmentId),
        'extensionMinutes': extensionMinutes,
      };

      print('[AppointmentApiService] Extend request data: $requestData');

      final response = await _httpClient.put(
        '/api/auth/providers/time/appointments/extend',
        data: requestData,
      );

      print(
        '[AppointmentApiService] Extend response status: ${response.statusCode}',
      );
      print('[AppointmentApiService] Extend response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final appointment = Appointment.fromJson(
          _transformAppointmentData(data),
        );
        return AppointmentResponse(success: true, appointment: appointment);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to extend appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Extend appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Delete appointment
  Future<AppointmentResponse> deleteAppointment(String appointmentId) async {
    try {
      print('[AppointmentApiService] Deleting appointment: $appointmentId');

      final response = await _httpClient.delete(
        '/api/auth/providers/appointments/$appointmentId',
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        return const AppointmentResponse(success: true);
      } else {
        return AppointmentResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to delete appointment',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Delete appointment error: $e');
      return AppointmentResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get available time slots
  Future<List<TimeSlot>> getAvailableTimeSlots({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  }) async {
    try {
      print('[AppointmentApiService] Fetching available time slots for: $date');

      final queryParams = <String, dynamic>{
        'date': date.toIso8601String().split('T')[0], // YYYY-MM-DD format
        'serviceId': serviceId,
      };

      if (locationId != null) queryParams['locationId'] = locationId;
      if (queueId != null) queryParams['queueId'] = queueId;

      final response = await _httpClient.get(
        '/api/auth/providers/appointments/available-slots',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        final slots =
            (data['slots'] as List?)
                ?.map((json) => TimeSlot.fromJson(json))
                .toList() ??
            [];
        return slots;
      } else {
        print(
          '[AppointmentApiService] Failed to fetch time slots: ${response.statusCode}',
        );
        return [];
      }
    } catch (e) {
      print('[AppointmentApiService] Get available time slots error: $e');
      return [];
    }
  }

  /// Get appointment statistics
  Future<AppointmentStats> getAppointmentStats({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  }) async {
    try {
      print(
        '[AppointmentApiService] Fetching appointment stats: $startDate - $endDate',
      );

      final queryParams = <String, dynamic>{
        'startDate': _formatDateTimeForApi(startDate),
        'endDate': _formatDateTimeForApi(endDate),
      };

      if (locationId != null) queryParams['locationId'] = locationId;

      final response = await _httpClient.get(
        '/api/auth/providers/appointments/stats',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        final data = responseData['data'] ?? responseData;
        return AppointmentStats.fromJson(data);
      } else {
        print(
          '[AppointmentApiService] Failed to fetch appointment stats: ${response.statusCode}',
        );
        return const AppointmentStats();
      }
    } catch (e) {
      print('[AppointmentApiService] Get appointment stats error: $e');
      return const AppointmentStats();
    }
  }

  /// Search appointments
  Future<AppointmentsResponse> searchAppointments(String query) async {
    try {
      print('[AppointmentApiService] Searching appointments: $query');

      final response = await _httpClient.get(
        '/api/auth/providers/appointments/search',
        queryParameters: {'q': query},
      );

      if (response.statusCode == 200) {
        final responseData = response.data;

        // Handle the nested response structure
        final data = responseData['data'] ?? responseData;
        final appointments =
            (data['appointments'] as List?)
                ?.map(
                  (json) =>
                      Appointment.fromJson(_transformAppointmentData(json)),
                )
                .toList() ??
            [];

        return AppointmentsResponse(
          success: true,
          appointments: appointments,
          totalCount: appointments.length,
        );
      } else {
        return AppointmentsResponse(
          success: false,
          error: AppointmentError(
            code: 'API_ERROR',
            message: 'Failed to search appointments',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[AppointmentApiService] Search appointments error: $e');
      return AppointmentsResponse(
        success: false,
        error: AppointmentError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }
}
