import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';
import '../providers/customer_provider.dart';

/// Widget for performing bulk actions on selected customers
class CustomerBulkActions extends ConsumerStatefulWidget {
  final List<Customer> selectedCustomers;
  final VoidCallback onClearSelection;

  const CustomerBulkActions({
    super.key,
    required this.selectedCustomers,
    required this.onClearSelection,
  });

  @override
  ConsumerState<CustomerBulkActions> createState() => _CustomerBulkActionsState();
}

class _CustomerBulkActionsState extends ConsumerState<CustomerBulkActions> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    if (widget.selectedCustomers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Selection count
            Expanded(
              child: Text(
                '${widget.selectedCustomers.length} customer${widget.selectedCustomers.length == 1 ? '' : 's'} selected',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            
            // Actions
            if (_isLoading)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            else ...[
              // Export action
              IconButton(
                onPressed: _exportCustomers,
                icon: const Icon(Icons.download, color: Colors.white),
                tooltip: 'Export customers',
              ),
              
              // Tag action
              IconButton(
                onPressed: _addTagsToCustomers,
                icon: const Icon(Icons.label, color: Colors.white),
                tooltip: 'Add tags',
              ),
              
              // Status action
              IconButton(
                onPressed: _changeCustomerStatus,
                icon: const Icon(Icons.edit, color: Colors.white),
                tooltip: 'Change status',
              ),
              
              // Delete action
              IconButton(
                onPressed: _deleteCustomers,
                icon: const Icon(Icons.delete, color: Colors.white),
                tooltip: 'Delete customers',
              ),
            ],
            
            // Clear selection
            IconButton(
              onPressed: widget.onClearSelection,
              icon: const Icon(Icons.close, color: Colors.white),
              tooltip: 'Clear selection',
            ),
          ],
        ),
      ),
    );
  }

  void _exportCustomers() {
    // TODO: Implement customer export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality coming soon'),
      ),
    );
  }

  void _addTagsToCustomers() {
    showDialog(
      context: context,
      builder: (context) => _BulkTagDialog(
        customers: widget.selectedCustomers,
        onTagsAdded: (tags) {
          _performBulkTagUpdate(tags);
        },
      ),
    );
  }

  void _changeCustomerStatus() {
    showDialog(
      context: context,
      builder: (context) => _BulkStatusDialog(
        customers: widget.selectedCustomers,
        onStatusChanged: (status) {
          _performBulkStatusUpdate(status);
        },
      ),
    );
  }

  void _deleteCustomers() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Customers'),
        content: Text(
          'Are you sure you want to delete ${widget.selectedCustomers.length} customer${widget.selectedCustomers.length == 1 ? '' : 's'}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performBulkDelete();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _performBulkTagUpdate(List<String> tags) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement bulk tag update API
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Tags added to ${widget.selectedCustomers.length} customers'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onClearSelection();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update customers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _performBulkStatusUpdate(CustomerStatus status) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement bulk status update API
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Status updated for ${widget.selectedCustomers.length} customers'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onClearSelection();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update customers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _performBulkDelete() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final repository = ref.read(customerRepositoryProvider);
      
      // Delete customers one by one
      for (final customer in widget.selectedCustomers) {
        await repository.deleteCustomer(customer.id);
        ref.read(customerNotifierProvider.notifier).removeCustomer(customer.id);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${widget.selectedCustomers.length} customers deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onClearSelection();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete customers: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

/// Dialog for adding tags to multiple customers
class _BulkTagDialog extends StatefulWidget {
  final List<Customer> customers;
  final Function(List<String>) onTagsAdded;

  const _BulkTagDialog({
    required this.customers,
    required this.onTagsAdded,
  });

  @override
  State<_BulkTagDialog> createState() => _BulkTagDialogState();
}

class _BulkTagDialogState extends State<_BulkTagDialog> {
  final _tagController = TextEditingController();
  final List<String> _tags = [];

  @override
  void dispose() {
    _tagController.dispose();
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
      });
      _tagController.clear();
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Add Tags to ${widget.customers.length} Customers'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _tagController,
            decoration: InputDecoration(
              labelText: 'Add tag',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                onPressed: _addTag,
                icon: const Icon(Icons.add),
              ),
            ),
            onSubmitted: (_) => _addTag(),
          ),
          if (_tags.isNotEmpty) ...[
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _tags.map((tag) => Chip(
                label: Text(tag),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeTag(tag),
              )).toList(),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _tags.isEmpty ? null : () {
            Navigator.of(context).pop();
            widget.onTagsAdded(_tags);
          },
          child: const Text('Add Tags'),
        ),
      ],
    );
  }
}

/// Dialog for changing status of multiple customers
class _BulkStatusDialog extends StatelessWidget {
  final List<Customer> customers;
  final Function(CustomerStatus) onStatusChanged;

  const _BulkStatusDialog({
    required this.customers,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Change Status for ${customers.length} Customers'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: CustomerStatus.values.map((status) {
          return ListTile(
            title: Text(_getStatusLabel(status)),
            leading: Icon(
              _getStatusIcon(status),
              color: _getStatusColor(status),
            ),
            onTap: () {
              Navigator.of(context).pop();
              onStatusChanged(status);
            },
          );
        }).toList(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  String _getStatusLabel(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return 'Active';
      case CustomerStatus.inactive:
        return 'Inactive';
      case CustomerStatus.blocked:
        return 'Blocked';
    }
  }

  IconData _getStatusIcon(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return Icons.check_circle;
      case CustomerStatus.inactive:
        return Icons.pause_circle;
      case CustomerStatus.blocked:
        return Icons.block;
    }
  }

  Color _getStatusColor(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return Colors.green;
      case CustomerStatus.inactive:
        return Colors.orange;
      case CustomerStatus.blocked:
        return Colors.red;
    }
  }
}
