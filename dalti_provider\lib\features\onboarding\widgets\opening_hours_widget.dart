import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';

/// Time slot model for opening hours
class TimeSlot {
  final TimeOfDay from;
  final TimeOfDay to;

  const TimeSlot({required this.from, required this.to});

  TimeSlot copyWith({TimeOfDay? from, TimeOfDay? to}) {
    return TimeSlot(from: from ?? this.from, to: to ?? this.to);
  }

  @override
  String toString() {
    return '${formatTimeOfDay(from)} - ${formatTimeOfDay(to)}';
  }

  /// Format time of day for display
  String formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String get formattedTime {
    return '${formatTimeOfDay(from)} - ${formatTimeOfDay(to)}';
  }
}

/// Opening hours widget for setting business hours
class OpeningHoursWidget extends ConsumerStatefulWidget {
  final Map<String, List<TimeSlot>> initialData;
  final Function(Map<String, List<TimeSlot>>) onChanged;
  final String title;
  final String? subtitle;
  final bool showHeader;

  const OpeningHoursWidget({
    super.key,
    required this.initialData,
    required this.onChanged,
    this.title = 'Opening Hours',
    this.subtitle,
    this.showHeader = true,
  });

  @override
  ConsumerState<OpeningHoursWidget> createState() => _OpeningHoursWidgetState();
}

class _OpeningHoursWidgetState extends ConsumerState<OpeningHoursWidget> {
  late Map<String, List<TimeSlot>> _openingHours;
  late Map<String, bool> _isOpen;

  final List<String> _daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  @override
  void initState() {
    super.initState();
    print('[OpeningHoursWidget] initState called');
    print(
      '[OpeningHoursWidget] initialData keys: ${widget.initialData.keys.toList()}',
    );
    print('[OpeningHoursWidget] initialData content: ${widget.initialData}');

    _initializeData(widget.initialData);
  }

  @override
  void didUpdateWidget(OpeningHoursWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    print('[OpeningHoursWidget] didUpdateWidget called');
    print(
      '[OpeningHoursWidget] new initialData keys: ${widget.initialData.keys.toList()}',
    );
    print(
      '[OpeningHoursWidget] new initialData content: ${widget.initialData}',
    );

    // Reinitialize if data changed
    if (widget.initialData != oldWidget.initialData) {
      print('[OpeningHoursWidget] initialData changed, reinitializing');
      _initializeData(widget.initialData);
    }
  }

  void _initializeData(Map<String, List<TimeSlot>> data) {
    _openingHours = Map.from(data);

    // Initialize open/closed state for each day
    _isOpen = {};
    for (String day in _daysOfWeek) {
      final hasSlots = _openingHours[day]?.isNotEmpty ?? false;
      _isOpen[day] = hasSlots;
      print(
        '[OpeningHoursWidget] Day $day: isOpen=$hasSlots, slots=${_openingHours[day]?.length ?? 0}',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header (conditionally shown)
        if (widget.showHeader) ...[
          Row(
            children: [
              Icon(Icons.schedule, color: context.colors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                widget.title,
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (widget.subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.subtitle!,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
          const SizedBox(height: 16),
        ],

        // Days list
        ...(_daysOfWeek.map((day) => _buildDayRow(day))),

        const SizedBox(height: 8),
        Text(
          'Tip: Toggle the switch to mark a day as open or closed',
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colors.onSurfaceVariant,
          ),
        ),
      ],
    );

    // If showHeader is true, wrap in Card with padding
    // If showHeader is false, return content without Card (for custom containers)
    if (widget.showHeader) {
      return Card(
        child: Padding(padding: const EdgeInsets.all(16), child: content),
      );
    } else {
      return content;
    }
  }

  Widget _buildDayRow(String day) {
    final isOpen = _isOpen[day] ?? false;
    final timeSlots = _openingHours[day] ?? [];

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day name and switch row with enhanced styling
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                // Day name with better spacing
                Expanded(
                  flex: 2,
                  child: Text(
                    day,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // Status indicator text
                Expanded(
                  flex: 1,
                  child: Text(
                    isOpen ? 'Open' : 'Closed',
                    style: context.textTheme.bodySmall?.copyWith(
                      color:
                          isOpen
                              ? context.colors.primary
                              : context.colors.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Open/Closed switch with better styling
                Transform.scale(
                  scale: 0.9,
                  child: Switch(
                    value: isOpen,
                    onChanged: (value) {
                      setState(() {
                        _isOpen[day] = value;
                        if (value) {
                          // If opening, add default time slot if none exists
                          if (timeSlots.isEmpty) {
                            _openingHours[day] = [
                              TimeSlot(
                                from: const TimeOfDay(hour: 9, minute: 0),
                                to: const TimeOfDay(hour: 17, minute: 0),
                              ),
                            ];
                          }
                        } else {
                          // If closing, clear time slots
                          _openingHours[day] = [];
                        }
                      });
                      _notifyChange();
                    },
                    activeColor: context.colors.primary,
                    activeTrackColor: context.colors.primaryContainer,
                  ),
                ),

                const SizedBox(width: 8),

                // Add time slot button with enhanced styling
                Container(
                  width: 36,
                  height: 36,
                  child:
                      isOpen
                          ? Material(
                            color: context.colors.primaryContainer.withOpacity(
                              0.7,
                            ),
                            borderRadius: BorderRadius.circular(18),
                            child: InkWell(
                              onTap: () => _addTimeSlot(day),
                              borderRadius: BorderRadius.circular(18),
                              child: Icon(
                                Icons.add_circle_outline,
                                size: 20,
                                color: context.colors.primary,
                              ),
                            ),
                          )
                          : Container(
                            decoration: BoxDecoration(
                              color: context.colors.surfaceVariant.withOpacity(
                                0.5,
                              ),
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: Icon(
                              Icons.add_circle_outline,
                              size: 20,
                              color: context.colors.onSurfaceVariant
                                  .withOpacity(0.5),
                            ),
                          ),
                ),
              ],
            ),
          ),

          // Time slots section (below the day name)
          if (isOpen) ...[
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: _buildTimeSlots(day, timeSlots),
            ),
          ] else ...[
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Text(
                'Closed',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeSlots(String day, List<TimeSlot> timeSlots) {
    if (timeSlots.isEmpty) {
      return Text(
        'No time slots set',
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colors.onSurfaceVariant,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          timeSlots.asMap().entries.map((entry) {
            final index = entry.key;
            final timeSlot = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  // Time slot display - full width
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: context.colors.primaryContainer.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: context.colors.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          // From time - takes available space
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectTime(day, index, true),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: context.colors.surface,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: context.colors.outline.withOpacity(
                                      0.5,
                                    ),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    timeSlot.formatTimeOfDay(timeSlot.from),
                                    style: context.textTheme.bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // Separator
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Icon(
                              Icons.arrow_forward,
                              size: 20,
                              color: context.colors.primary,
                            ),
                          ),

                          // To time - takes available space
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectTime(day, index, false),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: context.colors.surface,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: context.colors.outline.withOpacity(
                                      0.5,
                                    ),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    timeSlot.formatTimeOfDay(timeSlot.to),
                                    style: context.textTheme.bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w600),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Remove time slot button
                  if (timeSlots.length > 1)
                    InkWell(
                      onTap: () => _removeTimeSlot(day, index),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: context.colors.errorContainer.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.remove_circle_outline,
                          size: 20,
                          color: context.colors.error,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
    );
  }

  void _addTimeSlot(String day) {
    setState(() {
      final timeSlots = _openingHours[day] ?? [];
      timeSlots.add(
        TimeSlot(
          from: const TimeOfDay(hour: 9, minute: 0),
          to: const TimeOfDay(hour: 17, minute: 0),
        ),
      );
      _openingHours[day] = timeSlots;
    });
    _notifyChange();
  }

  void _removeTimeSlot(String day, int index) {
    setState(() {
      final timeSlots = _openingHours[day] ?? [];
      if (timeSlots.length > index) {
        timeSlots.removeAt(index);
        _openingHours[day] = timeSlots;
      }
    });
    _notifyChange();
  }

  Future<void> _selectTime(
    String day,
    int timeSlotIndex,
    bool isFromTime,
  ) async {
    final currentTimeSlot = _openingHours[day]?[timeSlotIndex];
    if (currentTimeSlot == null) return;

    final initialTime = isFromTime ? currentTimeSlot.from : currentTimeSlot.to;

    final selectedTime = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (selectedTime != null) {
      setState(() {
        final timeSlots = _openingHours[day] ?? [];
        if (timeSlots.length > timeSlotIndex) {
          if (isFromTime) {
            timeSlots[timeSlotIndex] = currentTimeSlot.copyWith(
              from: selectedTime,
            );
          } else {
            timeSlots[timeSlotIndex] = currentTimeSlot.copyWith(
              to: selectedTime,
            );
          }
          _openingHours[day] = timeSlots;
        }
      });
      _notifyChange();
    }
  }

  void _notifyChange() {
    widget.onChanged(_openingHours);
  }
}
