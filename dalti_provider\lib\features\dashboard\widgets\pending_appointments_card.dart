import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../appointments/models/appointment_models.dart';
import '../../appointments/providers/appointment_provider.dart';

/// Widget that displays pending appointments with confirm/cancel actions
class PendingAppointmentsCard extends ConsumerStatefulWidget {
  const PendingAppointmentsCard({super.key});

  @override
  ConsumerState<PendingAppointmentsCard> createState() =>
      _PendingAppointmentsCardState();
}

class _PendingAppointmentsCardState
    extends ConsumerState<PendingAppointmentsCard> {
  Timer? _refreshTimer;
  final Set<String> _processingAppointments = {};

  @override
  void initState() {
    super.initState();
    // Load appointments when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPendingAppointments();
      _startAutoRefresh();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// Start auto-refresh timer for pending appointments
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (mounted) {
        _loadPendingAppointments();
      }
    });
  }

  /// Load appointments and filter for pending status
  Future<void> _loadPendingAppointments() async {
    try {
      print('[PendingAppointments] Loading appointments...');
      await ref.read(appointmentNotifierProvider.notifier).loadAppointments();
      print('[PendingAppointments] Appointments loaded successfully');
    } catch (e, stackTrace) {
      print('[PendingAppointments] Error loading appointments: $e');
      print('[PendingAppointments] Stack trace: $stackTrace');
    }
  }

  /// Force refresh appointments (used after status changes)
  Future<void> _refreshAppointments() async {
    try {
      print('[PendingAppointments] Force refreshing appointments...');
      await ref
          .read(appointmentNotifierProvider.notifier)
          .refreshAppointments();
      print('[PendingAppointments] Force refresh completed');
    } catch (e, stackTrace) {
      print('[PendingAppointments] Error force refreshing appointments: $e');
      print('[PendingAppointments] Stack trace: $stackTrace');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final appointmentsAsync = ref.watch(appointmentNotifierProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.pending_actions,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Pending Appointments',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (appointmentsAsync.isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                else
                  IconButton(
                    onPressed: _loadPendingAppointments,
                    icon: Icon(
                      Icons.refresh,
                      size: 20,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    tooltip: 'Refresh pending appointments',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: EdgeInsets.zero,
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Content
            appointmentsAsync.when(
              data: (appointments) {
                // Filter for truly pending appointments (pending status from API)
                final pendingAppointments =
                    appointments
                        .where((apt) => apt.status == AppointmentStatus.pending)
                        .toList();

                print(
                  '[PendingAppointments] Found ${pendingAppointments.length} pending appointments',
                );
                for (final apt in pendingAppointments) {
                  print(
                    '[PendingAppointments] - ${apt.customerName}: ${apt.status.name}',
                  );
                }

                if (pendingAppointments.isEmpty) {
                  return _buildNoPendingAppointmentsState(context);
                }

                return _buildPendingList(context, pendingAppointments);
              },
              loading: () => _buildLoadingState(context),
              error: (error, stack) => _buildErrorState(context, error),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the list of pending appointments
  Widget _buildPendingList(
    BuildContext context,
    List<Appointment> appointments,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Show count
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            '${appointments.length} appointment${appointments.length == 1 ? '' : 's'} awaiting action',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 12),

        // List of appointments
        ...appointments.map(
          (appointment) => _buildAppointmentTile(context, appointment),
        ),
      ],
    );
  }

  /// Build individual appointment tile
  Widget _buildAppointmentTile(BuildContext context, Appointment appointment) {
    final theme = Theme.of(context);
    final timeFormat = DateFormat('MMM dd, h:mm a');
    final scheduledTime = timeFormat.format(appointment.scheduledTime);
    final isProcessing = _processingAppointments.contains(appointment.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Customer and service info
            Row(
              children: [
                // Customer avatar
                CircleAvatar(
                  radius: 18,
                  backgroundColor: Colors.blue.withValues(alpha: 0.2),
                  child: Text(
                    appointment.customerName.isNotEmpty
                        ? appointment.customerName[0].toUpperCase()
                        : 'C',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Appointment details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment.customerName,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        appointment.serviceName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        scheduledTime,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.blue.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      appointment.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(appointment.status),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(appointment.status),
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Action buttons
            if (isProcessing)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(8),
                  child: CircularProgressIndicator(),
                ),
              )
            else
              Row(
                children: [
                  // Confirm button
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _confirmAppointment(appointment),
                      icon: const Icon(Icons.check, size: 16),
                      label: const Text('Confirm'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Cancel button
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _cancelAppointment(appointment),
                      icon: const Icon(Icons.close, size: 16),
                      label: const Text('Cancel'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Get status color
  Color _getStatusColor(AppointmentStatus status) {
    // Since we only show pending appointments, always return orange
    return Colors.orange;
  }

  /// Get status text
  String _getStatusText(AppointmentStatus status) {
    // Since we only show pending appointments, always return 'Pending'
    return 'Pending';
  }

  /// Confirm appointment
  Future<void> _confirmAppointment(Appointment appointment) async {
    setState(() {
      _processingAppointments.add(appointment.id);
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(appointment.id, AppointmentStatus.confirmed);

      if (mounted) {
        // Refresh the appointments list to update the UI
        await _refreshAppointments();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Appointment confirmed for ${appointment.customerName}',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to confirm appointment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _processingAppointments.remove(appointment.id);
        });
      }
    }
  }

  /// Cancel appointment
  Future<void> _cancelAppointment(Appointment appointment) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Appointment'),
            content: Text(
              'Are you sure you want to cancel the appointment for ${appointment.customerName}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Keep'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() {
      _processingAppointments.add(appointment.id);
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(appointment.id, AppointmentStatus.canceled);

      if (mounted) {
        // Refresh the appointments list to update the UI
        await _refreshAppointments();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Appointment canceled for ${appointment.customerName}',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to cancel appointment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _processingAppointments.remove(appointment.id);
        });
      }
    }
  }

  /// Build no pending appointments state
  Widget _buildNoPendingAppointmentsState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            'No Pending Appointments',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'All appointments are confirmed or processed',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(BuildContext context, Object error) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
          const SizedBox(height: 12),
          Text(
            'Failed to load pending appointments',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _loadPendingAppointments,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
