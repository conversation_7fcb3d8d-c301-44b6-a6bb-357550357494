import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';
import '../models/notification_models.dart';
import '../services/notifications_api_service.dart';

// API Service Provider
final notificationsApiServiceProvider = Provider<NotificationsApiService>((
  ref,
) {
  final httpClient = ref.watch(httpClientProvider);
  return NotificationsApiService(httpClient);
});

// Notifications State
class NotificationsState {
  final List<NotificationItem> notifications;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentOffset;

  const NotificationsState({
    this.notifications = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentOffset = 0,
  });

  NotificationsState copyWith({
    List<NotificationItem>? notifications,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentOffset,
  }) {
    return NotificationsState(
      notifications: notifications ?? this.notifications,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentOffset: currentOffset ?? this.currentOffset,
    );
  }
}

// Notifications Notifier
class NotificationsNotifier extends StateNotifier<NotificationsState> {
  final NotificationsApiService _apiService;
  static const int _pageSize = 20;

  NotificationsNotifier(this._apiService) : super(const NotificationsState());

  /// Load notifications (initial load)
  Future<void> loadNotifications({bool unreadOnly = false}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final notifications = await _apiService.getNotifications(
        unreadOnly: unreadOnly,
        limit: _pageSize,
        offset: 0,
      );

      state = state.copyWith(
        notifications: notifications,
        isLoading: false,
        hasMore: notifications.length >= _pageSize,
        currentOffset: notifications.length,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Load more notifications (pagination)
  Future<void> loadMoreNotifications({bool unreadOnly = false}) async {
    if (state.isLoading || !state.hasMore) return;

    try {
      final newNotifications = await _apiService.getNotifications(
        unreadOnly: unreadOnly,
        limit: _pageSize,
        offset: state.currentOffset,
      );

      final allNotifications = [...state.notifications, ...newNotifications];

      state = state.copyWith(
        notifications: allNotifications,
        hasMore: newNotifications.length >= _pageSize,
        currentOffset: allNotifications.length,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Refresh notifications
  Future<void> refreshNotifications({bool unreadOnly = false}) async {
    await loadNotifications(unreadOnly: unreadOnly);
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _apiService.markAsRead(notificationId);

      // Update local state
      final updatedNotifications =
          state.notifications.map((notification) {
            if (notification.id == notificationId) {
              return notification.copyWith(
                isRead: true,
                readAt: DateTime.now(),
              );
            }
            return notification;
          }).toList();

      state = state.copyWith(notifications: updatedNotifications);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await _apiService.markAllAsRead();

      // Update local state
      final updatedNotifications =
          state.notifications.map((notification) {
            return notification.copyWith(isRead: true, readAt: DateTime.now());
          }).toList();

      state = state.copyWith(notifications: updatedNotifications);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await _apiService.deleteNotification(notificationId);

      // Update local state
      final updatedNotifications =
          state.notifications
              .where((notification) => notification.id != notificationId)
              .toList();

      state = state.copyWith(
        notifications: updatedNotifications,
        currentOffset: updatedNotifications.length,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Get unread count
  int get unreadCount {
    return state.notifications
        .where((notification) => !notification.isRead)
        .length;
  }
}

// Notifications Provider
final notificationsProvider =
    StateNotifierProvider<NotificationsNotifier, NotificationsState>((ref) {
      final apiService = ref.watch(notificationsApiServiceProvider);
      return NotificationsNotifier(apiService);
    });

// Unread count provider
final unreadNotificationsCountProvider = Provider<int>((ref) {
  final notificationsState = ref.watch(notificationsProvider);
  return notificationsState.notifications
      .where((notification) => !notification.isRead)
      .length;
});
