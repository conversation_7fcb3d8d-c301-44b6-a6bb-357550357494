import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dashboard_provider.dart';
import '../models/dashboard_models.dart';

/// Emergency queue control widget for pause/resume all queues functionality
class EmergencyQueueControlWidget extends ConsumerStatefulWidget {
  const EmergencyQueueControlWidget({super.key});

  @override
  ConsumerState<EmergencyQueueControlWidget> createState() => _EmergencyQueueControlWidgetState();
}

class _EmergencyQueueControlWidgetState extends ConsumerState<EmergencyQueueControlWidget> {
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardNotifierProvider);
    final theme = Theme.of(context);
    
    // Get queue status from dashboard data
    final scheduleData = dashboardState.data?.scheduleData;
    final activeQueues = scheduleData?.queueStatuses.where((q) => q.isActive).toList() ?? [];
    final hasActiveQueues = activeQueues.isNotEmpty;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.emergency,
                  color: theme.colorScheme.error,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Emergency Controls',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Status display
            _buildStatusDisplay(context, activeQueues),
            const SizedBox(height: 16),

            // Control buttons
            Row(
              children: [
                // Pause all button
                Expanded(
                  child: _buildControlButton(
                    context: context,
                    label: 'Pause All',
                    icon: Icons.pause_circle,
                    color: Colors.orange,
                    isEnabled: hasActiveQueues && !_isProcessing,
                    onPressed: () => _handleEmergencyControl('pause'),
                  ),
                ),
                const SizedBox(width: 12),

                // Resume all button
                Expanded(
                  child: _buildControlButton(
                    context: context,
                    label: 'Resume All',
                    icon: Icons.play_circle,
                    color: Colors.green,
                    isEnabled: !_isProcessing,
                    onPressed: () => _handleEmergencyControl('resume'),
                  ),
                ),
              ],
            ),

            // Processing indicator
            if (_isProcessing) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Processing emergency control...',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build status display showing current queue states
  Widget _buildStatusDisplay(BuildContext context, List<QueueStatus> activeQueues) {
    final theme = Theme.of(context);

    if (activeQueues.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: theme.colorScheme.onSurfaceVariant,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'No active queues',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.green.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                '${activeQueues.length} Active Queue${activeQueues.length == 1 ? '' : 's'}',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...activeQueues.take(3).map((queue) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    queue.queueName,
                    style: theme.textTheme.bodySmall,
                  ),
                ),
                Text(
                  '${queue.waitingCount} waiting',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          )),
          if (activeQueues.length > 3)
            Text(
              '+${activeQueues.length - 3} more',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }

  /// Build control button
  Widget _buildControlButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required Color color,
    required bool isEnabled,
    required VoidCallback onPressed,
  }) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 48,
      child: ElevatedButton.icon(
        onPressed: isEnabled ? onPressed : null,
        icon: Icon(
          icon,
          size: 20,
          color: isEnabled ? color : theme.colorScheme.onSurfaceVariant,
        ),
        label: Text(
          label,
          style: TextStyle(
            color: isEnabled ? color : theme.colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isEnabled 
              ? color.withOpacity(0.1) 
              : theme.colorScheme.surfaceVariant.withOpacity(0.3),
          foregroundColor: color,
          elevation: 0,
          side: BorderSide(
            color: isEnabled 
                ? color.withOpacity(0.3) 
                : theme.colorScheme.onSurfaceVariant.withOpacity(0.2),
          ),
        ),
      ),
    );
  }

  /// Handle emergency control action
  Future<void> _handleEmergencyControl(String action) async {
    final confirmed = await _showConfirmationDialog(action);
    if (!confirmed) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final request = EmergencyQueueControl(
        action: action,
        reason: 'Emergency control triggered from dashboard',
        timestamp: DateTime.now(),
      );

      await ref.read(dashboardNotifierProvider.notifier).emergencyQueueControl(request);

      if (mounted) {
        _showSuccessSnackBar(action);
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(action, e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  /// Show confirmation dialog
  Future<bool> _showConfirmationDialog(String action) async {
    final theme = Theme.of(context);
    final actionTitle = action == 'pause' ? 'Pause All Queues' : 'Resume All Queues';
    final actionDescription = action == 'pause' 
        ? 'This will pause all active queues. Customers will not be served until queues are resumed.'
        : 'This will resume all paused queues and continue normal operations.';

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          action == 'pause' ? Icons.pause_circle : Icons.play_circle,
          color: action == 'pause' ? Colors.orange : Colors.green,
          size: 32,
        ),
        title: Text(actionTitle),
        content: Text(actionDescription),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: action == 'pause' ? Colors.orange : Colors.green,
            ),
            child: Text(action == 'pause' ? 'Pause' : 'Resume'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Show success snack bar
  void _showSuccessSnackBar(String action) {
    final message = action == 'pause' 
        ? 'All queues have been paused successfully'
        : 'All queues have been resumed successfully';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show error snack bar
  void _showErrorSnackBar(String action, String error) {
    final message = 'Failed to $action queues: $error';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () => _handleEmergencyControl(action),
        ),
      ),
    );
  }
}
