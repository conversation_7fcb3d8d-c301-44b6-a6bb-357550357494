import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/schedule_provider.dart';
import '../models/schedule_models.dart';
import '../../locations/providers/location_provider.dart';
import '../../locations/models/location_models.dart';

class ScheduleFormScreen extends ConsumerStatefulWidget {
  final int? scheduleId; // null for add, non-null for edit

  const ScheduleFormScreen({
    super.key,
    this.scheduleId,
  });

  @override
  ConsumerState<ScheduleFormScreen> createState() => _ScheduleFormScreenState();
}

class _ScheduleFormScreenState extends ConsumerState<ScheduleFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();

  int? _selectedLocationId;
  int _selectedDayOfWeek = 0;
  bool _isLoading = false;
  Schedule? _existingSchedule;

  final List<String> _daysOfWeek = [
    'Sunday',
    'Monday', 
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ];

  @override
  void initState() {
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
      
      if (widget.scheduleId != null) {
        _loadExistingSchedule();
      }
    });
  }

  @override
  void dispose() {
    _startTimeController.dispose();
    _endTimeController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingSchedule() async {
    try {
      setState(() => _isLoading = true);
      
      final repository = ref.read(scheduleRepositoryProvider);
      final schedule = await repository.getScheduleById(widget.scheduleId!);
      
      setState(() {
        _existingSchedule = schedule;
        _selectedLocationId = schedule.locationId;
        _selectedDayOfWeek = schedule.dayOfWeek;
        _startTimeController.text = schedule.startTime;
        _endTimeController.text = schedule.endTime;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading schedule: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationNotifierProvider);
    final isEditing = widget.scheduleId != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Schedule' : 'Add Schedule'),
        actions: [
          if (isEditing)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteSchedule,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Location selection
                    _buildLocationDropdown(locationState.locations),
                    const SizedBox(height: 16),
                    
                    // Day of week selection
                    _buildDayOfWeekDropdown(),
                    const SizedBox(height: 16),
                    
                    // Time selection
                    Row(
                      children: [
                        Expanded(child: _buildTimeField('Start Time', _startTimeController)),
                        const SizedBox(width: 16),
                        Expanded(child: _buildTimeField('End Time', _endTimeController)),
                      ],
                    ),
                    const SizedBox(height: 24),
                    
                    // Conflict warning
                    _buildConflictWarning(),
                    const SizedBox(height: 24),
                    
                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => context.pop(),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _saveSchedule,
                            child: Text(isEditing ? 'Update' : 'Create'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildLocationDropdown(List<Location> locations) {
    return DropdownButtonFormField<int>(
      value: _selectedLocationId,
      decoration: const InputDecoration(
        labelText: 'Location',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_on),
      ),
      items: locations.map((location) {
        return DropdownMenuItem<int>(
          value: location.id,
          child: Text(location.name),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedLocationId = value;
        });
      },
      validator: (value) {
        if (value == null) {
          return 'Please select a location';
        }
        return null;
      },
    );
  }

  Widget _buildDayOfWeekDropdown() {
    return DropdownButtonFormField<int>(
      value: _selectedDayOfWeek,
      decoration: const InputDecoration(
        labelText: 'Day of Week',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.calendar_today),
      ),
      items: _daysOfWeek.asMap().entries.map((entry) {
        return DropdownMenuItem<int>(
          value: entry.key,
          child: Text(entry.value),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedDayOfWeek = value!;
        });
      },
    );
  }

  Widget _buildTimeField(String label, TextEditingController controller) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.access_time),
        suffixIcon: IconButton(
          icon: const Icon(Icons.schedule),
          onPressed: () => _selectTime(controller),
        ),
      ),
      readOnly: true,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select $label';
        }
        return null;
      },
    );
  }

  Widget _buildConflictWarning() {
    if (_selectedLocationId == null || 
        _startTimeController.text.isEmpty || 
        _endTimeController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<List<Schedule>>(
      future: _checkConflicts(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        if (snapshot.hasError) {
          return const SizedBox.shrink();
        }

        final conflicts = snapshot.data ?? [];
        if (conflicts.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border.all(color: Colors.green.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green.shade600),
                const SizedBox(width: 8),
                Text(
                  'No conflicts found',
                  style: TextStyle(color: Colors.green.shade700),
                ),
              ],
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            border: Border.all(color: Colors.red.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade600),
                  const SizedBox(width: 8),
                  Text(
                    'Schedule Conflicts',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ...conflicts.map((conflict) {
                return Text(
                  '• ${conflict.dayName} ${conflict.timeRange}',
                  style: TextStyle(color: Colors.red.shade600),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  Future<void> _selectTime(TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      final formattedTime = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      controller.text = formattedTime;
      setState(() {}); // Trigger rebuild for conflict check
    }
  }

  Future<List<Schedule>> _checkConflicts() async {
    if (_selectedLocationId == null || 
        _startTimeController.text.isEmpty || 
        _endTimeController.text.isEmpty) {
      return [];
    }

    try {
      final conflicts = await ref.read(scheduleNotifierProvider.notifier).checkConflicts(
        dayOfWeek: _selectedDayOfWeek,
        startTime: _startTimeController.text,
        endTime: _endTimeController.text,
        locationId: _selectedLocationId!,
        excludeScheduleId: widget.scheduleId,
      );
      return conflicts;
    } catch (e) {
      return [];
    }
  }

  Future<void> _saveSchedule() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Check for conflicts
    final conflicts = await _checkConflicts();
    if (conflicts.isNotEmpty) {
      final shouldContinue = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Schedule Conflicts'),
          content: const Text(
            'This schedule conflicts with existing schedules. Do you want to continue anyway?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Continue'),
            ),
          ],
        ),
      );

      if (shouldContinue != true) {
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      final scheduleNotifier = ref.read(scheduleNotifierProvider.notifier);

      if (widget.scheduleId != null) {
        // Update existing schedule
        final request = UpdateScheduleRequest(
          dayOfWeek: _selectedDayOfWeek,
          startTime: _startTimeController.text,
          endTime: _endTimeController.text,
          locationId: _selectedLocationId!,
        );

        await scheduleNotifier.updateSchedule(widget.scheduleId!, request);
      } else {
        // Create new schedule
        final request = CreateScheduleRequest(
          dayOfWeek: _selectedDayOfWeek,
          startTime: _startTimeController.text,
          endTime: _endTimeController.text,
          locationId: _selectedLocationId!,
        );

        await scheduleNotifier.createSchedule(request);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.scheduleId != null 
                ? 'Schedule updated successfully' 
                : 'Schedule created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving schedule: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteSchedule() async {
    if (widget.scheduleId == null) return;

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Schedule'),
        content: const Text('Are you sure you want to delete this schedule?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      setState(() => _isLoading = true);

      try {
        await ref.read(scheduleNotifierProvider.notifier).deleteSchedule(widget.scheduleId!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Schedule deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting schedule: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }
}
