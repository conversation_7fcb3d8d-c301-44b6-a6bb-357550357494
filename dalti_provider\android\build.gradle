buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0' // Or your AGP version
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.0' // Updated to 2.0.0
        classpath 'com.google.gms:google-services:4.4.2' // Your Google Services plugin version
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}

