/// Simple message models without freezed for quick implementation

/// Conversation status enum
enum ConversationStatus { active, archived, blocked, deleted }

/// Message status enum
enum MessageStatus { sent, delivered, read, failed }

/// Message type enum
enum MessageType { text, image, file, audio, video, other }

/// Conversation model representing a chat conversation with a customer
class Conversation {
  final String id;
  final String customerId;
  final String customerName;
  final String? customerAvatar;
  final String? customerPhone;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Message? lastMessage;
  final int unreadCount;
  final ConversationStatus status;
  final List<String> participantIds;
  final Map<String, dynamic>? metadata;

  const Conversation({
    required this.id,
    required this.customerId,
    required this.customerName,
    this.customerAvatar,
    this.customerPhone,
    required this.createdAt,
    this.updatedAt,
    this.lastMessage,
    this.unreadCount = 0,
    this.status = ConversationStatus.active,
    this.participantIds = const [],
    this.metadata,
  });

  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'].toString(),
      customerId: json['customerId'] ?? '',
      customerName: json['customerName'] ?? '',
      customerAvatar: json['customerAvatar'],
      customerPhone: json['customerPhone'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      lastMessage:
          json['lastMessage'] != null
              ? Message.fromJson(json['lastMessage'])
              : null,
      unreadCount: json['unreadCount'] ?? 0,
      status: ConversationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ConversationStatus.active,
      ),
      participantIds: List<String>.from(json['participantIds'] ?? []),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customerId': customerId,
      'customerName': customerName,
      'customerAvatar': customerAvatar,
      'customerPhone': customerPhone,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'lastMessage': lastMessage?.toJson(),
      'unreadCount': unreadCount,
      'status': status.name,
      'participantIds': participantIds,
      'metadata': metadata,
    };
  }
}

/// Message model representing individual messages in a conversation
class Message {
  final String id;
  final String conversationId;
  final String senderId;
  final String senderName;
  final MessageType type;
  final String content;
  final DateTime timestamp;
  final MessageStatus status;
  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;
  final DateTime? readAt;
  final DateTime? deliveredAt;

  const Message({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.senderName,
    required this.type,
    required this.content,
    required this.timestamp,
    this.status = MessageStatus.sent,
    this.replyToMessageId,
    this.metadata,
    this.readAt,
    this.deliveredAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    return Message(
      id: json['id'].toString(),
      conversationId: json['conversationId'].toString(),
      senderId: json['senderId'] ?? '',
      senderName: json['senderName'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      content: json['content'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? json['createdAt']),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      replyToMessageId: json['replyToMessageId'],
      metadata: json['metadata'],
      readAt: json['readAt'] != null ? DateTime.parse(json['readAt']) : null,
      deliveredAt:
          json['deliveredAt'] != null
              ? DateTime.parse(json['deliveredAt'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'conversationId': conversationId,
      'senderId': senderId,
      'senderName': senderName,
      'type': type.name,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'replyToMessageId': replyToMessageId,
      'metadata': metadata,
      'readAt': readAt?.toIso8601String(),
      'deliveredAt': deliveredAt?.toIso8601String(),
    };
  }
}

/// Request model for sending a new message
class SendMessageRequest {
  final String conversationId;
  final MessageType type;
  final String content;
  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;

  const SendMessageRequest({
    required this.conversationId,
    required this.type,
    required this.content,
    this.replyToMessageId,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'conversationId': conversationId,
      'type': type.name,
      'content': content,
      'replyToMessageId': replyToMessageId,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toBackendJson() {
    return {
      'conversationId': int.tryParse(conversationId) ?? conversationId,
      'content': content,
    };
  }
}

/// Request model for creating a new conversation
class CreateConversationRequest {
  final String customerId;
  final String? initialMessage;
  final Map<String, dynamic>? metadata;

  const CreateConversationRequest({
    required this.customerId,
    this.initialMessage,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'initialMessage': initialMessage,
      'metadata': metadata,
    };
  }

  Map<String, dynamic> toBackendJson() {
    return {
      'otherUserIds': [customerId],
      'isGroup': false,
      if (initialMessage != null) 'initialMessage': initialMessage,
      if (metadata != null) 'metadata': metadata,
    };
  }
}

/// Error model for message operations
class MessageError {
  final String code;
  final String message;
  final String? details;

  const MessageError({required this.code, required this.message, this.details});

  factory MessageError.fromJson(Map<String, dynamic> json) {
    return MessageError(
      code: json['code'] ?? 'UNKNOWN_ERROR',
      message: json['message'] ?? 'An unknown error occurred',
      details: json['details'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'code': code, 'message': message, 'details': details};
  }
}

/// Response models
class ConversationsResponse {
  final bool success;
  final List<Conversation> conversations;
  final int totalCount;
  final int unreadCount;
  final MessageError? error;

  const ConversationsResponse({
    required this.success,
    this.conversations = const [],
    this.totalCount = 0,
    this.unreadCount = 0,
    this.error,
  });
}

class ConversationResponse {
  final bool success;
  final Conversation? conversation;
  final MessageError? error;

  const ConversationResponse({
    required this.success,
    this.conversation,
    this.error,
  });
}

class MessagesResponse {
  final bool success;
  final List<Message> messages;
  final int totalCount;
  final MessageError? error;

  const MessagesResponse({
    required this.success,
    this.messages = const [],
    this.totalCount = 0,
    this.error,
  });
}

class SendMessageResponse {
  final bool success;
  final Message? message;
  final MessageError? error;

  const SendMessageResponse({required this.success, this.message, this.error});
}

/// Customer model for conversation creation
class CustomerForConversation {
  final String id;
  final String folderId;
  final String firstName;
  final String lastName;
  final String? email;
  final String? mobileNumber;

  const CustomerForConversation({
    required this.id,
    required this.folderId,
    required this.firstName,
    required this.lastName,
    this.email,
    this.mobileNumber,
  });

  String get displayName => '$firstName $lastName'.trim();
  String get initials {
    final first = firstName.isNotEmpty ? firstName[0] : '';
    final last = lastName.isNotEmpty ? lastName[0] : '';
    return '$first$last'.toUpperCase();
  }
}

/// Request model for marking a message as read
class MarkAsReadRequest {
  final String messageId;
  final String conversationId;

  const MarkAsReadRequest({
    required this.messageId,
    required this.conversationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'messageId': int.tryParse(messageId) ?? messageId,
      'conversationId': int.tryParse(conversationId) ?? conversationId,
    };
  }
}

/// Response model for mark as read operation
class MarkAsReadResponse {
  final bool success;
  final MessageError? error;

  const MarkAsReadResponse({required this.success, this.error});

  factory MarkAsReadResponse.fromJson(Map<String, dynamic> json) {
    return MarkAsReadResponse(
      success: json['success'] ?? false,
      error:
          json['error'] != null ? MessageError.fromJson(json['error']) : null,
    );
  }
}
