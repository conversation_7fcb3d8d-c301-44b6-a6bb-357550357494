import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import '../../customers/models/customer_models.dart';
import '../../customers/providers/customer_provider.dart';
import '../../services/models/service_models.dart';
import '../../services/providers/service_provider.dart';
import '../../locations/models/location_models.dart';
import '../../locations/providers/location_provider.dart';
import '../../queues/models/queue_models.dart';
import '../../queues/providers/queue_provider.dart';
import '../../../core/theme/app_colors.dart';

class AddAppointmentScreen extends ConsumerStatefulWidget {
  const AddAppointmentScreen({super.key});

  @override
  ConsumerState<AddAppointmentScreen> createState() =>
      _AddAppointmentScreenState();
}

class _AddAppointmentScreenState extends ConsumerState<AddAppointmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  Customer? _selectedCustomer;
  Service? _selectedService;
  Location? _selectedLocation;
  Queue? _selectedQueue;
  DateTime? _startTime;
  DateTime? _endTime;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Check for preset time from navigation
    _checkForPresetTime();

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _checkForPresetTime() {
    // Get preset time from GoRouter extra data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final extra = GoRouterState.of(context).extra;
      if (extra is Map<String, dynamic> && extra.containsKey('presetTime')) {
        final presetTime = extra['presetTime'] as DateTime?;
        if (presetTime != null) {
          setState(() {
            _startTime = presetTime;
            // Don't set end time yet - wait for service selection
          });
        }
      }
    });
  }

  void _loadData() {
    // Load customers, services, locations, and queues
    ref.read(customerNotifierProvider.notifier).loadCustomers();
    ref.read(serviceNotifierProvider.notifier).loadServices();
    ref.read(locationNotifierProvider.notifier).loadLocations();
    ref.read(queueNotifierProvider.notifier).loadQueues();
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _onServiceChanged(Service? service) {
    setState(() {
      _selectedService = service;
      // Auto-calculate end time based on service duration
      if (service != null && _startTime != null) {
        _endTime = _startTime!.add(Duration(minutes: service.duration));
      }
    });
  }

  void _onStartTimeChanged(DateTime? startTime) {
    setState(() {
      _startTime = startTime;
      // Auto-calculate end time based on service duration
      if (startTime != null && _selectedService != null) {
        _endTime = startTime.add(Duration(minutes: _selectedService!.duration));
      }
    });
  }

  void _onLocationChanged(Location? location) {
    setState(() {
      _selectedLocation = location;
      _selectedQueue = null; // Reset queue when location changes
    });
  }

  List<Queue> _getFilteredQueues() {
    final queueState = ref.watch(queueNotifierProvider);
    if (_selectedLocation == null || queueState.queues.isEmpty) {
      return [];
    }
    return queueState.queues
        .where((queue) => queue.locationId == _selectedLocation!.id)
        .toList();
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCustomer == null ||
        _selectedService == null ||
        _selectedLocation == null ||
        _selectedQueue == null ||
        _startTime == null ||
        _endTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Additional validation for DateTime objects
    if (_startTime!.isAfter(_endTime!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Start time must be before end time'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Ensure DateTime objects have proper precision (include seconds)
      final startTime = DateTime(
        _startTime!.year,
        _startTime!.month,
        _startTime!.day,
        _startTime!.hour,
        _startTime!.minute,
        0, // seconds
        0, // milliseconds
      );

      final endTime = DateTime(
        _endTime!.year,
        _endTime!.month,
        _endTime!.day,
        _endTime!.hour,
        _endTime!.minute,
        0, // seconds
        0, // milliseconds
      );

      // Debug logging
      print(
        '[AddAppointmentScreen] Start time: $startTime (${startTime.toIso8601String()})',
      );
      print(
        '[AddAppointmentScreen] End time: $endTime (${endTime.toIso8601String()})',
      );

      final request = CreateAppointmentRequest(
        customerUserId: _selectedCustomer!.id,
        serviceId: _selectedService!.id,
        placeId: _selectedLocation!.id,
        queueId: _selectedQueue!.id,
        startTime: startTime,
        endTime: endTime,
        notes:
            _notesController.text.trim().isNotEmpty
                ? _notesController.text.trim()
                : null,
      );

      print(
        '[AddAppointmentScreen] Creating appointment request: ${request.toJson()}',
      );

      await ref
          .read(appointmentNotifierProvider.notifier)
          .addNewAppointment(request);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment created successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } catch (e) {
      print('[AddAppointmentScreen] Error creating appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create appointment: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Appointment'),
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Customer Selection
            _buildCustomerDropdown(),
            const SizedBox(height: 16),

            // Service Selection
            _buildServiceDropdown(),
            const SizedBox(height: 16),

            // Location Selection
            _buildLocationDropdown(),
            const SizedBox(height: 16),

            // Queue Selection
            _buildQueueDropdown(),
            const SizedBox(height: 16),

            // Time Selection
            _buildTimeSelection(),
            const SizedBox(height: 16),

            // Notes
            _buildNotesField(),
            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerDropdown() {
    final customerState = ref.watch(customerNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Customer *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child:
              customerState.isLoading
                  ? DropdownButtonFormField<Customer>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Loading customers...',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : customerState.hasError
                  ? DropdownButtonFormField<Customer>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Error loading customers',
                      errorText: customerState.error,
                    ),
                    items: const [],
                    onChanged: null,
                  )
                  : DropdownButtonFormField<Customer>(
                    value: _selectedCustomer,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Select a customer',
                    ),
                    isExpanded: true,
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey.shade600,
                    ),
                    validator:
                        (value) =>
                            value == null ? 'Please select a customer' : null,
                    selectedItemBuilder: (context) {
                      return customerState.customers.map((customer) {
                        return Row(
                          children: [
                            CircleAvatar(
                              radius: 12,
                              backgroundColor: AppColors.primary.withValues(
                                alpha: 0.1,
                              ),
                              child: Text(
                                '${customer.firstName[0]}${customer.lastName[0]}',
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '${customer.firstName} ${customer.lastName}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        );
                      }).toList();
                    },
                    items:
                        customerState.customers.map((customer) {
                          return DropdownMenuItem<Customer>(
                            value: customer,
                            child: SizedBox(
                              height: 48,
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    radius: 16,
                                    backgroundColor: AppColors.primary
                                        .withValues(alpha: 0.1),
                                    child: Text(
                                      '${customer.firstName[0]}${customer.lastName[0]}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '${customer.firstName} ${customer.lastName}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        if (customer.phoneNumber != null &&
                                            customer.phoneNumber!.isNotEmpty)
                                          Text(
                                            customer.phoneNumber!,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                    onChanged: (customer) {
                      setState(() {
                        _selectedCustomer = customer;
                      });
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildServiceDropdown() {
    final serviceState = ref.watch(serviceNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child:
              serviceState.isLoading
                  ? DropdownButtonFormField<Service>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Loading services...',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : serviceState.hasError
                  ? DropdownButtonFormField<Service>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Error loading services',
                      errorText: serviceState.error,
                    ),
                    items: const [],
                    onChanged: null,
                  )
                  : DropdownButtonFormField<Service>(
                    value: _selectedService,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Select a service',
                    ),
                    isExpanded: true,
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey.shade600,
                    ),
                    validator:
                        (value) =>
                            value == null ? 'Please select a service' : null,
                    selectedItemBuilder: (context) {
                      return serviceState.services.map((service) {
                        return Row(
                          children: [
                            Expanded(
                              child: Text(
                                service.title,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Text(
                              '${service.duration}min',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        );
                      }).toList();
                    },
                    items:
                        serviceState.services.map((service) {
                          return DropdownMenuItem<Service>(
                            value: service,
                            child: SizedBox(
                              height: 48,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    service.title,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 2),
                                  Row(
                                    children: [
                                      Flexible(
                                        child: Text(
                                          '${service.duration} min',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '•',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Flexible(
                                        child: Text(
                                          service.price != null
                                              ? '${service.price} DA'
                                              : 'Price not set',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                    onChanged: _onServiceChanged,
                  ),
        ),
      ],
    );
  }

  Widget _buildLocationDropdown() {
    final locationState = ref.watch(locationNotifierProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child:
              locationState.isLoading
                  ? DropdownButtonFormField<Location>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Loading locations...',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : locationState.hasError
                  ? DropdownButtonFormField<Location>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Error loading locations',
                      errorText: locationState.error,
                    ),
                    items: const [],
                    onChanged: null,
                  )
                  : DropdownButtonFormField<Location>(
                    value: _selectedLocation,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Select a location',
                    ),
                    isExpanded: true,
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey.shade600,
                    ),
                    validator:
                        (value) =>
                            value == null ? 'Please select a location' : null,
                    selectedItemBuilder: (context) {
                      return locationState.locations.map((location) {
                        return Row(
                          children: [
                            Expanded(
                              child: Text(
                                location.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        );
                      }).toList();
                    },
                    items:
                        locationState.locations.map((location) {
                          return DropdownMenuItem<Location>(
                            value: location,
                            child: SizedBox(
                              height: 48,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    location.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  if (location.address.isNotEmpty) ...[
                                    const SizedBox(height: 2),
                                    Text(
                                      location.address,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                    onChanged: _onLocationChanged,
                  ),
        ),
      ],
    );
  }

  Widget _buildQueueDropdown() {
    final queueState = ref.watch(queueNotifierProvider);
    final filteredQueues = _getFilteredQueues();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Queue *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child:
              queueState.isLoading
                  ? DropdownButtonFormField<Queue>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Loading queues...',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : queueState.error != null
                  ? DropdownButtonFormField<Queue>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Error loading queues',
                      errorText: queueState.error,
                    ),
                    items: const [],
                    onChanged: null,
                  )
                  : _selectedLocation == null
                  ? DropdownButtonFormField<Queue>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Select a location first',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : filteredQueues.isEmpty
                  ? DropdownButtonFormField<Queue>(
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'No queues available for this location',
                    ),
                    items: [],
                    onChanged: null,
                  )
                  : DropdownButtonFormField<Queue>(
                    value: _selectedQueue,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      hintText: 'Select a queue',
                    ),
                    isExpanded: true,
                    icon: Icon(
                      Icons.arrow_drop_down,
                      color: Colors.grey.shade600,
                    ),
                    validator:
                        (value) =>
                            value == null ? 'Please select a queue' : null,
                    selectedItemBuilder: (context) {
                      return filteredQueues.map((queue) {
                        return Row(
                          children: [
                            Icon(
                              queue.isActive
                                  ? Icons.check_circle
                                  : Icons.pause_circle,
                              size: 16,
                              color:
                                  queue.isActive ? Colors.green : Colors.orange,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                queue.title,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        );
                      }).toList();
                    },
                    items:
                        filteredQueues.map((queue) {
                          return DropdownMenuItem<Queue>(
                            value: queue,
                            child: SizedBox(
                              height: 40,
                              child: Row(
                                children: [
                                  Icon(
                                    queue.isActive
                                        ? Icons.check_circle
                                        : Icons.pause_circle,
                                    size: 16,
                                    color:
                                        queue.isActive
                                            ? Colors.green
                                            : Colors.orange,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      queue.title,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                    onChanged: (queue) {
                      setState(() {
                        _selectedQueue = queue;
                      });
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Time *',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildTimeField(
                label: 'Start Time',
                value: _startTime,
                onChanged: _onStartTimeChanged,
                validator:
                    (value) =>
                        value == null ? 'Please select start time' : null,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildTimeField(
                label: 'End Time',
                value: _endTime,
                onChanged: (value) => setState(() => _endTime = value),
                validator: (value) {
                  if (value == null) return 'Please select end time';
                  if (_startTime != null && value.isBefore(_startTime!)) {
                    return 'End time must be after start time';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeField({
    required String label,
    required DateTime? value,
    required ValueChanged<DateTime?> onChanged,
    required String? Function(DateTime?) validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: value ?? DateTime.now(),
              firstDate: DateTime.now(),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );

            if (date != null && mounted) {
              final time = await showTimePicker(
                context: context,
                initialTime: TimeOfDay.fromDateTime(value ?? DateTime.now()),
              );

              if (time != null) {
                final dateTime = DateTime(
                  date.year,
                  date.month,
                  date.day,
                  time.hour,
                  time.minute,
                );
                onChanged(dateTime);
              }
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.access_time, size: 18, color: Colors.grey.shade600),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    value != null
                        ? DateFormat('MMM dd, yyyy HH:mm').format(value)
                        : 'Select $label',
                    style: TextStyle(
                      color:
                          value != null ? Colors.black87 : Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          maxLength: 500,
          decoration: InputDecoration(
            hintText: 'Add any additional notes...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => context.pop(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Text(
                      'Create Appointment',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
          ),
        ),
      ],
    );
  }
}
