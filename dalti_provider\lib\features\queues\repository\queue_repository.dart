import '../models/queue_models.dart';

/// Abstract repository interface for queue management operations
abstract class QueueRepository {
  /// Get all queues for the current provider
  /// 
  /// [isActive] - Filter by active status (optional)
  /// [locationId] - Filter by location ID (optional)
  /// 
  /// Returns list of queues
  Future<List<Queue>> getQueues({
    bool? isActive,
    int? locationId,
  });

  /// Get queue by ID
  /// 
  /// [id] - Queue ID
  /// 
  /// Returns queue details
  Future<Queue> getQueueById(int id);

  /// Create a new queue
  /// 
  /// [request] - Queue creation data
  /// 
  /// Returns created queue
  Future<Queue> createQueue(CreateQueueRequest request);

  /// Update an existing queue
  /// 
  /// [id] - Queue ID to update
  /// [request] - Queue update data
  /// 
  /// Returns updated queue
  Future<Queue> updateQueue(int id, UpdateQueueRequest request);

  /// Delete a queue
  /// 
  /// [id] - Queue ID to delete
  /// 
  /// Returns success status
  Future<bool> deleteQueue(int id);

  /// Get queues for a specific location
  /// 
  /// [locationId] - Location ID
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns queues for the location
  Future<List<Queue>> getQueuesByLocation(int locationId, {bool? isActive});

  /// Get services assigned to a specific queue
  /// 
  /// [queueId] - Queue ID
  /// 
  /// Returns list of queue service assignments
  Future<List<QueueService>> getQueueServices(int queueId);

  /// Assign a service to a queue
  /// 
  /// [queueId] - Queue ID
  /// [request] - Service assignment data
  /// 
  /// Returns the created queue service assignment
  Future<QueueService> assignServiceToQueue(int queueId, AssignServiceRequest request);

  /// Remove a service from a queue
  /// 
  /// [queueId] - Queue ID
  /// [serviceId] - Service ID to remove
  /// 
  /// Returns success status
  Future<bool> removeServiceFromQueue(int queueId, int serviceId);

  /// Get all queues grouped by location
  /// 
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns map of location ID -> queues
  Future<Map<int, List<Queue>>> getQueuesGroupedByLocation({bool? isActive});

  /// Toggle queue active status
  /// 
  /// [id] - Queue ID
  /// [isActive] - New active status
  /// 
  /// Returns updated queue
  Future<Queue> toggleQueueStatus(int id, bool isActive);

  /// Get queue statistics
  /// 
  /// Returns overall queue statistics
  Future<QueueStats> getQueueStats();

  /// Get queue statistics for a specific location
  /// 
  /// [locationId] - Location ID
  /// 
  /// Returns queue statistics for the location
  Future<QueueStats> getLocationQueueStats(int locationId);

  /// Validate queue data before creation/update
  /// 
  /// [title] - Queue title
  /// [locationId] - Location ID
  /// [serviceIds] - List of service IDs
  /// [excludeQueueId] - Queue ID to exclude from validation (for updates)
  /// 
  /// Returns validation result
  Future<QueueValidationResult> validateQueue({
    required String title,
    required int locationId,
    required List<int> serviceIds,
    int? excludeQueueId,
  });

  /// Check if a queue can be deleted
  /// 
  /// [id] - Queue ID
  /// 
  /// Returns true if queue can be safely deleted
  Future<bool> canDeleteQueue(int id);

  /// Get queues that have a specific service assigned
  /// 
  /// [serviceId] - Service ID
  /// 
  /// Returns list of queues containing the service
  Future<List<Queue>> getQueuesByService(int serviceId);

  /// Bulk assign services to a queue
  /// 
  /// [queueId] - Queue ID
  /// [serviceIds] - List of service IDs to assign
  /// 
  /// Returns list of created queue service assignments
  Future<List<QueueService>> bulkAssignServicesToQueue(int queueId, List<int> serviceIds);

  /// Bulk remove services from a queue
  /// 
  /// [queueId] - Queue ID
  /// [serviceIds] - List of service IDs to remove
  /// 
  /// Returns success status
  Future<bool> bulkRemoveServicesFromQueue(int queueId, List<int> serviceIds);

  /// Update queue services (replace all existing assignments)
  /// 
  /// [queueId] - Queue ID
  /// [serviceIds] - New list of service IDs
  /// 
  /// Returns updated queue with new service assignments
  Future<Queue> updateQueueServices(int queueId, List<int> serviceIds);

  /// Get active queues for a location
  /// 
  /// [locationId] - Location ID
  /// 
  /// Returns list of active queues
  Future<List<Queue>> getActiveQueuesByLocation(int locationId);

  /// Get inactive queues for a location
  /// 
  /// [locationId] - Location ID
  /// 
  /// Returns list of inactive queues
  Future<List<Queue>> getInactiveQueuesByLocation(int locationId);

  /// Search queues by title
  /// 
  /// [query] - Search query
  /// [locationId] - Filter by location ID (optional)
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns list of matching queues
  Future<List<Queue>> searchQueues({
    required String query,
    int? locationId,
    bool? isActive,
  });

  /// Check for queue title conflicts within a location
  /// 
  /// [title] - Queue title to check
  /// [locationId] - Location ID
  /// [excludeQueueId] - Queue ID to exclude from check (for updates)
  /// 
  /// Returns true if title conflicts with existing queue
  Future<bool> hasQueueTitleConflict({
    required String title,
    required int locationId,
    int? excludeQueueId,
  });

  /// Get queue dependencies (appointments, etc.)
  /// 
  /// [id] - Queue ID
  /// 
  /// Returns information about queue dependencies
  Future<QueueDependencies> getQueueDependencies(int id);
}

/// Queue dependencies model
class QueueDependencies {
  final bool hasActiveAppointments;
  final bool hasFutureAppointments;
  final int appointmentCount;
  final bool canDelete;
  final List<String> blockingReasons;

  const QueueDependencies({
    required this.hasActiveAppointments,
    required this.hasFutureAppointments,
    required this.appointmentCount,
    required this.canDelete,
    required this.blockingReasons,
  });

  factory QueueDependencies.safe() {
    return const QueueDependencies(
      hasActiveAppointments: false,
      hasFutureAppointments: false,
      appointmentCount: 0,
      canDelete: true,
      blockingReasons: [],
    );
  }

  factory QueueDependencies.blocked({
    bool hasActiveAppointments = false,
    bool hasFutureAppointments = false,
    int appointmentCount = 0,
    List<String> blockingReasons = const [],
  }) {
    return QueueDependencies(
      hasActiveAppointments: hasActiveAppointments,
      hasFutureAppointments: hasFutureAppointments,
      appointmentCount: appointmentCount,
      canDelete: false,
      blockingReasons: blockingReasons,
    );
  }
}
