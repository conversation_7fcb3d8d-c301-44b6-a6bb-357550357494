import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/features/dashboard/services/dashboard_api_service.dart';
import 'package:dalti_provider/features/dashboard/models/dashboard_models.dart';

// import 'dashboard_api_service_test.mocks.dart';

// @GenerateMocks([HttpClient])
void main() {
  group('DashboardApiService Tests', () {
    late DashboardApiService apiService;
    late MockHttpClient mockHttpClient;

    setUp(() {
      mockHttpClient = MockHttpClient();
      apiService = DashboardApiService(mockHttpClient);
    });

    group('getBusinessOverview', () {
      test('should return BusinessMetricsResponse on successful API call', () async {
        // Arrange
        final responseData = {
          'success': true,
          'data': {
            'todayRevenue': 1500.0,
            'todayAppointments': 12,
            'completedAppointments': 10,
            'activeQueues': 3,
            'totalCustomersToday': 35,
            'customerSatisfaction': 4.5,
            'revenueChange': 8.2,
          },
        };

        when(mockHttpClient.get('/api/provider/dashboard/business-overview'))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await apiService.getBusinessOverview();

        // Assert
        expect(result.success, true);
        expect(result.data, isNotNull);
        expect(result.data!.todayRevenue, 1500.0);
        expect(result.data!.todayAppointments, 12);
        verify(mockHttpClient.get('/api/provider/dashboard/business-overview')).called(1);
      });

      test('should return error response on API failure', () async {
        // Arrange
        when(mockHttpClient.get('/api/provider/dashboard/business-overview'))
            .thenAnswer((_) async => Response(
                  data: {'error': 'Server error'},
                  statusCode: 500,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await apiService.getBusinessOverview();

        // Assert
        expect(result.success, false);
        expect(result.error, isNotNull);
        expect(result.error!.code, 'FETCH_ERROR');
        expect(result.error!.message, 'Failed to fetch business overview');
      });

      test('should handle network exceptions', () async {
        // Arrange
        when(mockHttpClient.get('/api/provider/dashboard/business-overview'))
            .thenThrow(DioException(
              requestOptions: RequestOptions(path: ''),
              message: 'Network error',
            ));

        // Act
        final result = await apiService.getBusinessOverview();

        // Assert
        expect(result.success, false);
        expect(result.error, isNotNull);
        expect(result.error!.code, 'NETWORK_ERROR');
        expect(result.error!.message, 'Network error occurred');
      });
    });

    group('getTodaySchedule', () {
      test('should return ScheduleDataResponse on successful API call', () async {
        // Arrange
        final responseData = {
          'success': true,
          'data': {
            'nextAppointment': {
              'customerName': 'John Doe',
              'serviceName': 'Hair Cut',
              'scheduledTime': '2024-01-15T14:30:00.000Z',
              'status': 'confirmed',
            },
            'todayHours': {
              'isOpen': true,
              'openTime': '09:00',
              'closeTime': '18:00',
            },
            'queueStatuses': [
              {
                'queueId': 'queue_1',
                'queueName': 'Main Queue',
                'isActive': true,
                'waitingCount': 5,
                'estimatedWaitTime': 15,
              }
            ],
            'totalAppointments': 15,
            'completedAppointments': 8,
            'upcomingAppointments': 7,
          },
        };

        when(mockHttpClient.get('/api/provider/dashboard/today-schedule'))
            .thenAnswer((_) async => Response(
                  data: responseData,
                  statusCode: 200,
                  requestOptions: RequestOptions(path: ''),
                ));

        // Act
        final result = await apiService.getTodaySchedule();

        // Assert
        expect(result.success, true);
        expect(result.data, isNotNull);
        expect(result.data!.nextAppointment, isNotNull);
        expect(result.data!.nextAppointment!.customerName, 'John Doe');
        expect(result.data!.queueStatuses.length, 1);
        verify(mockHttpClient.get('/api/provider/dashboard/today-schedule')).called(1);
      });
    });

    group('getNotifications', () {
      test('should return NotificationsResponse with notifications list', () async {
        // Arrange
        final responseData = {
          'success': true,
          'notifications': [
            {
              'id': 'notif_1',
              'title': 'New Appointment',
              'message': 'John Doe booked an appointment',
              'type': 'appointment',
              'timestamp': '2024-01-15T10:30:00.000Z',
              'isRead': false,
            },
            {
              'id': 'notif_2',
              'title': 'Reschedule Request',
              'message': 'Jane Smith wants to reschedule',
              'type': 'reschedule',
              'timestamp': '2024-01-15T11:15:00.000Z',
              'isRead': true,
            }
          ],
          'totalCount': 2,
          'unreadCount': 1,
        };

        when(mockHttpClient.get(
          '/api/provider/dashboard/notifications',
          queryParameters: {'limit': 10, 'unreadOnly': false},
        )).thenAnswer((_) async => Response(
              data: responseData,
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ));

        // Act
        final result = await apiService.getNotifications();

        // Assert
        expect(result.success, true);
        expect(result.notifications.length, 2);
        expect(result.totalCount, 2);
        expect(result.unreadCount, 1);
        expect(result.notifications[0].title, 'New Appointment');
        expect(result.notifications[0].isRead, false);
      });

      test('should handle empty notifications list', () async {
        // Arrange
        final responseData = {
          'success': true,
          'notifications': [],
          'totalCount': 0,
          'unreadCount': 0,
        };

        when(mockHttpClient.get(
          '/api/provider/dashboard/notifications',
          queryParameters: {'limit': 10, 'unreadOnly': false},
        )).thenAnswer((_) async => Response(
              data: responseData,
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ));

        // Act
        final result = await apiService.getNotifications();

        // Assert
        expect(result.success, true);
        expect(result.notifications.isEmpty, true);
        expect(result.totalCount, 0);
        expect(result.unreadCount, 0);
      });
    });

    group('markNotificationAsRead', () {
      test('should return success response when marking notification as read', () async {
        // Arrange
        const notificationId = 'notif_123';
        when(mockHttpClient.patch(
          '/api/provider/dashboard/notifications/$notificationId/read',
        )).thenAnswer((_) async => Response(
              data: {'success': true, 'message': 'Notification marked as read'},
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ));

        // Act
        final result = await apiService.markNotificationAsRead(notificationId);

        // Assert
        expect(result.success, true);
        expect(result.message, 'Notification marked as read');
        verify(mockHttpClient.patch(
          '/api/provider/dashboard/notifications/$notificationId/read',
        )).called(1);
      });

      test('should return error response on API failure', () async {
        // Arrange
        const notificationId = 'notif_123';
        when(mockHttpClient.patch(
          '/api/provider/dashboard/notifications/$notificationId/read',
        )).thenAnswer((_) async => Response(
              data: {'error': 'Notification not found'},
              statusCode: 404,
              requestOptions: RequestOptions(path: ''),
            ));

        // Act
        final result = await apiService.markNotificationAsRead(notificationId);

        // Assert
        expect(result.success, false);
        expect(result.error, isNotNull);
        expect(result.error!.code, 'UPDATE_ERROR');
      });
    });

    group('emergencyQueueControl', () {
      test('should execute emergency queue control successfully', () async {
        // Arrange
        final request = EmergencyQueueControl(
          action: 'pause',
          reason: 'Emergency maintenance',
          timestamp: DateTime(2024, 1, 15, 12, 0),
        );

        final responseData = {
          'success': true,
          'data': {
            'affectedQueues': ['queue_1', 'queue_2'],
            'timestamp': '2024-01-15T12:00:00.000Z',
            'status': 'completed',
          },
        };

        when(mockHttpClient.post(
          '/api/provider/dashboard/queues/emergency-control',
          data: request.toJson(),
        )).thenAnswer((_) async => Response(
              data: responseData,
              statusCode: 200,
              requestOptions: RequestOptions(path: ''),
            ));

        // Act
        final result = await apiService.emergencyQueueControl(request);

        // Assert
        expect(result.success, true);
        expect(result.data, isNotNull);
        verify(mockHttpClient.post(
          '/api/provider/dashboard/queues/emergency-control',
          data: request.toJson(),
        )).called(1);
      });
    });
  });
}
