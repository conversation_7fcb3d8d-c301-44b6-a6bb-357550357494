import 'dart:async';
import '../storage/storage_service.dart';
import '../storage/web_storage_service.dart';
import '../network/http_client.dart';
import 'models/jwt_token.dart';

/// Service for managing JWT tokens
class JwtService {
  static const String _tokenKey = 'jwt_token';
  static const String _refreshTokenKey = 'refresh_token';
  
  JwtToken? _currentToken;
  Timer? _refreshTimer;
  final HttpClient _httpClient;
  bool _isInitialized = false;

  // Stream controller for token changes
  final StreamController<JwtToken?> _tokenController = StreamController<JwtToken?>.broadcast();

  JwtService(this._httpClient);

  /// Stream of token changes
  Stream<JwtToken?> get tokenStream => _tokenController.stream;

  /// Get current token
  JwtToken? get currentToken => _currentToken;

  /// Check if user is authenticated
  bool get isAuthenticated => _currentToken?.isValid ?? false;

  /// Get current user ID
  String? get currentUserId => _currentToken?.userId;

  /// Get current user email
  String? get currentUserEmail => _currentToken?.userEmail;

  /// Get current user roles
  List<String> get currentUserRoles => _currentToken?.userRoles ?? [];

  /// Initialize the service and load stored token
  Future<void> initialize() async {
    if (_isInitialized) {
      print('[JwtService] Already initialized, skipping...');
      return;
    }

    print('[JwtService] Initializing JWT service...');
    await _loadStoredToken();

    if (_currentToken != null) {
      print('[JwtService] Token loaded, scheduling refresh...');
      _scheduleTokenRefresh();
    } else {
      print('[JwtService] No token found during initialization');
    }

    _isInitialized = true;
    print('[JwtService] JWT service initialization complete');
  }

  /// Store a new token
  Future<void> storeToken(JwtToken token) async {
    print('[JwtService] Storing token...');
    _currentToken = token;

    // Store in secure storage using WebStorageService for better web support
    await WebStorageService.saveAuth(_tokenKey, token.toJson());

    // Schedule refresh if needed
    _scheduleTokenRefresh();

    // Notify listeners
    _tokenController.add(token);

    print('[JwtService] Token stored successfully');
  }

  /// Store token from API response
  Future<void> storeTokenFromResponse(Map<String, dynamic> response) async {
    final token = JwtToken.fromJson(response);
    await storeToken(token);
  }

  /// Clear stored token
  Future<void> clearToken() async {
    print('[JwtService] Clearing token...');
    _currentToken = null;
    _cancelRefreshTimer();

    // Clear from storage
    await WebStorageService.removeAuth(_tokenKey);
    await WebStorageService.removeAuth(_refreshTokenKey);

    // Notify listeners
    _tokenController.add(null);

    print('[JwtService] Token cleared successfully');
  }

  /// Refresh the current token
  Future<bool> refreshToken() async {
    if (_currentToken?.refreshToken == null) {
      print('[] ');
      return false;
    }

    try {
      print('[] ');

      // Use the correct refresh token endpoint
      final response = await _httpClient.post(
        '/api/auth/refresh-token',
        data: {
          'refreshToken': _currentToken!.refreshToken,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Ensure success field is set for compatibility
        responseData['success'] = true;

        await storeTokenFromResponse(responseData);
        print('[] ');
        return true;
      } else {
        print('[] ');
        return false;
      }
    } catch (e) {
      print('[] ');

      // If refresh fails with auth error, clear the token
      if (e.toString().contains('401') || e.toString().contains('403')) {
        await clearToken();
      }

      return false;
    }
  }

  /// Get authorization header value
  String? getAuthorizationHeader() {
    print('[JwtService] Getting authorization header...');
    print('[JwtService] Current token: ${_currentToken != null ? "exists" : "null"}');

    if (_currentToken != null) {
      print('[JwtService] Token valid: ${_currentToken!.isValid}');
      print('[JwtService] Token expired: ${_currentToken!.isExpired}');
      print('[JwtService] Token access token: ${_currentToken!.accessToken.substring(0, 10)}...');
    }

    if (_currentToken?.isValid == true) {
      final header = '${_currentToken!.tokenType} ${_currentToken!.accessToken}';
      print('[JwtService] Returning auth header: ${header.substring(0, 20)}...');
      return header;
    }

    print('[JwtService] No valid token, returning null');
    return null;
  }

  /// Check if token needs refresh and attempt to refresh it
  Future<bool> ensureValidToken() async {
    print('[JwtService] Ensuring valid token...');

    // If no current token, try to load from storage
    if (_currentToken == null) {
      print('[JwtService] No current token, attempting to load from storage...');
      await _loadStoredToken();
    }

    if (_currentToken == null) {
      print('[JwtService] No current token available after storage check');
      return false;
    }

    print('[JwtService] Current token status: valid=${_currentToken!.isValid}, expired=${_currentToken!.isExpired}');

    if (_currentToken!.isExpired) {
      print('[JwtService] Token is expired, attempting refresh...');
      return await refreshToken();
    }

    if (_currentToken!.willExpireSoon) {
      print('[JwtService] Token will expire soon, refreshing in background...');
      await refreshToken(); // Don't wait for result, continue with current token
    }

    final isValid = _currentToken!.isValid;
    print('[JwtService] Token validation result: $isValid');
    return isValid;
  }

  /// Load token from storage
  Future<void> _loadStoredToken() async {
    try {
      print('[JwtService] Loading token from storage...');
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>(_tokenKey);

      if (tokenData != null) {
        print('[JwtService] Token data found in storage');
        _currentToken = JwtToken.fromStoredJson(tokenData);

        // Check if stored token is still valid
        if (_currentToken!.isExpired) {
          print('[JwtService] Stored token is expired, clearing...');
          await clearToken();
        } else {
          print('[JwtService] Stored token is valid, loaded successfully');
          _tokenController.add(_currentToken);
        }
      } else {
        print('[JwtService] No token data found in storage');
      }
    } catch (e) {
      print('[JwtService] Error loading token from storage: $e');
      await clearToken();
    }
  }

  /// Schedule automatic token refresh
  void _scheduleTokenRefresh() {
    _cancelRefreshTimer();
    
    if (_currentToken?.expiresAt == null) return;
    
    // Schedule refresh 5 minutes before expiration
    final refreshTime = _currentToken!.expiresAt!.subtract(const Duration(minutes: 5));
    final now = DateTime.now();
    
    if (refreshTime.isAfter(now)) {
      final duration = refreshTime.difference(now);
      _refreshTimer = Timer(duration, () {
        print('[] ');
        refreshToken();
      });
      
      print(().toString());
    }
  }

  /// Cancel the refresh timer
  void _cancelRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Dispose the service
  void dispose() {
    _cancelRefreshTimer();
    _tokenController.close();
  }
}
