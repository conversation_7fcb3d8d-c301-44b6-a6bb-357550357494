import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../providers/onboarding_provider.dart';
import '../../locations/models/location_models.dart';
import '../../locations/constants/algerian_cities.dart';
import '../../locations/services/location_service.dart';
import '../../locations/providers/location_provider.dart';

/// Location setup step for onboarding
class LocationSetupStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const LocationSetupStep({
    super.key,
    required this.controller,
  });

  @override
  ConsumerState<LocationSetupStep> createState() => _LocationSetupStepState();
}

class _LocationSetupStepState extends ConsumerState<LocationSetupStep> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _countryController = TextEditingController(text: 'Algeria');
  final _postalCodeController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();

  bool _parking = false;
  bool _elevator = false;
  bool _handicapAccess = false;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _postalCodeController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  /// Load existing location data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final location = onboardingState.data?.primaryLocation;

    if (location != null) {
      _nameController.text = location.name;
      _addressController.text = location.address;
      _cityController.text = location.city;
      _parking = location.parking;
      _elevator = location.elevator;
      _handicapAccess = location.handicapAccess;

      // Mark step as completed if data exists
      widget.controller.markStepCompleted(OnboardingStep.locationSetup, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WizardFormPage(
      step: OnboardingStep.locationSetup,
      formKey: _formKey,
      fields: [
        // Location name field
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Location Name *',
            hintText: 'e.g., Main Clinic, Downtown Office',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.business),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Location name is required';
            }
            if (value.trim().length < 2) {
              return 'Location name must be at least 2 characters';
            }
            if (value.trim().length > 100) {
              return 'Location name must be less than 100 characters';
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.words,
        ),

        // Street address field
        TextFormField(
          controller: _addressController,
          decoration: const InputDecoration(
            labelText: 'Street Address *',
            hintText: 'e.g., 123 Main Street',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.location_on),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Street address is required';
            }
            if (value.trim().length < 5) {
              return 'Please enter a complete address';
            }
            if (value.trim().length > 200) {
              return 'Address must be less than 200 characters';
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.words,
          maxLines: 2,
        ),

        // City dropdown with Algerian cities
        DropdownButtonFormField<String>(
          value: _cityController.text.isNotEmpty && AlgerianCities.isValidCity(_cityController.text)
              ? _cityController.text
              : null,
          decoration: const InputDecoration(
            labelText: 'City *',
            hintText: 'Select an Algerian city',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.location_city),
          ),
          items: AlgerianCities.sortedCities.map((String city) {
            return DropdownMenuItem<String>(
              value: city,
              child: Text(city),
            );
          }).toList(),
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _cityController.text = newValue;
              });
              _onFieldChanged('');
            }
          },
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'City is required';
            }
            if (!AlgerianCities.isValidCity(value)) {
              return 'Please select a valid Algerian city';
            }
            return null;
          },
          isExpanded: true,
        ),

        // Country field - Fixed to Algeria and disabled
        TextFormField(
          controller: _countryController,
          enabled: false,
          decoration: const InputDecoration(
            labelText: 'Country *',
            hintText: 'Algeria',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.public),
            suffixIcon: Icon(Icons.lock, color: Colors.grey),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Country is required';
            }
            if (value.trim() != 'Algeria') {
              return 'Currently only available in Algeria';
            }
            return null;
          },
          style: TextStyle(
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),

        // Postal code field
        TextFormField(
          controller: _postalCodeController,
          decoration: const InputDecoration(
            labelText: 'Postal Code',
            hintText: 'e.g., 16000',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.markunread_mailbox),
          ),
          keyboardType: TextInputType.number,
          onChanged: _onFieldChanged,
        ),

        // Location coordinates section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: context.colors.outline.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.my_location,
                    color: context.colors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Location Coordinates',
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Get your current location to help customers find you easily.',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 16),

              // Latitude and Longitude fields
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _latitudeController,
                      enabled: false,
                      decoration: const InputDecoration(
                        labelText: 'Latitude *',
                        hintText: 'Will be filled automatically',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.place),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please get current location';
                        }
                        final lat = double.tryParse(value);
                        if (lat == null) {
                          return 'Invalid latitude';
                        }
                        if (lat < -90 || lat > 90) {
                          return 'Latitude must be between -90 and 90';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _longitudeController,
                      enabled: false,
                      decoration: const InputDecoration(
                        labelText: 'Longitude *',
                        hintText: 'Will be filled automatically',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.place),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please get current location';
                        }
                        final lng = double.tryParse(value);
                        if (lng == null) {
                          return 'Invalid longitude';
                        }
                        if (lng < -180 || lng > 180) {
                          return 'Longitude must be between -180 and 180';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Get location button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isGettingLocation ? null : _getCurrentLocation,
                  icon: _isGettingLocation
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.my_location),
                  label: Text(_isGettingLocation ? 'Getting Location...' : 'Get Current Location'),
                ),
              ),
            ],
          ),
        ),

        // Accessibility features section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: context.colors.outline.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.accessible,
                    color: context.colors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Accessibility Features',
                    style: context.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Select the accessibility features available at your location.',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 16),

              // Parking checkbox
              CheckboxListTile(
                title: const Text('Parking Available'),
                subtitle: const Text('On-site parking for customers'),
                value: _parking,
                onChanged: (bool? value) {
                  setState(() {
                    _parking = value ?? false;
                  });
                  _onFieldChanged('');
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),

              // Elevator checkbox
              CheckboxListTile(
                title: const Text('Elevator Access'),
                subtitle: const Text('Elevator available for upper floors'),
                value: _elevator,
                onChanged: (bool? value) {
                  setState(() {
                    _elevator = value ?? false;
                  });
                  _onFieldChanged('');
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),

              // Handicap access checkbox
              CheckboxListTile(
                title: const Text('Wheelchair Accessible'),
                subtitle: const Text('Wheelchair accessible entrance and facilities'),
                value: _handicapAccess,
                onChanged: (bool? value) {
                  setState(() {
                    _handicapAccess = value ?? false;
                  });
                  _onFieldChanged('');
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        ),

        // Info card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: context.colors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: context.colors.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'This will be your primary business location. You can add more locations later from the locations management section.',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle field changes to validate and update state
  void _onFieldChanged(String value) {
    // Validate form and update controller state
    final isValid = _validateForm();
    widget.controller.markStepCompleted(OnboardingStep.locationSetup, isValid);

    if (isValid) {
      _saveLocationData();
    }
  }

  /// Validate the form
  bool _validateForm() {
    return _nameController.text.trim().isNotEmpty &&
           _nameController.text.trim().length >= 2 &&
           _addressController.text.trim().isNotEmpty &&
           _addressController.text.trim().length >= 5 &&
           _cityController.text.trim().isNotEmpty &&
           AlgerianCities.isValidCity(_cityController.text.trim()) &&
           _latitudeController.text.trim().isNotEmpty &&
           _longitudeController.text.trim().isNotEmpty;
  }

  /// Get current device location
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      final result = await LocationService.getCurrentLocation();

      if (result.isSuccess) {
        setState(() {
          _latitudeController.text = result.latitude!.toString();
          _longitudeController.text = result.longitude!.toString();
        });

        _onFieldChanged('');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('Location acquired successfully!'),
                ],
              ),
              backgroundColor: context.colors.primary,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.error ?? 'Failed to get location'),
              backgroundColor: context.colors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: context.colors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGettingLocation = false;
        });
      }
    }
  }

  /// Save location data
  void _saveLocationData() {
    if (!_validateForm()) return;

    try {
      // Create location request object
      final locationRequest = CreateLocationRequest(
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        city: _cityController.text.trim(),
        country: 'Algeria',
        postalCode: _postalCodeController.text.trim().isNotEmpty
            ? _postalCodeController.text.trim()
            : '00000',
        latitude: double.parse(_latitudeController.text.trim()),
        longitude: double.parse(_longitudeController.text.trim()),
        parking: _parking,
        elevator: _elevator,
        handicapAccess: _handicapAccess,
      );

      // Create a temporary location object for onboarding
      final location = Location(
        id: 0, // Temporary ID for onboarding
        name: locationRequest.name,
        address: locationRequest.address,
        city: locationRequest.city,
        isMobileHidden: false,
        parking: locationRequest.parking,
        elevator: locationRequest.elevator,
        handicapAccess: locationRequest.handicapAccess,
      );

      // Save to onboarding provider
      ref.read(onboardingNotifierProvider.notifier).savePrimaryLocation(location);

      print('[LocationSetupStep] Location data saved: ${location.name}');
    } catch (e) {
      print('[LocationSetupStep] Error saving location data: $e');
    }
  }
}
