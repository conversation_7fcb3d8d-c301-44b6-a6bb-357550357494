import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/models/provider_category.dart';

void main() {
  group('Debug Categories', () {
    test('Check hardcoded categories structure', () {
      final parentCategories = ProviderCategories.getParentCategories();
      final allCategories = ProviderCategories.getAllCategories();
      final selectableCategories = ProviderCategories.getSelectableCategories();
      
      print('Parent categories count: ${parentCategories.length}');
      for (final parent in parentCategories) {
        print('Parent: ${parent.name} (ID: ${parent.id}, selectable: ${parent.isSelectable}, children: ${parent.children.length})');
        for (final child in parent.children) {
          print('  Child: ${child.name} (ID: ${child.id}, parentId: ${child.parentId}, selectable: ${child.isSelectable})');
        }
      }
      
      print('\nAll categories count: ${allCategories.length}');
      print('Selectable categories count: ${selectableCategories.length}');
      
      // Basic assertions
      expect(parentCategories.length, equals(3));
      expect(parentCategories.every((cat) => !cat.isSelectable), isTrue);
      expect(parentCategories.every((cat) => cat.children.isNotEmpty), isTrue);
      
      expect(selectableCategories.length, equals(12)); // 4 children per parent * 3 parents
      expect(selectableCategories.every((cat) => cat.isSelectable), isTrue);
      expect(selectableCategories.every((cat) => cat.parentId != null), isTrue);
    });

    test('Check specific category lookups', () {
      final healthcare = ProviderCategories.getCategoryById(1);
      final medicalClinic = ProviderCategories.getCategoryById(101);
      
      print('Healthcare category: ${healthcare?.name} (selectable: ${healthcare?.isSelectable})');
      print('Medical Clinic category: ${medicalClinic?.name} (selectable: ${medicalClinic?.isSelectable}, parentId: ${medicalClinic?.parentId})');
      
      expect(healthcare?.name, equals('Healthcare'));
      expect(healthcare?.isSelectable, isFalse);
      expect(medicalClinic?.name, equals('Medical Clinics'));
      expect(medicalClinic?.isSelectable, isTrue);
      expect(medicalClinic?.parentId, equals(1));
    });
  });
}
