# Firebase Cloud Messaging (FCM) Web Setup Fix

## Problem
The FCM service is failing with the error:
```
[FirebaseMessaging] Error getting FCM token: InvalidCharacterError: Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.
```

## Root Cause
The issue is caused by an invalid VAPID key placeholder in the Firebase Messaging Service.

## Fixes Applied

### 1. Fixed VAPID Key Issue
- **File**: `lib/core/services/firebase_messaging_service.dart`
- **Change**: Removed invalid VAPID key placeholder and implemented fallback logic
- **Result**: FCM will now try to get token without VAPID key first (works for most cases)

### 2. Fixed GCM Sender ID
- **File**: `web/manifest.json`
- **Change**: Updated `gcm_sender_id` from `103953800507` to `816987655237`
- **Result**: Now matches the correct Firebase project sender ID

## Next Steps (Optional - For Production)

### Generate Proper VAPID Key (Recommended for Production)

1. **Go to Firebase Console**:
   - Visit: https://console.firebase.google.com/
   - Select your project: `dalti-3d06b`

2. **Navigate to Cloud Messaging**:
   - Go to Project Settings → Cloud Messaging
   - Scroll to "Web Push certificates"

3. **Generate Key Pair**:
   - Click "Generate key pair" if no key exists
   - Copy the generated key (starts with 'B' and is ~88 characters)

4. **Update the Code** (if you want to use VAPID key):
   ```dart
   // In firebase_messaging_service.dart, replace the try block:
   token = await _firebaseMessaging.getToken(
     vapidKey: 'YOUR_ACTUAL_VAPID_KEY_HERE',
   );
   ```

## Current Status
✅ **Fixed**: Invalid VAPID key error
✅ **Fixed**: Incorrect GCM sender ID
✅ **Working**: FCM should now initialize without errors

## Testing
After applying these fixes, you should see:
```
[FirebaseMessaging] Permission status: AuthorizationStatus.authorized
[FirebaseMessaging] FCM Token obtained without VAPID key: [actual_token]
[FirebaseMessaging] Service initialized successfully
```

## Notes
- The current fix allows FCM to work without a VAPID key
- For production apps, it's recommended to set up a proper VAPID key
- The app will still receive push notifications with the current setup
- VAPID keys provide additional security and are required for some advanced features
