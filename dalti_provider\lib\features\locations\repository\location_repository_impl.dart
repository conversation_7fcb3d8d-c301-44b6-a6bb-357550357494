import '../services/location_api_service.dart';
import '../models/location_models.dart';
import 'location_repository.dart';

/// Concrete implementation of LocationRepository using API service
class LocationRepositoryImpl implements LocationRepository {
  final LocationApiService _apiService;

  LocationRepositoryImpl({
    required LocationApiService apiService,
  }) : _apiService = apiService;

  @override
  Future<List<Location>> getLocations({
    bool? isActive,
    String? search,
  }) async {
    try {
      print('[LocationRepository] Getting locations (isActive: $isActive, search: $search)');

      final locations = await _apiService.getLocations(
        isActive: isActive,
        search: search,
      );

      print(().toString());

      return locations;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<Location> getLocationById(int id) async {
    try {
      print(().toString());

      final location = await _apiService.getLocationById(id);

      print(().toString());

      return location;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<Location> createLocation(CreateLocationRequest request) async {
    try {
      print(().toString());

      final location = await _apiService.createLocation(request);

      print(().toString());

      return location;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<Location> updateLocation(int id, UpdateLocationRequest request) async {
    try {
      print(().toString());

      final location = await _apiService.updateLocation(id, request);

      print(().toString());

      return location;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<bool> deleteLocation(int id) async {
    try {
      print(().toString());

      final success = await _apiService.deleteLocation(id);

      if (success) {
        print(().toString());
      } else {
        print(().toString());
      }

      return success;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<bool> canDeleteLocation(int id) async {
    try {
      print(().toString());

      final canDelete = await _apiService.canDeleteLocation(id);

      print(().toString());

      return canDelete;
    } catch (e) {
      print(().toString());
      // If we can't check, assume we can try to delete
      return true;
    }
  }

  @override
  Future<List<Location>> getLocationsWithDetails() async {
    try {
      print(().toString());

      // For now, this is the same as getLocations
      // In the future, this could include additional data like queues, services, etc.
      final locations = await _apiService.getLocations();

      print(().toString());

      return locations;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<List<Location>> searchLocations(String query, {int? limit}) async {
    try {
      print('[LocationRepository] Searching locations with query: "$query" (limit: $limit)');

      final locations = await _apiService.getLocations(search: query);

      // Apply limit if specified
      final limitedLocations = limit != null && locations.length > limit
          ? locations.take(limit).toList()
          : locations;

      print(().toString());

      return limitedLocations;
    } catch (e) {
      print(().toString());
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getLocationStats(int id) async {
    try {
      print(().toString());

      final stats = await _apiService.getLocationStats(id);

      print(().toString());

      return stats;
    } catch (e) {
      print(().toString());
      // Return empty stats on error
      return {};
    }
  }
}
