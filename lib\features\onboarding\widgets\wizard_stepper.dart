import 'package:flutter/material.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';

/// A horizontal stepper widget for the onboarding wizard
class WizardStepper extends StatelessWidget {
  final OnboardingStep currentStep;
  final List<OnboardingStep> steps;
  final Function(OnboardingStep)? onStepTapped;
  final bool showLabels;
  final bool compact;

  const WizardStepper({
    super.key,
    required this.currentStep,
    required this.steps,
    this.onStepTapped,
    this.showLabels = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: compact ? 8.0 : 16.0,
        vertical: compact ? 8.0 : 12.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Step indicators
          Row(children: _buildStepIndicators(context)),

          if (showLabels && !compact) ...[
            const SizedBox(height: 8),
            // Step labels
            Row(children: _buildStepLabels(context)),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildStepIndicators(BuildContext context) {
    final widgets = <Widget>[];

    for (int i = 0; i < steps.length; i++) {
      final step = steps[i];
      final stepNumber = i + 1; // Use wizard position, not enum index
      final isActive = step == currentStep;
      final isCompleted = steps.indexOf(currentStep) > i;
      final isClickable = onStepTapped != null;

      // Debug print to see what numbers are being calculated
      print(
        'Step ${step.name}: wizard index=$i, stepNumber=$stepNumber, enum index=${step.index}',
      );

      // Add step indicator
      widgets.add(
        _StepIndicator(
          step: step,
          stepNumber: stepNumber,
          isActive: isActive,
          isCompleted: isCompleted,
          isClickable: isClickable,
          compact: compact,
          onTap: isClickable ? () => onStepTapped!(step) : null,
        ),
      );

      // Add connector line (except for last step)
      if (i < steps.length - 1) {
        widgets.add(
          Expanded(
            child: _StepConnector(isCompleted: isCompleted, compact: compact),
          ),
        );
      }
    }

    return widgets;
  }

  List<Widget> _buildStepLabels(BuildContext context) {
    final widgets = <Widget>[];

    for (int i = 0; i < steps.length; i++) {
      final step = steps[i];
      final isActive = step == currentStep;

      widgets.add(
        Expanded(
          child: Text(
            step.title,
            textAlign: TextAlign.center,
            style: context.textTheme.bodySmall?.copyWith(
              color:
                  isActive
                      ? context.colors.primary
                      : context.colors.onSurfaceVariant,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      );
    }

    return widgets;
  }
}

/// Individual step indicator widget
class _StepIndicator extends StatelessWidget {
  final OnboardingStep step;
  final int stepNumber;
  final bool isActive;
  final bool isCompleted;
  final bool isClickable;
  final bool compact;
  final VoidCallback? onTap;

  const _StepIndicator({
    required this.step,
    required this.stepNumber,
    required this.isActive,
    required this.isCompleted,
    required this.isClickable,
    required this.compact,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final size = compact ? 24.0 : 32.0;
    final fontSize = compact ? 12.0 : 14.0;

    Color backgroundColor;
    Color foregroundColor;

    if (isCompleted) {
      backgroundColor = context.colors.primary;
      foregroundColor = context.colors.onPrimary;
    } else if (isActive) {
      backgroundColor = context.colors.primary;
      foregroundColor = context.colors.onPrimary;
    } else {
      backgroundColor = context.colors.surfaceVariant;
      foregroundColor = context.colors.onSurfaceVariant;
    }

    Widget indicator = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        border:
            isActive && !isCompleted
                ? Border.all(color: context.colors.primary, width: 2)
                : null,
      ),
      child:
          isCompleted
              ? Icon(Icons.check, size: fontSize + 2, color: foregroundColor)
              : Center(
                child: Text(
                  '$stepNumber', // Use calculated step number instead of enum index
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    color: foregroundColor,
                  ),
                ),
              ),
    );

    if (isClickable && onTap != null) {
      indicator = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(size / 2),
        child: indicator,
      );
    }

    return indicator;
  }
}

/// Connector line between step indicators
class _StepConnector extends StatelessWidget {
  final bool isCompleted;
  final bool compact;

  const _StepConnector({required this.isCompleted, required this.compact});

  @override
  Widget build(BuildContext context) {
    final height = compact ? 2.0 : 3.0;

    return Container(
      height: height,
      margin: EdgeInsets.symmetric(horizontal: compact ? 4.0 : 8.0),
      decoration: BoxDecoration(
        color: isCompleted ? context.colors.primary : context.colors.outline,
        borderRadius: BorderRadius.circular(height / 2),
      ),
    );
  }
}

/// Compact version of the wizard stepper for smaller screens
class CompactWizardStepper extends StatelessWidget {
  final OnboardingStep currentStep;
  final List<OnboardingStep> steps;
  final Function(OnboardingStep)? onStepTapped;

  const CompactWizardStepper({
    super.key,
    required this.currentStep,
    required this.steps,
    this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    return WizardStepper(
      currentStep: currentStep,
      steps: steps,
      onStepTapped: onStepTapped,
      showLabels: false,
      compact: true,
    );
  }
}

/// Progress indicator showing completion percentage
class WizardProgressIndicator extends StatelessWidget {
  final double progress;
  final String? label;
  final bool showPercentage;

  const WizardProgressIndicator({
    super.key,
    required this.progress,
    this.label,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label!,
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (showPercentage)
                Text(
                  '${(progress * 100).round()}%',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        LinearProgressIndicator(
          value: progress,
          backgroundColor: context.colors.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(context.colors.primary),
        ),
      ],
    );
  }
}

/// Step navigation buttons
class WizardNavigationButtons extends StatelessWidget {
  final bool canGoBack;
  final bool canProceed;
  final bool isLoading;
  final String? nextButtonText;
  final String? backButtonText;
  final VoidCallback? onNext;
  final VoidCallback? onBack;
  final VoidCallback? onSkip;
  final bool showSkip;

  const WizardNavigationButtons({
    super.key,
    required this.canGoBack,
    required this.canProceed,
    this.isLoading = false,
    this.nextButtonText,
    this.backButtonText,
    this.onNext,
    this.onBack,
    this.onSkip,
    this.showSkip = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Back button
          if (canGoBack)
            Expanded(
              child: OutlinedButton(
                onPressed: isLoading ? null : onBack,
                child: Text(backButtonText ?? 'Back'),
              ),
            ),

          if (canGoBack) const SizedBox(width: 16),

          // Skip button (if enabled)
          if (showSkip && onSkip != null) ...[
            TextButton(
              onPressed: isLoading ? null : onSkip,
              child: const Text('Skip'),
            ),
            const SizedBox(width: 16),
          ],

          // Next button
          Expanded(
            flex: canGoBack ? 1 : 2,
            child: ElevatedButton(
              onPressed: (canProceed && !isLoading) ? onNext : null,
              child:
                  isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : Text(nextButtonText ?? 'Next'),
            ),
          ),
        ],
      ),
    );
  }
}
