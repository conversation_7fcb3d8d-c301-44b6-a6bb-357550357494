import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:dalti_provider/features/locations/widgets/location_form_fields.dart';

void main() {
  group('Location Form Validation Tests', () {
    late TextEditingController controller;

    setUp(() {
      controller = TextEditingController();
    });

    tearDown(() {
      controller.dispose();
    });

    testWidgets('Name field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.nameField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation
      expect(formField.validator!(''), 'Location name is required');
      expect(formField.validator!('  '), 'Location name is required');
      
      // Test too short validation
      expect(formField.validator!('A'), 'Location name must be at least 2 characters');
      
      // Test too long validation
      expect(formField.validator!('A' * 101), 'Location name must be less than 100 characters');
      
      // Test valid input
      expect(formField.validator!('Main Clinic'), null);
      expect(formField.validator!('Downtown Office'), null);
    });

    testWidgets('Address field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.addressField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation
      expect(formField.validator!(''), 'Street address is required');
      
      // Test too short validation
      expect(formField.validator!('123'), 'Please enter a complete address');
      
      // Test too long validation
      expect(formField.validator!('A' * 201), 'Address must be less than 200 characters');
      
      // Test valid input
      expect(formField.validator!('123 Main Street'), null);
      expect(formField.validator!('456 Oak Avenue, Suite 100'), null);
    });

    testWidgets('City field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.cityField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation
      expect(formField.validator!(''), 'City is required');
      
      // Test too short validation
      expect(formField.validator!('A'), 'City name must be at least 2 characters');
      
      // Test too long validation
      expect(formField.validator!('A' * 51), 'City name must be less than 50 characters');
      
      // Test valid input
      expect(formField.validator!('Toronto'), null);
      expect(formField.validator!('Vancouver'), null);
    });

    testWidgets('Postal code field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.postalCodeField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation
      expect(formField.validator!(''), 'Postal code is required');
      
      // Test too short validation
      expect(formField.validator!('12'), 'Please enter a valid postal code');
      
      // Test too long validation
      expect(formField.validator!('12345678901'), 'Postal code must be less than 10 characters');
      
      // Test valid input
      expect(formField.validator!('M5V 3A8'), null);
      expect(formField.validator!('12345'), null);
      expect(formField.validator!('SW1A 1AA'), null);
    });

    testWidgets('Latitude field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.latitudeField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation (required by default)
      expect(formField.validator!(''), 'Latitude is required');
      
      // Test invalid number
      expect(formField.validator!('abc'), 'Please enter a valid latitude');
      expect(formField.validator!('12.34.56'), 'Please enter a valid latitude');
      
      // Test out of range
      expect(formField.validator!('91'), 'Latitude must be between -90 and 90');
      expect(formField.validator!('-91'), 'Latitude must be between -90 and 90');
      
      // Test valid input
      expect(formField.validator!('43.6532'), null);
      expect(formField.validator!('-43.6532'), null);
      expect(formField.validator!('0'), null);
      expect(formField.validator!('90'), null);
      expect(formField.validator!('-90'), null);
    });

    testWidgets('Longitude field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.longitudeField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation (required by default)
      expect(formField.validator!(''), 'Longitude is required');
      
      // Test invalid number
      expect(formField.validator!('xyz'), 'Please enter a valid longitude');
      
      // Test out of range
      expect(formField.validator!('181'), 'Longitude must be between -180 and 180');
      expect(formField.validator!('-181'), 'Longitude must be between -180 and 180');
      
      // Test valid input
      expect(formField.validator!('-79.3832'), null);
      expect(formField.validator!('79.3832'), null);
      expect(formField.validator!('0'), null);
      expect(formField.validator!('180'), null);
      expect(formField.validator!('-180'), null);
    });

    testWidgets('Optional coordinate fields work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: Column(
                children: [
                  LocationFormFields.latitudeField(
                    controller: controller,
                    required: false,
                  ),
                  LocationFormFields.longitudeField(
                    controller: TextEditingController(),
                    required: false,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      final formFields = tester.widgetList<TextFormField>(find.byType(TextFormField)).toList();
      final latitudeField = formFields[0];
      final longitudeField = formFields[1];
      
      // Test empty validation (should pass when not required)
      expect(latitudeField.validator!(''), null);
      expect(longitudeField.validator!(''), null);
      
      // Test that validation still works for invalid values
      expect(latitudeField.validator!('abc'), 'Please enter a valid latitude');
      expect(longitudeField.validator!('xyz'), 'Please enter a valid longitude');
      
      // Test valid values still pass
      expect(latitudeField.validator!('43.6532'), null);
      expect(longitudeField.validator!('-79.3832'), null);
    });

    testWidgets('Country field validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: LocationFormFields.countryField(controller: controller),
            ),
          ),
        ),
      );

      final formField = tester.widget<TextFormField>(find.byType(TextFormField));
      
      // Test empty validation
      expect(formField.validator!(''), 'Country is required');
      
      // Test too short validation
      expect(formField.validator!('A'), 'Country name must be at least 2 characters');
      
      // Test too long validation
      expect(formField.validator!('A' * 51), 'Country name must be less than 50 characters');
      
      // Test valid input
      expect(formField.validator!('Canada'), null);
      expect(formField.validator!('United States'), null);
      expect(formField.validator!('UK'), null);
    });
  });
}
