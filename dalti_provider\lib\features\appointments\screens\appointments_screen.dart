import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/appointment_models.dart';
import '../widgets/table_calendar_widget.dart';

class AppointmentsScreen extends ConsumerWidget {
  const AppointmentsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text('Calendar'),
      //   actions: [
      //     IconButton(
      //       onPressed: () => _showAppointmentSearch(context),
      //       icon: const Icon(Icons.search),
      //       tooltip: 'Search appointments',
      //     ),
      //     IconButton(
      //       onPressed: () => _showAppointmentFilters(context),
      //       icon: const Icon(Icons.filter_list),
      //       tooltip: 'Filter appointments',
      //     ),
      //   ],
      // ),
      body: TableCalendarWidget(
        onAppointmentTap:
            (appointment) => _onAppointmentTap(context, appointment),
        onAddAppointment: () => _onAddAppointment(context),
        onAddAppointmentAtTime:
            (dateTime) => _onAddAppointmentAtTime(context, dateTime),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _onAddAppointment(context),
        child: const Icon(Icons.add),
        tooltip: 'Add Appointment',
      ),
    );
  }

  void _onAppointmentTap(BuildContext context, Appointment appointment) {
    context.push('/appointments/${appointment.id}/details', extra: appointment);
  }

  void _onAddAppointment(BuildContext context) {
    context.push('/appointments/add');
  }

  void _onAddAppointmentAtTime(BuildContext context, DateTime presetTime) {
    // Navigate to add appointment screen with preset time
    // We'll pass the preset time as extra data
    context.push('/appointments/add', extra: {'presetTime': presetTime});
  }
}
