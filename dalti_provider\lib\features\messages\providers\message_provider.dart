import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/message_models.dart';
import '../services/message_api_service.dart';
import '../services/customer_message_service.dart';
import '../repository/message_repository.dart';
import '../repository/message_repository_impl.dart';
import '../../customers/providers/customer_provider.dart';

part 'message_provider.g.dart';

/// Message API service provider
@riverpod
MessageApiService messageApiService(MessageApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return MessageApiService(httpClient);
}

/// Conversations state for managing conversations list
class ConversationsState {
  final List<Conversation> conversations;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final String? currentSearchQuery;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final int unreadCount;
  final bool hasMore;

  const ConversationsState({
    this.conversations = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.currentSearchQuery,
    this.currentPage = 1,
    this.totalPages = 0,
    this.totalCount = 0,
    this.unreadCount = 0,
    this.hasMore = false,
  });

  ConversationsState copyWith({
    List<Conversation>? conversations,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    String? currentSearchQuery,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    int? unreadCount,
    bool? hasMore,
  }) {
    return ConversationsState(
      conversations: conversations ?? this.conversations,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error,
      currentSearchQuery: currentSearchQuery ?? this.currentSearchQuery,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      unreadCount: unreadCount ?? this.unreadCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

/// Messages state for managing messages in a conversation
class MessagesState {
  final List<Message> messages;
  final bool isLoading;
  final bool isRefreshing;
  final bool isSending;
  final String? error;
  final String? currentSearchQuery;
  final int currentPage;
  final int totalPages;
  final int totalCount;
  final bool hasMore;

  const MessagesState({
    this.messages = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.isSending = false,
    this.error,
    this.currentSearchQuery,
    this.currentPage = 1,
    this.totalPages = 0,
    this.totalCount = 0,
    this.hasMore = false,
  });

  MessagesState copyWith({
    List<Message>? messages,
    bool? isLoading,
    bool? isRefreshing,
    bool? isSending,
    String? error,
    String? currentSearchQuery,
    int? currentPage,
    int? totalPages,
    int? totalCount,
    bool? hasMore,
  }) {
    return MessagesState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isSending: isSending ?? this.isSending,
      error: error,
      currentSearchQuery: currentSearchQuery ?? this.currentSearchQuery,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}

/// Message repository provider
@riverpod
MessageRepository messageRepository(MessageRepositoryRef ref) {
  final apiService = ref.watch(messageApiServiceProvider);
  return MessageRepositoryImpl(apiService);
}

/// Customer message service provider
@riverpod
CustomerMessageService customerMessageService(CustomerMessageServiceRef ref) {
  final customerRepository = ref.watch(customerRepositoryProvider);
  return CustomerMessageService(customerRepository);
}

/// Conversations notifier for managing conversations state
@riverpod
class ConversationsNotifier extends _$ConversationsNotifier {
  @override
  ConversationsState build() {
    return const ConversationsState();
  }

  /// Load conversations with optional search query
  Future<void> loadConversations({
    String? searchQuery,
    bool refresh = false,
  }) async {
    final repository = ref.read(messageRepositoryProvider);

    if (refresh) {
      state = state.copyWith(isRefreshing: true, error: null);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final conversations = await repository.getConversations(
        searchQuery: searchQuery,
        page: 1,
        pageSize: 20,
      );

      state = state.copyWith(
        conversations: conversations,
        isLoading: false,
        isRefreshing: false,
        currentSearchQuery: searchQuery,
        currentPage: 1,
        hasMore: conversations.length >= 20,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Load more conversations (pagination)
  Future<void> loadMoreConversations() async {
    if (state.isLoading || !state.hasMore) return;

    final repository = ref.read(messageRepositoryProvider);
    final nextPage = state.currentPage + 1;

    try {
      final newConversations = await repository.getConversations(
        searchQuery: state.currentSearchQuery,
        page: nextPage,
        pageSize: 20,
      );

      state = state.copyWith(
        conversations: [...state.conversations, ...newConversations],
        currentPage: nextPage,
        hasMore: newConversations.length >= 20,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Search conversations
  Future<void> searchConversations(String query) async {
    await loadConversations(searchQuery: query);
  }

  /// Create a new conversation
  Future<Conversation?> createConversation(
    CreateConversationRequest request,
  ) async {
    final repository = ref.read(messageRepositoryProvider);

    try {
      final conversation = await repository.createConversation(request);

      // Add to the beginning of the list
      state = state.copyWith(
        conversations: [conversation, ...state.conversations],
        totalCount: state.totalCount + 1,
      );

      return conversation;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// Archive a conversation
  Future<bool> archiveConversation(String conversationId) async {
    final repository = ref.read(messageRepositoryProvider);

    try {
      final success = await repository.archiveConversation(conversationId);

      if (success) {
        // Remove from current list or update status
        final updatedConversations =
            state.conversations.where((c) => c.id != conversationId).toList();

        state = state.copyWith(conversations: updatedConversations);
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Delete a conversation
  Future<bool> deleteConversation(String conversationId) async {
    final repository = ref.read(messageRepositoryProvider);

    try {
      final success = await repository.deleteConversation(conversationId);

      if (success) {
        final updatedConversations =
            state.conversations.where((c) => c.id != conversationId).toList();

        state = state.copyWith(conversations: updatedConversations);
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Refresh conversations
  Future<void> refreshConversations() async {
    await loadConversations(
      searchQuery: state.currentSearchQuery,
      refresh: true,
    );
  }

  /// Update unread count
  void updateUnreadCount(int count) {
    state = state.copyWith(unreadCount: count);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Messages notifier for managing messages in a conversation
@riverpod
class MessagesNotifier extends _$MessagesNotifier {
  @override
  MessagesState build(String conversationId) {
    return const MessagesState();
  }

  /// Load messages for the conversation
  Future<void> loadMessages({String? searchQuery, bool refresh = false}) async {
    final repository = ref.read(messageRepositoryProvider);
    final conversationId = this.conversationId;

    if (refresh) {
      state = state.copyWith(isRefreshing: true, error: null);
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final messages = await repository.getMessages(
        conversationId,
        searchQuery: searchQuery,
        page: 1,
        pageSize: 50,
      );

      state = state.copyWith(
        messages: messages,
        isLoading: false,
        isRefreshing: false,
        currentSearchQuery: searchQuery,
        currentPage: 1,
        hasMore: messages.length >= 50,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Load more messages (pagination)
  Future<void> loadMoreMessages() async {
    if (state.isLoading || !state.hasMore) return;

    final repository = ref.read(messageRepositoryProvider);
    final conversationId = this.conversationId;
    final nextPage = state.currentPage + 1;

    try {
      final newMessages = await repository.getMessages(
        conversationId,
        searchQuery: state.currentSearchQuery,
        page: nextPage,
        pageSize: 50,
      );

      state = state.copyWith(
        messages: [...state.messages, ...newMessages],
        currentPage: nextPage,
        hasMore: newMessages.length >= 50,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// Send a message
  Future<bool> sendMessage(SendMessageRequest request) async {
    final repository = ref.read(messageRepositoryProvider);

    state = state.copyWith(isSending: true, error: null);

    try {
      final message = await repository.sendMessage(request);

      // Add the new message to the end of the list (newest at bottom)
      state = state.copyWith(
        messages: [...state.messages, message],
        isSending: false,
        totalCount: state.totalCount + 1,
      );

      return true;
    } catch (e) {
      state = state.copyWith(isSending: false, error: e.toString());
      return false;
    }
  }

  /// Mark messages as read
  Future<bool> markMessagesAsRead(List<String> messageIds) async {
    final repository = ref.read(messageRepositoryProvider);
    final conversationId = this.conversationId;

    try {
      final success = await repository.markMessagesAsRead(
        conversationId,
        messageIds,
      );

      if (success) {
        // Update message read status in the local state
        final updatedMessages =
            state.messages.map((message) {
              if (messageIds.contains(message.id)) {
                return Message(
                  id: message.id,
                  conversationId: message.conversationId,
                  senderId: message.senderId,
                  senderName: message.senderName,
                  type: message.type,
                  content: message.content,
                  timestamp: message.timestamp,
                  status: MessageStatus.read,
                  replyToMessageId: message.replyToMessageId,
                  metadata: message.metadata,
                  readAt: DateTime.now(),
                  deliveredAt: message.deliveredAt,
                );
              }
              return message;
            }).toList();

        state = state.copyWith(messages: updatedMessages);
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Refresh messages
  Future<void> refreshMessages() async {
    await loadMessages(searchQuery: state.currentSearchQuery, refresh: true);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Mark a single message as read
  Future<bool> markMessageAsRead(String messageId) async {
    final repository = ref.read(messageRepositoryProvider);
    final conversationId = this.conversationId;

    try {
      final request = MarkAsReadRequest(
        messageId: messageId,
        conversationId: conversationId,
      );

      final response = await repository.markMessageAsRead(request);

      if (response.success) {
        // Update the message status in the local state
        final updatedMessages =
            state.messages.map((message) {
              if (message.id == messageId) {
                return Message(
                  id: message.id,
                  conversationId: message.conversationId,
                  senderId: message.senderId,
                  senderName: message.senderName,
                  type: message.type,
                  content: message.content,
                  timestamp: message.timestamp,
                  status: MessageStatus.read,
                  replyToMessageId: message.replyToMessageId,
                  metadata: message.metadata,
                  readAt: DateTime.now(),
                  deliveredAt: message.deliveredAt,
                );
              }
              return message;
            }).toList();

        state = state.copyWith(messages: updatedMessages);

        // Also refresh the unread count
        ref
            .read(unreadMessagesCountNotifierProvider.notifier)
            .refreshUnreadCount();

        return true;
      }

      return false;
    } catch (e) {
      print('Error marking message as read: $e');
      return false;
    }
  }
}

/// Unread messages count provider
@riverpod
class UnreadMessagesCountNotifier extends _$UnreadMessagesCountNotifier {
  @override
  int build() {
    _loadUnreadCount();
    return 0;
  }

  Future<void> _loadUnreadCount() async {
    final repository = ref.read(messageRepositoryProvider);

    try {
      final count = await repository.getUnreadMessagesCount();
      state = count;
    } catch (e) {
      // Handle error silently for unread count
      print('Error loading unread count: $e');
    }
  }

  /// Refresh unread count
  Future<void> refreshUnreadCount() async {
    await _loadUnreadCount();
  }

  /// Update unread count manually
  void updateCount(int count) {
    state = count;
  }
}

/// Provider for getting a specific conversation
@riverpod
Future<Conversation?> conversation(
  ConversationRef ref,
  String conversationId,
) async {
  final repository = ref.watch(messageRepositoryProvider);

  try {
    return await repository.getConversation(conversationId);
  } catch (e) {
    return null;
  }
}
