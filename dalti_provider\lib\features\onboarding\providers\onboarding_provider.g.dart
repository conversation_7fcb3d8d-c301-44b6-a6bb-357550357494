// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'onboarding_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$onboardingApiServiceHash() =>
    r'd3475c5d445a59e1f54f04cfda54226724942830';

/// Provider for OnboardingApiService
///
/// Copied from [onboardingApiService].
@ProviderFor(onboardingApiService)
final onboardingApiServiceProvider =
    AutoDisposeProvider<OnboardingApiService>.internal(
  onboardingApiService,
  name: r'onboardingApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onboardingApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OnboardingApiServiceRef = AutoDisposeProviderRef<OnboardingApiService>;
String _$onboardingRepositoryHash() =>
    r'279acce23d8b40656a8e828f55f1fc4456ada06c';

/// Provider for OnboardingRepository
///
/// Copied from [onboardingRepository].
@ProviderFor(onboardingRepository)
final onboardingRepositoryProvider =
    AutoDisposeProvider<OnboardingRepository>.internal(
  onboardingRepository,
  name: r'onboardingRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onboardingRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef OnboardingRepositoryRef = AutoDisposeProviderRef<OnboardingRepository>;
String _$needsOnboardingHash() => r'b917394968588306bd09c4714a0aacbe1c032959';

/// Provider to check if user needs onboarding
///
/// Copied from [needsOnboarding].
@ProviderFor(needsOnboarding)
final needsOnboardingProvider = AutoDisposeFutureProvider<bool>.internal(
  needsOnboarding,
  name: r'needsOnboardingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$needsOnboardingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NeedsOnboardingRef = AutoDisposeFutureProviderRef<bool>;
String _$businessCategoriesHash() =>
    r'22cef5aeb65130c2f749629bedcf40a0b4729402';

/// Provider for business categories
///
/// Copied from [businessCategories].
@ProviderFor(businessCategories)
final businessCategoriesProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  businessCategories,
  name: r'businessCategoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$businessCategoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BusinessCategoriesRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$algerianCitiesHash() => r'9099d629acdd3ec0ad7895de319169e801e06d3f';

/// Provider for Algerian cities
///
/// Copied from [algerianCities].
@ProviderFor(algerianCities)
final algerianCitiesProvider = AutoDisposeFutureProvider<List<String>>.internal(
  algerianCities,
  name: r'algerianCitiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$algerianCitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AlgerianCitiesRef = AutoDisposeFutureProviderRef<List<String>>;
String _$onboardingNotifierHash() =>
    r'71b37debae0e3aa41ae3c1732f9bae3993a728eb';

/// Provider for onboarding state management
///
/// Copied from [OnboardingNotifier].
@ProviderFor(OnboardingNotifier)
final onboardingNotifierProvider =
    AutoDisposeNotifierProvider<OnboardingNotifier, OnboardingState>.internal(
  OnboardingNotifier.new,
  name: r'onboardingNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$onboardingNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OnboardingNotifier = AutoDisposeNotifier<OnboardingState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
