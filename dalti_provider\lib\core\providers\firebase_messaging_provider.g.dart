// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'firebase_messaging_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$initializeFirebaseMessagingHash() =>
    r'0aad046983b4c6a395a315c9edd17222db849732';

/// Provider for Firebase messaging service initialization
///
/// Copied from [initializeFirebaseMessaging].
@ProviderFor(initializeFirebaseMessaging)
final initializeFirebaseMessagingProvider =
    AutoDisposeFutureProvider<void>.internal(
  initializeFirebaseMessaging,
  name: r'initializeFirebaseMessagingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initializeFirebaseMessagingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InitializeFirebaseMessagingRef = AutoDisposeFutureProviderRef<void>;
String _$fcmTokenHash() => r'087f0dfd6fff9c642390d1799e550c55da33b230';

/// Provider for getting FCM token
///
/// Copied from [fcmToken].
@ProviderFor(fcmToken)
final fcmTokenProvider = AutoDisposeFutureProvider<String?>.internal(
  fcmToken,
  name: r'fcmTokenProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fcmTokenHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FcmTokenRef = AutoDisposeFutureProviderRef<String?>;
String _$topicSubscriptionHash() => r'84ba5a5c27d1164cf8ee243ba83f8eaa844c6ca9';

/// Provider for subscribing to topics
///
/// Copied from [TopicSubscription].
@ProviderFor(TopicSubscription)
final topicSubscriptionProvider =
    AutoDisposeNotifierProvider<TopicSubscription, Set<String>>.internal(
  TopicSubscription.new,
  name: r'topicSubscriptionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$topicSubscriptionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TopicSubscription = AutoDisposeNotifier<Set<String>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
