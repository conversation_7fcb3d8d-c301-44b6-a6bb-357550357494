import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/onboarding_models.dart';
import '../models/wizard_persistence_models.dart';
import '../services/wizard_auto_save_service.dart';
import '../services/wizard_recovery_service.dart';
import 'wizard_navigation_provider.dart';
import 'onboarding_provider.dart';

/// Enhanced wizard navigation provider with persistence support
class EnhancedWizardNavigationNotifier extends StateNotifier<WizardNavigationState> {
  final Ref _ref;
  OnboardingStep? _previousStep;

  EnhancedWizardNavigationNotifier(this._ref) : super(
    const WizardNavigationState(
      currentStep: OnboardingStep.welcome,
      currentStepIndex: 0,
      canGoNext: true,
      canGoPrevious: false,
    ),
  ) {
    // Initialize auto-save monitoring
    _initializeAutoSave();
  }

  /// Initialize auto-save monitoring
  void _initializeAutoSave() {
    // Monitor state changes and trigger auto-save
    addListener((state) {
      if (_previousStep != null && _previousStep != state.currentStep) {
        _triggerAutoSaveOnStepChange(_previousStep!, state.currentStep);
      }
      _previousStep = state.currentStep;
    });
  }

  /// Navigate to the next step with auto-save
  void goToNextStep() {
    if (!state.canGoNext || state.isLoading) return;

    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    if (currentIndex < allSteps.length - 1) {
      final nextStep = allSteps[currentIndex + 1];
      final nextIndex = currentIndex + 1;
      
      _updateNavigationState(
        currentStep: nextStep,
        currentStepIndex: nextIndex,
      );
    }
  }

  /// Navigate to the previous step with auto-save
  void goToPreviousStep() {
    if (!state.canGoPrevious || state.isLoading) return;

    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    if (currentIndex > 0) {
      final previousStep = allSteps[currentIndex - 1];
      final previousIndex = currentIndex - 1;
      
      _updateNavigationState(
        currentStep: previousStep,
        currentStepIndex: previousIndex,
      );
    }
  }

  /// Jump to a specific step with auto-save
  void goToStep(OnboardingStep step) {
    if (state.isLoading) return;

    final allSteps = state.allSteps;
    final stepIndex = allSteps.indexOf(step);
    
    if (stepIndex >= 0) {
      _updateNavigationState(
        currentStep: step,
        currentStepIndex: stepIndex,
      );
    }
  }

  /// Mark a step as completed with auto-save
  void markStepCompleted(OnboardingStep step, bool isCompleted) {
    final updatedCompletionStatus = Map<OnboardingStep, bool>.from(state.stepCompletionStatus);
    updatedCompletionStatus[step] = isCompleted;
    
    state = state.copyWith(
      stepCompletionStatus: updatedCompletionStatus,
    );
    
    // Update navigation capabilities
    _updateNavigationCapabilities();
    
    // Trigger auto-save on step completion
    if (isCompleted) {
      _triggerAutoSaveOnStepComplete(step);
    }
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set error state
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// Reset wizard to welcome step
  void resetWizard() {
    state = const WizardNavigationState(
      currentStep: OnboardingStep.welcome,
      currentStepIndex: 0,
      canGoNext: true,
      canGoPrevious: false,
    );
    
    // Clear persisted state
    _ref.read(wizardAutoSaveServiceProvider).triggerAutoSave(
      reason: 'wizard_reset',
      immediate: true,
    );
  }

  /// Complete the wizard
  void completeWizard() {
    _updateNavigationState(
      currentStep: OnboardingStep.completed,
      currentStepIndex: -1,
    );
    
    // Force save on completion
    _ref.read(wizardAutoSaveServiceProvider).triggerAutoSave(
      reason: 'wizard_completed',
      immediate: true,
    );
  }

  /// Restore state from persistence
  Future<bool> restoreFromPersistence() async {
    try {
      setLoading(true);
      
      final recoveryService = _ref.read(wizardRecoveryServiceProvider);
      final recoveryInfo = await recoveryService.checkRecoveryAvailable();
      
      if (recoveryInfo == null) {
        setLoading(false);
        return false;
      }

      final result = await recoveryService.recoverState(_ref);
      
      if (result.success && result.recoveredStep != null) {
        // State has been restored by the recovery service
        // Just update our local state to match
        final allSteps = state.allSteps;
        final stepIndex = allSteps.indexOf(result.recoveredStep!);
        
        if (stepIndex >= 0) {
          state = state.copyWith(
            currentStep: result.recoveredStep!,
            currentStepIndex: stepIndex,
            isLoading: false,
          );
          
          _updateNavigationCapabilities();
          return true;
        }
      }
      
      setLoading(false);
      return false;
    } catch (e) {
      print('[EnhancedWizardNavigation] Error restoring from persistence: $e');
      setLoading(false);
      return false;
    }
  }

  /// Force save current state
  Future<PersistenceResult> forceSave({String? reason}) async {
    final autoSaveService = _ref.read(wizardAutoSaveServiceProvider);
    return await autoSaveService.forceSave(reason: reason);
  }

  /// Update navigation state and capabilities
  void _updateNavigationState({
    required OnboardingStep currentStep,
    required int currentStepIndex,
  }) {
    state = state.copyWith(
      currentStep: currentStep,
      currentStepIndex: currentStepIndex,
    );
    
    _updateNavigationCapabilities();
  }

  /// Update navigation capabilities based on current state
  void _updateNavigationCapabilities() {
    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    // Can go previous if not on first step and not completed
    final canGoPrevious = currentIndex > 0 && state.currentStep != OnboardingStep.completed;
    
    // Can go next based on current step logic
    bool canGoNext = false;
    
    switch (state.currentStep) {
      case OnboardingStep.welcome:
        canGoNext = true; // Welcome step can always proceed
        break;
      case OnboardingStep.businessProfile:
        canGoNext = state.isStepCompleted(OnboardingStep.businessProfile);
        break;
      case OnboardingStep.locationSetup:
        canGoNext = state.isStepCompleted(OnboardingStep.locationSetup);
        break;
      case OnboardingStep.serviceCreation:
        canGoNext = state.isStepCompleted(OnboardingStep.serviceCreation);
        break;
      case OnboardingStep.scheduleSetup:
        canGoNext = true; // Schedule is integrated into location step
        break;
      case OnboardingStep.queueManagement:
        canGoNext = state.isStepCompleted(OnboardingStep.queueManagement);
        break;
      case OnboardingStep.summary:
        canGoNext = true; // Summary can proceed to completion
        break;
      case OnboardingStep.completed:
        canGoNext = false; // No next step after completion
        break;
    }
    
    // Can't go next if on last step (summary)
    if (currentIndex >= allSteps.length - 1) {
      canGoNext = false;
    }
    
    state = state.copyWith(
      canGoNext: canGoNext,
      canGoPrevious: canGoPrevious,
    );
  }

  /// Get step validation requirements
  bool isStepValid(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.welcome:
        return true; // Welcome step is always valid
      case OnboardingStep.businessProfile:
        return state.isStepCompleted(OnboardingStep.businessProfile);
      case OnboardingStep.locationSetup:
        return state.isStepCompleted(OnboardingStep.locationSetup);
      case OnboardingStep.serviceCreation:
        return state.isStepCompleted(OnboardingStep.serviceCreation);
      case OnboardingStep.scheduleSetup:
        return true; // Integrated into location step
      case OnboardingStep.queueManagement:
        return state.isStepCompleted(OnboardingStep.queueManagement);
      case OnboardingStep.summary:
        return true; // Summary step shows completion status
      case OnboardingStep.completed:
        return true;
    }
  }

  /// Trigger auto-save on step change
  void _triggerAutoSaveOnStepChange(OnboardingStep fromStep, OnboardingStep toStep) {
    try {
      final autoSaveService = _ref.read(wizardAutoSaveServiceProvider);
      autoSaveService.onStepChange(fromStep, toStep);
    } catch (e) {
      print('[EnhancedWizardNavigation] Error triggering auto-save on step change: $e');
    }
  }

  /// Trigger auto-save on step completion
  void _triggerAutoSaveOnStepComplete(OnboardingStep step) {
    try {
      final autoSaveService = _ref.read(wizardAutoSaveServiceProvider);
      autoSaveService.onStepComplete(step);
    } catch (e) {
      print('[EnhancedWizardNavigation] Error triggering auto-save on step complete: $e');
    }
  }
}

/// Enhanced provider for wizard navigation with persistence
final enhancedWizardNavigationProvider = StateNotifierProvider<EnhancedWizardNavigationNotifier, WizardNavigationState>(
  (ref) => EnhancedWizardNavigationNotifier(ref),
);

/// Convenience providers for enhanced navigation
final enhancedCurrentStepProvider = Provider<OnboardingStep>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).currentStep;
});

final enhancedCanGoNextProvider = Provider<bool>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).canGoNext;
});

final enhancedCanGoPreviousProvider = Provider<bool>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).canGoPrevious;
});

final enhancedWizardProgressProvider = Provider<double>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).progress;
});

final enhancedIsWizardLoadingProvider = Provider<bool>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).isLoading;
});

final enhancedWizardErrorProvider = Provider<String?>((ref) {
  return ref.watch(enhancedWizardNavigationProvider).error;
});

/// Extension for enhanced navigation features
extension EnhancedNavigationExtension on WidgetRef {
  /// Get enhanced wizard navigation notifier
  EnhancedWizardNavigationNotifier get enhancedWizardNavigation => 
      read(enhancedWizardNavigationProvider.notifier);

  /// Restore wizard state from persistence
  Future<bool> restoreWizardState() => 
      enhancedWizardNavigation.restoreFromPersistence();

  /// Force save wizard state
  Future<PersistenceResult> saveWizardState({String? reason}) => 
      enhancedWizardNavigation.forceSave(reason: reason);
}
