import 'package:flutter/material.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/message_models.dart';

class ConversationCard extends StatelessWidget {
  final Conversation conversation;
  final VoidCallback onTap;
  final VoidCallback? onArchive;
  final VoidCallback? onDelete;

  const ConversationCard({
    super.key,
    required this.conversation,
    required this.onTap,
    this.onArchive,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              _buildAvatar(context),
              const SizedBox(width: 12),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer name and timestamp
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            conversation.customerName,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (conversation.lastMessage != null)
                          Text(
                            _formatTimestamp(
                              conversation.lastMessage!.timestamp,
                            ),
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.colors.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),

                    // Phone number
                    if (conversation.customerPhone != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        conversation.customerPhone!,
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.onSurfaceVariant,
                        ),
                      ),
                    ],

                    // Last message
                    if (conversation.lastMessage != null) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getLastMessagePreview(conversation.lastMessage!),
                              style: context.textTheme.bodyMedium?.copyWith(
                                color:
                                    conversation.unreadCount > 0
                                        ? context.colors.onSurface
                                        : context.colors.onSurfaceVariant,
                                fontWeight:
                                    conversation.unreadCount > 0
                                        ? FontWeight.w500
                                        : FontWeight.normal,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          // Message status indicator
                          if (conversation.lastMessage!.senderId.startsWith(
                            'provider_',
                          ))
                            _buildMessageStatusIcon(
                              context,
                              conversation.lastMessage!,
                            ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Unread count and actions
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Unread count badge
                  if (conversation.unreadCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: context.colors.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        conversation.unreadCount.toString(),
                        style: context.textTheme.labelSmall?.copyWith(
                          color: context.colors.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleAction(value),
                    icon: Icon(
                      Icons.more_vert,
                      size: 20,
                      color: context.colors.onSurfaceVariant,
                    ),
                    itemBuilder:
                        (context) => [
                          if (onArchive != null)
                            const PopupMenuItem(
                              value: 'archive',
                              child: ListTile(
                                leading: Icon(Icons.archive),
                                title: Text('Archive'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          if (onDelete != null)
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete),
                                title: Text('Delete'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                        ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    if (conversation.customerAvatar != null) {
      return CircleAvatar(
        radius: 24,
        backgroundImage: NetworkImage(conversation.customerAvatar!),
        onBackgroundImageError: (_, __) {
          // Fallback to initials if image fails to load
        },
        child:
            conversation.customerAvatar == null
                ? Text(
                  _getInitials(conversation.customerName),
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.colors.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      radius: 24,
      backgroundColor: context.colors.primary,
      child: Text(
        _getInitials(conversation.customerName),
        style: context.textTheme.titleMedium?.copyWith(
          color: context.colors.onPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildMessageStatusIcon(BuildContext context, Message message) {
    IconData icon;
    Color color;

    switch (message.status) {
      case MessageStatus.sent:
        icon = Icons.check;
        color = context.colors.onSurfaceVariant;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = context.colors.onSurfaceVariant;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = context.colors.primary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = context.colors.error;
        break;
    }

    return Icon(icon, size: 16, color: color);
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '?';

    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    }

    return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'
        .toUpperCase();
  }

  String _getLastMessagePreview(Message message) {
    switch (message.type) {
      case MessageType.text:
        return message.content;
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 File';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.other:
        return '📄 Attachment';
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d ago';
      } else {
        return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
      }
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _handleAction(String action) {
    switch (action) {
      case 'archive':
        onArchive?.call();
        break;
      case 'delete':
        onDelete?.call();
        break;
    }
  }
}
