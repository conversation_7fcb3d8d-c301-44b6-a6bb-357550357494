import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../schedules/models/opening_hours_models.dart';
import '../../../../core/theme/theme_provider.dart';

/// Dialog for editing time slots
class TimeSlotDialog extends ConsumerStatefulWidget {
  final TimeSlot? initialTimeSlot;
  final String dayName;

  const TimeSlotDialog({
    super.key,
    this.initialTimeSlot,
    required this.dayName,
  });

  @override
  ConsumerState<TimeSlotDialog> createState() => _TimeSlotDialogState();

  /// Show the time slot dialog
  static Future<TimeSlot?> show(
    BuildContext context, {
    TimeSlot? initialTimeSlot,
    required String dayName,
  }) {
    return showDialog<TimeSlot>(
      context: context,
      builder: (context) => TimeSlotDialog(
        initialTimeSlot: initialTimeSlot,
        dayName: dayName,
      ),
    );
  }
}

class _TimeSlotDialogState extends ConsumerState<TimeSlotDialog> {
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    
    if (widget.initialTimeSlot != null) {
      _startTime = _parseTimeString(widget.initialTimeSlot!.timeFrom);
      _endTime = _parseTimeString(widget.initialTimeSlot!.timeTo);
    } else {
      _startTime = const TimeOfDay(hour: 9, minute: 0);
      _endTime = const TimeOfDay(hour: 17, minute: 0);
    }
  }

  TimeOfDay _parseTimeString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  bool _validateTimes() {
    final startMinutes = _startTime.hour * 60 + _startTime.minute;
    final endMinutes = _endTime.hour * 60 + _endTime.minute;
    
    if (startMinutes >= endMinutes) {
      setState(() {
        _errorMessage = 'End time must be after start time';
      });
      return false;
    }
    
    setState(() {
      _errorMessage = null;
    });
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = ref.watch(themeProvider);
    
    return AlertDialog(
      title: Text(
        widget.initialTimeSlot != null 
            ? 'Edit ${widget.dayName} Hours'
            : 'Add ${widget.dayName} Hours',
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Start time
          ListTile(
            leading: Icon(
              Icons.access_time,
              color: theme.colors.primary,
            ),
            title: const Text('Start Time'),
            subtitle: Text(_formatTimeOfDay(_startTime)),
            onTap: () => _selectTime(context, true),
            contentPadding: EdgeInsets.zero,
          ),
          
          const SizedBox(height: 8),
          
          // End time
          ListTile(
            leading: Icon(
              Icons.access_time_filled,
              color: theme.colors.primary,
            ),
            title: const Text('End Time'),
            subtitle: Text(_formatTimeOfDay(_endTime)),
            onTap: () => _selectTime(context, false),
            contentPadding: EdgeInsets.zero,
          ),
          
          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colors.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colors.error.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: theme.colors.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colors.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Duration info
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: theme.colors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Duration: ${_calculateDuration()}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _validateTimes() ? _saveTimeSlot : null,
          child: Text(widget.initialTimeSlot != null ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
      _validateTimes();
    }
  }

  String _calculateDuration() {
    final startMinutes = _startTime.hour * 60 + _startTime.minute;
    final endMinutes = _endTime.hour * 60 + _endTime.minute;
    
    if (endMinutes <= startMinutes) {
      return 'Invalid';
    }
    
    final durationMinutes = endMinutes - startMinutes;
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;
    
    if (hours == 0) {
      return '${minutes}min';
    } else if (minutes == 0) {
      return '${hours}h';
    } else {
      return '${hours}h ${minutes}min';
    }
  }

  void _saveTimeSlot() {
    if (!_validateTimes()) return;
    
    final timeSlot = TimeSlot(
      timeFrom: _formatTimeOfDay(_startTime),
      timeTo: _formatTimeOfDay(_endTime),
    );
    
    Navigator.of(context).pop(timeSlot);
  }
}

/// Quick time preset buttons
class TimePresetButtons extends StatelessWidget {
  final ValueChanged<TimeSlot> onPresetSelected;

  const TimePresetButtons({
    super.key,
    required this.onPresetSelected,
  });

  static const List<Map<String, dynamic>> _presets = [
    {'label': 'Morning', 'start': '08:00', 'end': '12:00'},
    {'label': 'Afternoon', 'start': '14:00', 'end': '18:00'},
    {'label': 'Full Day', 'start': '08:00', 'end': '18:00'},
    {'label': 'Evening', 'start': '18:00', 'end': '22:00'},
  ];

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _presets.map((preset) {
        return ActionChip(
          label: Text(preset['label']),
          onPressed: () {
            final timeSlot = TimeSlot(
              timeFrom: preset['start'],
              timeTo: preset['end'],
            );
            onPresetSelected(timeSlot);
          },
        );
      }).toList(),
    );
  }
}
