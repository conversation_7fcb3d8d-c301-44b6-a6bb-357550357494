import '../storage/storage_service.dart';
import '../storage/web_storage_service.dart';
import '../auth/jwt_service.dart';

/// Debug utilities for session persistence testing
class SessionDebug {
  /// Print detailed session state information
  static void printSessionState() {
    print('=== SESSION DEBUG INFO ===');
    
    try {
      // Check raw storage data using WebStorageService
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      print('Raw token data in WebStorage: ${tokenData != null ? "EXISTS" : "NULL"}');

      if (tokenData != null) {
        print('Token keys: ${tokenData.keys.toList()}');
        print('Access token: ${tokenData['access_token']?.toString().substring(0, 10)}...');
        print('Expires at: ${tokenData['expires_at']}');
        print('Token type: ${tokenData['token_type']}');
      }

      // Check other auth data
      final allAuthKeys = WebStorageService.getAuthKeys();
      print('All auth storage keys: $allAuthKeys');
      
    } catch (e) {
      print('Error reading storage: $e');
    }
    
    print('=== END SESSION DEBUG ===');
  }
  
  /// Test session persistence by storing and retrieving a test token
  static Future<void> testSessionPersistence() async {
    print('=== TESTING SESSION PERSISTENCE ===');
    
    try {
      // Store test data
      final testData = {
        'access_token': 'test_token_12345',
        'refresh_token': 'refresh_token_67890',
        'expires_at': DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
        'token_type': 'Bearer',
        'payload': {
          'sub': 'test_user_123',
          'email': '<EMAIL>',
        }
      };
      
      await WebStorageService.saveAuth('jwt_token', testData);
      print('✅ Test token stored');

      // Retrieve test data
      final retrievedData = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      print('✅ Test token retrieved: ${retrievedData != null ? "SUCCESS" : "FAILED"}');

      if (retrievedData != null) {
        print('Retrieved access token: ${retrievedData['access_token']}');
        print('Retrieved expires at: ${retrievedData['expires_at']}');
      }

      // Clean up
      await WebStorageService.removeAuth('jwt_token');
      print('✅ Test token cleaned up');
      
    } catch (e) {
      print('❌ Session persistence test failed: $e');
    }
    
    print('=== END SESSION PERSISTENCE TEST ===');
  }
  
  /// Compare JWT service state with storage state
  static void compareJwtServiceWithStorage(JwtService jwtService) {
    print('=== JWT SERVICE vs STORAGE COMPARISON ===');
    
    try {
      // JWT Service state
      print('JWT Service:');
      print('  - Is authenticated: ${jwtService.isAuthenticated}');
      print('  - Current token: ${jwtService.currentToken != null ? "EXISTS" : "NULL"}');
      print('  - User ID: ${jwtService.currentUserId}');
      print('  - User email: ${jwtService.currentUserEmail}');
      
      if (jwtService.currentToken != null) {
        print('  - Access token: ${jwtService.currentToken!.accessToken.substring(0, 10)}...');
        print('  - Expires at: ${jwtService.currentToken!.expiresAt}');
        print('  - Is valid: ${jwtService.currentToken!.isValid}');
        print('  - Is expired: ${jwtService.currentToken!.isExpired}');
      }
      
      // Storage state
      print('WebStorage:');
      final tokenData = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      print('  - Token data: ${tokenData != null ? "EXISTS" : "NULL"}');
      
      if (tokenData != null) {
        print('  - Access token: ${tokenData['access_token']?.toString().substring(0, 10)}...');
        print('  - Expires at: ${tokenData['expires_at']}');
        print('  - Token type: ${tokenData['token_type']}');
      }
      
      // Comparison
      final jwtHasToken = jwtService.currentToken != null;
      final storageHasToken = tokenData != null;
      
      if (jwtHasToken == storageHasToken) {
        print('✅ JWT Service and Storage are in sync');
      } else {
        print('❌ JWT Service and Storage are out of sync!');
        print('   JWT has token: $jwtHasToken, Storage has token: $storageHasToken');
      }
      
    } catch (e) {
      print('❌ Error comparing JWT service with storage: $e');
    }
    
    print('=== END COMPARISON ===');
  }
  
  /// Print browser storage information (web-specific)
  static void printBrowserStorageInfo() {
    print('=== BROWSER STORAGE INFO ===');
    
    try {
      // Check if we're on web and can access browser storage
      print('Platform: Web');
      print('Storage implementation: Hive (IndexedDB)');
      
      // Print all auth box contents
      final authBox = StorageService.auth;
      print('Auth box keys: ${authBox.keys.toList()}');
      print('Auth box length: ${authBox.length}');
      
      for (final key in authBox.keys) {
        final value = authBox.get(key);
        print('  $key: ${value.runtimeType} - ${value.toString().substring(0, 50)}...');
      }
      
    } catch (e) {
      print('❌ Error accessing browser storage: $e');
    }
    
    print('=== END BROWSER STORAGE INFO ===');
  }
}
