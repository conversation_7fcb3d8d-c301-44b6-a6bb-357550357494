import 'package:flutter/material.dart';

/// Dalti Provider App Colors
/// Centralized color definitions for consistent theming
class AppColors {
  // Dalti Brand Colors
  static const Color primary = Color(0xFF19727F); // #19727F
  static const Color primaryVariant = Color(0xFF0F5A65);
  static const Color secondary = Color(0xFF4ECDC4);
  static const Color accent = Color(0xFFFFE66D);
  
  // Neutral Colors
  static const Color surface = Color(0xFFFAFAFA);
  static const Color background = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFE74C3C);
  static const Color success = Color(0xFF27AE60);
  static const Color warning = Color(0xFFF39C12);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF0F1419);
  static const Color darkSurface = Color(0xFF1C2127);
  static const Color darkSurfaceVariant = Color(0xFF242A30);
  static const Color darkSurfaceContainer = Color(0xFF2A3038);
  static const Color darkSurfaceContainerHigh = Color(0xFF323A42);
  
  // Dark theme primary colors
  static const Color darkPrimary = Color(0xFF19727F);
  static const Color darkPrimaryContainer = Color(0xFF1A4A52);
  static const Color darkSecondary = Color(0xFF3ABAB3);
  static const Color darkTertiary = Color(0xFF19727F);
  
  // Dark theme text colors
  static const Color darkOnSurface = Color(0xFFF8F9FA);
  static const Color darkOnSurfaceVariant = Color(0xFFB8BCC8);
  static const Color darkOnSurfaceSecondary = Color(0xFF6C7278);
  static const Color darkOnPrimary = Color(0xFFFFFFFF);
  static const Color darkOnPrimaryContainer = Color(0xFFB8F5FF);
  
  // Additional colors
  static const Color darkInactiveElement = Color(0xFF3A4048);
  static const Color darkProgressBackground = Color(0xFF2A3038);

  // Status Colors
  static const Color statusActive = Color(0xFF27AE60);
  static const Color statusInactive = Color(0xFFF39C12);
  static const Color statusBlocked = Color(0xFFE74C3C);
  static const Color statusPending = Color(0xFF3498DB);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryVariant],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, Color(0xFF3ABAB3)],
  );

  // Utility methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }
}
