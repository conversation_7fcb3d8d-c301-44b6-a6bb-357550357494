// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'realtime_queue_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$allQueuesStatusHash() => r'2898672969f7bf643204310d5c86d021db01a2af';

/// Real-time queue status provider for all queues
///
/// Copied from [allQueuesStatus].
@ProviderFor(allQueuesStatus)
final allQueuesStatusProvider =
    AutoDisposeStreamProvider<Map<String, QueueStatus>>.internal(
  allQueuesStatus,
  name: r'allQueuesStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$allQueuesStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AllQueuesStatusRef
    = AutoDisposeStreamProviderRef<Map<String, QueueStatus>>;
String _$queueStatusHash() => r'1a136daa2c6a5314358d4bb044758355d08f6845';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Real-time queue status provider for a specific queue
///
/// Copied from [queueStatus].
@ProviderFor(queueStatus)
const queueStatusProvider = QueueStatusFamily();

/// Real-time queue status provider for a specific queue
///
/// Copied from [queueStatus].
class QueueStatusFamily extends Family<AsyncValue<QueueStatus>> {
  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [queueStatus].
  const QueueStatusFamily();

  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [queueStatus].
  QueueStatusProvider call(
    String queueId,
  ) {
    return QueueStatusProvider(
      queueId,
    );
  }

  @override
  QueueStatusProvider getProviderOverride(
    covariant QueueStatusProvider provider,
  ) {
    return call(
      provider.queueId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'queueStatusProvider';
}

/// Real-time queue status provider for a specific queue
///
/// Copied from [queueStatus].
class QueueStatusProvider extends AutoDisposeStreamProvider<QueueStatus> {
  /// Real-time queue status provider for a specific queue
  ///
  /// Copied from [queueStatus].
  QueueStatusProvider(
    String queueId,
  ) : this._internal(
          (ref) => queueStatus(
            ref as QueueStatusRef,
            queueId,
          ),
          from: queueStatusProvider,
          name: r'queueStatusProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$queueStatusHash,
          dependencies: QueueStatusFamily._dependencies,
          allTransitiveDependencies:
              QueueStatusFamily._allTransitiveDependencies,
          queueId: queueId,
        );

  QueueStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.queueId,
  }) : super.internal();

  final String queueId;

  @override
  Override overrideWith(
    Stream<QueueStatus> Function(QueueStatusRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QueueStatusProvider._internal(
        (ref) => create(ref as QueueStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        queueId: queueId,
      ),
    );
  }

  @override
  AutoDisposeStreamProviderElement<QueueStatus> createElement() {
    return _QueueStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QueueStatusProvider && other.queueId == queueId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, queueId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin QueueStatusRef on AutoDisposeStreamProviderRef<QueueStatus> {
  /// The parameter `queueId` of this provider.
  String get queueId;
}

class _QueueStatusProviderElement
    extends AutoDisposeStreamProviderElement<QueueStatus> with QueueStatusRef {
  _QueueStatusProviderElement(super.provider);

  @override
  String get queueId => (origin as QueueStatusProvider).queueId;
}

String _$queueStatisticsHash() => r'e2f6d8d8199e4a28b11fa9fdcfa1db5025290029';

/// Provider for queue statistics
///
/// Copied from [queueStatistics].
@ProviderFor(queueStatistics)
const queueStatisticsProvider = QueueStatisticsFamily();

/// Provider for queue statistics
///
/// Copied from [queueStatistics].
class QueueStatisticsFamily extends Family<QueueStatistics> {
  /// Provider for queue statistics
  ///
  /// Copied from [queueStatistics].
  const QueueStatisticsFamily();

  /// Provider for queue statistics
  ///
  /// Copied from [queueStatistics].
  QueueStatisticsProvider call(
    String queueId,
  ) {
    return QueueStatisticsProvider(
      queueId,
    );
  }

  @override
  QueueStatisticsProvider getProviderOverride(
    covariant QueueStatisticsProvider provider,
  ) {
    return call(
      provider.queueId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'queueStatisticsProvider';
}

/// Provider for queue statistics
///
/// Copied from [queueStatistics].
class QueueStatisticsProvider extends AutoDisposeProvider<QueueStatistics> {
  /// Provider for queue statistics
  ///
  /// Copied from [queueStatistics].
  QueueStatisticsProvider(
    String queueId,
  ) : this._internal(
          (ref) => queueStatistics(
            ref as QueueStatisticsRef,
            queueId,
          ),
          from: queueStatisticsProvider,
          name: r'queueStatisticsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$queueStatisticsHash,
          dependencies: QueueStatisticsFamily._dependencies,
          allTransitiveDependencies:
              QueueStatisticsFamily._allTransitiveDependencies,
          queueId: queueId,
        );

  QueueStatisticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.queueId,
  }) : super.internal();

  final String queueId;

  @override
  Override overrideWith(
    QueueStatistics Function(QueueStatisticsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: QueueStatisticsProvider._internal(
        (ref) => create(ref as QueueStatisticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        queueId: queueId,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<QueueStatistics> createElement() {
    return _QueueStatisticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is QueueStatisticsProvider && other.queueId == queueId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, queueId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin QueueStatisticsRef on AutoDisposeProviderRef<QueueStatistics> {
  /// The parameter `queueId` of this provider.
  String get queueId;
}

class _QueueStatisticsProviderElement
    extends AutoDisposeProviderElement<QueueStatistics>
    with QueueStatisticsRef {
  _QueueStatisticsProviderElement(super.provider);

  @override
  String get queueId => (origin as QueueStatisticsProvider).queueId;
}

String _$queueOperationsHash() => r'0bc1fe2393792158846779ae840545571a005c09';

/// Provider for managing queue operations
///
/// Copied from [QueueOperations].
@ProviderFor(QueueOperations)
final queueOperationsProvider =
    AutoDisposeNotifierProvider<QueueOperations, Map<String, bool>>.internal(
  QueueOperations.new,
  name: r'queueOperationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueOperationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QueueOperations = AutoDisposeNotifier<Map<String, bool>>;
String _$queueMemberFilterHash() => r'3d3e99a3ae24be1d91796f8ff7e71d996ee77455';

/// Provider for queue member filtering and search
///
/// Copied from [QueueMemberFilter].
@ProviderFor(QueueMemberFilter)
final queueMemberFilterProvider = AutoDisposeNotifierProvider<QueueMemberFilter,
    Map<String, QueueMemberFilterState>>.internal(
  QueueMemberFilter.new,
  name: r'queueMemberFilterProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueMemberFilterHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QueueMemberFilter
    = AutoDisposeNotifier<Map<String, QueueMemberFilterState>>;
String _$queueAlertsHash() => r'1fef949f03bdc6655da80ec42d11e73d30d4705a';

/// Provider for queue alerts and notifications
///
/// Copied from [QueueAlerts].
@ProviderFor(QueueAlerts)
final queueAlertsProvider = AutoDisposeNotifierProvider<QueueAlerts,
    Map<String, List<QueueAlert>>>.internal(
  QueueAlerts.new,
  name: r'queueAlertsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$queueAlertsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QueueAlerts = AutoDisposeNotifier<Map<String, List<QueueAlert>>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
