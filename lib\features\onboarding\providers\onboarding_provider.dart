import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/onboarding_models.dart';
import '../repository/onboarding_repository.dart';
import '../repository/onboarding_repository_impl.dart';
import '../services/onboarding_api_service.dart';
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/schedule_models.dart';
import '../../queues/models/queue_models.dart';

part 'onboarding_provider.g.dart';

/// Provider for OnboardingApiService
@riverpod
OnboardingApiService onboardingApiService(OnboardingApiServiceRef ref) {
  final apiService = ref.watch(apiServiceProvider);
  return OnboardingApiService(apiService: apiService);
}

/// Provider for OnboardingRepository
@riverpod
OnboardingRepository onboardingRepository(OnboardingRepositoryRef ref) {
  final apiService = ref.watch(onboardingApiServiceProvider);
  return OnboardingRepositoryImpl(apiService: apiService);
}

/// Provider for onboarding state management
@riverpod
class OnboardingNotifier extends _$OnboardingNotifier {
  @override
  OnboardingState build() {
    // Initialize and load existing progress
    _loadProgress();
    return const OnboardingState();
  }

  /// Load existing onboarding progress
  Future<void> _loadProgress() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final repository = ref.read(onboardingRepositoryProvider);
      final data = await repository.loadProgress();
      
      if (data != null) {
        state = state.copyWith(
          isLoading: false,
          data: data,
          canProceed: _canProceedFromStep(data.currentStep, data),
          canGoBack: data.currentStep != OnboardingStep.businessProfile,
        );
      } else {
        // Initialize new onboarding
        final newData = OnboardingData(
          startedAt: DateTime.now(),
        );
        state = state.copyWith(
          isLoading: false,
          data: newData,
          canProceed: false,
          canGoBack: false,
        );
      }
    } catch (e) {
      print('[OnboardingProvider] Error loading progress: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load onboarding progress: $e',
      );
    }
  }

  /// Start or restart onboarding
  Future<void> startOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final repository = ref.read(onboardingRepositoryProvider);
      
      // Clear any existing progress
      await repository.clearProgress();
      
      // Create new onboarding data
      final newData = OnboardingData(
        startedAt: DateTime.now(),
      );
      
      // Save initial progress
      await repository.saveProgress(newData);
      
      state = state.copyWith(
        isLoading: false,
        data: newData,
        canProceed: false,
        canGoBack: false,
      );
      
      print('[OnboardingProvider] Onboarding started');
    } catch (e) {
      print('[OnboardingProvider] Error starting onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start onboarding: $e',
      );
    }
  }

  /// Move to next step
  Future<void> nextStep() async {
    final currentData = state.data;
    if (currentData == null) return;
    
    final nextStep = currentData.currentStep.next;
    if (nextStep == null) return;
    
    await goToStep(nextStep);
  }

  /// Move to previous step
  Future<void> previousStep() async {
    final currentData = state.data;
    if (currentData == null) return;
    
    final previousStep = currentData.currentStep.previous;
    if (previousStep == null) return;
    
    await goToStep(previousStep);
  }

  /// Go to specific step
  Future<void> goToStep(OnboardingStep step) async {
    try {
      final currentData = state.data;
      if (currentData == null) return;
      
      state = state.copyWith(isLoading: true, error: null);
      
      final updatedData = currentData.copyWith(
        currentStep: step,
        stepIndex: step.index,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      
      state = state.copyWith(
        isLoading: false,
        data: updatedData,
        canProceed: _canProceedFromStep(step, updatedData),
        canGoBack: step != OnboardingStep.businessProfile,
      );
      
      print('[OnboardingProvider] Moved to step: $step');
    } catch (e) {
      print('[OnboardingProvider] Error going to step: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to navigate to step: $e',
      );
    }
  }

  /// Save business profile
  Future<void> saveBusinessProfile(BusinessProfile profile) async {
    try {
      state = state.copyWith(isSaving: true, error: null);
      
      final currentData = state.data;
      if (currentData == null) return;
      
      final updatedData = currentData.copyWith(
        businessProfile: profile,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      await repository.saveBusinessProfile(profile);
      
      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: true,
      );
      
      print('[OnboardingProvider] Business profile saved');
    } catch (e) {
      print('[OnboardingProvider] Error saving business profile: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save business profile: $e',
      );
    }
  }

  /// Save primary location
  Future<void> savePrimaryLocation(Location location) async {
    try {
      state = state.copyWith(isSaving: true, error: null);
      
      final currentData = state.data;
      if (currentData == null) return;
      
      final updatedData = currentData.copyWith(
        primaryLocation: location,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      
      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: true,
      );
      
      print('[OnboardingProvider] Primary location saved');
    } catch (e) {
      print('[OnboardingProvider] Error saving primary location: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save primary location: $e',
      );
    }
  }

  /// Save services
  Future<void> saveServices(List<Service> services) async {
    try {
      state = state.copyWith(isSaving: true, error: null);
      
      final currentData = state.data;
      if (currentData == null) return;
      
      final updatedData = currentData.copyWith(
        services: services,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      
      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: services.isNotEmpty,
      );
      
      print('[OnboardingProvider] Services saved: ${services.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving services: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save services: $e',
      );
    }
  }

  /// Save schedules
  Future<void> saveSchedules(List<Schedule> schedules) async {
    try {
      state = state.copyWith(isSaving: true, error: null);
      
      final currentData = state.data;
      if (currentData == null) return;
      
      final updatedData = currentData.copyWith(
        schedules: schedules,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      
      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: schedules.isNotEmpty,
      );
      
      print('[OnboardingProvider] Schedules saved: ${schedules.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving schedules: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save schedules: $e',
      );
    }
  }

  /// Save queues with opening hours
  Future<void> saveQueuesWithHours(List<QueueWithOpeningHours> queuesWithHours) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(
        queuesWithHours: queuesWithHours,
      );

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: queuesWithHours.isNotEmpty,
      );

      print('[OnboardingProvider] Queues with hours saved: ${queuesWithHours.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving queues with hours: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save queues with hours: $e',
      );
    }
  }

  /// Save queues (legacy method for backward compatibility)
  Future<void> saveQueues(List<Queue> queues) async {
    try {
      state = state.copyWith(isSaving: true, error: null);
      
      final currentData = state.data;
      if (currentData == null) return;
      
      final updatedData = currentData.copyWith(
        queues: queues,
      );
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      
      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: queues.isNotEmpty,
      );
      
      print('[OnboardingProvider] Queues saved: ${queues.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving queues: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save queues: $e',
      );
    }
  }

  /// Complete onboarding
  Future<OnboardingCompletion?> completeOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final currentData = state.data;
      if (currentData == null || !currentData.canComplete) {
        throw Exception('Cannot complete onboarding - missing required data');
      }
      
      final repository = ref.read(onboardingRepositoryProvider);
      final completion = await repository.submitOnboarding(currentData);
      
      final completedData = currentData.copyWith(
        isCompleted: true,
        currentStep: OnboardingStep.completed,
        completedAt: DateTime.now(),
      );
      
      await repository.saveProgress(completedData);
      
      state = state.copyWith(
        isLoading: false,
        data: completedData,
        canProceed: false,
        canGoBack: false,
      );
      
      print('[OnboardingProvider] Onboarding completed successfully');
      return completion;
    } catch (e) {
      print('[OnboardingProvider] Error completing onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to complete onboarding: $e',
      );
      return null;
    }
  }

  /// Validate current step
  Future<StepValidationResult> validateCurrentStep(Map<String, dynamic> data) async {
    try {
      final currentData = state.data;
      if (currentData == null) {
        return const StepValidationResult(
          isValid: false,
          errors: ['No onboarding data available'],
        );
      }
      
      final repository = ref.read(onboardingRepositoryProvider);
      return await repository.validateStep(currentData.currentStep, data);
    } catch (e) {
      print('[OnboardingProvider] Error validating step: $e');
      return StepValidationResult(
        isValid: false,
        errors: ['Validation failed: $e'],
      );
    }
  }

  /// Check if can proceed from current step
  bool _canProceedFromStep(OnboardingStep step, OnboardingData data) {
    switch (step) {
      case OnboardingStep.businessProfile:
        return data.businessProfile != null;
      case OnboardingStep.locationSetup:
        return data.primaryLocation != null;
      case OnboardingStep.serviceCreation:
        return data.services.isNotEmpty;
      case OnboardingStep.scheduleSetup:
        return data.schedules.isNotEmpty;
      case OnboardingStep.queueManagement:
        return data.queues.isNotEmpty;
      case OnboardingStep.summary:
        return data.canComplete;
      case OnboardingStep.completed:
        return false;
    }
  }

  /// Reset onboarding
  Future<void> resetOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final repository = ref.read(onboardingRepositoryProvider);
      await repository.resetOnboarding();
      
      await startOnboarding();
      
      print('[OnboardingProvider] Onboarding reset successfully');
    } catch (e) {
      print('[OnboardingProvider] Error resetting onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to reset onboarding: $e',
      );
    }
  }
}

/// Provider to check if user needs onboarding
@riverpod
Future<bool> needsOnboarding(NeedsOnboardingRef ref) async {
  try {
    final repository = ref.watch(onboardingRepositoryProvider);
    return await repository.needsOnboarding();
  } catch (e) {
    print('[OnboardingProvider] Error checking onboarding need: $e');
    return true; // Assume needs onboarding on error
  }
}

/// Provider for business categories
@riverpod
Future<List<Map<String, dynamic>>> businessCategories(BusinessCategoriesRef ref) async {
  try {
    final apiService = ref.watch(onboardingApiServiceProvider);
    return await apiService.getBusinessCategories();
  } catch (e) {
    print('[OnboardingProvider] Error getting business categories: $e');
    return [];
  }
}

/// Provider for Algerian cities
@riverpod
Future<List<String>> algerianCities(AlgerianCitiesRef ref) async {
  try {
    final apiService = ref.watch(onboardingApiServiceProvider);
    return await apiService.getAlgerianCities();
  } catch (e) {
    print('[OnboardingProvider] Error getting Algerian cities: $e');
    return [];
  }
}
