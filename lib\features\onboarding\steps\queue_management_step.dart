import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../providers/onboarding_provider.dart';
import '../../queues/models/queue_models.dart';
import '../../services/models/service_models.dart';

/// Queue management step for onboarding
class QueueManagementStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const QueueManagementStep({
    super.key,
    required this.controller,
  });

  @override
  ConsumerState<QueueManagementStep> createState() => _QueueManagementStepState();
}

class _QueueManagementStepState extends ConsumerState<QueueManagementStep> {
  final List<QueueWithOpeningHours> _queues = [];
  final _formKey = GlobalKey<FormState>();

  // Form controllers for adding new queue
  final _titleController = TextEditingController();

  List<int> _selectedServiceIds = [];
  Map<String, TimeOfDay> _openingHours = {};
  bool _showAddForm = false;

  // Days of the week
  static const List<String> _daysOfWeek = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  @override
  void initState() {
    super.initState();
    _initializeOpeningHours();
    _loadExistingData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  /// Initialize default opening hours
  void _initializeOpeningHours() {
    for (final day in _daysOfWeek) {
      _openingHours['${day}_open'] = const TimeOfDay(hour: 9, minute: 0);
      _openingHours['${day}_close'] = const TimeOfDay(hour: 17, minute: 0);
    }
  }

  /// Load existing queue data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final queues = onboardingState.data?.queues ?? [];

    if (queues.isNotEmpty) {
      // Convert existing queues to QueueWithOpeningHours format
      _queues.clear();
      for (final queue in queues) {
        _queues.add(QueueWithOpeningHours(
          title: queue.title,
          serviceIds: queue.serviceIds,
          openingHours: _getDefaultOpeningHours(),
        ));
      }

      // Mark step as completed if queues exist
      widget.controller.markStepCompleted(OnboardingStep.queueManagement, true);
    }
  }

  /// Get default opening hours
  Map<String, String> _getDefaultOpeningHours() {
    final hours = <String, String>{};
    for (final day in _daysOfWeek) {
      hours['${day}_open'] = '09:00';
      hours['${day}_close'] = '17:00';
    }
    return hours;
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final availableServices = onboardingState.data?.services ?? [];

    if (availableServices.isEmpty) {
      return WizardCenteredPage(
        step: OnboardingStep.queueManagement,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_outlined,
              size: 64,
              color: context.colors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'No Services Available',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'You need to create at least one service before setting up queues. Please go back to the previous step.',
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return WizardPage(
      step: OnboardingStep.queueManagement,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Queues list
          if (_queues.isNotEmpty) ...[
            Text(
              'Your Queues (${_queues.length})',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            // Queues list
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _queues.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) => _buildQueueCard(_queues[index], index, availableServices),
            ),

            const SizedBox(height: 24),
          ],

          // Add queue button or form
          if (!_showAddForm) ...[
            // Add queue button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    _showAddForm = true;
                    _selectedServiceIds.clear();
                  });
                },
                icon: const Icon(Icons.add),
                label: Text(_queues.isEmpty ? 'Create Your First Queue' : 'Add Another Queue'),
              ),
            ),

            if (_queues.isEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.colors.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: context.colors.outline.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: context.colors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Create at least one queue to organize your services. Each queue can have its own opening hours and assigned services.',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ] else ...[
            // Add queue form
            _buildAddQueueForm(availableServices),
          ],
        ],
      ),
    );
  }

  /// Build queue card widget
  Widget _buildQueueCard(QueueWithOpeningHours queue, int index, List<Service> availableServices) {
    final assignedServices = availableServices.where((service) => queue.serviceIds.contains(service.id)).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.queue,
                  color: context.colors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    queue.title,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _removeQueue(index),
                  icon: const Icon(Icons.delete_outline),
                  iconSize: 20,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Assigned services
            if (assignedServices.isNotEmpty) ...[
              Text(
                'Assigned Services:',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: assignedServices.map((service) => Chip(
                  label: Text(
                    service.title,
                    style: context.textTheme.bodySmall,
                  ),
                  backgroundColor: context.colors.primaryContainer.withOpacity(0.3),
                )).toList(),
              ),
              const SizedBox(height: 12),
            ],

            // Opening hours summary
            Text(
              'Opening Hours:',
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            _buildOpeningHoursSummary(queue.openingHours),
          ],
        ),
      ),
    );
  }

  /// Build opening hours summary
  Widget _buildOpeningHoursSummary(Map<String, String> openingHours) {
    return Column(
      children: _daysOfWeek.map((day) {
        final openTime = openingHours['${day}_open'] ?? '09:00';
        final closeTime = openingHours['${day}_close'] ?? '17:00';

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              SizedBox(
                width: 80,
                child: Text(
                  day.substring(0, 3),
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ),
              Text(
                '$openTime - $closeTime',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// Build add queue form
  Widget _buildAddQueueForm(List<Service> availableServices) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form header
              Row(
                children: [
                  Icon(
                    Icons.add_circle_outline,
                    color: context.colors.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Create New Queue',
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _cancelAddQueue,
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Queue title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Queue Name *',
                  hintText: 'e.g., General Queue, VIP Queue, Walk-ins',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.queue),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Queue name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Service selection
              Text(
                'Assign Services *',
                style: context.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select which services will be available in this queue:',
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 12),

              // Services checkboxes
              ...availableServices.map((service) => CheckboxListTile(
                title: Text(service.title),
                subtitle: Text('${service.duration} min • ${service.price?.toStringAsFixed(0) ?? '0'} DA'),
                value: _selectedServiceIds.contains(service.id),
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedServiceIds.add(service.id);
                    } else {
                      _selectedServiceIds.remove(service.id);
                    }
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              )),

              if (_selectedServiceIds.isEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'Please select at least one service',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.error,
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _cancelAddQueue,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedServiceIds.isNotEmpty ? _addQueue : null,
                      child: const Text('Create Queue'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Add new queue
  void _addQueue() {
    if (!_formKey.currentState!.validate() || _selectedServiceIds.isEmpty) {
      return;
    }

    final queue = QueueWithOpeningHours(
      title: _titleController.text.trim(),
      serviceIds: List.from(_selectedServiceIds),
      openingHours: _getDefaultOpeningHours(),
    );

    setState(() {
      _queues.add(queue);
      _showAddForm = false;
    });

    _clearForm();
    _updateOnboardingData();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('Queue "${queue.title}" created successfully!'),
          ],
        ),
        backgroundColor: context.colors.primary,
      ),
    );
  }

  /// Remove queue
  void _removeQueue(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Queue'),
        content: Text('Are you sure you want to remove "${_queues[index].title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _queues.removeAt(index);
              });
              _updateOnboardingData();
            },
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  /// Cancel add queue
  void _cancelAddQueue() {
    setState(() {
      _showAddForm = false;
    });
    _clearForm();
  }

  /// Clear form fields
  void _clearForm() {
    _titleController.clear();
    setState(() {
      _selectedServiceIds.clear();
    });
  }

  /// Update onboarding data with current queues
  void _updateOnboardingData() {
    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveQueuesWithHours(_queues);

    // Update step completion
    widget.controller.markStepCompleted(OnboardingStep.queueManagement, _queues.isNotEmpty);
  }
}
