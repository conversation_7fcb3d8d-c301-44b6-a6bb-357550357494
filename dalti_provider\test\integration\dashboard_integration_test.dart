import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';
import 'package:dalti_provider/features/dashboard/screens/dashboard_screen.dart';
import 'package:dalti_provider/features/dashboard/widgets/business_overview_card.dart';
import 'package:dalti_provider/features/dashboard/widgets/today_schedule_card.dart';
import 'package:dalti_provider/features/dashboard/widgets/notifications_panel.dart';
import 'package:dalti_provider/features/dashboard/widgets/emergency_queue_control.dart';
import 'package:dalti_provider/features/dashboard/widgets/quick_actions_panel.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Dashboard Integration Tests', () {
    testWidgets('should load and display complete dashboard', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Assert - Check that all main components are present
      expect(find.byType(BusinessOverviewCard), findsOneWidget);
      expect(find.byType(TodayScheduleCard), findsOneWidget);
      expect(find.byType(NotificationsPanel), findsOneWidget);
      expect(find.byType(EmergencyQueueControlWidget), findsOneWidget);
      expect(find.byType(QuickActionsPanel), findsOneWidget);
    });

    testWidgets('should handle pull-to-refresh functionality', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Perform pull-to-refresh
      await tester.fling(
        find.byType(CustomScrollView),
        Offset(0, 300),
        1000,
      );
      await tester.pump();
      await tester.pump(Duration(seconds: 1));

      // Assert - Should show refresh indicator
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });

    testWidgets('should expand and collapse notifications panel', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Tap notifications header to expand
      await tester.tap(find.text('Notifications'));
      await tester.pumpAndSettle();

      // Assert - Should show expanded content
      expect(find.byIcon(Icons.expand_less), findsOneWidget);

      // Act - Tap again to collapse
      await tester.tap(find.text('Notifications'));
      await tester.pumpAndSettle();

      // Assert - Should show collapsed state
      expect(find.byIcon(Icons.expand_more), findsOneWidget);
    });

    testWidgets('should display emergency queue control dialog', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Tap pause all button
      await tester.tap(find.text('Pause All'));
      await tester.pumpAndSettle();

      // Assert - Should show confirmation dialog
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Pause All Queues'), findsOneWidget);

      // Act - Cancel the dialog
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Dialog should be dismissed
      expect(find.byType(AlertDialog), findsNothing);
    });

    testWidgets('should navigate through quick actions', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Tap on New Appointment action
      await tester.tap(find.text('New Appointment'));
      await tester.pumpAndSettle();

      // Assert - Should show coming soon dialog
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('New Appointment'), findsWidgets);

      // Act - Close dialog
      await tester.tap(find.text('Got it'));
      await tester.pumpAndSettle();

      // Assert - Dialog should be dismissed
      expect(find.byType(AlertDialog), findsNothing);
    });

    testWidgets('should handle scroll behavior correctly', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Scroll down to see all content
      await tester.drag(
        find.byType(CustomScrollView),
        Offset(0, -500),
      );
      await tester.pumpAndSettle();

      // Assert - Should still find all components
      expect(find.byType(QuickActionsPanel), findsOneWidget);
      expect(find.byType(EmergencyQueueControlWidget), findsOneWidget);

      // Act - Scroll back up
      await tester.drag(
        find.byType(CustomScrollView),
        Offset(0, 500),
      );
      await tester.pumpAndSettle();

      // Assert - Should see header components
      expect(find.byType(BusinessOverviewCard), findsOneWidget);
    });

    testWidgets('should handle error states gracefully', (tester) async {
      // This test would require mocking network failures
      // For now, we'll test the basic error handling structure

      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      // Act - Wait for potential error states
      await tester.pumpAndSettle(Duration(seconds: 5));

      // Assert - Should not crash and should show some content
      expect(find.byType(DashboardScreen), findsOneWidget);
      expect(tester.takeException(), isNull);
    });

    testWidgets('should maintain state during widget rebuilds', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Expand notifications panel
      await tester.tap(find.text('Notifications'));
      await tester.pumpAndSettle();

      // Trigger a rebuild by hot reload simulation
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Assert - State should be maintained (this is a basic check)
      expect(find.byType(DashboardScreen), findsOneWidget);
    });

    testWidgets('should handle multiple rapid taps without crashing', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Act - Rapidly tap notifications panel
      for (int i = 0; i < 5; i++) {
        await tester.tap(find.text('Notifications'));
        await tester.pump(Duration(milliseconds: 100));
      }

      await tester.pumpAndSettle();

      // Assert - Should not crash
      expect(tester.takeException(), isNull);
      expect(find.byType(DashboardScreen), findsOneWidget);
    });

    testWidgets('should display loading states appropriately', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      // Act - Check initial loading state
      await tester.pump();

      // Assert - Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsWidgets);

      // Wait for data to load
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Assert - Loading should be replaced with content
      expect(find.byType(BusinessOverviewCard), findsOneWidget);
    });

    testWidgets('should handle accessibility features', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: DashboardScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Check for semantic labels
      expect(find.bySemanticsLabel('Business Overview Card'), findsOneWidget);
      
      // Check that interactive elements are accessible
      final semantics = tester.getSemantics(find.text('Notifications'));
      expect(semantics.hasAction(SemanticsAction.tap), true);
    });
  });
}
