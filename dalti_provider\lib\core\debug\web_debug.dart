import 'package:flutter/foundation.dart';
import '../config/app_config.dart';

/// Debug utility for web-specific issues
class WebDebug {
  /// Print web-specific debug information
  static void printWebInfo() {
    print('[] ');
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
    print('[] ');
    print('[] ');
    
    if (kIsWeb) {
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
      
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Check for common web issues
  static void checkWebIssues() {
    print('[] ');
    
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    // Check API URL
    final apiUrl = AppConfig.apiBaseUrl;
    print('[] ');
    print('[] ');
    
    if (apiUrl.startsWith('https://')) {
      print('[] ');
    } else if (apiUrl.startsWith('http://')) {
      print('[] ');
    }
    
    if (apiUrl.contains('localhost') || apiUrl.contains('127.0.0.1')) {
      print('[] ');
    } else {
      print('[] ');
    }
    
    // Check common issues
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
  }
  
  /// Print instructions for debugging web issues
  static void printWebDebuggingInstructions() {
    print('[] ');
    
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
  }
  
  /// Test API connectivity (basic check)
  static void testApiConnectivity() {
    print('[] ');
    
    final apiUrl = AppConfig.apiBaseUrl;
    print('[] ');
    
    if (kIsWeb) {
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Print web storage information
  static void printWebStorageInfo() {
    print('[] ');
    
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    
    print('[] ');
  }
}
