import 'package:freezed_annotation/freezed_annotation.dart';
import 'onboarding_models.dart';
import '../providers/wizard_navigation_provider.dart';

part 'wizard_persistence_models.freezed.dart';
part 'wizard_persistence_models.g.dart';

/// Model for persisted wizard state
@freezed
class PersistedWizardState with _$PersistedWizardState {
  const factory PersistedWizardState({
    /// Version of the persisted data structure for migration support
    @Default(1) int version,
    
    /// Timestamp when the state was saved
    required DateTime savedAt,
    
    /// Timestamp when the wizard was started
    required DateTime startedAt,
    
    /// Current wizard step
    required OnboardingStep currentStep,
    
    /// Current step index
    required int currentStepIndex,
    
    /// Step completion status
    @Default({}) Map<OnboardingStep, bool> stepCompletionStatus,
    
    /// Complete onboarding data
    OnboardingData? onboardingData,
    
    /// Navigation state
    WizardNavigationData? navigationData,
    
    /// Session information
    SessionInfo? sessionInfo,
    
    /// Auto-save settings
    @Default(AutoSaveSettings()) AutoSaveSettings autoSaveSettings,
  }) = _PersistedWizardState;

  factory PersistedWizardState.fromJson(Map<String, dynamic> json) =>
      _$PersistedWizardStateFromJson(json);
}

/// Navigation-specific data for persistence
@freezed
class WizardNavigationData with _$WizardNavigationData {
  const factory WizardNavigationData({
    required OnboardingStep currentStep,
    required int currentStepIndex,
    required bool canGoNext,
    required bool canGoPrevious,
    @Default(false) bool isLoading,
    String? error,
    @Default({}) Map<OnboardingStep, bool> stepCompletionStatus,
  }) = _WizardNavigationData;

  factory WizardNavigationData.fromJson(Map<String, dynamic> json) =>
      _$WizardNavigationDataFromJson(json);
}

/// Session information for persistence
@freezed
class SessionInfo with _$SessionInfo {
  const factory SessionInfo({
    /// Unique session ID
    required String sessionId,
    
    /// User ID if available
    String? userId,
    
    /// Device/browser information
    String? deviceInfo,
    
    /// Session expiration time
    DateTime? expiresAt,
    
    /// Whether this is a guest session
    @Default(false) bool isGuest,
    
    /// Last activity timestamp
    DateTime? lastActivity,
  }) = _SessionInfo;

  factory SessionInfo.fromJson(Map<String, dynamic> json) =>
      _$SessionInfoFromJson(json);
}

/// Auto-save configuration
@freezed
class AutoSaveSettings with _$AutoSaveSettings {
  const factory AutoSaveSettings({
    /// Whether auto-save is enabled
    @Default(true) bool enabled,
    
    /// Debounce delay in milliseconds
    @Default(2000) int debounceDelayMs,
    
    /// Save on step navigation
    @Default(true) bool saveOnStepChange,
    
    /// Save on form field changes
    @Default(true) bool saveOnFieldChange,
    
    /// Save on step completion
    @Default(true) bool saveOnStepComplete,
    
    /// Maximum number of auto-save attempts
    @Default(3) int maxRetries,
  }) = _AutoSaveSettings;

  factory AutoSaveSettings.fromJson(Map<String, dynamic> json) =>
      _$AutoSaveSettingsFromJson(json);
}

/// Result of persistence operations
@freezed
class PersistenceResult with _$PersistenceResult {
  const factory PersistenceResult({
    required bool success,
    String? error,
    DateTime? savedAt,
    int? dataSize,
  }) = _PersistenceResult;

  factory PersistenceResult.fromJson(Map<String, dynamic> json) =>
      _$PersistenceResultFromJson(json);
}

/// Recovery options for restored state
@freezed
class RecoveryOptions with _$RecoveryOptions {
  const factory RecoveryOptions({
    /// Whether to show recovery dialog to user
    @Default(true) bool showRecoveryDialog,
    
    /// Whether to auto-restore without asking
    @Default(false) bool autoRestore,
    
    /// Maximum age of data to consider for recovery (in hours)
    @Default(24) int maxRecoveryAgeHours,
    
    /// Whether to validate data integrity before recovery
    @Default(true) bool validateIntegrity,
    
    /// Whether to migrate old data versions
    @Default(true) bool enableMigration,
  }) = _RecoveryOptions;

  factory RecoveryOptions.fromJson(Map<String, dynamic> json) =>
      _$RecoveryOptionsFromJson(json);
}

/// Storage configuration
@freezed
class StorageConfig with _$StorageConfig {
  const factory StorageConfig({
    /// Storage key prefix
    @Default('dalti_wizard') String keyPrefix,
    
    /// Storage version for migration
    @Default(1) int version,
    
    /// Whether to compress data
    @Default(false) bool enableCompression,
    
    /// Whether to encrypt sensitive data
    @Default(false) bool enableEncryption,
    
    /// Maximum storage size in bytes
    @Default(5242880) int maxStorageSize, // 5MB default
    
    /// Cleanup old data after days
    @Default(30) int cleanupAfterDays,
  }) = _StorageConfig;

  factory StorageConfig.fromJson(Map<String, dynamic> json) =>
      _$StorageConfigFromJson(json);
}

/// Migration information
@freezed
class MigrationInfo with _$MigrationInfo {
  const factory MigrationInfo({
    required int fromVersion,
    required int toVersion,
    required DateTime migratedAt,
    @Default([]) List<String> changes,
    bool? dataLoss,
  }) = _MigrationInfo;

  factory MigrationInfo.fromJson(Map<String, dynamic> json) =>
      _$MigrationInfoFromJson(json);
}

/// Storage statistics
@freezed
class StorageStats with _$StorageStats {
  const factory StorageStats({
    required int totalSize,
    required int itemCount,
    DateTime? lastSaved,
    DateTime? lastCleanup,
    @Default([]) List<String> keys,
  }) = _StorageStats;

  factory StorageStats.fromJson(Map<String, dynamic> json) =>
      _$StorageStatsFromJson(json);
}

/// Extensions for easier conversion
extension PersistedWizardStateExtensions on PersistedWizardState {
  /// Check if the persisted state is expired
  bool get isExpired {
    final maxAge = Duration(hours: 24); // 24 hours default
    return DateTime.now().difference(savedAt) > maxAge;
  }

  /// Check if the persisted state is valid
  bool get isValid {
    return !isExpired && 
           currentStep != OnboardingStep.completed &&
           onboardingData != null;
  }

  /// Get age of the persisted state in hours
  double get ageInHours {
    return DateTime.now().difference(savedAt).inMilliseconds / (1000 * 60 * 60);
  }

  /// Check if migration is needed
  bool get needsMigration {
    return version < 1; // Current version is 1
  }
}

extension WizardNavigationDataExtensions on WizardNavigationData {
  /// Convert to WizardNavigationState
  WizardNavigationState toNavigationState() {
    return WizardNavigationState(
      currentStep: currentStep,
      currentStepIndex: currentStepIndex,
      canGoNext: canGoNext,
      canGoPrevious: canGoPrevious,
      isLoading: isLoading,
      error: error,
      stepCompletionStatus: stepCompletionStatus,
    );
  }
}

/// Helper class for creating persistence models
class PersistenceModelFactory {
  /// Create a new persisted wizard state
  static PersistedWizardState create({
    required OnboardingStep currentStep,
    required int currentStepIndex,
    required Map<OnboardingStep, bool> stepCompletionStatus,
    OnboardingData? onboardingData,
    WizardNavigationData? navigationData,
    SessionInfo? sessionInfo,
    AutoSaveSettings? autoSaveSettings,
  }) {
    return PersistedWizardState(
      savedAt: DateTime.now(),
      startedAt: onboardingData?.startedAt ?? DateTime.now(),
      currentStep: currentStep,
      currentStepIndex: currentStepIndex,
      stepCompletionStatus: stepCompletionStatus,
      onboardingData: onboardingData,
      navigationData: navigationData,
      sessionInfo: sessionInfo,
      autoSaveSettings: autoSaveSettings ?? const AutoSaveSettings(),
    );
  }

  /// Create navigation data from current state
  static WizardNavigationData createNavigationData(WizardNavigationState state) {
    return WizardNavigationData(
      currentStep: state.currentStep,
      currentStepIndex: state.currentStepIndex,
      canGoNext: state.canGoNext,
      canGoPrevious: state.canGoPrevious,
      isLoading: state.isLoading,
      error: state.error,
      stepCompletionStatus: state.stepCompletionStatus,
    );
  }

  /// Create session info
  static SessionInfo createSessionInfo({
    String? userId,
    String? deviceInfo,
    Duration? sessionDuration,
    bool isGuest = false,
  }) {
    final now = DateTime.now();
    return SessionInfo(
      sessionId: _generateSessionId(),
      userId: userId,
      deviceInfo: deviceInfo,
      expiresAt: sessionDuration != null ? now.add(sessionDuration) : null,
      isGuest: isGuest,
      lastActivity: now,
    );
  }

  /// Generate a unique session ID
  static String _generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp * 1000 + (timestamp % 1000)).toString();
    return 'session_$random';
  }
}
