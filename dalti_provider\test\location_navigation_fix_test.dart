import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:dalti_provider/features/locations/screens/locations_screen.dart';
import 'package:dalti_provider/features/locations/screens/location_details_screen.dart';
import 'package:dalti_provider/features/locations/screens/edit_location_screen.dart';
import 'package:dalti_provider/core/routing/app_routes.dart';

void main() {
  group('Location Navigation Fix Tests', () {
    late GoRouter router;
    String? lastNavigatedRoute;

    setUp(() {
      lastNavigatedRoute = null;
      
      // Create a router that captures navigation attempts
      router = GoRouter(
        initialLocation: AppRoutes.locations,
        routes: [
          GoRoute(
            path: AppRoutes.dashboard,
            builder: (context, state) => const Scaffold(
              body: Text('Dashboard'),
            ),
          ),
          GoRoute(
            path: AppRoutes.locations,
            builder: (context, state) => const LocationsScreen(),
          ),
          GoRoute(
            path: AppRoutes.addLocation,
            builder: (context, state) => const Scaffold(
              body: Text('Add Location'),
            ),
          ),
          GoRoute(
            path: '/locations/:id',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return LocationDetailsScreen(locationId: locationId);
            },
          ),
          GoRoute(
            path: '/locations/:id/edit',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return EditLocationScreen(locationId: locationId);
            },
          ),
        ],
        redirect: (context, state) {
          lastNavigatedRoute = state.uri.path;
          return null;
        },
      );
    });

    test('AppRoutes constants are correctly defined', () {
      // Verify that the route constants are defined correctly
      expect(AppRoutes.locations, equals('/locations'));
      expect(AppRoutes.locationDetails, equals('/locations/:id'));
      expect(AppRoutes.editLocation, equals('/locations/:id/edit'));
      expect(AppRoutes.addLocation, equals('/locations/add'));
    });

    test('Route patterns should not be concatenated with IDs', () {
      // This test documents the correct vs incorrect navigation patterns
      
      // ❌ INCORRECT (what was causing the bug):
      // context.push('${AppRoutes.editLocation}/${locationId}');
      // Results in: '/locations/:id/edit/2' (malformed)
      
      // ✅ CORRECT (the fix):
      // context.push('/locations/${locationId}/edit');
      // Results in: '/locations/2/edit' (correct)
      
      const locationId = '2';
      
      // Simulate the old incorrect approach
      final incorrectRoute = '${AppRoutes.editLocation}/$locationId';
      expect(incorrectRoute, equals('/locations/:id/edit/2')); // This is malformed
      
      // Simulate the new correct approach
      final correctRoute = '/locations/$locationId/edit';
      expect(correctRoute, equals('/locations/2/edit')); // This is correct
      
      // Verify the correct route matches the expected pattern
      expect(correctRoute, matches(r'^/locations/\d+/edit$'));
    });

    test('Location details route should be correctly formatted', () {
      const locationId = '5';
      
      // ❌ INCORRECT:
      final incorrectDetailsRoute = '${AppRoutes.locationDetails}/$locationId';
      expect(incorrectDetailsRoute, equals('/locations/:id/5')); // Malformed
      
      // ✅ CORRECT:
      final correctDetailsRoute = '/locations/$locationId';
      expect(correctDetailsRoute, equals('/locations/5')); // Correct
      
      // Verify the correct route matches the expected pattern
      expect(correctDetailsRoute, matches(r'^/locations/\d+$'));
    });

    testWidgets('LocationsScreen navigation methods use correct routes', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify we're on the locations screen
      expect(find.byType(LocationsScreen), findsOneWidget);
      expect(lastNavigatedRoute, equals(AppRoutes.locations));
    });

    testWidgets('Router can handle correctly formatted location routes', (WidgetTester tester) async {
      // Test that the router can handle the correctly formatted routes
      
      // Create a router with a specific location details route
      final detailsRouter = GoRouter(
        initialLocation: '/locations/1',
        routes: [
          GoRoute(
            path: '/locations/:id',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return LocationDetailsScreen(locationId: locationId);
            },
          ),
          GoRoute(
            path: '/locations/:id/edit',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return EditLocationScreen(locationId: locationId);
            },
          ),
        ],
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: detailsRouter,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the LocationDetailsScreen is displayed
      expect(find.byType(LocationDetailsScreen), findsOneWidget);
    });

    testWidgets('Router can handle correctly formatted edit routes', (WidgetTester tester) async {
      // Test that the router can handle the correctly formatted edit routes
      
      // Create a router with a specific edit location route
      final editRouter = GoRouter(
        initialLocation: '/locations/1/edit',
        routes: [
          GoRoute(
            path: '/locations/:id',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return LocationDetailsScreen(locationId: locationId);
            },
          ),
          GoRoute(
            path: '/locations/:id/edit',
            builder: (context, state) {
              final locationId = state.pathParameters['id'] ?? '';
              return EditLocationScreen(locationId: locationId);
            },
          ),
        ],
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: editRouter,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the EditLocationScreen is displayed
      expect(find.byType(EditLocationScreen), findsOneWidget);
    });

    test('Route parameter extraction works correctly', () {
      // Test that route parameters can be extracted correctly from the fixed routes
      
      const testRoutes = [
        '/locations/1',
        '/locations/123',
        '/locations/1/edit',
        '/locations/456/edit',
      ];
      
      for (final route in testRoutes) {
        // Extract ID from details route
        if (route.endsWith('/edit')) {
          final match = RegExp(r'/locations/(\d+)/edit').firstMatch(route);
          expect(match, isNotNull);
          expect(match!.group(1), isNotNull);
          expect(int.tryParse(match.group(1)!), isNotNull);
        } else {
          final match = RegExp(r'/locations/(\d+)$').firstMatch(route);
          expect(match, isNotNull);
          expect(match!.group(1), isNotNull);
          expect(int.tryParse(match.group(1)!), isNotNull);
        }
      }
    });

    test('Malformed routes should be identifiable', () {
      // Test that we can identify malformed routes that would cause the original bug
      
      const malformedRoutes = [
        '/locations/:id/edit/2',  // The original bug
        '/locations/:id/5',       // Similar issue for details
        '/locations/:id/edit/:id', // Double parameter issue
      ];
      
      for (final route in malformedRoutes) {
        // These routes contain ':id' which indicates they're malformed
        expect(route.contains(':id'), isTrue, 
            reason: 'Route $route should be identified as malformed');
      }
      
      const wellFormedRoutes = [
        '/locations/2/edit',
        '/locations/5',
        '/locations/123/edit',
      ];
      
      for (final route in wellFormedRoutes) {
        // These routes should not contain ':id'
        expect(route.contains(':id'), isFalse,
            reason: 'Route $route should be identified as well-formed');
      }
    });
  });
}
