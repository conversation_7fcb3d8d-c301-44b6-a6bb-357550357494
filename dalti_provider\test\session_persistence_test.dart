import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/storage/storage_service.dart';
import 'package:dalti_provider/core/auth/models/jwt_token.dart';
import 'package:hive_flutter/hive_flutter.dart';

void main() {
  group('Session Persistence Tests', () {
    late JwtService jwtService;
    late HttpClient httpClient;

    setUpAll(() async {
      // Initialize Hive for testing
      await Hive.initFlutter();
      await StorageService.initForTesting();
    });

    setUp(() {
      httpClient = HttpClient();
      jwtService = JwtService(httpClient);
    });

    tearDown(() async {
      // Clear storage after each test
      await StorageService.clearAuth();
    });

    test('JWT service persists token across initialization', () async {
      // Create a test token with payload
      final testToken = JwtToken(
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_456',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
        payload: {
          'sub': 'test_user_123',
          'email': '<EMAIL>',
          'roles': ['provider'],
        },
      );

      // Store the token
      await jwtService.storeToken(testToken);
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentUserId, equals('test_user_123'));

      // Create a new JWT service instance (simulating app restart)
      final newJwtService = JwtService(httpClient);
      expect(newJwtService.isAuthenticated, isFalse); // Should be false before initialization

      // Initialize the new service (this should load the stored token)
      await newJwtService.initialize();

      // Verify the token was loaded
      expect(newJwtService.isAuthenticated, isTrue);
      expect(newJwtService.currentUserId, equals('test_user_123'));
      expect(newJwtService.currentUserEmail, equals('<EMAIL>'));
      expect(newJwtService.currentToken?.accessToken, equals('test_access_token_123'));
    });

    test('JWT service handles expired token correctly on initialization', () async {
      // Create an expired test token
      final expiredToken = JwtToken(
        accessToken: 'expired_access_token',
        refreshToken: 'expired_refresh_token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)), // Expired
        payload: {
          'sub': 'test_user_123',
          'email': '<EMAIL>',
          'roles': ['provider'],
        },
      );

      // Store the expired token
      await jwtService.storeToken(expiredToken);

      // Create a new JWT service instance
      final newJwtService = JwtService(httpClient);

      // Initialize the new service
      await newJwtService.initialize();

      // Verify the expired token was cleared
      expect(newJwtService.isAuthenticated, isFalse);
      expect(newJwtService.currentToken, isNull);
    });

    test('JWT service handles missing token gracefully', () async {
      // Ensure no token is stored
      await StorageService.clearAuth();

      // Create a new JWT service instance
      final newJwtService = JwtService(httpClient);

      // Initialize the service
      await newJwtService.initialize();

      // Verify no authentication
      expect(newJwtService.isAuthenticated, isFalse);
      expect(newJwtService.currentToken, isNull);
      expect(newJwtService.currentUserId, isNull);
    });

    test('JWT service prevents double initialization', () async {
      // Create a test token
      final testToken = JwtToken(
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_456',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
        payload: {
          'sub': 'test_user_123',
          'email': '<EMAIL>',
          'roles': ['provider'],
        },
      );

      await jwtService.storeToken(testToken);

      // Initialize multiple times
      await jwtService.initialize();
      await jwtService.initialize();
      await jwtService.initialize();

      // Should still work correctly
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentUserId, equals('test_user_123'));
    });
  });
}
