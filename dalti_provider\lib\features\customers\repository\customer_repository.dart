import '../models/customer_models.dart';

/// Abstract repository interface for customer operations
abstract class CustomerRepository {
  /// Get customers with optional filters and pagination
  Future<List<Customer>> getCustomers({
    CustomerSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  });

  /// Get a single customer by ID
  Future<Customer> getCustomer(String customerId);

  /// Create a new customer
  Future<Customer> createCustomer(CustomerRequest request);

  /// Update an existing customer
  Future<Customer> updateCustomer(String customerId, CustomerRequest request);

  /// Delete a customer
  Future<void> deleteCustomer(String customerId);

  /// Get customer appointment history
  Future<List<CustomerAppointment>> getCustomerAppointments(
    String customerId, {
    int page = 1,
    int pageSize = 20,
  });

  /// Search customers by query
  Future<List<Customer>> searchCustomers(String query);

  /// Get customer statistics
  Future<CustomerStats> getCustomerStats();

  /// Get customers count
  Future<int> getCustomersCount({CustomerSearchFilters? filters});

  /// Check if customer exists by email
  Future<bool> customerExistsByEmail(String email);

  /// Check if customer exists by phone
  Future<bool> customerExistsByPhone(String phoneNumber);

  /// Get recent customers (last 30 days)
  Future<List<Customer>> getRecentCustomers({int limit = 10});

  /// Get top customers by spending
  Future<List<Customer>> getTopCustomers({int limit = 10});

  /// Clear cached customer data
  Future<void> clearCache();

  /// Refresh customer data from server
  Future<void> refreshCustomers();
}
