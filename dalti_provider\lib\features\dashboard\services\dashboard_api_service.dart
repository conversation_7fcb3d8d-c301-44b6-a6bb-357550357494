import '../../../core/network/http_client.dart';
import '../models/dashboard_models.dart';

/// API service for dashboard data operations
class DashboardApiService {
  final HttpClient _httpClient;

  DashboardApiService(this._httpClient);

  /// Fetch business overview metrics
  Future<BusinessMetricsResponse> getBusinessOverview() async {
    try {
      print('[DashboardApiService] Fetching business overview...');
      
      final response = await _httpClient.get('/api/provider/dashboard/overview');
      
      print('[DashboardApiService] Business overview response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = BusinessMetricsResponse.fromJson(response.data);

        print('[DashboardApiService] Business overview fetched successfully');
        return responseData;
      } else {
        print('[DashboardApiService] Business overview fetch failed: ${response.statusCode}');
        return BusinessMetricsResponse(
          success: false,
          error: DashboardError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch business overview',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Business overview error: $e');
      return BusinessMetricsResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Fetch today's schedule and queue data
  Future<ScheduleDataResponse> getTodaySchedule() async {
    try {
      print('[DashboardApiService] Fetching today\'s schedule...');
      
      final response = await _httpClient.get('/api/provider/dashboard/schedule/today');
      
      print('[DashboardApiService] Schedule response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = ScheduleDataResponse.fromJson(response.data);

        print('[DashboardApiService] Schedule fetched successfully');
        return responseData;
      } else {
        print('[DashboardApiService] Schedule fetch failed: ${response.statusCode}');
        return ScheduleDataResponse(
          success: false,
          error: DashboardError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch schedule data',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Schedule error: $e');
      return ScheduleDataResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Fetch notifications
  Future<NotificationsResponse> getNotifications({
    int limit = 10,
    bool unreadOnly = false,
  }) async {
    try {
      print('[DashboardApiService] Fetching notifications (limit: $limit, unreadOnly: $unreadOnly)...');
      
      final queryParams = <String, dynamic>{
        'limit': limit.toString(),
        if (unreadOnly) 'unreadOnly': 'true',
      };
      
      final response = await _httpClient.get(
        '/api/provider/dashboard/notifications',
        queryParameters: queryParams,
      );
      
      print('[DashboardApiService] Notifications response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = NotificationsResponse.fromJson(response.data);

        print('[DashboardApiService] Notifications fetched successfully');
        return responseData;
      } else {
        print('[DashboardApiService] Notifications fetch failed: ${response.statusCode}');
        return NotificationsResponse(
          success: false,
          error: DashboardError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch notifications',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Notifications error: $e');
      return NotificationsResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Fetch quick stats for real-time updates
  Future<QuickStatsResponse> getQuickStats() async {
    try {
      print('[DashboardApiService] Fetching quick stats...');
      
      final response = await _httpClient.get('/api/provider/dashboard/quick-stats');
      
      print('[DashboardApiService] Quick stats response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = QuickStatsResponse.fromJson(response.data);

        print('[DashboardApiService] Quick stats fetched successfully');
        return responseData;
      } else {
        print('[DashboardApiService] Quick stats fetch failed: ${response.statusCode}');
        return QuickStatsResponse(
          success: false,
          error: DashboardError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch quick stats',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Quick stats error: $e');
      return QuickStatsResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Mark notification as read
  Future<BasicResponse> markNotificationAsRead(String notificationId) async {
    try {
      print('[DashboardApiService] Marking notification as read: $notificationId');
      
      final response = await _httpClient.patch(
        '/api/provider/dashboard/notifications/$notificationId/read',
      );
      
      print('[DashboardApiService] Mark as read response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        print('[DashboardApiService] Notification marked as read successfully');
        return const BasicResponse(success: true, message: 'Notification marked as read');
      } else {
        print('[DashboardApiService] Mark as read failed: ${response.statusCode}');
        return BasicResponse(
          success: false,
          error: DashboardError(
            code: 'UPDATE_ERROR',
            message: 'Failed to mark notification as read',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Mark as read error: $e');
      return BasicResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Emergency queue control (pause/resume all queues)
  Future<EmergencyControlResponse> emergencyQueueControl(
    EmergencyQueueControl request,
  ) async {
    try {
      print('[DashboardApiService] Emergency queue control: ${request.action}');
      
      final response = await _httpClient.post(
        '/api/provider/dashboard/queues/emergency-control',
        data: request.toJson(),
      );
      
      print('[DashboardApiService] Emergency control response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = EmergencyControlResponse.fromJson(response.data);

        print('[DashboardApiService] Emergency control executed successfully');
        return responseData;
      } else {
        print('[DashboardApiService] Emergency control failed: ${response.statusCode}');
        return EmergencyControlResponse(
          success: false,
          error: DashboardError(
            code: 'CONTROL_ERROR',
            message: 'Failed to execute emergency queue control',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[DashboardApiService] Emergency control error: $e');
      return EmergencyControlResponse(
        success: false,
        error: DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }
}
