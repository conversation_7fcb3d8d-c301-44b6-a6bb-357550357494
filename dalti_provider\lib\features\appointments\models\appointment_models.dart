import 'package:freezed_annotation/freezed_annotation.dart';

part 'appointment_models.freezed.dart';
part 'appointment_models.g.dart';

/// Appointment status enumeration
enum AppointmentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('InProgress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('canceled')
  canceled,
  @JsonValue('no_show')
  noShow,
  @JsonValue('rescheduled')
  rescheduled,
}

/// Main appointment model for appointment management
@freezed
class Appointment with _$Appointment {
  const factory Appointment({
    required String id,
    required String customerId,
    required String customerName,
    String? customerPhone,
    String? customerEmail,
    required String serviceId,
    required String serviceName,
    required DateTime scheduledTime,
    DateTime? expectedEndTime, // Expected end time from API
    required int duration, // in minutes
    required AppointmentStatus status,
    String? locationId,
    String? locationName,
    String? queueId,
    String? queueName,
    String? providerId,
    String? providerName,
    double? price,
    String? notes,
    String? cancellationReason,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
    DateTime? realAppointmentStartTime, // When service actually started
    DateTime? realAppointmentEndTime, // When service actually ended
    // Additional fields for appointment management
    String? reminderSent,
    bool? isRecurring,
    String? recurringPattern,
    List<String>? tags,
  }) = _Appointment;

  factory Appointment.fromJson(Map<String, dynamic> json) =>
      _$AppointmentFromJson(json);
}

/// Appointment request model for creating/updating appointments
@freezed
class AppointmentRequest with _$AppointmentRequest {
  const factory AppointmentRequest({
    required String customerId,
    required String serviceId,
    required DateTime scheduledTime,
    required int duration,
    String? locationId,
    String? queueId,
    String? notes,
    List<String>? tags,
  }) = _AppointmentRequest;

  factory AppointmentRequest.fromJson(Map<String, dynamic> json) =>
      _$AppointmentRequestFromJson(json);
}

/// Appointment search filters
@freezed
class AppointmentSearchFilters with _$AppointmentSearchFilters {
  const factory AppointmentSearchFilters({
    String? customerName,
    String? serviceName,
    String? locationId,
    String? queueId,
    AppointmentStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? tags,
  }) = _AppointmentSearchFilters;

  factory AppointmentSearchFilters.fromJson(Map<String, dynamic> json) =>
      _$AppointmentSearchFiltersFromJson(json);
}

/// Appointment response wrapper
@freezed
class AppointmentResponse with _$AppointmentResponse {
  const factory AppointmentResponse({
    required bool success,
    Appointment? appointment,
    AppointmentError? error,
  }) = _AppointmentResponse;

  factory AppointmentResponse.fromJson(Map<String, dynamic> json) =>
      _$AppointmentResponseFromJson(json);
}

/// Appointments list response
@freezed
class AppointmentsResponse with _$AppointmentsResponse {
  const factory AppointmentsResponse({
    required bool success,
    @Default([]) List<Appointment> appointments,
    @Default(0) int totalCount,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    AppointmentError? error,
  }) = _AppointmentsResponse;

  factory AppointmentsResponse.fromJson(Map<String, dynamic> json) =>
      _$AppointmentsResponseFromJson(json);
}

/// Appointment error model
@freezed
class AppointmentError with _$AppointmentError {
  const factory AppointmentError({
    required String code,
    required String message,
    String? details,
  }) = _AppointmentError;

  factory AppointmentError.fromJson(Map<String, dynamic> json) =>
      _$AppointmentErrorFromJson(json);
}

/// Calendar view data model
@freezed
class CalendarViewData with _$CalendarViewData {
  const factory CalendarViewData({
    required DateTime date,
    @Default([]) List<Appointment> appointments,
    @Default(0) int totalAppointments,
    @Default(0) int completedAppointments,
    @Default(0) int cancelledAppointments,
  }) = _CalendarViewData;

  factory CalendarViewData.fromJson(Map<String, dynamic> json) =>
      _$CalendarViewDataFromJson(json);
}

/// Time slot model for appointment scheduling
@freezed
class TimeSlot with _$TimeSlot {
  const factory TimeSlot({
    required DateTime startTime,
    required DateTime endTime,
    required bool isAvailable,
    String? appointmentId,
    String? reason, // Why slot is unavailable
  }) = _TimeSlot;

  factory TimeSlot.fromJson(Map<String, dynamic> json) =>
      _$TimeSlotFromJson(json);
}

/// Appointment statistics model
@freezed
class AppointmentStats with _$AppointmentStats {
  const factory AppointmentStats({
    @Default(0) int totalAppointments,
    @Default(0) int scheduledAppointments,
    @Default(0) int completedAppointments,
    @Default(0) int cancelledAppointments,
    @Default(0) int noShowAppointments,
    @Default(0.0) double completionRate,
    @Default(0.0) double cancellationRate,
    @Default(0.0) double noShowRate,
    @Default(0.0) double totalRevenue,
  }) = _AppointmentStats;

  factory AppointmentStats.fromJson(Map<String, dynamic> json) =>
      _$AppointmentStatsFromJson(json);
}

/// Appointment creation request model matching the new API schema
@freezed
class CreateAppointmentRequest with _$CreateAppointmentRequest {
  const factory CreateAppointmentRequest({
    required String customerUserId, // UUID string
    required int serviceId,
    required int placeId,
    required int queueId,
    required DateTime startTime,
    required DateTime endTime,
    String? notes, // Optional, max 500 chars
  }) = _CreateAppointmentRequest;

  factory CreateAppointmentRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateAppointmentRequestFromJson(json);
}

/// Appointment update request model matching the API schema
@freezed
class UpdateAppointmentRequest with _$UpdateAppointmentRequest {
  const factory UpdateAppointmentRequest({
    required int serviceId,
    required int queueId,
    required String expectedStartTime, // ISO 8601 date-time string
    required String expectedEndTime, // ISO 8601 date-time string
    String? notes, // Optional, max 500 chars
  }) = _UpdateAppointmentRequest;

  factory UpdateAppointmentRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateAppointmentRequestFromJson(json);

  /// Helper factory to create request from DateTime objects
  factory UpdateAppointmentRequest.fromDateTimes({
    required int serviceId,
    required int queueId,
    required DateTime expectedStartTime,
    required DateTime expectedEndTime,
    String? notes,
  }) {
    return UpdateAppointmentRequest(
      serviceId: serviceId,
      queueId: queueId,
      expectedStartTime: expectedStartTime.toUtc().toIso8601String(),
      expectedEndTime: expectedEndTime.toUtc().toIso8601String(),
      notes: notes,
    );
  }
}

/// Extension for CreateAppointmentRequest validation
extension CreateAppointmentRequestValidation on CreateAppointmentRequest {
  /// Validate that end time is after start time
  bool get isValidTimeRange => endTime.isAfter(startTime);

  /// Validate notes length (max 500 chars)
  bool get isValidNotesLength => notes == null || notes!.length <= 500;

  /// Check if all validations pass
  bool get isValid => isValidTimeRange && isValidNotesLength;

  /// Get validation errors
  List<String> get validationErrors {
    final errors = <String>[];

    if (!isValidTimeRange) {
      errors.add('End time must be after start time');
    }

    if (!isValidNotesLength) {
      errors.add('Notes must be 500 characters or less');
    }

    return errors;
  }
}
