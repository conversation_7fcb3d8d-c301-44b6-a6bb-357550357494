import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/service_provider.dart';
import '../widgets/service_form.dart';
import '../models/service_models.dart';

class EditServiceScreen extends ConsumerStatefulWidget {
  final String serviceId;

  const EditServiceScreen({super.key, required this.serviceId});

  @override
  ConsumerState<EditServiceScreen> createState() => _EditServiceScreenState();
}

class _EditServiceScreenState extends ConsumerState<EditServiceScreen> {
  final GlobalKey<ServiceFormState> _formKey = GlobalKey<ServiceFormState>();
  bool _isLoading = false;
  Service? _service;

  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to avoid modifying provider during widget tree building
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadService();
    });
  }

  Future<void> _loadService() async {
    try {
      final serviceId = int.tryParse(widget.serviceId);
      if (serviceId != null) {
        await ref
            .read(serviceNotifierProvider.notifier)
            .selectService(serviceId);
        final serviceState = ref.read(serviceNotifierProvider);
        if (mounted) {
          setState(() {
            _service = serviceState.selectedService;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshService() async {
    try {
      final serviceId = int.tryParse(widget.serviceId);
      if (serviceId != null) {
        await ref
            .read(serviceNotifierProvider.notifier)
            .refreshService(serviceId);
        final serviceState = ref.read(serviceNotifierProvider);
        if (mounted) {
          setState(() {
            _service = serviceState.selectedService;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_service == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Edit Service')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Edit ${_service!.title}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshService,
            tooltip: 'Refresh Service',
          ),
          TextButton(
            onPressed: _isLoading ? null : _saveService,
            child:
                _isLoading
                    ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : const Text('Save'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: ServiceForm(
          key: _formKey,
          initialService: _service,
          isEditing: true,
          onCreateService: (_) {}, // Not used in edit mode
          onUpdateService: _onUpdateService,
        ),
      ),
    );
  }

  Future<void> _saveService() async {
    if (_formKey.currentState?.validateAndSave() == true) {
      // The form will call _onUpdateService if validation passes
    }
  }

  Future<void> _onUpdateService(
    int serviceId,
    UpdateServiceRequest request,
  ) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ref
          .read(serviceNotifierProvider.notifier)
          .updateService(serviceId, request);

      if (mounted) {
        if (success) {
          // Refresh the services list to show updated data
          await ref
              .read(serviceNotifierProvider.notifier)
              .loadServices(forceRefresh: true);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Service "${_service!.title}" updated successfully',
              ),
              backgroundColor: Colors.green,
            ),
          );
          context.pop(); // Go back to services list
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update service'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
