import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';

/// Bottom sheet for filtering customers
class CustomerFilterSheet extends StatefulWidget {
  final Function(CustomerSearchFilters) onApplyFilters;
  final CustomerSearchFilters? initialFilters;

  const CustomerFilterSheet({
    super.key,
    required this.onApplyFilters,
    this.initialFilters,
  });

  @override
  State<CustomerFilterSheet> createState() => _CustomerFilterSheetState();
}

class _CustomerFilterSheetState extends State<CustomerFilterSheet> {
  CustomerStatus? _selectedStatus;
  String? _selectedWilaya;
  DateTime? _createdAfter;
  DateTime? _createdBefore;
  int _minAppointments = 0;
  double _minSpent = 0.0;
  String _sortBy = 'firstName';
  bool _ascending = true;

  final List<String> _algerianWilayas = [
    'Adrar', 'Chlef', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>rass<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Tiaret',
    'Tizi O<PERSON>', '<PERSON>ger', '<PERSON>jelfa', 'Jijel', 'S<PERSON>tif', '<PERSON>da', 'Skikda',
    'Sidi Bel Abbès', '<PERSON>ba', '<PERSON>uelma', '<PERSON>', 'Médéa', 'Mostaganem',
    'M\'Sila', 'Mascara', 'Ouargla', 'Oran', '<PERSON>adh', 'Illizi', 'Bordj Bou Arréridj',
    'Boumerdès', 'El Tarf', 'Tindouf', 'Tissemsilt', 'El Oued', 'Khenchela',
    'Souk Ahras', 'Tipaza', 'Mila', 'Aïn Defla', 'Naâma', 'Aïn Témouchent',
    'Ghardaïa', 'Relizane', 'Timimoun', 'Bordj Badji Mokhtar', 'Ouled Djellal',
    'Béni Abbès', 'In Salah', 'In Guezzam', 'Touggourt', 'Djanet', 'El M\'Ghair',
    'El Meniaa'
  ];

  final List<Map<String, String>> _sortOptions = [
    {'value': 'firstName', 'label': 'First Name'},
    {'value': 'lastName', 'label': 'Last Name'},
    {'value': 'createdAt', 'label': 'Date Added'},
    {'value': 'totalAppointments', 'label': 'Appointments'},
    {'value': 'totalSpent', 'label': 'Total Spent'},
    {'value': 'lastAppointmentDate', 'label': 'Last Visit'},
  ];

  @override
  void initState() {
    super.initState();
    _initializeFilters();
  }

  void _initializeFilters() {
    if (widget.initialFilters != null) {
      final filters = widget.initialFilters!;
      _selectedStatus = filters.status;
      _selectedWilaya = filters.wilaya;
      _createdAfter = filters.createdAfter;
      _createdBefore = filters.createdBefore;
      _minAppointments = filters.minAppointments;
      _minSpent = filters.minSpent;
      _sortBy = filters.sortBy;
      _ascending = filters.ascending;
    }
  }

  void _applyFilters() {
    final filters = CustomerSearchFilters(
      status: _selectedStatus,
      wilaya: _selectedWilaya,
      createdAfter: _createdAfter,
      createdBefore: _createdBefore,
      minAppointments: _minAppointments,
      minSpent: _minSpent,
      sortBy: _sortBy,
      ascending: _ascending,
    );

    widget.onApplyFilters(filters);
    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedWilaya = null;
      _createdAfter = null;
      _createdBefore = null;
      _minAppointments = 0;
      _minSpent = 0.0;
      _sortBy = 'firstName';
      _ascending = true;
    });
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _createdAfter ?? DateTime.now() : _createdBefore ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _createdAfter = picked;
        } else {
          _createdBefore = picked;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Text(
                  'Filter Customers',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Filter content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status filter
                  _buildSectionTitle('Status'),
                  _buildStatusFilter(),
                  const SizedBox(height: 20),

                  // Wilaya filter
                  _buildSectionTitle('Wilaya'),
                  _buildWilayaFilter(),
                  const SizedBox(height: 20),

                  // Date range filter
                  _buildSectionTitle('Date Added'),
                  _buildDateRangeFilter(),
                  const SizedBox(height: 20),

                  // Minimum appointments filter
                  _buildSectionTitle('Minimum Appointments'),
                  _buildMinAppointmentsFilter(),
                  const SizedBox(height: 20),

                  // Minimum spent filter
                  _buildSectionTitle('Minimum Spent (DA)'),
                  _buildMinSpentFilter(),
                  const SizedBox(height: 20),

                  // Sort options
                  _buildSectionTitle('Sort By'),
                  _buildSortOptions(),
                ],
              ),
            ),
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      side: BorderSide(color: AppColors.primary),
                    ),
                    child: Text(
                      'Cancel',
                      style: TextStyle(color: AppColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Wrap(
      spacing: 8,
      children: CustomerStatus.values.map((status) {
        final isSelected = _selectedStatus == status;
        return FilterChip(
          label: Text(_getStatusLabel(status)),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedStatus = selected ? status : null;
            });
          },
          selectedColor: AppColors.primary.withOpacity(0.2),
          checkmarkColor: AppColors.primary,
        );
      }).toList(),
    );
  }

  Widget _buildWilayaFilter() {
    return DropdownButtonFormField<String>(
      value: _selectedWilaya,
      decoration: const InputDecoration(
        hintText: 'Select wilaya',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: _algerianWilayas.map((wilaya) {
        return DropdownMenuItem(
          value: wilaya,
          child: Text(wilaya),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedWilaya = value;
        });
      },
    );
  }

  Widget _buildDateRangeFilter() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => _selectDate(context, true),
            child: Text(
              _createdAfter != null
                  ? 'From: ${_createdAfter!.day}/${_createdAfter!.month}/${_createdAfter!.year}'
                  : 'From Date',
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton(
            onPressed: () => _selectDate(context, false),
            child: Text(
              _createdBefore != null
                  ? 'To: ${_createdBefore!.day}/${_createdBefore!.month}/${_createdBefore!.year}'
                  : 'To Date',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMinAppointmentsFilter() {
    return Column(
      children: [
        Slider(
          value: _minAppointments.toDouble(),
          min: 0,
          max: 50,
          divisions: 50,
          label: _minAppointments.toString(),
          onChanged: (value) {
            setState(() {
              _minAppointments = value.round();
            });
          },
        ),
        Text('${_minAppointments} appointments'),
      ],
    );
  }

  Widget _buildMinSpentFilter() {
    return Slider(
      value: _minSpent,
      min: 0,
      max: 10000,
      divisions: 100,
      label: '${_minSpent.round()} DA',
      onChanged: (value) {
        setState(() {
          _minSpent = value;
        });
      },
    );
  }

  Widget _buildSortOptions() {
    return Column(
      children: [
        DropdownButtonFormField<String>(
          value: _sortBy,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _sortOptions.map((option) {
            return DropdownMenuItem(
              value: option['value'],
              child: Text(option['label']!),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _sortBy = value!;
            });
          },
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('Ascending'),
                value: true,
                groupValue: _ascending,
                onChanged: (value) {
                  setState(() {
                    _ascending = value!;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('Descending'),
                value: false,
                groupValue: _ascending,
                onChanged: (value) {
                  setState(() {
                    _ascending = value!;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getStatusLabel(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return 'Active';
      case CustomerStatus.inactive:
        return 'Inactive';
      case CustomerStatus.blocked:
        return 'Blocked';
    }
  }
}
