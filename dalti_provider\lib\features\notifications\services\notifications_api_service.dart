import 'package:dio/dio.dart';
import '../../../core/network/http_client.dart';
import '../models/notification_models.dart';

class NotificationsApiService {
  final HttpClient _httpClient;

  NotificationsApiService(this._httpClient);

  /// Get notifications list
  Future<List<NotificationItem>> getNotifications({
    bool unreadOnly = false,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'unreadOnly': unreadOnly.toString(),
      };

      if (limit != null) {
        queryParams['limit'] = limit.toString();
      }

      if (offset != null) {
        queryParams['offset'] = offset.toString();
      }

      print(
        '[NotificationsApiService] Fetching notifications with params: $queryParams',
      );

      final response = await _httpClient.get(
        '/api/auth/notifications/mobile/list',
        queryParameters: queryParams,
      );

      print(
        '[NotificationsApiService] Notifications response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        final notifications =
            data
                .map(
                  (json) =>
                      NotificationItem.fromJson(json as Map<String, dynamic>),
                )
                .toList();

        print(
          '[NotificationsApiService] Successfully fetched ${notifications.length} notifications',
        );
        return notifications;
      } else {
        print(
          '[NotificationsApiService] Failed to fetch notifications: ${response.statusCode}',
        );
        throw Exception(
          'Failed to fetch notifications: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('[NotificationsApiService] DioException: ${e.message}');
      print('[NotificationsApiService] Response data: ${e.response?.data}');

      // For development: return mock data if API endpoint is not found
      if (e.response?.statusCode == 404) {
        print(
          '[NotificationsApiService] API endpoint not found (404), returning mock data for testing',
        );
        return _getMockNotifications();
      }

      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('[NotificationsApiService] Unexpected error: $e');
      throw Exception('Failed to fetch notifications: $e');
    }
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      print(
        '[NotificationsApiService] Marking notification as read: $notificationId',
      );

      final response = await _httpClient.post(
        '/api/auth/notifications/mobile/mark-as-read',
        data: {'notificationId': notificationId},
      );

      print(
        '[NotificationsApiService] Mark as read response status: ${response.statusCode}',
      );

      if (response.statusCode != 200) {
        print(
          '[NotificationsApiService] Failed to mark notification as read: ${response.statusCode}',
        );
        throw Exception(
          'Failed to mark notification as read: ${response.statusCode}',
        );
      }

      print(
        '[NotificationsApiService] Successfully marked notification as read',
      );
    } on DioException catch (e) {
      print(
        '[NotificationsApiService] DioException marking as read: ${e.message}',
      );
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print('[NotificationsApiService] Unexpected error marking as read: $e');
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      print('[NotificationsApiService] Marking all notifications as read');

      final response = await _httpClient.post(
        '/api/auth/notifications/mobile/mark-all-as-read',
      );

      print(
        '[NotificationsApiService] Mark all as read response status: ${response.statusCode}',
      );

      if (response.statusCode != 200) {
        print(
          '[NotificationsApiService] Failed to mark all notifications as read: ${response.statusCode}',
        );
        throw Exception(
          'Failed to mark all notifications as read: ${response.statusCode}',
        );
      }

      print(
        '[NotificationsApiService] Successfully marked all notifications as read',
      );
    } on DioException catch (e) {
      print(
        '[NotificationsApiService] DioException marking all as read: ${e.message}',
      );
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print(
        '[NotificationsApiService] Unexpected error marking all as read: $e',
      );
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      print('[NotificationsApiService] Deleting notification: $notificationId');

      final response = await _httpClient.delete(
        '/api/auth/notifications/mobile/$notificationId',
      );

      print(
        '[NotificationsApiService] Delete notification response status: ${response.statusCode}',
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        print(
          '[NotificationsApiService] Failed to delete notification: ${response.statusCode}',
        );
        throw Exception(
          'Failed to delete notification: ${response.statusCode}',
        );
      }

      print('[NotificationsApiService] Successfully deleted notification');
    } on DioException catch (e) {
      print(
        '[NotificationsApiService] DioException deleting notification: ${e.message}',
      );
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      print(
        '[NotificationsApiService] Unexpected error deleting notification: $e',
      );
      throw Exception('Failed to delete notification: $e');
    }
  }

  /// Generate mock notifications for testing when API is not available
  List<NotificationItem> _getMockNotifications() {
    final now = DateTime.now();
    return [
      NotificationItem(
        id: '1',
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        userId: 'current_user',
        type: NotificationType.appointmentConfirmedCustomer.value,
        title: 'Appointment Confirmed',
        message:
            'Your appointment with John Doe has been confirmed for tomorrow at 2:00 PM.',
        isRead: false,
        readAt: null,
        link: '/appointments/123',
        actorId: 'customer_456',
        actor: NotificationActor(
          id: 'customer_456',
          username: 'john.doe',
          firstName: 'John',
          lastName: 'Doe',
        ),
      ),
      NotificationItem(
        id: '2',
        createdAt: now.subtract(const Duration(hours: 5)),
        updatedAt: now.subtract(const Duration(hours: 5)),
        userId: 'current_user',
        type: NotificationType.messageReceived.value,
        title: 'New Message',
        message: 'You have received a new message from Sarah Wilson.',
        isRead: false,
        readAt: null,
        link: '/messages/conversation/789',
        actorId: 'customer_789',
        actor: NotificationActor(
          id: 'customer_789',
          username: 'sarah.wilson',
          firstName: 'Sarah',
          lastName: 'Wilson',
        ),
      ),
      NotificationItem(
        id: '3',
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(hours: 1)),
        userId: 'current_user',
        type: NotificationType.appointmentCompletedCustomer.value,
        title: 'Service Completed',
        message: 'Service for Mike Johnson has been marked as completed.',
        isRead: true,
        readAt: now.subtract(const Duration(hours: 1)),
        link: '/appointments/456',
        actorId: 'customer_101',
        actor: NotificationActor(
          id: 'customer_101',
          username: 'mike.johnson',
          firstName: 'Mike',
          lastName: 'Johnson',
        ),
      ),
      NotificationItem(
        id: '4',
        createdAt: now.subtract(const Duration(days: 2)),
        updatedAt: now.subtract(const Duration(minutes: 30)),
        userId: 'current_user',
        type: NotificationType.appointmentCancelledCustomer.value,
        title: 'Appointment Cancelled',
        message:
            'Emma Davis has cancelled her appointment scheduled for Friday.',
        isRead: true,
        readAt: now.subtract(const Duration(minutes: 30)),
        link: '/appointments/789',
        actorId: 'customer_202',
        actor: NotificationActor(
          id: 'customer_202',
          username: 'emma.davis',
          firstName: 'Emma',
          lastName: 'Davis',
        ),
      ),
      NotificationItem(
        id: '5',
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now.subtract(const Duration(days: 3)),
        userId: 'current_user',
        type: NotificationType.systemNotification.value,
        title: 'System Update',
        message:
            'The system will undergo maintenance tonight from 11 PM to 1 AM.',
        isRead: false,
        readAt: null,
        link: null,
        actorId: null,
        actor: null,
      ),
    ];
  }
}
