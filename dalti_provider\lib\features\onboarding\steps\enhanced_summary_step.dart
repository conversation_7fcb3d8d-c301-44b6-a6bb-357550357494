import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../services/models/service_models.dart';

/// Enhanced Summary step showing all collected data
class EnhancedSummaryStep extends ConsumerWidget {
  const EnhancedSummaryStep({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final data = onboardingState.data;

    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Step header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colors.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: theme.colors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Summary',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Completion status
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colors.primary.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.celebration,
                            color: theme.colors.primary,
                            size: 32,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Setup Complete!',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colors.primary,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Your Dalti Provider account is ready to use. Review your setup below.',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: theme.colors.primary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Business Profile Summary
                    if (data?.businessProfile != null) ...[
                      _buildSectionCard(
                        theme,
                        'Business Information',
                        Icons.business,
                        [
                          _buildSummaryItem('Business Name', data!.businessProfile!.businessName),
                          _buildSummaryItem('Description', data.businessProfile!.description),
                          _buildSummaryItem('Category', data.businessProfile!.categoryName),
                          if (data.businessProfile!.mobile != null)
                            _buildSummaryItem('Mobile', data.businessProfile!.mobile!),
                          if (data.businessProfile!.landline != null)
                            _buildSummaryItem('Landline', data.businessProfile!.landline!),
                          if (data.businessProfile!.website != null)
                            _buildSummaryItem('Website', data.businessProfile!.website!),
                        ],
                        onEdit: () => _navigateToStep(ref, OnboardingStep.businessProfile),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Location Summary
                    if (data?.primaryLocation != null) ...[
                      _buildSectionCard(
                        theme,
                        'Primary Location',
                        Icons.location_on,
                        [
                          _buildSummaryItem('Name', data!.primaryLocation!.name),
                          _buildSummaryItem('Address', data.primaryLocation!.address),
                          _buildSummaryItem('City', data.primaryLocation!.city),
                          _buildSummaryItem('Features', _getLocationFeatures(data.primaryLocation!)),
                        ],
                        onEdit: () => _navigateToStep(ref, OnboardingStep.locationSetup),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Services Summary
                    if (data?.services != null && data!.services.isNotEmpty) ...[
                      _buildSectionCard(
                        theme,
                        'Services (${data.services.length})',
                        Icons.medical_services,
                        data.services.map((service) => 
                          _buildServiceSummaryItem(theme, service)
                        ).toList(),
                        onEdit: () => _navigateToStep(ref, OnboardingStep.serviceCreation),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Queues Summary
                    if (data?.queuesWithLocationLinks != null && data!.queuesWithLocationLinks.isNotEmpty) ...[
                      _buildSectionCard(
                        theme,
                        'Queues (${data.queuesWithLocationLinks.length})',
                        Icons.queue,
                        data.queuesWithLocationLinks.map((queue) => 
                          _buildQueueSummaryItem(theme, queue, data.services)
                        ).toList(),
                        onEdit: () => _navigateToStep(ref, OnboardingStep.queueManagement),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Next steps info
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.colors.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.colors.outline.withOpacity(0.2),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.lightbulb_outline,
                                color: theme.colors.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'What\'s Next?',
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            '• Start accepting bookings from customers\n'
                            '• Manage your schedule and appointments\n'
                            '• Add more locations and services as needed\n'
                            '• Monitor your business analytics',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colors.onSurface.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Navigation buttons
            WizardNavigationButtons(
              isValid: true,
              completeButtonText: 'Complete Setup',
              onComplete: () => _completeSetup(ref),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(
    ThemeData theme,
    String title,
    IconData icon,
    List<Widget> items,
    {VoidCallback? onEdit}
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: theme.colors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onEdit != null)
                  TextButton.icon(
                    onPressed: onEdit,
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit'),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            ...items,
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceSummaryItem(ThemeData theme, Service service) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (service.color != null)
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Color(int.parse('0xFF${service.color!.replaceAll('#', '')}')),
                    shape: BoxShape.circle,
                  ),
                ),
              if (service.color != null) const SizedBox(width: 8),
              Expanded(
                child: Text(
                  service.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${service.duration} min • ${service.price?.toStringAsFixed(0) ?? '0'} DA • ${service.serviceDeliveryType}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colors.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQueueSummaryItem(ThemeData theme, QueueWithLocationLink queue, List<Service> services) {
    final assignedServices = services.where((service) => queue.serviceIndices.contains(service.id)).toList();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            queue.title,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Services: ${assignedServices.map((s) => s.title).join(', ')}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colors.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  String _getLocationFeatures(Location location) {
    final features = <String>[];
    if (location.parking) features.add('Parking');
    if (location.elevator) features.add('Elevator');
    if (location.handicapAccess) features.add('Wheelchair Access');
    return features.isEmpty ? 'None' : features.join(', ');
  }

  void _navigateToStep(WidgetRef ref, OnboardingStep step) {
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.goToStep(step);
  }

  void _completeSetup(WidgetRef ref) {
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.completeWizard();
    
    // Here you would typically submit the data to the API
    // For now, we'll just show a completion dialog
    // This will be handled by the main wizard screen
  }
}
