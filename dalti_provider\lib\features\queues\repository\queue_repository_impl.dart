import '../services/queue_api_service.dart';
import '../models/queue_models.dart';
import 'queue_repository.dart';

/// Concrete implementation of QueueRepository using API service
class QueueRepositoryImpl implements QueueRepository {
  final QueueApiService _apiService;

  QueueRepositoryImpl({required QueueApiService apiService})
    : _apiService = apiService;

  @override
  Future<List<Queue>> getQueues({bool? isActive, int? locationId}) async {
    try {
      print(
        '[QueueRepository] Getting queues (isActive: $isActive, locationId: $locationId)',
      );

      final queues = await _apiService.getQueues(
        isActive: isActive,
        locationId: locationId,
      );

      print('[QueueRepository] Retrieved ${queues.length} queues');
      return queues;
    } catch (e) {
      print('[QueueRepository] Error getting queues: $e');
      rethrow;
    }
  }

  @override
  Future<Queue> getQueueById(int id) async {
    try {
      print('[QueueRepository] Getting queue by ID: $id');

      final queue = await _apiService.getQueueById(id);

      print('[QueueRepository] Retrieved queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueRepository] Error getting queue by ID: $e');
      rethrow;
    }
  }

  @override
  Future<Queue> createQueue(CreateQueueRequest request) async {
    try {
      print('[QueueRepository] Creating queue: ${request.toJson()}');

      // Validate queue data
      final validation = await validateQueue(
        title: request.title,
        locationId: request.locationId,
        serviceIds: request.serviceIds,
      );

      if (!validation.isValid) {
        throw Exception('Invalid queue data: ${validation.errorMessage}');
      }

      final queue = await _apiService.createQueue(request);

      print('[QueueRepository] Created queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueRepository] Error creating queue: $e');
      rethrow;
    }
  }

  @override
  Future<Queue> updateQueue(int id, UpdateQueueRequest request) async {
    try {
      print('[QueueRepository] Updating queue $id: ${request.toJson()}');

      // Validate queue data if title is being updated
      if (request.title != null) {
        final currentQueue = await getQueueById(id);
        final validation = await validateQueue(
          title: request.title!,
          locationId: currentQueue.locationId,
          serviceIds: request.serviceIds ?? currentQueue.serviceIds,
          excludeQueueId: id,
        );

        if (!validation.isValid) {
          throw Exception('Invalid queue data: ${validation.errorMessage}');
        }
      }

      final queue = await _apiService.updateQueue(id, request);

      print('[QueueRepository] Updated queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueRepository] Error updating queue: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deleteQueue(int id) async {
    try {
      print('[QueueRepository] Deleting queue: $id');

      // Check if queue can be deleted
      final canDelete = await canDeleteQueue(id);
      if (!canDelete) {
        throw Exception('Cannot delete queue: Queue has dependencies');
      }

      final result = await _apiService.deleteQueue(id);

      print('[QueueRepository] Deleted queue: $id');
      return result;
    } catch (e) {
      print('[QueueRepository] Error deleting queue: $e');
      rethrow;
    }
  }

  @override
  Future<List<Queue>> getQueuesByLocation(
    int locationId, {
    bool? isActive,
  }) async {
    try {
      print('[QueueRepository] Getting queues for location: $locationId');

      final queues = await _apiService.getQueuesByLocation(
        locationId,
        isActive: isActive,
      );

      print(
        '[QueueRepository] Retrieved ${queues.length} queues for location $locationId',
      );
      return queues;
    } catch (e) {
      print('[QueueRepository] Error getting queues by location: $e');
      rethrow;
    }
  }

  @override
  Future<List<QueueService>> getQueueServices(int queueId) async {
    try {
      print('[QueueRepository] Getting services for queue: $queueId');

      final queueServices = await _apiService.getQueueServices(queueId);

      print(
        '[QueueRepository] Retrieved ${queueServices.length} services for queue $queueId',
      );
      return queueServices;
    } catch (e) {
      print('[QueueRepository] Error getting queue services: $e');
      rethrow;
    }
  }

  @override
  Future<QueueService> assignServiceToQueue(
    int queueId,
    AssignServiceRequest request,
  ) async {
    try {
      print(
        '[QueueRepository] Assigning service to queue $queueId: ${request.toJson()}',
      );

      final queueService = await _apiService.assignServiceToQueue(
        queueId,
        request,
      );

      print('[QueueRepository] Assigned service to queue');
      return queueService;
    } catch (e) {
      print('[QueueRepository] Error assigning service to queue: $e');
      rethrow;
    }
  }

  @override
  Future<bool> removeServiceFromQueue(int queueId, int serviceId) async {
    try {
      print(
        '[QueueRepository] Removing service $serviceId from queue $queueId',
      );

      final result = await _apiService.removeServiceFromQueue(
        queueId,
        serviceId,
      );

      print('[QueueRepository] Removed service from queue');
      return result;
    } catch (e) {
      print('[QueueRepository] Error removing service from queue: $e');
      rethrow;
    }
  }

  @override
  Future<Map<int, List<Queue>>> getQueuesGroupedByLocation({
    bool? isActive,
  }) async {
    try {
      print('[QueueRepository] Getting queues grouped by location');

      final queues = await getQueues(isActive: isActive);

      final groupedQueues = <int, List<Queue>>{};
      for (final queue in queues) {
        groupedQueues.putIfAbsent(queue.locationId, () => []).add(queue);
      }

      print(
        '[QueueRepository] Grouped queues by ${groupedQueues.length} locations',
      );
      return groupedQueues;
    } catch (e) {
      print('[QueueRepository] Error getting queues grouped by location: $e');
      rethrow;
    }
  }

  @override
  Future<Queue> toggleQueueStatus(int id, bool isActive) async {
    try {
      print('[QueueRepository] Toggling queue $id status to: $isActive');

      // Get current queue data
      final currentQueue = await getQueueById(id);

      final request = UpdateQueueRequest(
        title: currentQueue.title,
        serviceIds: currentQueue.serviceIds,
        isActive: isActive,
        openingHours:
            currentQueue.openingHours?.days.map((day) => day.toJson()).toList(),
      );

      final queue = await updateQueue(id, request);

      print('[QueueRepository] Toggled queue status: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueRepository] Error toggling queue status: $e');
      rethrow;
    }
  }

  @override
  Future<QueueStats> getQueueStats() async {
    try {
      print('[QueueRepository] Getting queue statistics');

      final queues = await getQueues();
      final stats = QueueStats.fromQueues(queues);

      print(
        '[QueueRepository] Retrieved queue stats: ${stats.totalQueues} total',
      );
      return stats;
    } catch (e) {
      print('[QueueRepository] Error getting queue stats: $e');
      rethrow;
    }
  }

  @override
  Future<QueueStats> getLocationQueueStats(int locationId) async {
    try {
      print('[QueueRepository] Getting queue stats for location: $locationId');

      final queues = await getQueuesByLocation(locationId);
      final stats = QueueStats.fromQueues(queues);

      print(
        '[QueueRepository] Retrieved location queue stats: ${stats.totalQueues} total',
      );
      return stats;
    } catch (e) {
      print('[QueueRepository] Error getting location queue stats: $e');
      rethrow;
    }
  }

  @override
  Future<QueueValidationResult> validateQueue({
    required String title,
    required int locationId,
    required List<int> serviceIds,
    int? excludeQueueId,
  }) async {
    try {
      final warnings = <String>[];

      // Validate title
      if (title.trim().isEmpty) {
        return QueueValidationResult.invalid('Queue title cannot be empty');
      }

      if (title.length < 2) {
        return QueueValidationResult.invalid(
          'Queue title must be at least 2 characters long',
        );
      }

      if (title.length > 100) {
        return QueueValidationResult.invalid(
          'Queue title cannot exceed 100 characters',
        );
      }

      // Check for title conflicts
      final hasConflict = await hasQueueTitleConflict(
        title: title,
        locationId: locationId,
        excludeQueueId: excludeQueueId,
      );

      if (hasConflict) {
        return QueueValidationResult.invalid(
          'A queue with this title already exists at this location',
        );
      }

      // Validate service IDs
      if (serviceIds.isEmpty) {
        return QueueValidationResult.invalid(
          'Queue must have at least one service assigned',
        );
      }

      if (serviceIds.length > 20) {
        warnings.add(
          'Queue has many services (${serviceIds.length}). Consider splitting into multiple queues.',
        );
      }

      return QueueValidationResult.valid(warnings: warnings);
    } catch (e) {
      return QueueValidationResult.invalid('Error validating queue: $e');
    }
  }

  @override
  Future<bool> canDeleteQueue(int id) async {
    try {
      final dependencies = await getQueueDependencies(id);
      return dependencies.canDelete;
    } catch (e) {
      print('[QueueRepository] Error checking if queue can be deleted: $e');
      return false;
    }
  }

  @override
  Future<List<Queue>> getQueuesByService(int serviceId) async {
    try {
      print('[QueueRepository] Getting queues for service: $serviceId');

      final allQueues = await getQueues();
      final queuesWithService =
          allQueues.where((queue) => queue.hasService(serviceId)).toList();

      print(
        '[QueueRepository] Found ${queuesWithService.length} queues with service $serviceId',
      );
      return queuesWithService;
    } catch (e) {
      print('[QueueRepository] Error getting queues by service: $e');
      rethrow;
    }
  }

  @override
  Future<List<QueueService>> bulkAssignServicesToQueue(
    int queueId,
    List<int> serviceIds,
  ) async {
    try {
      print(
        '[QueueRepository] Bulk assigning ${serviceIds.length} services to queue $queueId',
      );

      final queueServices = await _apiService.bulkAssignServicesToQueue(
        queueId,
        serviceIds,
      );

      print('[QueueRepository] Bulk assigned ${queueServices.length} services');
      return queueServices;
    } catch (e) {
      print('[QueueRepository] Error bulk assigning services: $e');
      rethrow;
    }
  }

  @override
  Future<bool> bulkRemoveServicesFromQueue(
    int queueId,
    List<int> serviceIds,
  ) async {
    try {
      print(
        '[QueueRepository] Bulk removing ${serviceIds.length} services from queue $queueId',
      );

      final result = await _apiService.bulkRemoveServicesFromQueue(
        queueId,
        serviceIds,
      );

      print('[QueueRepository] Bulk removed services: $result');
      return result;
    } catch (e) {
      print('[QueueRepository] Error bulk removing services: $e');
      rethrow;
    }
  }

  @override
  Future<Queue> updateQueueServices(int queueId, List<int> serviceIds) async {
    try {
      print(
        '[QueueRepository] Updating queue $queueId services to: $serviceIds',
      );

      // Get current services
      final currentServices = await getQueueServices(queueId);
      final currentServiceIds = currentServices.map((s) => s.serviceId).toSet();
      final newServiceIds = serviceIds.toSet();

      // Calculate services to add and remove
      final servicesToAdd =
          newServiceIds.difference(currentServiceIds).toList();
      final servicesToRemove =
          currentServiceIds.difference(newServiceIds).toList();

      // Remove services first
      if (servicesToRemove.isNotEmpty) {
        await bulkRemoveServicesFromQueue(queueId, servicesToRemove);
      }

      // Add new services
      if (servicesToAdd.isNotEmpty) {
        await bulkAssignServicesToQueue(queueId, servicesToAdd);
      }

      // Get updated queue
      final updatedQueue = await getQueueById(queueId);

      print('[QueueRepository] Updated queue services');
      return updatedQueue;
    } catch (e) {
      print('[QueueRepository] Error updating queue services: $e');
      rethrow;
    }
  }

  @override
  Future<List<Queue>> getActiveQueuesByLocation(int locationId) async {
    return getQueuesByLocation(locationId, isActive: true);
  }

  @override
  Future<List<Queue>> getInactiveQueuesByLocation(int locationId) async {
    return getQueuesByLocation(locationId, isActive: false);
  }

  @override
  Future<List<Queue>> searchQueues({
    required String query,
    int? locationId,
    bool? isActive,
  }) async {
    try {
      print('[QueueRepository] Searching queues with query: "$query"');

      final queues = await getQueues(
        locationId: locationId,
        isActive: isActive,
      );
      final filteredQueues =
          queues.where((queue) {
            return queue.title.toLowerCase().contains(query.toLowerCase());
          }).toList();

      print(
        '[QueueRepository] Found ${filteredQueues.length} queues matching query',
      );
      return filteredQueues;
    } catch (e) {
      print('[QueueRepository] Error searching queues: $e');
      rethrow;
    }
  }

  @override
  Future<bool> hasQueueTitleConflict({
    required String title,
    required int locationId,
    int? excludeQueueId,
  }) async {
    try {
      final queues = await getQueuesByLocation(locationId);

      return queues.any((queue) {
        return queue.title.toLowerCase() == title.toLowerCase() &&
            queue.id != excludeQueueId;
      });
    } catch (e) {
      print('[QueueRepository] Error checking title conflict: $e');
      return false;
    }
  }

  @override
  Future<QueueDependencies> getQueueDependencies(int id) async {
    try {
      print('[QueueRepository] Getting dependencies for queue: $id');

      // For now, we'll assume queues can be deleted unless the API returns a 409 error
      // In a real implementation, this would check for appointments, etc.

      return QueueDependencies.safe();
    } catch (e) {
      print('[QueueRepository] Error getting queue dependencies: $e');
      return QueueDependencies.blocked(
        blockingReasons: ['Error checking dependencies: $e'],
      );
    }
  }
}
