import 'package:flutter/material.dart';
import '../models/location_models.dart';
import '../../schedules/models/opening_hours_models.dart';

class LocationInfoCard extends StatelessWidget {
  final Location location;

  const LocationInfoCard({super.key, required this.location});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.business,
                  size: 24,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Location Information',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Location Name
            _buildInfoRow(
              context,
              icon: Icons.location_on,
              label: 'Name',
              value: location.name,
            ),

            const SizedBox(height: 12),

            // Address
            _buildInfoRow(
              context,
              icon: Icons.home,
              label: 'Address',
              value: location.address,
            ),

            const SizedBox(height: 12),

            // City
            _buildInfoRow(
              context,
              icon: Icons.location_city,
              label: 'City',
              value: location.city,
            ),

            const SizedBox(height: 12),

            // Timezone (if available)
            if (location.timezone != null)
              _buildInfoRow(
                context,
                icon: Icons.schedule,
                label: 'Timezone',
                value: location.timezone!,
              ),

            if (location.timezone != null) const SizedBox(height: 12),

            // Coordinates (if available)
            if (location.latitude != null && location.longitude != null)
              _buildInfoRow(
                context,
                icon: Icons.my_location,
                label: 'Coordinates',
                value:
                    '${location.latitude!.toStringAsFixed(6)}, ${location.longitude!.toStringAsFixed(6)}',
              ),

            if (location.latitude != null && location.longitude != null)
              const SizedBox(height: 16),

            // Opening Hours Section (if available)
            if (location.openingHours != null &&
                location.openingHours!.isNotEmpty) ...[
              Text(
                'Opening Hours',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
              ),

              const SizedBox(height: 12),

              _buildOpeningHoursSection(),

              const SizedBox(height: 16),
            ],

            // Amenities Section
            Text(
              'Amenities',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 8),

            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildAmenityChip(
                  icon: Icons.local_parking,
                  label: 'Parking',
                  available: location.parking,
                ),
                _buildAmenityChip(
                  icon: Icons.elevator,
                  label: 'Elevator',
                  available: location.elevator,
                ),
                _buildAmenityChip(
                  icon: Icons.accessible,
                  label: 'Accessible',
                  available: location.handicapAccess,
                ),
              ],
            ),

            // Mobile Visibility Status
            if (location.isMobileHidden) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.visibility_off,
                      size: 16,
                      color: Colors.orange.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Hidden from Mobile App',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade600),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(value, style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAmenityChip({
    required IconData icon,
    required String label,
    required bool available,
  }) {
    final color = available ? Colors.green : Colors.grey;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(available ? icon : Icons.close, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOpeningHoursSection() {
    if (location.openingHours == null || location.openingHours!.isEmpty) {
      return const Text('No opening hours available');
    }

    // Sort days in proper order
    final daysOrder = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    final sortedHours = List<DayOpeningHours>.from(location.openingHours!);
    sortedHours.sort((a, b) {
      final aIndex = daysOrder.indexOf(a.dayOfWeek);
      final bIndex = daysOrder.indexOf(b.dayOfWeek);
      return aIndex.compareTo(bIndex);
    });

    return Column(
      children:
          sortedHours.map((dayHours) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      dayHours.dayOfWeek,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color:
                            dayHours.isActive
                                ? Colors.black87
                                : Colors.grey.shade600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child:
                        dayHours.isActive && dayHours.hours.isNotEmpty
                            ? Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children:
                                  dayHours.hours.map((timeSlot) {
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.blue.withValues(
                                          alpha: 0.1,
                                        ),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.blue.withValues(
                                            alpha: 0.3,
                                          ),
                                        ),
                                      ),
                                      child: Text(
                                        '${timeSlot.timeFrom} - ${timeSlot.timeTo}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.blue,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                            )
                            : Text(
                              'Closed',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }
}
