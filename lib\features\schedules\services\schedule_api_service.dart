import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../models/schedule_models.dart';

/// API service for schedule management operations
class ScheduleApiService extends ApiService {
  ScheduleApiService(super.httpClient);

  /// Get all schedules for the current provider
  Future<List<Schedule>> getSchedules({
    int? locationId,
    int? dayOfWeek,
    bool? isActive,
  }) async {
    try {
      print('[ScheduleApiService] Getting schedules (locationId: $locationId, dayOfWeek: $dayOfWeek, isActive: $isActive)');

      final queryParams = <String, dynamic>{};
      if (locationId != null) queryParams['locationId'] = locationId;
      if (dayOfWeek != null) queryParams['dayOfWeek'] = dayOfWeek;
      if (isActive != null) queryParams['isActive'] = isActive;

      final response = await httpClient.get(
        '/api/auth/providers/schedules',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final schedulesList = data['data'] as List;
          final schedules = schedulesList
              .map((json) => Schedule.fromJson(json as Map<String, dynamic>))
              .toList();

          print('[ScheduleApiService] Successfully retrieved ${schedules.length} schedules');
          return schedules;
        } else {
          throw Exception('Invalid response format: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('Failed to get schedules: ${response.statusCode}');
      }
    } catch (e) {
      print('[ScheduleApiService] Error getting schedules: $e');
      rethrow;
    }
  }

  /// Get schedule by ID
  Future<Schedule> getScheduleById(int id) async {
    try {
      print('[ScheduleApiService] Getting schedule by ID: $id');

      final response = await httpClient.get('/api/auth/providers/schedules/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final schedule = Schedule.fromJson(data['data'] as Map<String, dynamic>);
          print('[ScheduleApiService] Successfully retrieved schedule: ${schedule.id}');
          return schedule;
        } else {
          throw Exception('Invalid response format: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('Failed to get schedule: ${response.statusCode}');
      }
    } catch (e) {
      print('[ScheduleApiService] Error getting schedule by ID: $e');
      rethrow;
    }
  }

  /// Create a new schedule
  Future<Schedule> createSchedule(CreateScheduleRequest request) async {
    try {
      print('[ScheduleApiService] Creating schedule: ${request.toJson()}');

      final response = await httpClient.post(
        '/api/auth/providers/schedules',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final schedule = Schedule.fromJson(data['data'] as Map<String, dynamic>);
          print('[ScheduleApiService] Successfully created schedule: ${schedule.id}');
          return schedule;
        } else {
          throw Exception('Invalid response format: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('Failed to create schedule: ${response.statusCode}');
      }
    } catch (e) {
      print('[ScheduleApiService] Error creating schedule: $e');
      rethrow;
    }
  }

  /// Update an existing schedule
  Future<Schedule> updateSchedule(int id, UpdateScheduleRequest request) async {
    try {
      print('[ScheduleApiService] Updating schedule $id: ${request.toJson()}');

      final response = await httpClient.put(
        '/api/auth/providers/schedules/$id',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final schedule = Schedule.fromJson(data['data'] as Map<String, dynamic>);
          print('[ScheduleApiService] Successfully updated schedule: ${schedule.id}');
          return schedule;
        } else {
          throw Exception('Invalid response format: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('Failed to update schedule: ${response.statusCode}');
      }
    } catch (e) {
      print('[ScheduleApiService] Error updating schedule: $e');
      rethrow;
    }
  }

  /// Delete a schedule
  Future<bool> deleteSchedule(int id) async {
    try {
      print('[ScheduleApiService] Deleting schedule: $id');

      final response = await httpClient.delete('/api/auth/providers/schedules/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          print('[ScheduleApiService] Successfully deleted schedule: $id');
          return true;
        } else {
          throw Exception('Invalid response format: ${data['message'] ?? 'Unknown error'}');
        }
      } else {
        throw Exception('Failed to delete schedule: ${response.statusCode}');
      }
    } catch (e) {
      print('[ScheduleApiService] Error deleting schedule: $e');
      rethrow;
    }
  }

  /// Check for schedule conflicts
  Future<List<Schedule>> checkScheduleConflicts({
    required int dayOfWeek,
    required String startTime,
    required String endTime,
    required int locationId,
    int? excludeScheduleId,
  }) async {
    try {
      print('[ScheduleApiService] Checking schedule conflicts for location $locationId on day $dayOfWeek');

      // Get all schedules for the location and day
      final schedules = await getSchedules(
        locationId: locationId,
        dayOfWeek: dayOfWeek,
        isActive: true,
      );

      // Filter out the excluded schedule if provided
      final relevantSchedules = excludeScheduleId != null
          ? schedules.where((s) => s.id != excludeScheduleId).toList()
          : schedules;

      // Check for conflicts
      final conflicts = <Schedule>[];
      final newTimeSlot = TimeSlot(startTime: startTime, endTime: endTime);

      for (final schedule in relevantSchedules) {
        final existingTimeSlot = TimeSlot(
          startTime: schedule.startTime,
          endTime: schedule.endTime,
        );

        if (newTimeSlot.conflictsWith(existingTimeSlot)) {
          conflicts.add(schedule);
        }
      }

      print('[ScheduleApiService] Found ${conflicts.length} schedule conflicts');
      return conflicts;
    } catch (e) {
      print('[ScheduleApiService] Error checking schedule conflicts: $e');
      rethrow;
    }
  }

  /// Bulk create schedules
  Future<List<Schedule>> createBulkSchedules(List<CreateScheduleRequest> requests) async {
    try {
      print('[ScheduleApiService] Creating ${requests.length} schedules in bulk');

      final createdSchedules = <Schedule>[];

      // Create schedules one by one (API doesn't support bulk creation)
      for (final request in requests) {
        try {
          final schedule = await createSchedule(request);
          createdSchedules.add(schedule);
        } catch (e) {
          print('[ScheduleApiService] Failed to create schedule: ${request.toJson()}, error: $e');
          // Continue with other schedules
        }
      }

      print('[ScheduleApiService] Successfully created ${createdSchedules.length}/${requests.length} schedules');
      return createdSchedules;
    } catch (e) {
      print('[ScheduleApiService] Error in bulk schedule creation: $e');
      rethrow;
    }
  }

  /// Get schedule statistics for a location
  Future<Map<String, dynamic>> getScheduleStats(int locationId) async {
    try {
      print('[ScheduleApiService] Getting schedule stats for location: $locationId');

      final schedules = await getSchedules(locationId: locationId, isActive: true);

      // Calculate statistics
      final stats = <String, dynamic>{
        'totalSchedules': schedules.length,
        'daysWithSchedules': schedules.map((s) => s.dayOfWeek).toSet().length,
        'totalHours': _calculateTotalHours(schedules),
        'schedulesByDay': _groupSchedulesByDay(schedules),
      };

      print('[ScheduleApiService] Calculated schedule stats: $stats');
      return stats;
    } catch (e) {
      print('[ScheduleApiService] Error getting schedule stats: $e');
      return {};
    }
  }

  /// Calculate total hours from schedules
  double _calculateTotalHours(List<Schedule> schedules) {
    double totalHours = 0;
    for (final schedule in schedules) {
      final startMinutes = _timeToMinutes(schedule.startTime);
      final endMinutes = _timeToMinutes(schedule.endTime);
      totalHours += (endMinutes - startMinutes) / 60.0;
    }
    return totalHours;
  }

  /// Group schedules by day of week
  Map<int, int> _groupSchedulesByDay(List<Schedule> schedules) {
    final groupedSchedules = <int, int>{};
    for (final schedule in schedules) {
      groupedSchedules[schedule.dayOfWeek] = 
          (groupedSchedules[schedule.dayOfWeek] ?? 0) + 1;
    }
    return groupedSchedules;
  }

  /// Convert time string (HH:MM) to minutes since midnight
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  }
}
