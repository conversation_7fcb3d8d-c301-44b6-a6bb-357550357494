{"buildFiles": ["D:\\flutter-test\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\dalti-provider\\dalti_provider\\android\\app\\.cxx\\Debug\\485p1m4j\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\dalti-provider\\dalti_provider\\android\\app\\.cxx\\Debug\\485p1m4j\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}