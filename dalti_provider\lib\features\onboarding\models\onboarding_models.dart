// Onboarding models using existing model types
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/schedule_models.dart';
import '../../schedules/models/opening_hours_models.dart';
import '../../queues/models/queue_models.dart';

/// Represents the current step in the onboarding process
enum OnboardingStep {
  welcome,
  businessProfile,
  locationSetup,
  serviceCreation,
  queueManagement,
  summary,
  completed;

  /// Get total number of steps (excluding completed)
  static int get totalSteps => OnboardingStep.values.length - 1;

  /// Get step index (0-based, excluding completed)
  int get stepIndex {
    if (this == OnboardingStep.completed) return -1;
    return OnboardingStep.values.indexOf(this);
  }

  /// Get step title for display
  String get title {
    switch (this) {
      case OnboardingStep.welcome:
        return 'Welcome';
      case OnboardingStep.businessProfile:
        return 'Business Info';
      case OnboardingStep.locationSetup:
        return 'Location';
      case OnboardingStep.serviceCreation:
        return 'Services';
      case OnboardingStep.queueManagement:
        return 'Queues';
      case OnboardingStep.summary:
        return 'Summary';
      case OnboardingStep.completed:
        return 'Completed';
    }
  }

  /// Get step icon
  String get icon {
    switch (this) {
      case OnboardingStep.welcome:
        return '👋';
      case OnboardingStep.businessProfile:
        return '🏢';
      case OnboardingStep.locationSetup:
        return '📍';
      case OnboardingStep.serviceCreation:
        return '⚕️';
      case OnboardingStep.queueManagement:
        return '📋';
      case OnboardingStep.summary:
        return '✅';
      case OnboardingStep.completed:
        return '🎉';
    }
  }
}

/// Business profile information collected during onboarding
class BusinessProfile {
  final String businessName;
  final String description;
  final int categoryId;
  final String categoryName;
  final String? shortName;
  final String? logoUrl;
  final String? website;
  // Phone numbers - at least one is required
  final String? mobile;
  final String? landline;
  final String? fax;

  const BusinessProfile({
    required this.businessName,
    required this.description,
    required this.categoryId,
    required this.categoryName,
    this.shortName,
    this.logoUrl,
    this.website,
    this.mobile,
    this.landline,
    this.fax,
  });

  factory BusinessProfile.fromJson(Map<String, dynamic> json) {
    return BusinessProfile(
      businessName: json['businessName'] as String,
      description: json['description'] as String,
      categoryId: json['categoryId'] as int,
      categoryName: json['categoryName'] as String,
      shortName: json['shortName'] as String?,
      logoUrl: json['logoUrl'] as String?,
      website: json['website'] as String?,
      mobile: json['mobile'] as String?,
      landline: json['landline'] as String?,
      fax: json['fax'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'businessName': businessName,
      'description': description,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'shortName': shortName,
      'logoUrl': logoUrl,
      'website': website,
      'mobile': mobile,
      'landline': landline,
      'fax': fax,
    };
  }

  /// Check if at least one phone number is provided
  bool get hasPhoneNumber {
    return (mobile?.isNotEmpty ?? false) ||
        (landline?.isNotEmpty ?? false) ||
        (fax?.isNotEmpty ?? false);
  }
}

/// Onboarding progress and state
class OnboardingData {
  final OnboardingStep currentStep;
  final int stepIndex;
  final bool isCompleted;
  final BusinessProfile? businessProfile;
  final Location? primaryLocation;
  final List<Service> services;
  final List<Schedule> schedules;
  final List<Queue> queues;
  final List<QueueWithOpeningHours> queuesWithHours;
  final List<QueueWithLocationLink> queuesWithLocationLinks;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? lastUpdatedAt;

  const OnboardingData({
    this.currentStep = OnboardingStep.welcome,
    this.stepIndex = 0,
    this.isCompleted = false,
    this.businessProfile,
    this.primaryLocation,
    this.services = const [],
    this.schedules = const [],
    this.queues = const [],
    this.queuesWithHours = const [],
    this.queuesWithLocationLinks = const [],
    this.startedAt,
    this.completedAt,
    this.lastUpdatedAt,
  });

  OnboardingData copyWith({
    OnboardingStep? currentStep,
    int? stepIndex,
    bool? isCompleted,
    BusinessProfile? businessProfile,
    Location? primaryLocation,
    List<Service>? services,
    List<Schedule>? schedules,
    List<Queue>? queues,
    List<QueueWithOpeningHours>? queuesWithHours,
    List<QueueWithLocationLink>? queuesWithLocationLinks,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? lastUpdatedAt,
  }) {
    return OnboardingData(
      currentStep: currentStep ?? this.currentStep,
      stepIndex: stepIndex ?? this.stepIndex,
      isCompleted: isCompleted ?? this.isCompleted,
      businessProfile: businessProfile ?? this.businessProfile,
      primaryLocation: primaryLocation ?? this.primaryLocation,
      services: services ?? this.services,
      schedules: schedules ?? this.schedules,
      queues: queues ?? this.queues,
      queuesWithHours: queuesWithHours ?? this.queuesWithHours,
      queuesWithLocationLinks:
          queuesWithLocationLinks ?? this.queuesWithLocationLinks,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt,
    );
  }

  factory OnboardingData.fromJson(Map<String, dynamic> json) {
    return OnboardingData(
      currentStep: OnboardingStep.values.firstWhere(
        (e) => e.toString() == json['currentStep'],
        orElse: () => OnboardingStep.businessProfile,
      ),
      stepIndex: json['stepIndex'] as int? ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      businessProfile:
          json['businessProfile'] != null
              ? BusinessProfile.fromJson(json['businessProfile'])
              : null,
      primaryLocation:
          json['primaryLocation'] != null
              ? Location.fromJson(json['primaryLocation'])
              : null,
      services:
          (json['services'] as List<dynamic>?)
              ?.map((e) => Service.fromJson(e))
              .toList() ??
          [],
      schedules:
          (json['schedules'] as List<dynamic>?)
              ?.map((e) => Schedule.fromJson(e))
              .toList() ??
          [],
      queues:
          (json['queues'] as List<dynamic>?)
              ?.map((e) => Queue.fromJson(e))
              .toList() ??
          [],
      queuesWithHours:
          (json['queuesWithHours'] as List<dynamic>?)
              ?.map((e) => QueueWithOpeningHours.fromJson(e))
              .toList() ??
          [],
      queuesWithLocationLinks:
          (json['queuesWithLocationLinks'] as List<dynamic>?)
              ?.map((e) => QueueWithLocationLink.fromJson(e))
              .toList() ??
          [],
      startedAt:
          json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt:
          json['completedAt'] != null
              ? DateTime.parse(json['completedAt'])
              : null,
      lastUpdatedAt:
          json['lastUpdatedAt'] != null
              ? DateTime.parse(json['lastUpdatedAt'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentStep': currentStep.toString(),
      'stepIndex': stepIndex,
      'isCompleted': isCompleted,
      'businessProfile': businessProfile?.toJson(),
      'primaryLocation': primaryLocation?.toJson(),
      'services': services.map((e) => e.toJson()).toList(),
      'schedules': schedules.map((e) => e.toJson()).toList(),
      'queues': queues.map((e) => e.toJson()).toList(),
      'queuesWithHours': queuesWithHours.map((e) => e.toJson()).toList(),
      'queuesWithLocationLinks':
          queuesWithLocationLinks.map((e) => e.toJson()).toList(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'lastUpdatedAt': lastUpdatedAt?.toIso8601String(),
    };
  }
}

/// Enhanced OnboardingData with support for multiple locations and integrated opening hours
class EnhancedOnboardingData {
  final OnboardingStep currentStep;
  final int stepIndex;
  final bool isCompleted;
  final bool isSkipped; // Track if onboarding was skipped
  final BusinessProfile? businessProfile;
  final List<EnhancedLocation> locations; // Support multiple locations
  final List<Service> services;
  final List<QueueWithLocationLink>
  queuesWithLocationLinks; // Enhanced queues with location links
  final DateTime? startedAt;
  final DateTime? completedAt;
  final DateTime? skippedAt; // Track when onboarding was skipped
  final DateTime? lastUpdatedAt;

  const EnhancedOnboardingData({
    this.currentStep = OnboardingStep.welcome,
    this.stepIndex = 0,
    this.isCompleted = false,
    this.isSkipped = false,
    this.businessProfile,
    this.locations = const [],
    this.services = const [],
    this.queuesWithLocationLinks = const [],
    this.startedAt,
    this.completedAt,
    this.skippedAt,
    this.lastUpdatedAt,
  });

  /// Get primary location (first location or null)
  EnhancedLocation? get primaryLocation =>
      locations.isNotEmpty ? locations.first : null;

  /// Convert to legacy OnboardingData for backward compatibility
  OnboardingData toLegacyOnboardingData() {
    return OnboardingData(
      currentStep: currentStep,
      stepIndex: stepIndex,
      isCompleted: isCompleted,
      businessProfile: businessProfile,
      primaryLocation: primaryLocation?.toLegacyLocation(),
      services: services,
      schedules: _extractSchedulesFromLocations(),
      queues: _convertToLegacyQueues(),
      queuesWithHours: _convertToQueuesWithHours(),
      startedAt: startedAt,
      completedAt: completedAt,
      lastUpdatedAt: lastUpdatedAt,
    );
  }

  /// Extract schedules from locations for backward compatibility
  List<Schedule> _extractSchedulesFromLocations() {
    final schedules = <Schedule>[];
    for (int i = 0; i < locations.length; i++) {
      final location = locations[i];
      if (location.openingHours != null) {
        // Convert opening hours to legacy schedule format
        for (final dayHours in location.openingHours!.days) {
          if (dayHours.isActive && dayHours.hours.isNotEmpty) {
            final firstSlot = dayHours.hours.first;
            schedules.add(
              Schedule(
                id: i + 1, // Use location index + 1 as schedule ID
                locationId: location.id,
                dayOfWeek: _dayNameToNumber(dayHours.dayOfWeek),
                startTime: firstSlot.timeFrom,
                endTime: firstSlot.timeTo,
                isActive: true,
              ),
            );
          }
        }
      }
    }
    return schedules;
  }

  /// Convert day name to number for legacy Schedule model
  int _dayNameToNumber(String dayName) {
    const dayMap = {
      'Sunday': 0,
      'Monday': 1,
      'Tuesday': 2,
      'Wednesday': 3,
      'Thursday': 4,
      'Friday': 5,
      'Saturday': 6,
    };
    return dayMap[dayName] ?? 0;
  }

  /// Convert to legacy Queue objects
  List<Queue> _convertToLegacyQueues() {
    return queuesWithLocationLinks
        .map(
          (queue) => Queue(
            id: queue.id,
            title: queue.title,
            isActive: queue.isActive,
            sProvidingPlaceId: queue.locationId,
            services:
                queue.serviceIds
                    .map(
                      (serviceId) =>
                          QueueService(queueId: queue.id, serviceId: serviceId),
                    )
                    .toList(),
          ),
        )
        .toList();
  }

  /// Convert to QueueWithOpeningHours for backward compatibility
  List<QueueWithOpeningHours> _convertToQueuesWithHours() {
    return queuesWithLocationLinks.map((queue) {
      final openingHoursMap =
          queue.customOpeningHours?.toLegacyFormat() ?? <String, String>{};
      return QueueWithOpeningHours(
        title: queue.title,
        serviceIds: queue.serviceIds,
        openingHours: openingHoursMap,
        isActive: queue.isActive,
      );
    }).toList();
  }

  EnhancedOnboardingData copyWith({
    OnboardingStep? currentStep,
    int? stepIndex,
    bool? isCompleted,
    bool? isSkipped,
    BusinessProfile? businessProfile,
    List<EnhancedLocation>? locations,
    List<Service>? services,
    List<QueueWithLocationLink>? queuesWithLocationLinks,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? skippedAt,
    DateTime? lastUpdatedAt,
  }) {
    return EnhancedOnboardingData(
      currentStep: currentStep ?? this.currentStep,
      stepIndex: stepIndex ?? this.stepIndex,
      isCompleted: isCompleted ?? this.isCompleted,
      isSkipped: isSkipped ?? this.isSkipped,
      businessProfile: businessProfile ?? this.businessProfile,
      locations: locations ?? this.locations,
      services: services ?? this.services,
      queuesWithLocationLinks:
          queuesWithLocationLinks ?? this.queuesWithLocationLinks,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      skippedAt: skippedAt ?? this.skippedAt,
      lastUpdatedAt: lastUpdatedAt ?? this.lastUpdatedAt,
    );
  }

  /// Check if onboarding can be completed
  bool get canComplete {
    return businessProfile != null &&
        locations.isNotEmpty &&
        services.isNotEmpty &&
        queuesWithLocationLinks.isNotEmpty;
  }

  /// Check if a specific step is completed
  bool isStepCompleted(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.welcome:
        return true; // Welcome step is always completed once viewed
      case OnboardingStep.businessProfile:
        return businessProfile != null;
      case OnboardingStep.locationSetup:
        return locations.isNotEmpty;
      case OnboardingStep.serviceCreation:
        return services.isNotEmpty;
      case OnboardingStep.queueManagement:
        return queuesWithLocationLinks.isNotEmpty;
      case OnboardingStep.summary:
        return isCompleted;
      case OnboardingStep.completed:
        return isCompleted;
    }
  }
}

/// Enhanced Queue model with location linking for onboarding
class QueueWithLocationLink {
  final int id;
  final String title;
  final int locationId; // Links to specific location
  final List<int> serviceIds;
  final OpeningHours? customOpeningHours; // null means inherit from location
  final bool isActive;

  const QueueWithLocationLink({
    required this.id,
    required this.title,
    required this.locationId,
    required this.serviceIds,
    this.customOpeningHours,
    this.isActive = true,
  });

  /// Check if queue inherits opening hours from location
  bool get inheritsOpeningHours => customOpeningHours == null;

  factory QueueWithLocationLink.fromJson(Map<String, dynamic> json) {
    return QueueWithLocationLink(
      id: json['id'] as int,
      title: json['title'] as String,
      locationId: json['locationId'] as int,
      serviceIds: (json['serviceIds'] as List<dynamic>).cast<int>(),
      customOpeningHours:
          json['customOpeningHours'] != null
              ? OpeningHours.fromJson(json['customOpeningHours'])
              : null,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'locationId': locationId,
      'serviceIds': serviceIds,
      'customOpeningHours': customOpeningHours?.toJson(),
      'isActive': isActive,
    };
  }

  QueueWithLocationLink copyWith({
    int? id,
    String? title,
    int? locationId,
    List<int>? serviceIds,
    OpeningHours? customOpeningHours,
    bool? isActive,
  }) {
    return QueueWithLocationLink(
      id: id ?? this.id,
      title: title ?? this.title,
      locationId: locationId ?? this.locationId,
      serviceIds: serviceIds ?? this.serviceIds,
      customOpeningHours: customOpeningHours ?? this.customOpeningHours,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// Onboarding state for UI management
class OnboardingState {
  final bool isLoading;
  final bool isSaving;
  final OnboardingData? data;
  final String? error;
  final bool canProceed;
  final bool canGoBack;

  const OnboardingState({
    this.isLoading = false,
    this.isSaving = false,
    this.data,
    this.error,
    this.canProceed = false,
    this.canGoBack = false,
  });

  OnboardingState copyWith({
    bool? isLoading,
    bool? isSaving,
    OnboardingData? data,
    String? error,
    bool? canProceed,
    bool? canGoBack,
  }) {
    return OnboardingState(
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      data: data ?? this.data,
      error: error ?? this.error,
      canProceed: canProceed ?? this.canProceed,
      canGoBack: canGoBack ?? this.canGoBack,
    );
  }
}

/// Step validation result
class StepValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const StepValidationResult({
    this.isValid = false,
    this.errors = const [],
    this.warnings = const [],
  });
}

/// Onboarding completion summary
class OnboardingCompletion {
  final BusinessProfile businessProfile;
  final Location primaryLocation;
  final List<Service> services;
  final List<Schedule> schedules;
  final List<Queue> queues;
  final DateTime completedAt;
  final int totalSteps;
  final Duration totalTime;

  const OnboardingCompletion({
    required this.businessProfile,
    required this.primaryLocation,
    required this.services,
    required this.schedules,
    required this.queues,
    required this.completedAt,
    this.totalSteps = 0,
    this.totalTime = Duration.zero,
  });

  factory OnboardingCompletion.fromJson(Map<String, dynamic> json) {
    return OnboardingCompletion(
      businessProfile: BusinessProfile.fromJson(json['businessProfile']),
      primaryLocation: Location.fromJson(json['primaryLocation']),
      services:
          (json['services'] as List<dynamic>)
              .map((e) => Service.fromJson(e))
              .toList(),
      schedules:
          (json['schedules'] as List<dynamic>)
              .map((e) => Schedule.fromJson(e))
              .toList(),
      queues:
          (json['queues'] as List<dynamic>)
              .map((e) => Queue.fromJson(e))
              .toList(),
      completedAt: DateTime.parse(json['completedAt']),
      totalSteps: json['totalSteps'] as int? ?? 0,
      totalTime: Duration(milliseconds: json['totalTime'] as int? ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'businessProfile': businessProfile.toJson(),
      'primaryLocation': primaryLocation.toJson(),
      'services': services.map((e) => e.toJson()).toList(),
      'schedules': schedules.map((e) => e.toJson()).toList(),
      'queues': queues.map((e) => e.toJson()).toList(),
      'completedAt': completedAt.toIso8601String(),
      'totalSteps': totalSteps,
      'totalTime': totalTime.inMilliseconds,
    };
  }
}

/// Extension methods for OnboardingStep
extension OnboardingStepExtension on OnboardingStep {
  /// Get the display title for the step
  String get title {
    switch (this) {
      case OnboardingStep.welcome:
        return 'Welcome';
      case OnboardingStep.businessProfile:
        return 'Business Profile';
      case OnboardingStep.locationSetup:
        return 'Location Setup';
      case OnboardingStep.serviceCreation:
        return 'Services';
      case OnboardingStep.queueManagement:
        return 'Queue Setup';
      case OnboardingStep.summary:
        return 'Summary';
      case OnboardingStep.completed:
        return 'Completed';
    }
  }

  /// Get the description for the step
  String get description {
    switch (this) {
      case OnboardingStep.welcome:
        return 'Welcome to Dalti Provider';
      case OnboardingStep.businessProfile:
        return 'Tell us about your business';
      case OnboardingStep.locationSetup:
        return 'Add your business location';
      case OnboardingStep.serviceCreation:
        return 'Define your services';
      case OnboardingStep.queueManagement:
        return 'Configure your queues';
      case OnboardingStep.summary:
        return 'Review your setup';
      case OnboardingStep.completed:
        return 'Setup complete!';
    }
  }

  /// Get the icon for the step
  String get iconName {
    switch (this) {
      case OnboardingStep.welcome:
        return 'waving_hand';
      case OnboardingStep.businessProfile:
        return 'business';
      case OnboardingStep.locationSetup:
        return 'location_on';
      case OnboardingStep.serviceCreation:
        return 'room_service';
      case OnboardingStep.queueManagement:
        return 'queue';
      case OnboardingStep.summary:
        return 'summarize';
      case OnboardingStep.completed:
        return 'check_circle';
    }
  }

  /// Check if this is the first step
  bool get isFirst => this == OnboardingStep.businessProfile;

  /// Check if this is the last step
  bool get isLast => this == OnboardingStep.completed;

  /// Get the next step
  OnboardingStep? get next {
    final steps = OnboardingStep.values;
    final currentIndex = steps.indexOf(this);
    if (currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
    return null;
  }

  /// Get the previous step
  OnboardingStep? get previous {
    final steps = OnboardingStep.values;
    final currentIndex = steps.indexOf(this);
    if (currentIndex > 0) {
      return steps[currentIndex - 1];
    }
    return null;
  }

  /// Get step index (0-based)
  int get index => OnboardingStep.values.indexOf(this);
}

/// Queue with opening hours for onboarding
class QueueWithOpeningHours {
  final String title;
  final List<int> serviceIds;
  final Map<String, String> openingHours; // day_open/day_close format
  final bool isActive;

  const QueueWithOpeningHours({
    required this.title,
    required this.serviceIds,
    required this.openingHours,
    this.isActive = true,
  });

  factory QueueWithOpeningHours.fromJson(Map<String, dynamic> json) {
    return QueueWithOpeningHours(
      title: json['title'] as String,
      serviceIds: (json['serviceIds'] as List<dynamic>).cast<int>(),
      openingHours: Map<String, String>.from(json['openingHours']),
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'serviceIds': serviceIds,
      'openingHours': openingHours,
      'isActive': isActive,
    };
  }
}

/// Extension methods for OnboardingData
extension OnboardingDataExtension on OnboardingData {
  /// Calculate completion percentage
  double get completionPercentage {
    if (isCompleted) return 1.0;
    return stepIndex / OnboardingStep.totalSteps;
  }

  /// Check if a specific step is completed
  bool isStepCompleted(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.welcome:
        return true; // Welcome step is always completed once viewed
      case OnboardingStep.businessProfile:
        return businessProfile != null;
      case OnboardingStep.locationSetup:
        return primaryLocation != null;
      case OnboardingStep.serviceCreation:
        return services.isNotEmpty;
      case OnboardingStep.queueManagement:
        return queuesWithHours.isNotEmpty;
      case OnboardingStep.summary:
        return isCompleted;
      case OnboardingStep.completed:
        return isCompleted;
    }
  }

  /// Get the next incomplete step
  OnboardingStep? get nextIncompleteStep {
    for (final step in OnboardingStep.values) {
      if (step == OnboardingStep.completed) break;
      if (!isStepCompleted(step)) {
        return step;
      }
    }
    return null;
  }

  /// Check if onboarding can be completed
  bool get canComplete {
    return businessProfile != null &&
        primaryLocation != null &&
        services.isNotEmpty &&
        queuesWithHours.isNotEmpty;
  }

  /// Get duration since started
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }
}
