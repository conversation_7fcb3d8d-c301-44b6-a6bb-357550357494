# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# TaskMaster AI files
.taskmaster/state.json
.taskmaster/reports/
.taskmaster/docs/research/

# Chrome/Browser debugging files
.dart_tool/chrome-device/

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
*.log

# Dependency directories
node_modules/

# Environment variables
.env
.env.local
.env.development
.env.production

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.cache

# Flutter specific
*.g.dart
*.freezed.dart
*.mocks.dart

# Coverage reports
coverage/
lcov.info

# Generated files
*.generated.dart