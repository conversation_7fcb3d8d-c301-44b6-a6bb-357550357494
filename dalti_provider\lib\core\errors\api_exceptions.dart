import 'package:dio/dio.dart';

/// Base class for all API exceptions
abstract class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final dynamic data;

  const ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
    this.data,
  });

  @override
  String toString() => 'ApiException: $message';
}

/// Network-related exceptions
class NetworkException extends ApiException {
  const NetworkException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Authentication-related exceptions
class AuthException extends ApiException {
  const AuthException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Quota/Limit exceeded exceptions (usually 403 with specific message)
class QuotaExceededException extends ApiException {
  const QuotaExceededException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Validation-related exceptions (API returns 500 for these)
class ValidationException extends ApiException {
  final Map<String, List<String>>? fieldErrors;

  const ValidationException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
    this.fieldErrors,
  });
}

/// Server-related exceptions
class ServerException extends ApiException {
  const ServerException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Client-related exceptions (4xx errors)
class ClientException extends ApiException {
  const ClientException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Timeout exceptions
class TimeoutException extends ApiException {
  const TimeoutException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Unknown exceptions
class UnknownException extends ApiException {
  const UnknownException({
    required super.message,
    super.statusCode,
    super.errorCode,
    super.data,
  });
}

/// Utility class to convert DioException to ApiException
class ApiExceptionHandler {
  static ApiException handleDioException(DioException dioException) {
    final statusCode = dioException.response?.statusCode;
    final data = dioException.response?.data;

    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return TimeoutException(
          message: 'Request timeout. Please check your internet connection.',
          statusCode: statusCode,
          data: data,
        );

      case DioExceptionType.connectionError:
        return NetworkException(
          message: 'Network error. Please check your internet connection.',
          statusCode: statusCode,
          data: data,
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(dioException);

      case DioExceptionType.cancel:
        return NetworkException(
          message: 'Request was cancelled.',
          statusCode: statusCode,
          data: data,
        );

      case DioExceptionType.unknown:
        return UnknownException(
          message: 'An unknown error occurred.',
          statusCode: statusCode,
          data: data,
        );

      default:
        return UnknownException(
          message: 'An unexpected error occurred.',
          statusCode: statusCode,
          data: data,
        );
    }
  }

  static ApiException _handleBadResponse(DioException dioException) {
    final statusCode = dioException.response?.statusCode ?? 0;
    final data = dioException.response?.data;
    final message = _extractErrorMessage(data) ?? 'An error occurred';

    // Handle API's quirky behavior
    switch (statusCode) {
      case 400:
        // API returns 400 for auth errors (should be 401)
        if (_isAuthError(data)) {
          return AuthException(
            message: message,
            statusCode: statusCode,
            data: data,
          );
        }
        return ClientException(
          message: message,
          statusCode: statusCode,
          data: data,
        );

      case 401:
        return AuthException(
          message: message,
          statusCode: statusCode,
          data: data,
        );

      case 403:
        // Check if this is a quota/limit exceeded error
        if (_isQuotaExceededError(data)) {
          return QuotaExceededException(
            message: message,
            statusCode: statusCode,
            data: data,
          );
        }
        return AuthException(
          message:
              message.isNotEmpty
                  ? message
                  : 'Access forbidden. You don\'t have permission to access this resource.',
          statusCode: statusCode,
          data: data,
        );

      case 404:
        return ClientException(
          message: 'Resource not found.',
          statusCode: statusCode,
          data: data,
        );

      case 422:
        return ValidationException(
          message: message,
          statusCode: statusCode,
          data: data,
          fieldErrors: _extractFieldErrors(data),
        );

      case 500:
        // API returns 500 for validation errors (should be 400/422)
        if (_isValidationError(data)) {
          return ValidationException(
            message: message,
            statusCode: statusCode,
            data: data,
            fieldErrors: _extractFieldErrors(data),
          );
        }
        return ServerException(
          message: 'Internal server error. Please try again later.',
          statusCode: statusCode,
          data: data,
        );

      case 502:
      case 503:
      case 504:
        return ServerException(
          message: 'Server is temporarily unavailable. Please try again later.',
          statusCode: statusCode,
          data: data,
        );

      default:
        if (statusCode >= 400 && statusCode < 500) {
          return ClientException(
            message: message,
            statusCode: statusCode,
            data: data,
          );
        } else if (statusCode >= 500) {
          return ServerException(
            message: message,
            statusCode: statusCode,
            data: data,
          );
        }
        return UnknownException(
          message: message,
          statusCode: statusCode,
          data: data,
        );
    }
  }

  static String? _extractErrorMessage(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data['message'] ?? data['error'] ?? data['detail'];
    }
    if (data is String) {
      return data;
    }
    return null;
  }

  static Map<String, List<String>>? _extractFieldErrors(dynamic data) {
    if (data is Map<String, dynamic>) {
      final errors = data['errors'] ?? data['field_errors'];
      if (errors is Map<String, dynamic>) {
        return errors.map((key, value) {
          if (value is List) {
            return MapEntry(key, value.cast<String>());
          } else if (value is String) {
            return MapEntry(key, [value]);
          }
          return MapEntry(key, [value.toString()]);
        });
      }
    }
    return null;
  }

  static bool _isAuthError(dynamic data) {
    if (data is Map<String, dynamic>) {
      final message = data['message']?.toString().toLowerCase() ?? '';
      return message.contains('unauthorized') ||
          message.contains('invalid token') ||
          message.contains('authentication');
    }
    return false;
  }

  static bool _isValidationError(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data.containsKey('errors') ||
          data.containsKey('field_errors') ||
          data.containsKey('validation_errors');
    }
    return false;
  }

  static bool _isQuotaExceededError(dynamic data) {
    if (data is Map<String, dynamic>) {
      final message = data['message']?.toString().toLowerCase() ?? '';
      final isQuotaError =
          message.contains('limit reached') ||
          message.contains('quota exceeded') ||
          message.contains('upgrade your subscription') ||
          message.contains('maximum number') ||
          message.contains('can create up to');

      print(
        '[ApiExceptionHandler] Checking quota error: message="$message", isQuotaError=$isQuotaError',
      );
      return isQuotaError;
    }
    return false;
  }
}
