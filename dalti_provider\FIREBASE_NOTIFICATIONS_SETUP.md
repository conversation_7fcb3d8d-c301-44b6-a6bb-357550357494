# Firebase Notifications Setup Guide

This guide explains how Firebase Cloud Messaging (FCM) notifications are set up in the Dalti Provider app.

## What's Already Configured

### 1. Dependencies
The following packages are already added to `pubspec.yaml`:
- `firebase_core: ^3.15.0` - Core Firebase functionality
- `firebase_messaging: ^15.1.4` - Firebase Cloud Messaging
- `flutter_local_notifications: ^17.2.1` - Local notifications for foreground messages

### 2. Android Configuration
The Android app is already configured with:
- Firebase project connection via `google-services.json`
- FCM service declaration in `AndroidManifest.xml`
- Default notification icon and channel configuration
- Firebase BOM and messaging dependencies in `build.gradle.kts`

### 3. Firebase Options
The `firebase_options.dart` file contains configuration for all platforms (Android, iOS, Web, etc.)

### 4. Services Created
- `FirebaseMessagingService` - Core FCM functionality
- `NotificationService` - High-level notification management
- `NotificationProvider` - Riverpod providers for state management

## How It Works

### 1. Initialization
Firebase and notifications are initialized in `main.dart`:
```dart
// Initialize Firebase
await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

// Set up background message handler
FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

// Initialize Firebase Messaging
await FirebaseMessagingService.initialize();
await NotificationService.initialize();
```

### 2. Authentication Integration
When a user logs in, the app automatically:
- Subscribes to provider-specific notification topics
- Registers the device token with the backend (TODO: implement API endpoint)

When a user logs out, the app:
- Unsubscribes from all notification topics
- Unregisters the device token from the backend

### 3. Message Handling
The app handles three types of message states:
- **Foreground**: Shows local notification when app is open
- **Background**: Handled by system, opens app when tapped
- **Terminated**: Handled by system, opens app when tapped

### 4. Navigation
When a notification is tapped, the app can navigate to specific screens based on the message data:
- `type: "appointment"` → Navigate to appointment details
- `type: "message"` → Navigate to conversation
- `type: "booking"` → Navigate to booking details
- Default → Navigate to home screen

## Testing Notifications

### 1. Using the Test Screen
A debug screen is available at `lib/features/debug/screens/notification_test_screen.dart` that shows:
- Notification permission status
- Device FCM token (can be copied)
- Currently subscribed topics
- Ability to subscribe/unsubscribe from test topics

### 2. Sending Test Notifications
You can send test notifications using:

#### Firebase Console
1. Go to Firebase Console → Cloud Messaging
2. Click "Send your first message"
3. Enter title and text
4. Select target (topic or device token)
5. Send the message

#### Using curl (for topic-based notifications)
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "/topics/test",
    "notification": {
      "title": "Test Notification",
      "body": "This is a test message"
    },
    "data": {
      "type": "test",
      "click_action": "FLUTTER_NOTIFICATION_CLICK"
    }
  }'
```

#### Using curl (for device-specific notifications)
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "DEVICE_TOKEN_HERE",
    "notification": {
      "title": "Test Notification",
      "body": "This is a test message"
    },
    "data": {
      "type": "appointment",
      "appointmentId": "123"
    }
  }'
```

## Backend Integration (TODO)

### 1. Device Token Registration
The app is configured to use this API endpoint for FCM token management:

```
POST /api/auth/notifications/mobile/save-fcm-token
{
  "userId": "string",
  "fcmToken": "string",
  "platform": "android|ios"
}
```

You may also want to implement a removal endpoint:

```
DELETE /api/auth/notifications/mobile/remove-fcm-token
{
  "userId": "string",
  "fcmToken": "string",
  "platform": "android|ios"
}
```

### 2. Sending Notifications
Your backend should send notifications for:
- New appointment bookings
- Appointment confirmations/cancellations
- New messages
- Schedule changes
- System announcements

### 3. Topic Structure
The app subscribes to these topics:
- `provider_{providerId}` - Provider-specific notifications
- `appointments` - General appointment notifications
- `bookings` - Booking-related notifications
- `messages` - Message notifications
- `general` - System-wide announcements

## Troubleshooting

### 1. Notifications Not Received
- Check if permissions are granted
- Verify device token is valid
- Ensure app is subscribed to the correct topics
- Check Firebase Console logs

### 2. App Not Opening on Notification Tap
- Verify `click_action` is set to `FLUTTER_NOTIFICATION_CLICK`
- Check if navigation logic is implemented correctly
- Ensure app handles initial message when opened from terminated state

### 3. iOS Setup (When Ready)
For iOS, you'll need to:
1. Add `GoogleService-Info.plist` to the iOS project
2. Configure APNs certificates in Firebase Console
3. Add notification capabilities to iOS project
4. Handle iOS-specific notification permissions

## Security Notes

1. **Server Key**: Keep your Firebase server key secure and never expose it in client code
2. **Topic Security**: Use Firebase Security Rules to control who can subscribe to topics
3. **Data Validation**: Always validate notification data on the client side
4. **User Consent**: Ensure users consent to receiving notifications

## Next Steps

1. Implement backend API endpoints for device token management
2. Add notification sending logic to your backend for relevant events
3. Test notifications thoroughly on different devices and scenarios
4. Set up iOS configuration when ready
5. Implement analytics to track notification engagement
