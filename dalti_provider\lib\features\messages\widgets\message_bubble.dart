import 'package:flutter/material.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/message_models.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isFromProvider;
  final bool showSenderName;
  final bool showTimestamp;
  final VoidCallback? onTap;

  const MessageBubble({
    super.key,
    required this.message,
    required this.isFromProvider,
    this.showSenderName = true,
    this.showTimestamp = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Column(
        crossAxisAlignment:
            isFromProvider ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Sender name
          if (showSenderName && !isFromProvider)
            Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text(
                message.senderName,
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

          // Message bubble
          Row(
            mainAxisAlignment:
                isFromProvider
                    ? MainAxisAlignment.end
                    : MainAxisAlignment.start,
            children: [
              if (!isFromProvider) const SizedBox(width: 8),

              Flexible(
                child: GestureDetector(
                  onTap: onTap,
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width * 0.75,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isFromProvider
                              ? context.colors.primary
                              : context.colors.surfaceVariant,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(16),
                        topRight: const Radius.circular(16),
                        bottomLeft: Radius.circular(isFromProvider ? 16 : 4),
                        bottomRight: Radius.circular(isFromProvider ? 4 : 16),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reply indicator
                        if (message.replyToMessageId != null)
                          _buildReplyIndicator(context),

                        // Message content
                        _buildMessageContent(context),

                        // Attachments (not implemented in simple models)
                        // if (message.attachments?.isNotEmpty == true)
                        //   _buildAttachments(context),

                        // Message info (timestamp and status)
                        if (showTimestamp || isFromProvider)
                          _buildMessageInfo(context),
                      ],
                    ),
                  ),
                ),
              ),

              if (isFromProvider) const SizedBox(width: 8),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReplyIndicator(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color:
            isFromProvider
                ? context.colors.onPrimary.withOpacity(0.1)
                : context.colors.onSurfaceVariant.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.reply,
            size: 16,
            color:
                isFromProvider
                    ? context.colors.onPrimary
                    : context.colors.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            'Replying to message',
            style: context.textTheme.bodySmall?.copyWith(
              color:
                  isFromProvider
                      ? context.colors.onPrimary
                      : context.colors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    switch (message.type) {
      case MessageType.text:
        return Text(
          message.content,
          style: context.textTheme.bodyMedium?.copyWith(
            color:
                isFromProvider
                    ? context.colors.onPrimary
                    : context.colors.onSurface,
          ),
        );

      case MessageType.image:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: context.colors.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.image, size: 48),
            ),
            if (message.content.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                message.content,
                style: context.textTheme.bodyMedium?.copyWith(
                  color:
                      isFromProvider
                          ? context.colors.onPrimary
                          : context.colors.onSurface,
                ),
              ),
            ],
          ],
        );

      case MessageType.file:
        return Row(
          children: [
            Icon(
              Icons.attach_file,
              color:
                  isFromProvider
                      ? context.colors.onPrimary
                      : context.colors.onSurface,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message.content.isNotEmpty
                    ? message.content
                    : 'File attachment',
                style: context.textTheme.bodyMedium?.copyWith(
                  color:
                      isFromProvider
                          ? context.colors.onPrimary
                          : context.colors.onSurface,
                ),
              ),
            ),
          ],
        );

      case MessageType.audio:
        return Row(
          children: [
            Icon(
              Icons.audiotrack,
              color:
                  isFromProvider
                      ? context.colors.onPrimary
                      : context.colors.onSurface,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message.content.isNotEmpty ? message.content : 'Audio message',
                style: context.textTheme.bodyMedium?.copyWith(
                  color:
                      isFromProvider
                          ? context.colors.onPrimary
                          : context.colors.onSurface,
                ),
              ),
            ),
          ],
        );

      case MessageType.other:
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.colors.surfaceVariant.withOpacity(0.5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: context.colors.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message.content,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        );

      case MessageType.video:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.videocam, color: context.colors.onPrimaryContainer),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  message.content,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onPrimaryContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
    }
  }

  Widget _buildAttachments(BuildContext context) {
    // Attachments not implemented in simple models
    return const SizedBox.shrink();
  }

  Widget _buildMessageInfo(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showTimestamp)
            Text(
              _formatTimestamp(message.timestamp),
              style: context.textTheme.bodySmall?.copyWith(
                color:
                    isFromProvider
                        ? context.colors.onPrimary.withOpacity(0.7)
                        : context.colors.onSurfaceVariant,
                fontSize: 11,
              ),
            ),

          if (isFromProvider) ...[
            const SizedBox(width: 4),
            _buildMessageStatusIcon(context),
          ],
        ],
      ),
    );
  }

  Widget _buildMessageStatusIcon(BuildContext context) {
    IconData icon;
    Color color = context.colors.onPrimary.withOpacity(0.7);

    switch (message.status) {
      case MessageStatus.sent:
        icon = Icons.check;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = context.colors.onPrimary;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = context.colors.error;
        break;
    }

    return Icon(icon, size: 12, color: color);
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inMinutes > 0) {
      return '${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      return 'now';
    }
  }
}
