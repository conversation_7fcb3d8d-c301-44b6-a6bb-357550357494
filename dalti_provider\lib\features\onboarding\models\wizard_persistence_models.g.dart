// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'wizard_persistence_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PersistedWizardStateImpl _$$PersistedWizardStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PersistedWizardStateImpl(
      version: (json['version'] as num?)?.toInt() ?? 1,
      savedAt: DateTime.parse(json['savedAt'] as String),
      startedAt: DateTime.parse(json['startedAt'] as String),
      currentStep: $enumDecode(_$OnboardingStepEnumMap, json['currentStep']),
      currentStepIndex: (json['currentStepIndex'] as num).toInt(),
      stepCompletionStatus:
          (json['stepCompletionStatus'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$OnboardingStepEnumMap, k), e as bool),
              ) ??
              const {},
      onboardingData: json['onboardingData'] == null
          ? null
          : OnboardingData.fromJson(
              json['onboardingData'] as Map<String, dynamic>),
      navigationData: json['navigationData'] == null
          ? null
          : WizardNavigationData.fromJson(
              json['navigationData'] as Map<String, dynamic>),
      sessionInfo: json['sessionInfo'] == null
          ? null
          : SessionInfo.fromJson(json['sessionInfo'] as Map<String, dynamic>),
      autoSaveSettings: json['autoSaveSettings'] == null
          ? const AutoSaveSettings()
          : AutoSaveSettings.fromJson(
              json['autoSaveSettings'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$PersistedWizardStateImplToJson(
        _$PersistedWizardStateImpl instance) =>
    <String, dynamic>{
      'version': instance.version,
      'savedAt': instance.savedAt.toIso8601String(),
      'startedAt': instance.startedAt.toIso8601String(),
      'currentStep': _$OnboardingStepEnumMap[instance.currentStep]!,
      'currentStepIndex': instance.currentStepIndex,
      'stepCompletionStatus': instance.stepCompletionStatus
          .map((k, e) => MapEntry(_$OnboardingStepEnumMap[k]!, e)),
      if (instance.onboardingData?.toJson() case final value?)
        'onboardingData': value,
      if (instance.navigationData?.toJson() case final value?)
        'navigationData': value,
      if (instance.sessionInfo?.toJson() case final value?)
        'sessionInfo': value,
      'autoSaveSettings': instance.autoSaveSettings.toJson(),
    };

const _$OnboardingStepEnumMap = {
  OnboardingStep.welcome: 'welcome',
  OnboardingStep.businessProfile: 'businessProfile',
  OnboardingStep.locationSetup: 'locationSetup',
  OnboardingStep.serviceCreation: 'serviceCreation',
  OnboardingStep.queueManagement: 'queueManagement',
  OnboardingStep.summary: 'summary',
  OnboardingStep.completed: 'completed',
};

_$WizardNavigationDataImpl _$$WizardNavigationDataImplFromJson(
        Map<String, dynamic> json) =>
    _$WizardNavigationDataImpl(
      currentStep: $enumDecode(_$OnboardingStepEnumMap, json['currentStep']),
      currentStepIndex: (json['currentStepIndex'] as num).toInt(),
      canGoNext: json['canGoNext'] as bool,
      canGoPrevious: json['canGoPrevious'] as bool,
      isLoading: json['isLoading'] as bool? ?? false,
      error: json['error'] as String?,
      stepCompletionStatus:
          (json['stepCompletionStatus'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$OnboardingStepEnumMap, k), e as bool),
              ) ??
              const {},
    );

Map<String, dynamic> _$$WizardNavigationDataImplToJson(
        _$WizardNavigationDataImpl instance) =>
    <String, dynamic>{
      'currentStep': _$OnboardingStepEnumMap[instance.currentStep]!,
      'currentStepIndex': instance.currentStepIndex,
      'canGoNext': instance.canGoNext,
      'canGoPrevious': instance.canGoPrevious,
      'isLoading': instance.isLoading,
      if (instance.error case final value?) 'error': value,
      'stepCompletionStatus': instance.stepCompletionStatus
          .map((k, e) => MapEntry(_$OnboardingStepEnumMap[k]!, e)),
    };

_$SessionInfoImpl _$$SessionInfoImplFromJson(Map<String, dynamic> json) =>
    _$SessionInfoImpl(
      sessionId: json['sessionId'] as String,
      userId: json['userId'] as String?,
      deviceInfo: json['deviceInfo'] as String?,
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
      isGuest: json['isGuest'] as bool? ?? false,
      lastActivity: json['lastActivity'] == null
          ? null
          : DateTime.parse(json['lastActivity'] as String),
    );

Map<String, dynamic> _$$SessionInfoImplToJson(_$SessionInfoImpl instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      if (instance.userId case final value?) 'userId': value,
      if (instance.deviceInfo case final value?) 'deviceInfo': value,
      if (instance.expiresAt?.toIso8601String() case final value?)
        'expiresAt': value,
      'isGuest': instance.isGuest,
      if (instance.lastActivity?.toIso8601String() case final value?)
        'lastActivity': value,
    };

_$AutoSaveSettingsImpl _$$AutoSaveSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$AutoSaveSettingsImpl(
      enabled: json['enabled'] as bool? ?? true,
      debounceDelayMs: (json['debounceDelayMs'] as num?)?.toInt() ?? 2000,
      saveOnStepChange: json['saveOnStepChange'] as bool? ?? true,
      saveOnFieldChange: json['saveOnFieldChange'] as bool? ?? true,
      saveOnStepComplete: json['saveOnStepComplete'] as bool? ?? true,
      maxRetries: (json['maxRetries'] as num?)?.toInt() ?? 3,
    );

Map<String, dynamic> _$$AutoSaveSettingsImplToJson(
        _$AutoSaveSettingsImpl instance) =>
    <String, dynamic>{
      'enabled': instance.enabled,
      'debounceDelayMs': instance.debounceDelayMs,
      'saveOnStepChange': instance.saveOnStepChange,
      'saveOnFieldChange': instance.saveOnFieldChange,
      'saveOnStepComplete': instance.saveOnStepComplete,
      'maxRetries': instance.maxRetries,
    };

_$PersistenceResultImpl _$$PersistenceResultImplFromJson(
        Map<String, dynamic> json) =>
    _$PersistenceResultImpl(
      success: json['success'] as bool,
      error: json['error'] as String?,
      savedAt: json['savedAt'] == null
          ? null
          : DateTime.parse(json['savedAt'] as String),
      dataSize: (json['dataSize'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$PersistenceResultImplToJson(
        _$PersistenceResultImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      if (instance.error case final value?) 'error': value,
      if (instance.savedAt?.toIso8601String() case final value?)
        'savedAt': value,
      if (instance.dataSize case final value?) 'dataSize': value,
    };

_$RecoveryOptionsImpl _$$RecoveryOptionsImplFromJson(
        Map<String, dynamic> json) =>
    _$RecoveryOptionsImpl(
      showRecoveryDialog: json['showRecoveryDialog'] as bool? ?? true,
      autoRestore: json['autoRestore'] as bool? ?? false,
      maxRecoveryAgeHours: (json['maxRecoveryAgeHours'] as num?)?.toInt() ?? 24,
      validateIntegrity: json['validateIntegrity'] as bool? ?? true,
      enableMigration: json['enableMigration'] as bool? ?? true,
    );

Map<String, dynamic> _$$RecoveryOptionsImplToJson(
        _$RecoveryOptionsImpl instance) =>
    <String, dynamic>{
      'showRecoveryDialog': instance.showRecoveryDialog,
      'autoRestore': instance.autoRestore,
      'maxRecoveryAgeHours': instance.maxRecoveryAgeHours,
      'validateIntegrity': instance.validateIntegrity,
      'enableMigration': instance.enableMigration,
    };

_$StorageConfigImpl _$$StorageConfigImplFromJson(Map<String, dynamic> json) =>
    _$StorageConfigImpl(
      keyPrefix: json['keyPrefix'] as String? ?? 'dalti_wizard',
      version: (json['version'] as num?)?.toInt() ?? 1,
      enableCompression: json['enableCompression'] as bool? ?? false,
      enableEncryption: json['enableEncryption'] as bool? ?? false,
      maxStorageSize: (json['maxStorageSize'] as num?)?.toInt() ?? 5242880,
      cleanupAfterDays: (json['cleanupAfterDays'] as num?)?.toInt() ?? 30,
    );

Map<String, dynamic> _$$StorageConfigImplToJson(_$StorageConfigImpl instance) =>
    <String, dynamic>{
      'keyPrefix': instance.keyPrefix,
      'version': instance.version,
      'enableCompression': instance.enableCompression,
      'enableEncryption': instance.enableEncryption,
      'maxStorageSize': instance.maxStorageSize,
      'cleanupAfterDays': instance.cleanupAfterDays,
    };

_$MigrationInfoImpl _$$MigrationInfoImplFromJson(Map<String, dynamic> json) =>
    _$MigrationInfoImpl(
      fromVersion: (json['fromVersion'] as num).toInt(),
      toVersion: (json['toVersion'] as num).toInt(),
      migratedAt: DateTime.parse(json['migratedAt'] as String),
      changes: (json['changes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      dataLoss: json['dataLoss'] as bool?,
    );

Map<String, dynamic> _$$MigrationInfoImplToJson(_$MigrationInfoImpl instance) =>
    <String, dynamic>{
      'fromVersion': instance.fromVersion,
      'toVersion': instance.toVersion,
      'migratedAt': instance.migratedAt.toIso8601String(),
      'changes': instance.changes,
      if (instance.dataLoss case final value?) 'dataLoss': value,
    };

_$StorageStatsImpl _$$StorageStatsImplFromJson(Map<String, dynamic> json) =>
    _$StorageStatsImpl(
      totalSize: (json['totalSize'] as num).toInt(),
      itemCount: (json['itemCount'] as num).toInt(),
      lastSaved: json['lastSaved'] == null
          ? null
          : DateTime.parse(json['lastSaved'] as String),
      lastCleanup: json['lastCleanup'] == null
          ? null
          : DateTime.parse(json['lastCleanup'] as String),
      keys:
          (json['keys'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
    );

Map<String, dynamic> _$$StorageStatsImplToJson(_$StorageStatsImpl instance) =>
    <String, dynamic>{
      'totalSize': instance.totalSize,
      'itemCount': instance.itemCount,
      if (instance.lastSaved?.toIso8601String() case final value?)
        'lastSaved': value,
      if (instance.lastCleanup?.toIso8601String() case final value?)
        'lastCleanup': value,
      'keys': instance.keys,
    };
