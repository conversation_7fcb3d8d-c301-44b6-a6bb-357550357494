import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:developer' as developer;
import '../providers/realtime_providers.dart';
import '../providers/app_providers.dart';

/// Helper class to initialize all real-time services
class RealtimeServiceInitializer {
  static bool _isInitialized = false;

  /// Initialize all real-time services
  /// Call this once during app startup after authentication
  static Future<void> initialize(WidgetRef ref) async {
    if (_isInitialized) {
      developer.log(
        '[RealtimeServiceInitializer] Services already initialized',
      );
      return;
    }

    try {
      developer.log(
        '[RealtimeServiceInitializer] 🚀 Starting real-time services initialization...',
      );

      // Check if user is authenticated
      final jwtService = ref.read(jwtServiceProvider);
      if (!jwtService.isAuthenticated) {
        developer.log(
          '[RealtimeServiceInitializer] ❌ User not authenticated - skipping initialization',
        );
        return;
      }

      // Initialize real-time services
      await ref.read(initializeRealtimeServicesProvider.future);

      _isInitialized = true;
      developer.log(
        '[RealtimeServiceInitializer] ✅ Real-time services initialized successfully',
      );
    } catch (e, stackTrace) {
      developer.log(
        '[RealtimeServiceInitializer] ❌ Failed to initialize real-time services: $e',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Check if real-time services are initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization status (useful for testing or logout)
  static void reset() {
    _isInitialized = false;
    developer.log(
      '[RealtimeServiceInitializer] 🔄 Reset initialization status',
    );
  }

  /// Initialize with error handling and retry logic
  static Future<bool> initializeWithRetry(
    WidgetRef ref, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await initialize(ref);
        return true;
      } catch (e) {
        developer.log(
          '[RealtimeServiceInitializer] ⚠️ Initialization attempt $attempt failed: $e',
        );

        if (attempt < maxRetries) {
          developer.log(
            '[RealtimeServiceInitializer] 🔄 Retrying in ${retryDelay.inSeconds} seconds...',
          );
          await Future.delayed(retryDelay);
        }
      }
    }

    developer.log(
      '[RealtimeServiceInitializer] ❌ Failed to initialize after $maxRetries attempts',
    );
    return false;
  }

  /// Get initialization status with connection health
  static Future<RealtimeServiceStatus> getStatus(WidgetRef ref) async {
    if (!_isInitialized) {
      return RealtimeServiceStatus(
        isInitialized: false,
        isWebSocketConnected: false,
        error: 'Services not initialized',
      );
    }

    try {
      final webSocketService = ref.read(webSocketServiceProvider);
      final isConnected = webSocketService.isConnected;

      return RealtimeServiceStatus(
        isInitialized: true,
        isWebSocketConnected: isConnected,
      );
    } catch (e) {
      return RealtimeServiceStatus(
        isInitialized: true,
        isWebSocketConnected: false,
        error: e.toString(),
      );
    }
  }
}

/// Status of real-time services
class RealtimeServiceStatus {
  final bool isInitialized;
  final bool isWebSocketConnected;
  final String? error;

  const RealtimeServiceStatus({
    required this.isInitialized,
    required this.isWebSocketConnected,
    this.error,
  });

  bool get isHealthy => isInitialized && isWebSocketConnected && error == null;

  @override
  String toString() {
    return 'RealtimeServiceStatus('
        'initialized: $isInitialized, '
        'connected: $isWebSocketConnected, '
        'error: $error'
        ')';
  }
}

/// Extension to make initialization easier in widgets
extension RealtimeInitializationExtension on WidgetRef {
  /// Initialize real-time services
  Future<void> initializeRealtimeServices() async {
    await RealtimeServiceInitializer.initialize(this);
  }

  /// Initialize with retry logic
  Future<bool> initializeRealtimeServicesWithRetry({
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    return await RealtimeServiceInitializer.initializeWithRetry(
      this,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
    );
  }

  /// Get real-time services status
  Future<RealtimeServiceStatus> getRealtimeServicesStatus() async {
    return await RealtimeServiceInitializer.getStatus(this);
  }
}

// Note: realtimeServiceStatusProvider removed due to type compatibility issues
// Use RealtimeServiceInitializer.getStatus(ref) directly in widgets instead

/// Example usage in app initialization:
/// 
/// ```dart
/// class MyApp extends ConsumerWidget {
///   @override
///   Widget build(BuildContext context, WidgetRef ref) {
///     // Initialize real-time services after authentication
///     ref.listen(authStateProvider, (previous, next) {
///       if (next.isAuthenticated && !RealtimeServiceInitializer.isInitialized) {
///         ref.initializeRealtimeServices();
///       } else if (!next.isAuthenticated) {
///         RealtimeServiceInitializer.reset();
///       }
///     });
///     
///     return MaterialApp(
///       // ... your app configuration
///     );
///   }
/// }
/// ```
/// 
/// Or in a specific screen:
/// 
/// ```dart
/// class DashboardScreen extends ConsumerStatefulWidget {
///   @override
///   ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
/// }
/// 
/// class _DashboardScreenState extends ConsumerState<DashboardScreen> {
///   @override
///   void initState() {
///     super.initState();
///     // Initialize real-time services when dashboard loads
///     WidgetsBinding.instance.addPostFrameCallback((_) {
///       ref.initializeRealtimeServicesWithRetry();
///     });
///   }
///   
///   @override
///   Widget build(BuildContext context) {
///     final serviceStatus = ref.watch(realtimeServiceStatusProvider);
///     
///     return Scaffold(
///       appBar: AppBar(
///         title: Text('Dashboard'),
///         actions: [
///           serviceStatus.when(
///             data: (status) => Icon(
///               status.isHealthy ? Icons.wifi : Icons.wifi_off,
///               color: status.isHealthy ? Colors.green : Colors.red,
///             ),
///             loading: () => SizedBox(
///               width: 20,
///               height: 20,
///               child: CircularProgressIndicator(strokeWidth: 2),
///             ),
///             error: (_, __) => Icon(Icons.error, color: Colors.red),
///           ),
///         ],
///       ),
///       body: // ... your dashboard content
///     );
///   }
/// }
/// ```
