import 'package:flutter/material.dart';

class LocationSchedulesSection extends StatelessWidget {
  final int locationId;
  final VoidCallback? onAddSchedule;

  const LocationSchedulesSection({
    super.key,
    required this.locationId,
    this.onAddSchedule,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 24,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Working Hours',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: onAddSchedule,
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('Add Schedule'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Placeholder content - will be replaced with actual schedule data
            _buildPlaceholderContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.schedule,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'No Schedule Set',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Set working hours for this location to allow customers to book appointments.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onAddSchedule,
            icon: const Icon(Icons.add),
            label: const Text('Set Working Hours'),
          ),
        ],
      ),
    );
  }

  // This method will be used when schedule data is available
  Widget _buildSchedulesList(List<dynamic> schedules) {
    return Column(
      children: [
        _buildWeeklyScheduleGrid(schedules),
      ],
    );
  }

  Widget _buildWeeklyScheduleGrid(List<dynamic> schedules) {
    final days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: days.asMap().entries.map((entry) {
          final dayIndex = entry.key;
          final dayName = entry.value;
          
          return _buildDayScheduleRow(dayIndex, dayName, schedules);
        }).toList(),
      ),
    );
  }

  Widget _buildDayScheduleRow(int dayIndex, String dayName, List<dynamic> schedules) {
    // Find schedules for this day
    final daySchedules = schedules.where((s) => s['dayOfWeek'] == dayIndex).toList();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: dayIndex < 6 ? 1 : 0,
          ),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            child: Text(
              dayName,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: daySchedules.isEmpty
                ? Text(
                    'Closed',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                : Wrap(
                    spacing: 8,
                    children: daySchedules.map((schedule) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          '${schedule['startTime']} - ${schedule['endTime']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
          ),
        ],
      ),
    );
  }
}
