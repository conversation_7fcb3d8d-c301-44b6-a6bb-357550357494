import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../widgets/searchable_multi_select.dart';
import '../providers/onboarding_provider.dart';
import '../../services/models/service_models.dart';
import '../../services/widgets/color_picker_widget.dart';

/// Service creation step for onboarding
class ServiceCreationStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const ServiceCreationStep({super.key, required this.controller});

  @override
  ConsumerState<ServiceCreationStep> createState() =>
      _ServiceCreationStepState();
}

class _ServiceCreationStepState extends ConsumerState<ServiceCreationStep> {
  final List<CreateServiceRequest> _services = [];
  final _formKey = GlobalKey<FormState>();

  // Form controllers for adding new service
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _pointsController = TextEditingController();

  int _duration = 30; // Default 30 minutes
  String? _selectedColor;
  bool _acceptOnline = true;
  bool _acceptNew = true;
  bool _notificationOn = true;
  bool _isPublic = true;
  String _deliveryType = 'at_location';
  bool _showAddForm = false;

  // Served regions for customer location services
  List<String> _servedRegions = [];

  // Algerian wilayas (provinces)
  static const List<String> _algerianWilayas = [
    'Adrar',
    'Chlef',
    'Laghouat',
    'Oum El Bouaghi',
    'Batna',
    'Béjaïa',
    'Biskra',
    'Béchar',
    'Blida',
    'Bouira',
    'Tamanrasset',
    'Tébessa',
    'Tlemcen',
    'Tiaret',
    'Tizi Ouzou',
    'Algiers',
    'Djelfa',
    'Jijel',
    'Sétif',
    'Saïda',
    'Skikda',
    'Sidi Bel Abbès',
    'Annaba',
    'Guelma',
    'Constantine',
    'Médéa',
    'Mostaganem',
    'M\'Sila',
    'Mascara',
    'Ouargla',
    'Oran',
    'El Bayadh',
    'Illizi',
    'Bordj Bou Arréridj',
    'Boumerdès',
    'El Tarf',
    'Tindouf',
    'Tissemsilt',
    'El Oued',
    'Khenchela',
    'Souk Ahras',
    'Tipaza',
    'Mila',
    'Aïn Defla',
    'Naâma',
    'Aïn Témouchent',
    'Ghardaïa',
    'Relizane',
  ];

  @override
  void initState() {
    super.initState();
    _pointsController.text = '1'; // Set default points requirement
    _loadExistingData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _pointsController.dispose();
    super.dispose();
  }

  /// Load existing service data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final services = onboardingState.data?.services ?? [];

    if (services.isNotEmpty) {
      // Convert existing services to CreateServiceRequest format
      _services.clear();
      for (final service in services) {
        _services.add(
          CreateServiceRequest(
            title: service.title,
            duration: service.duration,
            price: service.price ?? 0.0,
            description: service.description,
            color: service.color,
            acceptOnline: service.acceptOnline,
            acceptNew: service.acceptNew,
            notificationOn: service.notificationOn,
            pointsRequirements: service.pointsRequirements ?? 1,
            isPublic: service.isPublic ?? true,
            deliveryType: service.deliveryType ?? 'at_location',
            servedRegions: service.servedRegions,
            // Note: IDs will be regenerated with unique negative values when updating onboarding data
          ),
        );
      }

      // Mark step as completed if services exist
      widget.controller.markStepCompleted(OnboardingStep.serviceCreation, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WizardPage(
      step: OnboardingStep.serviceCreation,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Services list
          if (_services.isNotEmpty) ...[
            Text(
              'Your Services (${_services.length})',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),

            // Services list
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _services.length,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder:
                  (context, index) =>
                      _buildServiceCard(_services[index], index),
            ),

            const SizedBox(height: 24),
          ],

          // Add service button or form
          if (!_showAddForm) ...[
            // Add service button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    _showAddForm = true;
                  });
                },
                icon: const Icon(Icons.add),
                label: Text(
                  _services.isEmpty
                      ? 'Add Your First Service'
                      : 'Add Another Service',
                ),
              ),
            ),

            if (_services.isEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.colors.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: context.colors.outline.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: context.colors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Add at least one service to continue. You can add more services later from the services management section.',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ] else ...[
            // Add service form
            _buildAddServiceForm(),
          ],
        ],
      ),
    );
  }

  /// Build service card widget
  Widget _buildServiceCard(CreateServiceRequest service, int index) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Service color indicator
                if (service.color != null)
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Color(
                        int.parse('0xFF${service.color!.replaceAll('#', '')}'),
                      ),
                      shape: BoxShape.circle,
                    ),
                  ),
                if (service.color != null) const SizedBox(width: 8),

                // Service title
                Expanded(
                  child: Text(
                    service.title,
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Remove button
                IconButton(
                  onPressed: () => _removeService(index),
                  icon: const Icon(Icons.delete_outline),
                  iconSize: 20,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),

            if (service.description != null &&
                service.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                service.description!,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            ],

            const SizedBox(height: 12),

            // Service details
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildServiceDetail(Icons.schedule, '${service.duration} min'),
                _buildServiceDetail(
                  Icons.attach_money,
                  '${(service.price ?? 0.0).toStringAsFixed(0)} DA',
                ),
                if (service.acceptOnline)
                  _buildServiceDetail(Icons.online_prediction, 'Online'),
                if (service.acceptNew)
                  _buildServiceDetail(Icons.person_add, 'New Clients'),
                if (service.pointsRequirements != null)
                  _buildServiceDetail(
                    Icons.stars,
                    '${service.pointsRequirements} pts',
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build service detail chip
  Widget _buildServiceDetail(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: context.colors.onSurfaceVariant),
        const SizedBox(width: 4),
        Text(
          text,
          style: context.textTheme.bodySmall?.copyWith(
            color: context.colors.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Build add service form
  Widget _buildAddServiceForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form header
              Row(
                children: [
                  Icon(Icons.add_circle_outline, color: context.colors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'Add New Service',
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _cancelAddService,
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Service title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Service Title *',
                  hintText: 'e.g., Consultation, Haircut, Massage',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.room_service),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Service title is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Title must be at least 2 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Service description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Describe your service',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
                textCapitalization: TextCapitalization.sentences,
              ),

              const SizedBox(height: 16),

              // Duration and Price row
              Row(
                children: [
                  // Duration
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Duration (minutes) *',
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<int>(
                          value: _duration,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.schedule),
                          ),
                          items:
                              [
                                    5,
                                    10,
                                    15,
                                    20,
                                    30,
                                    45,
                                    60,
                                    90,
                                    120,
                                    150,
                                    180,
                                    240,
                                    300,
                                    360,
                                  ]
                                  .map(
                                    (duration) => DropdownMenuItem(
                                      value: duration,
                                      child: Text('$duration min'),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            setState(() {
                              _duration = value ?? 30;
                            });
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Price
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Price (DA) *',
                          style: context.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: _priceController,
                          decoration: const InputDecoration(
                            hintText: '0',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.attach_money),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d+\.?\d{0,2}'),
                            ),
                          ],
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Price is required';
                            }
                            final price = double.tryParse(value);
                            if (price == null || price < 0) {
                              return 'Enter valid price';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Color picker
              ColorPickerWidget(
                selectedColor:
                    _selectedColor ?? '#2196F3', // Default blue color
                onColorChanged: (color) {
                  setState(() {
                    _selectedColor = color;
                  });
                },
              ),

              const SizedBox(height: 16),

              // Service options
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Service Options',
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),

                  CheckboxListTile(
                    title: const Text('Accept Online Bookings'),
                    subtitle: const Text('Allow customers to book online'),
                    value: _acceptOnline,
                    onChanged: (value) {
                      setState(() {
                        _acceptOnline = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Accept New Customers'),
                    subtitle: const Text('Allow new customers to book'),
                    value: _acceptNew,
                    onChanged: (value) {
                      setState(() {
                        _acceptNew = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: const Text('Get notified for new bookings'),
                    value: _notificationOn,
                    onChanged: (value) {
                      setState(() {
                        _notificationOn = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Points requirement (required)
              TextFormField(
                controller: _pointsController,
                decoration: const InputDecoration(
                  labelText: 'Credit Points Required *',
                  hintText: 'Credits needed to book this service (minimum 1)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.account_balance_wallet),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Credit points requirement is required';
                  }
                  final points = int.tryParse(value);
                  if (points == null || points < 1) {
                    return 'Credit points must be a positive number';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Service visibility
              SwitchListTile(
                title: const Text('Public Service'),
                subtitle: const Text('Visible to all customers'),
                value: _isPublic,
                onChanged: (value) {
                  setState(() {
                    _isPublic = value;
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),

              const SizedBox(height: 16),

              // Delivery type
              DropdownButtonFormField<String>(
                value: _deliveryType,
                decoration: const InputDecoration(
                  labelText: 'Service Delivery Type *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.delivery_dining),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'at_location',
                    child: Text('At Business Location'),
                  ),
                  DropdownMenuItem(
                    value: 'at_customer',
                    child: Text('At Customer Location'),
                  ),
                  DropdownMenuItem(
                    value: 'both',
                    child: Text('Both Locations'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _deliveryType = value!;
                    if (value == 'at_location') {
                      _servedRegions.clear();
                    }
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a delivery type';
                  }
                  return null;
                },
              ),

              // Wilaya selection for customer location services
              if (_deliveryType == 'at_customer' ||
                  _deliveryType == 'both') ...[
                const SizedBox(height: 16),
                SearchableMultiSelect(
                  items: _algerianWilayas,
                  selectedItems: _servedRegions,
                  onSelectionChanged: (selectedWilayas) {
                    setState(() {
                      _servedRegions = selectedWilayas;
                    });
                  },
                  labelText: 'Served Regions *',
                  hintText: 'Choose the wilayas where you provide services',
                  searchHintText: 'Search wilayas...',
                  prefixIcon: const Icon(Icons.location_on),
                ),
              ],

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _cancelAddService,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _addService,
                      child: const Text('Add Service'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Add new service
  void _addService() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate delivery type and served regions
    if ((_deliveryType == 'at_customer' || _deliveryType == 'both') &&
        _servedRegions.isEmpty) {
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Please select at least one wilaya for customer location services',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
      return;
    }

    final price =
        _priceController.text.isNotEmpty
            ? double.tryParse(_priceController.text)
            : null;
    final points = int.parse(_pointsController.text); // Required field

    final service = CreateServiceRequest(
      title: _titleController.text.trim(),
      duration: _duration,
      pointsRequirements: points,
      price: price,
      isPublic: _isPublic,
      deliveryType: _deliveryType,
      servedRegions: _servedRegions.isNotEmpty ? _servedRegions : null,
      description:
          _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
      color: _selectedColor,
      acceptOnline: _acceptOnline,
      acceptNew: _acceptNew,
      notificationOn: _notificationOn,
    );

    setState(() {
      _services.add(service);
      _showAddForm = false;
    });

    _clearForm();
    _updateOnboardingData();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('Service "${service.title}" added successfully!'),
          ],
        ),
        backgroundColor: context.colors.primary,
      ),
    );
  }

  /// Remove service
  void _removeService(int index) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove Service'),
            content: Text(
              'Are you sure you want to remove "${_services[index].title}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _services.removeAt(index);
                  });
                  _updateOnboardingData();
                },
                child: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  /// Cancel add service
  void _cancelAddService() {
    setState(() {
      _showAddForm = false;
    });
    _clearForm();
  }

  /// Clear form fields
  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _pointsController.text = '1'; // Default to 1 point
    setState(() {
      _duration = 30;
      _selectedColor = null;
      _acceptOnline = true;
      _acceptNew = true;
      _notificationOn = true;
      _isPublic = true;
      _deliveryType = 'at_location';
      _servedRegions.clear();
    });
  }

  /// Update onboarding data with current services
  void _updateOnboardingData() {
    // Convert CreateServiceRequest to Service objects for onboarding
    final services =
        _services
            .asMap()
            .entries
            .map(
              (entry) => Service(
                id:
                    -(entry.key +
                        1), // Use unique negative IDs for temporary services to avoid conflicts
                title: entry.value.title,
                duration: entry.value.duration,
                description: entry.value.description,
                price: entry.value.price,
                color: entry.value.color,
                acceptOnline: entry.value.acceptOnline,
                acceptNew: entry.value.acceptNew,
                notificationOn: entry.value.notificationOn,
                pointsRequirements: entry.value.pointsRequirements,
                isActive: entry.value.isPublic, // Map isPublic to isActive
                isPublic: entry.value.isPublic,
                deliveryType: entry.value.deliveryType,
                servedRegions: entry.value.servedRegions,
              ),
            )
            .toList();

    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveServices(services);

    // Update step completion
    widget.controller.markStepCompleted(
      OnboardingStep.serviceCreation,
      _services.isNotEmpty,
    );
  }
}
