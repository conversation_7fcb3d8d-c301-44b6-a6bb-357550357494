import '../../../core/network/api_service.dart';

/// Service for handling location integrations with queues and schedules
class LocationIntegrationService extends ApiService {
  LocationIntegrationService(super.httpClient);

  /// Get queues for a specific location
  Future<List<Map<String, dynamic>>> getLocationQueues(int locationId, {bool? isActive}) async {
    try {
      print('[] ');

      final queryParams = <String, dynamic>{};
      if (isActive != null) queryParams['isActive'] = isActive;

      final response = await httpClient.get(
        '/api/auth/providers/locations/$locationId/queues',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic> && responseData['data'] is List) {
          final queues = (responseData['data'] as List)
              .map((queueJson) => queueJson as Map<String, dynamic>)
              .toList();

          print(().toString());
          return queues;
        } else if (responseData is List) {
          final queues = responseData
              .map((queueJson) => queueJson as Map<String, dynamic>)
              .toList();

          print(().toString());
          return queues;
        } else {
          print(().toString());
          return [];
        }
      } else {
        print(().toString());
        return [];
      }
    } catch (e) {
      print(().toString());
      return [];
    }
  }

  /// Get schedules for a specific location
  Future<List<Map<String, dynamic>>> getLocationSchedules(int locationId, {
    int? dayOfWeek,
    bool? isActive,
  }) async {
    try {
      print('[] ');

      final queryParams = <String, dynamic>{
        'locationId': locationId,
      };
      if (dayOfWeek != null) queryParams['dayOfWeek'] = dayOfWeek;
      if (isActive != null) queryParams['isActive'] = isActive;

      final response = await httpClient.get(
        '/api/auth/providers/schedules',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic> && responseData['data'] is List) {
          final schedules = (responseData['data'] as List)
              .map((scheduleJson) => scheduleJson as Map<String, dynamic>)
              .toList();

          print(().toString());
          return schedules;
        } else if (responseData is List) {
          final schedules = responseData
              .map((scheduleJson) => scheduleJson as Map<String, dynamic>)
              .toList();

          print(().toString());
          return schedules;
        } else {
          print(().toString());
          return [];
        }
      } else {
        print(().toString());
        return [];
      }
    } catch (e) {
      print(().toString());
      return [];
    }
  }

  /// Check if a location has any dependencies (queues or schedules)
  Future<LocationDependencies> getLocationDependencies(int locationId) async {
    try {
      print('[] ');

      // Get queues and schedules in parallel
      final results = await Future.wait([
        getLocationQueues(locationId),
        getLocationSchedules(locationId),
      ]);

      final queues = results[0];
      final schedules = results[1];

      final dependencies = LocationDependencies(
        hasQueues: queues.isNotEmpty,
        hasSchedules: schedules.isNotEmpty,
        queueCount: queues.length,
        scheduleCount: schedules.length,
        canDelete: queues.isEmpty && schedules.isEmpty,
      );

      print(().toString());

      return dependencies;
    } catch (e) {
      print(().toString());
      
      // Return safe defaults on error
      return const LocationDependencies(
        hasQueues: false,
        hasSchedules: false,
        queueCount: 0,
        scheduleCount: 0,
        canDelete: true,
      );
    }
  }

  /// Get location statistics including queue and schedule counts
  Future<LocationStats> getLocationStats(int locationId) async {
    try {
      print('[] ');

      final dependencies = await getLocationDependencies(locationId);

      final stats = LocationStats(
        locationId: locationId,
        totalQueues: dependencies.queueCount,
        activeQueues: 0, // Will be calculated when we have queue status
        totalSchedules: dependencies.scheduleCount,
        activeSchedules: 0, // Will be calculated when we have schedule status
        hasActiveOperations: dependencies.hasQueues || dependencies.hasSchedules,
      );

      print(().toString());
      return stats;
    } catch (e) {
      print(().toString());
      
      return LocationStats(
        locationId: locationId,
        totalQueues: 0,
        activeQueues: 0,
        totalSchedules: 0,
        activeSchedules: 0,
        hasActiveOperations: false,
      );
    }
  }
}

/// Model for location dependencies
class LocationDependencies {
  final bool hasQueues;
  final bool hasSchedules;
  final int queueCount;
  final int scheduleCount;
  final bool canDelete;

  const LocationDependencies({
    required this.hasQueues,
    required this.hasSchedules,
    required this.queueCount,
    required this.scheduleCount,
    required this.canDelete,
  });
}

/// Model for location statistics
class LocationStats {
  final int locationId;
  final int totalQueues;
  final int activeQueues;
  final int totalSchedules;
  final int activeSchedules;
  final bool hasActiveOperations;

  const LocationStats({
    required this.locationId,
    required this.totalQueues,
    required this.activeQueues,
    required this.totalSchedules,
    required this.activeSchedules,
    required this.hasActiveOperations,
  });
}
