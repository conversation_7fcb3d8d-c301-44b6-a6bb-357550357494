import 'package:flutter/foundation.dart';
import '../storage/web_storage_service.dart';

/// Test utilities for web storage functionality
class WebStorageTest {
  /// Test basic SharedPreferences functionality
  static Future<void> testBasicStorage() async {
    print('=== TESTING WEB STORAGE ===');
    
    try {
      // Test string storage
      await WebStorageService.saveAuth('test_string', 'Hello World');
      final retrievedString = WebStorageService.getAuth<String>('test_string');
      print('String test: ${retrievedString == 'Hello World' ? "✅ PASS" : "❌ FAIL"}');
      
      // Test JSON object storage
      final testObject = {
        'access_token': 'test_token_123',
        'expires_at': DateTime.now().toIso8601String(),
        'user_id': 'user_123',
        'nested': {
          'key': 'value',
          'number': 42
        }
      };
      
      await WebStorageService.saveAuth('test_object', testObject);
      final retrievedObject = WebStorageService.getAuth<Map<String, dynamic>>('test_object');
      
      final objectTestPass = retrievedObject != null && 
                            retrievedObject['access_token'] == 'test_token_123' &&
                            retrievedObject['nested']['number'] == 42;
      print('Object test: ${objectTestPass ? "✅ PASS" : "❌ FAIL"}');
      
      if (retrievedObject != null) {
        print('Retrieved object keys: ${retrievedObject.keys.toList()}');
        print('Access token: ${retrievedObject['access_token']}');
        print('Nested value: ${retrievedObject['nested']['key']}');
      }
      
      // Test persistence simulation (clear and reload)
      print('Testing persistence...');
      final beforeClear = WebStorageService.getAuth<Map<String, dynamic>>('test_object');
      print('Before clear: ${beforeClear != null ? "EXISTS" : "NULL"}');
      
      // Clean up test data
      await WebStorageService.removeAuth('test_string');
      await WebStorageService.removeAuth('test_object');
      
      final afterClear = WebStorageService.getAuth<Map<String, dynamic>>('test_object');
      print('After clear: ${afterClear != null ? "EXISTS" : "NULL"}');
      
      print('✅ Web storage test completed successfully');
      
    } catch (e) {
      print('❌ Web storage test failed: $e');
    }
    
    print('=== END WEB STORAGE TEST ===');
  }
  
  /// Test JWT token storage specifically
  static Future<void> testJwtTokenStorage() async {
    print('=== TESTING JWT TOKEN STORAGE ===');
    
    try {
      // Create a realistic JWT token structure
      final jwtToken = {
        'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_payload.signature',
        'refresh_token': 'refresh_token_12345',
        'token_type': 'Bearer',
        'expires_at': DateTime.now().add(const Duration(hours: 1)).toIso8601String(),
        'payload': {
          'sub': 'user_123',
          'email': '<EMAIL>',
          'roles': ['provider'],
          'exp': DateTime.now().add(const Duration(hours: 1)).millisecondsSinceEpoch ~/ 1000,
        }
      };
      
      // Store the token
      await WebStorageService.saveAuth('jwt_token', jwtToken);
      print('✅ JWT token stored');
      
      // Retrieve the token
      final retrievedToken = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      print('✅ JWT token retrieved: ${retrievedToken != null ? "SUCCESS" : "FAILED"}');
      
      if (retrievedToken != null) {
        print('Token type: ${retrievedToken['token_type']}');
        print('Access token: ${retrievedToken['access_token']?.toString().substring(0, 20)}...');
        print('Expires at: ${retrievedToken['expires_at']}');
        print('User ID: ${retrievedToken['payload']['sub']}');
        print('User email: ${retrievedToken['payload']['email']}');
      }
      
      // Test token persistence across "app restart" simulation
      print('Simulating app restart...');
      
      // In a real app restart, we would create a new service instance
      // For this test, we just verify the token is still there
      final persistedToken = WebStorageService.getAuth<Map<String, dynamic>>('jwt_token');
      final persistenceTest = persistedToken != null && 
                             persistedToken['access_token'] == jwtToken['access_token'];
      print('Persistence test: ${persistenceTest ? "✅ PASS" : "❌ FAIL"}');
      
      // Clean up
      await WebStorageService.removeAuth('jwt_token');
      print('✅ Test cleanup completed');
      
    } catch (e) {
      print('❌ JWT token storage test failed: $e');
    }
    
    print('=== END JWT TOKEN STORAGE TEST ===');
  }
  
  /// Print current storage state for debugging
  static void printStorageState() {
    print('=== CURRENT STORAGE STATE ===');
    
    try {
      if (kIsWeb) {
        print('Platform: Web (using SharedPreferences)');
      } else {
        print('Platform: Non-web (using Hive)');
      }
      
      final authKeys = WebStorageService.getAuthKeys();
      print('Auth keys: $authKeys');
      
      for (final key in authKeys) {
        final value = WebStorageService.getAuth(key);
        if (value is Map) {
          print('$key: Map with ${value.length} keys');
        } else {
          print('$key: ${value.runtimeType} - ${value.toString().substring(0, 50)}...');
        }
      }
      
    } catch (e) {
      print('❌ Error reading storage state: $e');
    }
    
    print('=== END STORAGE STATE ===');
  }
  
  /// Run all storage tests
  static Future<void> runAllTests() async {
    await testBasicStorage();
    await testJwtTokenStorage();
    printStorageState();
  }
}
