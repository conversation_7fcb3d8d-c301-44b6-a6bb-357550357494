import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../lib/core/services/firebase_messaging_service.dart';
import '../lib/firebase_options.dart';

/// Test to compare FCM behavior between Customer and Provider apps
/// This test helps identify differences in FCM setup and behavior
void main() {
  group('FCM Comparison Tests', () {
    setUpAll(() async {
      // Initialize Firebase for testing
      if (!kIsWeb) {
        TestWidgetsFlutterBinding.ensureInitialized();
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
      }
    });

    test('FCM Service Initialization State', () async {
      print('\n=== FCM Service Initialization Test ===');
      
      // Test 1: Check if FCM can be initialized without login
      print('1. Testing FCM initialization without login...');
      try {
        final result = await FirebaseMessagingService.initialize();
        print('   Result: ${result != null ? "Initialized" : "Blocked"}');
        if (result == null) {
          print('   ✅ Expected: FCM blocked until login (Provider app pattern)');
        } else {
          print('   ⚠️  Unexpected: FCM initialized without login');
        }
      } catch (e) {
        print('   ❌ Error: $e');
      }

      // Test 2: Check if we can get Firebase Messaging instance
      print('\n2. Testing Firebase Messaging instance access...');
      try {
        final messaging = FirebaseMessaging.instance;
        print('   ✅ Firebase Messaging instance accessible: ${messaging.runtimeType}');
      } catch (e) {
        print('   ❌ Error accessing Firebase Messaging: $e');
      }

      // Test 3: Check background message handler setup
      print('\n3. Testing background message handler...');
      try {
        // This should be set up in main.dart
        print('   ✅ Background handler should be set up in main.dart');
        print('   Note: Handler function: firebaseMessagingBackgroundHandler');
      } catch (e) {
        print('   ❌ Error with background handler: $e');
      }
    });

    test('FCM Token Generation Comparison', () async {
      print('\n=== FCM Token Generation Test ===');
      
      // Test 1: Try to get token without initialization
      print('1. Testing token generation without FCM initialization...');
      try {
        final token = await FirebaseMessagingService.getToken();
        print('   Token result: ${token != null ? "Token available" : "No token"}');
        if (token != null) {
          print('   Token preview: ${token.substring(0, 20)}...');
        }
      } catch (e) {
        print('   ❌ Error getting token: $e');
      }

      // Test 2: Try direct Firebase Messaging token
      print('\n2. Testing direct Firebase Messaging token...');
      try {
        if (!kIsWeb) {
          final directToken = await FirebaseMessaging.instance.getToken();
          print('   Direct token result: ${directToken != null ? "Available" : "Not available"}');
          if (directToken != null) {
            print('   Direct token preview: ${directToken.substring(0, 20)}...');
          }
        } else {
          print('   Skipping direct token test on web (requires VAPID key)');
        }
      } catch (e) {
        print('   ❌ Error getting direct token: $e');
      }
    });

    test('FCM Permissions Test', () async {
      print('\n=== FCM Permissions Test ===');
      
      try {
        final settings = await FirebaseMessaging.instance.requestPermission(
          alert: true,
          announcement: false,
          badge: true,
          carPlay: false,
          criticalAlert: false,
          provisional: false,
          sound: true,
        );
        
        print('Permission status: ${settings.authorizationStatus}');
        print('Alert: ${settings.alert}');
        print('Badge: ${settings.badge}');
        print('Sound: ${settings.sound}');
        
        if (settings.authorizationStatus == AuthorizationStatus.authorized) {
          print('✅ Notifications authorized');
        } else {
          print('⚠️  Notifications not authorized: ${settings.authorizationStatus}');
        }
      } catch (e) {
        print('❌ Error requesting permissions: $e');
      }
    });

    test('Compare with Customer App Pattern', () async {
      print('\n=== Customer App Pattern Comparison ===');
      
      print('Customer App Pattern:');
      print('1. Firebase.initializeApp() in main.dart ✅');
      print('2. Background handler setup in main.dart ✅');
      print('3. FCM initialization immediately in main.dart ❌');
      print('4. Token generation immediately ❌');
      print('5. Token sent to server after login ✅');
      
      print('\nProvider App Current Pattern:');
      print('1. Firebase.initializeApp() in main.dart ✅');
      print('2. Background handler setup in main.dart ✅');
      print('3. FCM initialization blocked until login ✅');
      print('4. Token generation blocked until login ✅');
      print('5. Token sent to server after login ✅');
      
      print('\n🔍 Key Difference: FCM initialization timing');
      print('   Customer: Immediate FCM init');
      print('   Provider: Delayed FCM init (after login)');
      
      print('\n💡 Recommendation: Test immediate FCM init pattern');
    });

    test('Firebase Configuration Comparison', () async {
      print('\n=== Firebase Configuration Comparison ===');
      
      print('Customer App Config:');
      print('- Project ID: dalti-3d06b ✅ (same)');
      print('- Package: com.example.dalti');
      print('- App ID: 1:816987655237:android:99a76db360ab3c312e672c');
      print('- API Key: AIzaSyD3z86nxd9i4okjur85vpg4be-um8lQzn8');
      
      print('\nProvider App Config:');
      print('- Project ID: dalti-3d06b ✅ (same)');
      print('- Package: com.example.dalti_provider');
      print('- App ID: 1:816987655237:android:5c8650ead20f8cb12e672c');
      print('- API Key: AIzaSyCLV3ESOoycIrloOQ3IxZbOesUcfc8kJpY');
      
      print('\n✅ Configuration looks correct for separate apps in same project');
    });

    test('Dependencies Version Comparison', () async {
      print('\n=== Dependencies Version Comparison ===');
      
      print('Customer App Dependencies:');
      print('- firebase_core: ^2.32.0');
      print('- firebase_messaging: ^14.9.3');
      print('- flutter_local_notifications: ^17.2.1');
      
      print('\nProvider App Dependencies:');
      print('- firebase_core: ^3.15.0 (newer)');
      print('- firebase_messaging: ^15.1.4 (newer)');
      print('- flutter_local_notifications: ^17.2.1 (same)');
      
      print('\n⚠️  Provider app uses newer Firebase versions');
      print('   This might cause behavioral differences');
      print('   Consider testing with Customer app versions if issues persist');
    });
  });
}
