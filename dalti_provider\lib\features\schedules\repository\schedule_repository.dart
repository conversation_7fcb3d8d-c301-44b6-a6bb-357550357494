import '../models/schedule_models.dart';

/// Abstract repository interface for schedule management operations
abstract class ScheduleRepository {
  /// Get all schedules for the current provider
  /// 
  /// [locationId] - Filter by location ID (optional)
  /// [dayOfWeek] - Filter by day of week (0=Sunday, 6=Saturday) (optional)
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns list of schedules
  Future<List<Schedule>> getSchedules({
    int? locationId,
    int? dayOfWeek,
    bool? isActive,
  });

  /// Get schedule by ID
  /// 
  /// [id] - Schedule ID
  /// 
  /// Returns schedule details
  Future<Schedule> getScheduleById(int id);

  /// Create a new schedule
  /// 
  /// [request] - Schedule creation data
  /// 
  /// Returns created schedule
  Future<Schedule> createSchedule(CreateScheduleRequest request);

  /// Update an existing schedule
  /// 
  /// [id] - Schedule ID to update
  /// [request] - Schedule update data
  /// 
  /// Returns updated schedule
  Future<Schedule> updateSchedule(int id, UpdateScheduleRequest request);

  /// Delete a schedule
  /// 
  /// [id] - Schedule ID to delete
  /// 
  /// Returns success status
  Future<bool> deleteSchedule(int id);

  /// Get schedules for a specific location
  /// 
  /// [locationId] - Location ID
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns schedules for the location
  Future<List<Schedule>> getSchedulesByLocation(int locationId, {bool? isActive});

  /// Get schedules for a specific day across all locations
  /// 
  /// [dayOfWeek] - Day of week (0=Sunday, 6=Saturday)
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns schedules for the day
  Future<List<Schedule>> getSchedulesByDay(int dayOfWeek, {bool? isActive});

  /// Get weekly schedule for a specific location
  /// 
  /// [locationId] - Location ID
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns map of day -> schedules for the week
  Future<Map<int, List<Schedule>>> getWeeklySchedule(int locationId, {bool? isActive});

  /// Check for schedule conflicts
  /// 
  /// [dayOfWeek] - Day of week
  /// [startTime] - Start time (HH:MM)
  /// [endTime] - End time (HH:MM)
  /// [locationId] - Location ID
  /// [excludeScheduleId] - Schedule ID to exclude from conflict check (for updates)
  /// 
  /// Returns list of conflicting schedules
  Future<List<Schedule>> checkScheduleConflicts({
    required int dayOfWeek,
    required String startTime,
    required String endTime,
    required int locationId,
    int? excludeScheduleId,
  });

  /// Get schedule statistics for a location
  /// 
  /// [locationId] - Location ID
  /// 
  /// Returns statistics like total hours, days covered, etc.
  Future<Map<String, dynamic>> getScheduleStats(int locationId);

  /// Bulk create schedules (for setting up weekly schedules)
  /// 
  /// [requests] - List of schedule creation requests
  /// 
  /// Returns list of created schedules
  Future<List<Schedule>> createBulkSchedules(List<CreateScheduleRequest> requests);

  /// Get all schedules grouped by location
  /// 
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns map of location ID -> schedules
  Future<Map<int, List<Schedule>>> getSchedulesGroupedByLocation({bool? isActive});

  /// Get all schedules grouped by day of week
  /// 
  /// [locationId] - Filter by location ID (optional)
  /// [isActive] - Filter by active status (optional)
  /// 
  /// Returns map of day -> schedules
  Future<Map<int, List<Schedule>>> getSchedulesGroupedByDay({
    int? locationId,
    bool? isActive,
  });

  /// Toggle schedule active status
  /// 
  /// [id] - Schedule ID
  /// [isActive] - New active status
  /// 
  /// Returns updated schedule
  Future<Schedule> toggleScheduleStatus(int id, bool isActive);

  /// Get schedules for today across all locations
  /// 
  /// Returns today's schedules
  Future<List<Schedule>> getTodaySchedules();

  /// Validate schedule time format and logic
  /// 
  /// [startTime] - Start time (HH:MM)
  /// [endTime] - End time (HH:MM)
  /// 
  /// Returns validation result with error message if invalid
  Future<ScheduleValidationResult> validateScheduleTime(String startTime, String endTime);
}

/// Schedule validation result
class ScheduleValidationResult {
  final bool isValid;
  final String? errorMessage;

  const ScheduleValidationResult({
    required this.isValid,
    this.errorMessage,
  });

  factory ScheduleValidationResult.valid() {
    return const ScheduleValidationResult(isValid: true);
  }

  factory ScheduleValidationResult.invalid(String message) {
    return ScheduleValidationResult(isValid: false, errorMessage: message);
  }
}
