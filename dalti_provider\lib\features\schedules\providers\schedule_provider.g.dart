// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'schedule_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$scheduleApiServiceHash() =>
    r'67d560d7920aac76224a1453cdef96993e5de560';

/// Schedule API service provider
///
/// Copied from [scheduleApiService].
@ProviderFor(scheduleApiService)
final scheduleApiServiceProvider =
    AutoDisposeProvider<ScheduleApiService>.internal(
  scheduleApiService,
  name: r'scheduleApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$scheduleApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ScheduleApiServiceRef = AutoDisposeProviderRef<ScheduleApiService>;
String _$scheduleRepositoryHash() =>
    r'a9e94dc2f3c2ba1f274ec4d3effe7f9f99397b18';

/// Schedule repository provider
///
/// Copied from [scheduleRepository].
@ProviderFor(scheduleRepository)
final scheduleRepositoryProvider =
    AutoDisposeProvider<ScheduleRepository>.internal(
  scheduleRepository,
  name: r'scheduleRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$scheduleRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ScheduleRepositoryRef = AutoDisposeProviderRef<ScheduleRepository>;
String _$scheduleNotifierHash() => r'08b8316df00eaacebca523d3fadc2404fdf887d1';

/// Schedule provider for managing schedule state
///
/// Copied from [ScheduleNotifier].
@ProviderFor(ScheduleNotifier)
final scheduleNotifierProvider =
    AutoDisposeNotifierProvider<ScheduleNotifier, ScheduleData>.internal(
  ScheduleNotifier.new,
  name: r'scheduleNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$scheduleNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ScheduleNotifier = AutoDisposeNotifier<ScheduleData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
