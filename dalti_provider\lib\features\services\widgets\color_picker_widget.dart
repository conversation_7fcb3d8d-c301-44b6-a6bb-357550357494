import 'package:flutter/material.dart';

class ColorPickerWidget extends StatefulWidget {
  final String selectedColor;
  final Function(String) onColorChanged;

  const ColorPickerWidget({
    super.key,
    required this.selectedColor,
    required this.onColorChanged,
  });

  @override
  State<ColorPickerWidget> createState() => _ColorPickerWidgetState();
}

class _ColorPickerWidgetState extends State<ColorPickerWidget> {
  late TextEditingController _colorController;

  @override
  void initState() {
    super.initState();
    // Initialize controller with the hex part only (without #)
    final hexValue = widget.selectedColor.startsWith('#')
        ? widget.selectedColor.substring(1)
        : widget.selectedColor;
    _colorController = TextEditingController(text: hexValue);
  }

  @override
  void didUpdateWidget(ColorPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update controller when selectedColor changes from parent
    if (widget.selectedColor != oldWidget.selectedColor) {
      final hexValue = widget.selectedColor.startsWith('#')
          ? widget.selectedColor.substring(1)
          : widget.selectedColor;
      _colorController.text = hexValue;
    }
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  static const List<String> predefinedColors = [
    '#F44336', // Red
    '#E91E63', // Pink
    '#9C27B0', // Purple
    '#673AB7', // Deep Purple
    '#3F51B5', // Indigo
    '#2196F3', // Blue
    '#03A9F4', // Light Blue
    '#00BCD4', // Cyan
    '#009688', // Teal
    '#4CAF50', // Green
    '#8BC34A', // Light Green
    '#CDDC39', // Lime
    '#FFEB3B', // Yellow
    '#FFC107', // Amber
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#795548', // Brown
    '#9E9E9E', // Grey
    '#607D8B', // Blue Grey
    '#000000', // Black
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Service Color',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Selected color preview
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: _parseColor(widget.selectedColor),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: theme.colorScheme.outline,
                        width: 2,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    widget.selectedColor.toUpperCase(),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontFamily: 'monospace',
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Color grid
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: predefinedColors.map((color) {
                  final isSelected = color.toLowerCase() == widget.selectedColor.toLowerCase();
                  
                  return GestureDetector(
                    onTap: () {
                      // Update the text controller when a color is selected
                      final hexValue = color.startsWith('#') ? color.substring(1) : color;
                      _colorController.text = hexValue;
                      widget.onColorChanged(color);
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _parseColor(color),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isSelected 
                              ? theme.colorScheme.primary
                              : theme.colorScheme.outline.withValues(alpha: 0.3),
                          width: isSelected ? 3 : 1,
                        ),
                      ),
                      child: isSelected
                          ? Icon(
                              Icons.check,
                              color: _getContrastColor(_parseColor(color)),
                              size: 20,
                            )
                          : null,
                    ),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 12),
              
              // Custom color input
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _colorController,
                      decoration: const InputDecoration(
                        labelText: 'Custom Color',
                        hintText: 'RRGGBB',
                        prefixText: '#',
                        border: OutlineInputBorder(),
                        isDense: true,
                      ),
                      maxLength: 6,
                      onChanged: (value) {
                        if (value.length == 6 && _isValidHexColor(value)) {
                          // Add # prefix when calling the callback
                          widget.onColorChanged('#$value');
                        }
                      },
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (!_isValidHexColor(value)) {
                            return 'Invalid hex color';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _parseColor(String colorString) {
    try {
      String hexColor = colorString.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return Colors.blue; // Fallback color
    }
  }

  Color _getContrastColor(Color color) {
    // Calculate luminance to determine if we should use black or white text
    final luminance = (0.299 * color.r + 0.587 * color.g + 0.114 * color.b) / 255;
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  bool _isValidHexColor(String hex) {
    final hexPattern = RegExp(r'^[0-9A-Fa-f]{6}$');
    return hexPattern.hasMatch(hex);
  }
}
