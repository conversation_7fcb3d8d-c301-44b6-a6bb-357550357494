import 'package:phone_numbers_parser/phone_numbers_parser.dart';

/// Utility class for validating Algerian phone numbers
class PhoneValidator {
  // Private constructor to prevent instantiation
  PhoneValidator._();

  /// Validate an Algerian phone number
  /// Returns null if valid, error message if invalid
  static String? validateAlgerianPhone(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.trim().isEmpty) {
      return null; // Allow empty for optional fields
    }

    final cleanPhone = phoneNumber.trim();

    try {
      // Parse the phone number with Algeria country code
      final parsedNumber = PhoneNumber.parse(
        cleanPhone,
        callerCountry: IsoCode.DZ,
      );

      // Check if the number is valid
      if (!parsedNumber.isValid()) {
        return 'Invalid phone number format';
      }

      // Check if it's specifically an Algerian number
      if (parsedNumber.isoCode != IsoCode.DZ) {
        return 'Please enter an Algerian phone number';
      }

      return null; // Valid
    } catch (e) {
      // If parsing fails, try with +213 prefix
      try {
        String prefixedPhone = cleanPhone;
        if (!cleanPhone.startsWith('+213') && !cleanPhone.startsWith('213')) {
          // Remove leading 0 if present and add +213
          if (cleanPhone.startsWith('0')) {
            prefixedPhone = '+213${cleanPhone.substring(1)}';
          } else {
            prefixedPhone = '+213$cleanPhone';
          }
        }

        final parsedNumber = PhoneNumber.parse(prefixedPhone);

        if (!parsedNumber.isValid()) {
          return 'Invalid phone number format';
        }

        if (parsedNumber.isoCode != IsoCode.DZ) {
          return 'Please enter an Algerian phone number';
        }

        return null; // Valid
      } catch (e) {
        return 'Invalid phone number format';
      }
    }
  }

  /// Format an Algerian phone number to international format
  /// Returns formatted number or original if invalid
  static String formatAlgerianPhone(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.trim().isEmpty) {
      return '';
    }

    final cleanPhone = phoneNumber.trim();

    try {
      final parsedNumber = PhoneNumber.parse(
        cleanPhone,
        callerCountry: IsoCode.DZ,
      );

      if (parsedNumber.isValid() && parsedNumber.isoCode == IsoCode.DZ) {
        return parsedNumber.international;
      }
    } catch (e) {
      // Try with +213 prefix
      try {
        String prefixedPhone = cleanPhone;
        if (!cleanPhone.startsWith('+213') && !cleanPhone.startsWith('213')) {
          if (cleanPhone.startsWith('0')) {
            prefixedPhone = '+213${cleanPhone.substring(1)}';
          } else {
            prefixedPhone = '+213$cleanPhone';
          }
        }

        final parsedNumber = PhoneNumber.parse(prefixedPhone);

        if (parsedNumber.isValid() && parsedNumber.isoCode == IsoCode.DZ) {
          return parsedNumber.international;
        }
      } catch (e) {
        // Return original if formatting fails
      }
    }

    return cleanPhone;
  }

  /// Check if at least one valid phone number is provided
  static bool hasValidPhone({String? mobile, String? landline, String? fax}) {
    return validateAlgerianPhone(mobile) == null &&
            mobile?.trim().isNotEmpty == true ||
        validateAlgerianPhone(landline) == null &&
            landline?.trim().isNotEmpty == true ||
        validateAlgerianPhone(fax) == null && fax?.trim().isNotEmpty == true;
  }

  /// Get validation error for phone requirement
  static String? validatePhoneRequirement({
    String? mobile,
    String? landline,
    String? fax,
    bool isRequired = true,
  }) {
    if (!isRequired) return null;

    final hasMobile = mobile?.trim().isNotEmpty == true;
    final hasLandline = landline?.trim().isNotEmpty == true;
    final hasFax = fax?.trim().isNotEmpty == true;

    if (!hasMobile && !hasLandline && !hasFax) {
      return 'At least one phone number is required';
    }

    // Check if any provided phone number is valid
    if (hasMobile && validateAlgerianPhone(mobile) != null) {
      return 'Mobile phone: ${validateAlgerianPhone(mobile)}';
    }
    if (hasLandline && validateAlgerianPhone(landline) != null) {
      return 'Landline phone: ${validateAlgerianPhone(landline)}';
    }
    if (hasFax && validateAlgerianPhone(fax) != null) {
      return 'Fax number: ${validateAlgerianPhone(fax)}';
    }

    return null; // All provided numbers are valid
  }
}
