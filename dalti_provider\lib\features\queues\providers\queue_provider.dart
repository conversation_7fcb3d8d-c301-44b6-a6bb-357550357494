import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/queue_models.dart';
import '../repository/queue_repository.dart';
import '../repository/queue_repository_impl.dart';
import '../services/queue_api_service.dart';

part 'queue_provider.g.dart';

/// Queue API service provider
@riverpod
QueueApiService queueApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return QueueApiService(httpClient);
}

/// Queue repository provider
@riverpod
QueueRepository queueRepository(Ref ref) {
  final apiService = ref.watch(queueApiServiceProvider);
  return QueueRepositoryImpl(apiService: apiService);
}

/// Queue provider for managing queue state
@riverpod
class QueueNotifier extends _$QueueNotifier {
  @override
  QueueData build() {
    return const QueueData(state: QueueState.initial);
  }

  /// Load all queues
  Future<void> loadQueues({
    bool? isActive,
    int? locationId,
    bool forceRefresh = false,
  }) async {
    try {
      // Don't reload if already loaded and not forcing refresh
      if (state.state == QueueState.loaded && !forceRefresh) {
        return;
      }

      state = state.copyWith(
        state: QueueState.loading,
        isLoading: true,
        clearError: true,
      );

      print(
        '[QueueProvider] Loading queues (isActive: $isActive, locationId: $locationId)',
      );

      final repository = ref.read(queueRepositoryProvider);
      final queues = await repository.getQueues(
        isActive: isActive,
        locationId: locationId,
      );

      // Group queues by location and create lookup map
      final queuesByLocation = <int, List<Queue>>{};
      final queuesById = <int, Queue>{};

      for (final queue in queues) {
        queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
        queuesById[queue.id] = queue;
      }

      state = state.copyWith(
        state: QueueState.loaded,
        queues: queues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
        isLoading: false,
      );

      print('[QueueProvider] Loaded ${queues.length} queues');
    } catch (e) {
      print('[QueueProvider] Error loading queues: $e');
      state = state.copyWith(
        state: QueueState.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// Load queues for a specific location
  Future<void> loadQueuesByLocation(int locationId, {bool? isActive}) async {
    try {
      state = state.copyWith(
        state: QueueState.loading,
        isLoading: true,
        clearError: true,
      );

      print('[QueueProvider] Loading queues for location: $locationId');

      final repository = ref.read(queueRepositoryProvider);
      final queues = await repository.getQueuesByLocation(
        locationId,
        isActive: isActive,
      );

      // Update state with location-specific queues
      final queuesByLocation = Map<int, List<Queue>>.from(
        state.queuesByLocation,
      );
      queuesByLocation[locationId] = queues;

      final queuesById = Map<int, Queue>.from(state.queuesById);
      for (final queue in queues) {
        queuesById[queue.id] = queue;
      }

      // Update the main queues list by merging with existing queues from other locations
      final allQueues = <Queue>[];
      for (final locationQueues in queuesByLocation.values) {
        allQueues.addAll(locationQueues);
      }

      state = state.copyWith(
        state: QueueState.loaded,
        queues: allQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
        isLoading: false,
      );

      print(
        '[QueueProvider] Loaded ${queues.length} queues for location $locationId',
      );
    } catch (e) {
      print('[QueueProvider] Error loading queues for location: $e');
      state = state.copyWith(
        state: QueueState.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// Create a new queue
  Future<Queue?> createQueue(CreateQueueRequest request) async {
    try {
      print('[QueueProvider] Creating queue: ${request.toJson()}');

      final repository = ref.read(queueRepositoryProvider);
      final queue = await repository.createQueue(request);

      // Add to current state
      final updatedQueues = [...state.queues, queue];

      // Update grouped data
      final queuesByLocation = Map<int, List<Queue>>.from(
        state.queuesByLocation,
      );
      queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);

      final queuesById = Map<int, Queue>.from(state.queuesById);
      queuesById[queue.id] = queue;

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );

      print('[QueueProvider] Created queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueProvider] Error creating queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return null;
    }
  }

  /// Update an existing queue
  Future<Queue?> updateQueue(int id, UpdateQueueRequest request) async {
    try {
      print('[QueueProvider] Updating queue $id: ${request.toJson()}');

      final repository = ref.read(queueRepositoryProvider);
      final updatedQueue = await repository.updateQueue(id, request);

      // Update in current state
      final updatedQueues =
          state.queues.map((queue) {
            return queue.id == id ? updatedQueue : queue;
          }).toList();

      // Rebuild grouped data
      final queuesByLocation = <int, List<Queue>>{};
      final queuesById = <int, Queue>{};

      for (final queue in updatedQueues) {
        queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
        queuesById[queue.id] = queue;
      }

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );

      print('[QueueProvider] Updated queue: ${updatedQueue.id}');
      return updatedQueue;
    } catch (e) {
      print('[QueueProvider] Error updating queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return null;
    }
  }

  /// Delete a queue
  Future<bool> deleteQueue(int id) async {
    try {
      print('[QueueProvider] Deleting queue: $id');

      final repository = ref.read(queueRepositoryProvider);
      final success = await repository.deleteQueue(id);

      if (success) {
        // Remove from current state
        final updatedQueues =
            state.queues.where((queue) => queue.id != id).toList();

        // Rebuild grouped data
        final queuesByLocation = <int, List<Queue>>{};
        final queuesById = <int, Queue>{};

        for (final queue in updatedQueues) {
          queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
          queuesById[queue.id] = queue;
        }

        state = state.copyWith(
          queues: updatedQueues,
          queuesByLocation: queuesByLocation,
          queuesById: queuesById,
        );

        print('[QueueProvider] Deleted queue: $id');
      }

      return success;
    } catch (e) {
      print('[QueueProvider] Error deleting queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return false;
    }
  }

  /// Toggle queue active status
  Future<Queue?> toggleQueueStatus(int id, bool isActive) async {
    try {
      print('[QueueProvider] Toggling queue $id status to: $isActive');

      final repository = ref.read(queueRepositoryProvider);
      final updatedQueue = await repository.toggleQueueStatus(id, isActive);

      // Update in current state
      final updatedQueues =
          state.queues.map((queue) {
            return queue.id == id ? updatedQueue : queue;
          }).toList();

      // Rebuild grouped data
      final queuesByLocation = <int, List<Queue>>{};
      final queuesById = <int, Queue>{};

      for (final queue in updatedQueues) {
        queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
        queuesById[queue.id] = queue;
      }

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );

      print('[QueueProvider] Toggled queue status: ${updatedQueue.id}');
      return updatedQueue;
    } catch (e) {
      print('[QueueProvider] Error toggling queue status: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return null;
    }
  }

  /// Assign service to queue
  Future<bool> assignServiceToQueue(int queueId, int serviceId) async {
    try {
      print('[QueueProvider] Assigning service $serviceId to queue $queueId');

      final repository = ref.read(queueRepositoryProvider);
      final request = AssignServiceRequest(serviceId: serviceId);
      await repository.assignServiceToQueue(queueId, request);

      // Refresh the specific queue to get updated services
      await _refreshQueue(queueId);

      print('[QueueProvider] Assigned service to queue');
      return true;
    } catch (e) {
      print('[QueueProvider] Error assigning service to queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return false;
    }
  }

  /// Remove service from queue
  Future<bool> removeServiceFromQueue(int queueId, int serviceId) async {
    try {
      print('[QueueProvider] Removing service $serviceId from queue $queueId');

      final repository = ref.read(queueRepositoryProvider);
      await repository.removeServiceFromQueue(queueId, serviceId);

      // Refresh the specific queue to get updated services
      await _refreshQueue(queueId);

      print('[QueueProvider] Removed service from queue');
      return true;
    } catch (e) {
      print('[QueueProvider] Error removing service from queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return false;
    }
  }

  /// Update queue services
  Future<bool> updateQueueServices(int queueId, List<int> serviceIds) async {
    try {
      print('[QueueProvider] Updating queue $queueId services to: $serviceIds');

      final repository = ref.read(queueRepositoryProvider);
      final updatedQueue = await repository.updateQueueServices(
        queueId,
        serviceIds,
      );

      // Update in current state
      final updatedQueues =
          state.queues.map((queue) {
            return queue.id == queueId ? updatedQueue : queue;
          }).toList();

      // Rebuild grouped data
      final queuesByLocation = <int, List<Queue>>{};
      final queuesById = <int, Queue>{};

      for (final queue in updatedQueues) {
        queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
        queuesById[queue.id] = queue;
      }

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );

      print('[QueueProvider] Updated queue services');
      return true;
    } catch (e) {
      print('[QueueProvider] Error updating queue services: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return false;
    }
  }

  /// Get queues for a specific location
  List<Queue> getQueuesForLocation(int locationId) {
    return state.queuesByLocation[locationId] ?? [];
  }

  /// Get queue by ID
  Queue? getQueueById(int id) {
    return state.queuesById[id];
  }

  /// Get active queues for a location
  List<Queue> getActiveQueuesForLocation(int locationId) {
    return getQueuesForLocation(locationId).where((q) => q.isActive).toList();
  }

  /// Get inactive queues for a location
  List<Queue> getInactiveQueuesForLocation(int locationId) {
    return getQueuesForLocation(locationId).where((q) => !q.isActive).toList();
  }

  /// Search queues
  List<Queue> searchQueues(String query, {int? locationId}) {
    final queues =
        locationId != null ? getQueuesForLocation(locationId) : state.queues;

    return queues.where((queue) {
      return queue.title.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// Clear all queue data
  void clearQueues() {
    state = const QueueData(state: QueueState.initial);
  }

  /// Refresh queues
  Future<void> refreshQueues() async {
    await loadQueues(forceRefresh: true);
  }

  /// Refresh a specific queue
  Future<void> _refreshQueue(int queueId) async {
    try {
      final repository = ref.read(queueRepositoryProvider);
      final updatedQueue = await repository.getQueueById(queueId);

      // Update in current state
      final updatedQueues =
          state.queues.map((queue) {
            return queue.id == queueId ? updatedQueue : queue;
          }).toList();

      // Update grouped data
      final queuesByLocation = <int, List<Queue>>{};
      final queuesById = <int, Queue>{};

      for (final queue in updatedQueues) {
        queuesByLocation.putIfAbsent(queue.locationId, () => []).add(queue);
        queuesById[queue.id] = queue;
      }

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );
    } catch (e) {
      print('[QueueProvider] Error refreshing queue: $e');
    }
  }

  /// Load a single queue by ID
  Future<Queue?> loadQueueById(int id) async {
    try {
      print('[QueueProvider] Loading queue: $id');

      final repository = ref.read(queueRepositoryProvider);
      final queue = await repository.getQueueById(id);

      // Update state
      final updatedQueues = [...state.queues];
      final existingIndex = updatedQueues.indexWhere((q) => q.id == id);
      if (existingIndex >= 0) {
        updatedQueues[existingIndex] = queue;
      } else {
        updatedQueues.add(queue);
      }

      // Update grouped data
      final queuesByLocation = Map<int, List<Queue>>.from(
        state.queuesByLocation,
      );
      final locationQueues = queuesByLocation[queue.locationId] ?? [];
      final locationIndex = locationQueues.indexWhere((q) => q.id == id);
      if (locationIndex >= 0) {
        locationQueues[locationIndex] = queue;
      } else {
        locationQueues.add(queue);
      }
      queuesByLocation[queue.locationId] = locationQueues;

      final queuesById = Map<int, Queue>.from(state.queuesById);
      queuesById[queue.id] = queue;

      state = state.copyWith(
        queues: updatedQueues,
        queuesByLocation: queuesByLocation,
        queuesById: queuesById,
      );

      print('[QueueProvider] Loaded queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueProvider] Error loading queue: $e');
      state = state.copyWith(state: QueueState.error, error: e.toString());
      return null;
    }
  }
}
