/// Authentication request models
class LoginRequest {
  final String identifier; // email or phone
  final String password;

  const LoginRequest({required this.identifier, required this.password});

  Map<String, dynamic> toJson() {
    return {'identifier': identifier, 'password': password};
  }
}

class EmailOtpRequest {
  final String email;
  final String firstName;
  final String lastName;
  final String password;
  final bool isProviderRegistration;

  const EmailOtpRequest({
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.password,
    required this.isProviderRegistration,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'password': password,
      'isProviderRegistration': isProviderRegistration,
    };
  }
}

class ProviderRegistrationRequest {
  final String otp;
  final String identifier; // email or phone
  final String password;
  final String firstName;
  final String lastName;
  final int providerCategoryId;
  final String businessName;
  final String phone;
  final String email;

  const ProviderRegistrationRequest({
    required this.otp,
    required this.identifier,
    required this.password,
    required this.firstName,
    required this.lastName,
    required this.providerCategoryId,
    required this.businessName,
    required this.phone,
    required this.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'otp': otp,
      'identifier': identifier,
      'password': password,
      'firstName': firstName,
      'lastName': lastName,
      'providerCategoryId': providerCategoryId,
      'businessName': businessName,
      'phone': phone,
      'email': email,
    };
  }
}

// Remove ResendOtpRequest as it's not needed with the new API structure

/// Password reset request models
class PasswordResetRequest {
  final String email;

  const PasswordResetRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {'email': email};
  }
}

class PasswordResetOtpVerificationRequest {
  final String email;
  final String otp;

  const PasswordResetOtpVerificationRequest({
    required this.email,
    required this.otp,
  });

  Map<String, dynamic> toJson() {
    return {'email': email, 'otp': otp};
  }
}

class PasswordResetConfirmRequest {
  final String resetToken;
  final String newPassword;

  const PasswordResetConfirmRequest({
    required this.resetToken,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() {
    return {'resetToken': resetToken, 'newPassword': newPassword};
  }
}

/// Authentication response models
class AuthResponse {
  final bool success;
  final String message;
  final String? accessToken;
  final String? refreshToken;
  final UserData? user;
  final ProviderData? provider;

  const AuthResponse({
    required this.success,
    required this.message,
    this.accessToken,
    this.refreshToken,
    this.user,
    this.provider,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success:
          json['success'] ??
          true, // API doesn't return success field, assume true if no error
      message: json['message'] ?? 'Login successful',
      accessToken:
          json['sessionId'], // API returns sessionId instead of accessToken
      refreshToken: json['refreshToken'],
      user: json['user'] != null ? UserData.fromJson(json['user']) : null,
      provider:
          json['provider'] != null
              ? ProviderData.fromJson(json['provider'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'user': user?.toJson(),
      'provider': provider?.toJson(),
    };
  }
}

class EmailOtpResponse {
  final bool success;
  final String message;

  const EmailOtpResponse({required this.success, required this.message});

  factory EmailOtpResponse.fromJson(Map<String, dynamic> json) {
    return EmailOtpResponse(
      success: json['success'] ?? true, // API doesn't return success field
      message: json['message'] ?? 'OTP sent successfully',
    );
  }
}

/// Password reset response models
class PasswordResetResponse {
  final bool success;
  final String message;

  const PasswordResetResponse({required this.success, required this.message});

  factory PasswordResetResponse.fromJson(Map<String, dynamic> json) {
    return PasswordResetResponse(
      success: json['success'] ?? true,
      message:
          json['message'] ??
          'Password reset OTP sent successfully to your email address.',
    );
  }
}

class PasswordResetOtpVerificationResponse {
  final bool success;
  final String message;
  final String? resetToken;

  const PasswordResetOtpVerificationResponse({
    required this.success,
    required this.message,
    this.resetToken,
  });

  factory PasswordResetOtpVerificationResponse.fromJson(
    Map<String, dynamic> json,
  ) {
    return PasswordResetOtpVerificationResponse(
      success: json['success'] ?? true,
      message:
          json['message'] ??
          'OTP verified successfully. Use the reset token to set your new password.',
      resetToken: json['resetToken'],
    );
  }
}

class PasswordResetConfirmResponse {
  final bool success;
  final String message;

  const PasswordResetConfirmResponse({
    required this.success,
    required this.message,
  });

  factory PasswordResetConfirmResponse.fromJson(Map<String, dynamic> json) {
    return PasswordResetConfirmResponse(
      success: json['success'] ?? true,
      message:
          json['message'] ??
          'Password reset successfully. You can now log in with your new password.',
    );
  }
}

// OtpVerificationResponse is now the same as AuthResponse since registration completes with login

class UserData {
  final String id; // API returns UUID string, not int
  final String email;
  final String? phone;
  final String firstName;
  final String lastName;
  final String role;
  final bool emailVerified;
  final bool phoneVerified;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserData({
    required this.id,
    required this.email,
    this.phone,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.emailVerified = false,
    this.phoneVerified = false,
    this.createdAt,
    this.updatedAt,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id']?.toString() ?? '',
      email: json['email'] ?? '',
      phone: json['phone'],
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      role: json['role'] ?? 'CUSTOMER',
      emailVerified: json['emailVerified'] ?? false,
      phoneVerified: json['phoneVerified'] ?? false,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'firstName': firstName,
      'lastName': lastName,
      'role': role,
      'emailVerified': emailVerified,
      'phoneVerified': phoneVerified,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  String get fullName => '$firstName $lastName';
}

/// Provider data model
class ProviderData {
  final int id;
  final String userId;
  final String title;
  final String? phone;
  final int providerCategoryId;
  final bool isSetupComplete;
  final ProviderCategoryData? category;

  const ProviderData({
    required this.id,
    required this.userId,
    required this.title,
    this.phone,
    required this.providerCategoryId,
    required this.isSetupComplete,
    this.category,
  });

  factory ProviderData.fromJson(Map<String, dynamic> json) {
    return ProviderData(
      id: json['id'] ?? 0,
      userId: json['userId']?.toString() ?? '',
      title: json['title'] ?? '',
      phone: json['phone'],
      providerCategoryId: json['providerCategoryId'] ?? 0,
      isSetupComplete: json['isSetupComplete'] ?? false,
      category:
          json['category'] != null
              ? ProviderCategoryData.fromJson(json['category'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'phone': phone,
      'providerCategoryId': providerCategoryId,
      'isSetupComplete': isSetupComplete,
      'category': category?.toJson(),
    };
  }
}

/// Provider category data from API
class ProviderCategoryData {
  final int id;
  final String title;
  final int? parentId;

  const ProviderCategoryData({
    required this.id,
    required this.title,
    this.parentId,
  });

  factory ProviderCategoryData.fromJson(Map<String, dynamic> json) {
    return ProviderCategoryData(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      parentId: json['parentId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'title': title, 'parentId': parentId};
  }
}
