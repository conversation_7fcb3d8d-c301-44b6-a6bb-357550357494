import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/wizard_persistence_models.dart';
import '../models/onboarding_models.dart';
import '../providers/wizard_navigation_provider.dart';
import '../providers/onboarding_provider.dart';
import 'wizard_persistence_service.dart';

/// Service for automatic saving of wizard state
class WizardAutoSaveService {
  final WizardPersistenceService _persistenceService;
  final AutoSaveSettings _settings;
  final Ref _ref;
  
  Timer? _debounceTimer;
  bool _isEnabled = true;
  int _saveAttempts = 0;
  DateTime? _lastSaveTime;
  
  WizardAutoSaveService({
    required WizardPersistenceService persistenceService,
    required AutoSaveSettings settings,
    required Ref ref,
  }) : _persistenceService = persistenceService,
       _settings = settings,
       _ref = ref;

  /// Enable auto-save
  void enable() {
    _isEnabled = true;
    print('[WizardAutoSave] Auto-save enabled');
  }

  /// Disable auto-save
  void disable() {
    _isEnabled = false;
    _cancelPendingSave();
    print('[WizardAutoSave] Auto-save disabled');
  }

  /// Check if auto-save is enabled
  bool get isEnabled => _isEnabled && _settings.enabled;

  /// Get last save time
  DateTime? get lastSaveTime => _lastSaveTime;

  /// Trigger auto-save with debouncing
  void triggerAutoSave({
    String? reason,
    bool immediate = false,
  }) {
    if (!isEnabled) {
      return;
    }

    print('[WizardAutoSave] Auto-save triggered: ${reason ?? 'unknown reason'}');

    if (immediate) {
      _performSave(reason: reason);
    } else {
      _scheduleDebounced(reason: reason);
    }
  }

  /// Save on field change (debounced)
  void onFieldChange(String fieldName, dynamic value) {
    if (!_settings.saveOnFieldChange) {
      return;
    }

    triggerAutoSave(reason: 'field_change:$fieldName');
  }

  /// Save on step change (immediate)
  void onStepChange(OnboardingStep fromStep, OnboardingStep toStep) {
    if (!_settings.saveOnStepChange) {
      return;
    }

    triggerAutoSave(
      reason: 'step_change:${fromStep.name}->${toStep.name}',
      immediate: true,
    );
  }

  /// Save on step completion (immediate)
  void onStepComplete(OnboardingStep step) {
    if (!_settings.saveOnStepComplete) {
      return;
    }

    triggerAutoSave(
      reason: 'step_complete:${step.name}',
      immediate: true,
    );
  }

  /// Force save immediately
  Future<PersistenceResult> forceSave({String? reason}) async {
    return await _performSave(reason: reason ?? 'force_save');
  }

  /// Schedule debounced save
  void _scheduleDebounced({String? reason}) {
    _cancelPendingSave();
    
    _debounceTimer = Timer(
      Duration(milliseconds: _settings.debounceDelayMs),
      () => _performSave(reason: reason),
    );
  }

  /// Cancel pending save
  void _cancelPendingSave() {
    _debounceTimer?.cancel();
    _debounceTimer = null;
  }

  /// Perform the actual save operation
  Future<PersistenceResult> _performSave({String? reason}) async {
    try {
      // Reset save attempts for new save operation
      _saveAttempts = 0;
      
      return await _attemptSave(reason: reason);
    } catch (e) {
      print('[WizardAutoSave] Error in save operation: $e');
      return PersistenceResult(
        success: false,
        error: 'Save operation failed: $e',
      );
    }
  }

  /// Attempt save with retry logic
  Future<PersistenceResult> _attemptSave({String? reason}) async {
    _saveAttempts++;
    
    try {
      // Get current state
      final navigationState = _ref.read(wizardNavigationProvider);
      final onboardingState = _ref.read(onboardingNotifierProvider);
      
      if (onboardingState.data == null) {
        return const PersistenceResult(
          success: false,
          error: 'No onboarding data to save',
        );
      }

      // Create session info
      final sessionInfo = PersistenceModelFactory.createSessionInfo(
        userId: null, // TODO: Get from auth provider
        deviceInfo: _getDeviceInfo(),
        sessionDuration: const Duration(hours: 24),
        isGuest: true, // TODO: Get from auth provider
      );

      // Convert to persistable state
      final persistedState = WizardStateConverter.fromCurrentState(
        navigationState: navigationState,
        onboardingData: onboardingState.data!,
        sessionInfo: sessionInfo,
        autoSaveSettings: _settings,
      );

      // Save state
      final result = await _persistenceService.saveState(persistedState);
      
      if (result.success) {
        _lastSaveTime = DateTime.now();
        _saveAttempts = 0;
        print('[WizardAutoSave] Save successful: ${reason ?? 'auto-save'}');
      } else {
        print('[WizardAutoSave] Save failed (attempt $_saveAttempts): ${result.error}');
        
        // Retry if we haven't exceeded max attempts
        if (_saveAttempts < _settings.maxRetries) {
          print('[WizardAutoSave] Retrying save in 1 second...');
          await Future.delayed(const Duration(seconds: 1));
          return await _attemptSave(reason: reason);
        }
      }
      
      return result;
    } catch (e) {
      print('[WizardAutoSave] Save attempt failed: $e');
      
      // Retry if we haven't exceeded max attempts
      if (_saveAttempts < _settings.maxRetries) {
        print('[WizardAutoSave] Retrying save in 1 second...');
        await Future.delayed(const Duration(seconds: 1));
        return await _attemptSave(reason: reason);
      }
      
      return PersistenceResult(
        success: false,
        error: 'Save failed after $_saveAttempts attempts: $e',
      );
    }
  }

  /// Get device/browser information
  String _getDeviceInfo() {
    try {
      // Basic browser information
      final userAgent = 'Flutter Web'; // In a real app, you'd get actual user agent
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'flutter_web_$timestamp';
    } catch (e) {
      return 'unknown_device';
    }
  }

  /// Dispose resources
  void dispose() {
    _cancelPendingSave();
    print('[WizardAutoSave] Auto-save service disposed');
  }
}

/// Provider for auto-save service
final wizardAutoSaveServiceProvider = Provider<WizardAutoSaveService>((ref) {
  final persistenceService = WizardPersistenceServiceFactory.getInstance();
  const settings = AutoSaveSettings(); // Use default settings
  
  final service = WizardAutoSaveService(
    persistenceService: persistenceService,
    settings: settings,
    ref: ref,
  );

  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Mixin for widgets that want to trigger auto-save
mixin AutoSaveMixin {
  /// Trigger auto-save on field change
  void triggerAutoSaveOnFieldChange(WidgetRef ref, String fieldName, dynamic value) {
    final autoSaveService = ref.read(wizardAutoSaveServiceProvider);
    autoSaveService.onFieldChange(fieldName, value);
  }

  /// Trigger auto-save on step change
  void triggerAutoSaveOnStepChange(WidgetRef ref, OnboardingStep fromStep, OnboardingStep toStep) {
    final autoSaveService = ref.read(wizardAutoSaveServiceProvider);
    autoSaveService.onStepChange(fromStep, toStep);
  }

  /// Trigger auto-save on step completion
  void triggerAutoSaveOnStepComplete(WidgetRef ref, OnboardingStep step) {
    final autoSaveService = ref.read(wizardAutoSaveServiceProvider);
    autoSaveService.onStepComplete(step);
  }

  /// Force save immediately
  Future<PersistenceResult> forceSave(WidgetRef ref, {String? reason}) async {
    final autoSaveService = ref.read(wizardAutoSaveServiceProvider);
    return await autoSaveService.forceSave(reason: reason);
  }
}

/// Extension for easy auto-save integration
extension AutoSaveExtension on WidgetRef {
  /// Get auto-save service
  WizardAutoSaveService get autoSave => read(wizardAutoSaveServiceProvider);

  /// Trigger auto-save
  void triggerAutoSave({String? reason, bool immediate = false}) {
    autoSave.triggerAutoSave(reason: reason, immediate: immediate);
  }

  /// Save on field change
  void saveOnFieldChange(String fieldName, dynamic value) {
    autoSave.onFieldChange(fieldName, value);
  }

  /// Save on step change
  void saveOnStepChange(OnboardingStep fromStep, OnboardingStep toStep) {
    autoSave.onStepChange(fromStep, toStep);
  }

  /// Save on step completion
  void saveOnStepComplete(OnboardingStep step) {
    autoSave.onStepComplete(step);
  }
}
