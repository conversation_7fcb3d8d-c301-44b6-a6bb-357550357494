import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import '../../../core/theme/app_colors.dart';

/// Full-screen appointment details screen
class AppointmentDetailsScreen extends ConsumerStatefulWidget {
  final Appointment appointment;

  const AppointmentDetailsScreen({super.key, required this.appointment});

  @override
  ConsumerState<AppointmentDetailsScreen> createState() =>
      _AppointmentDetailsScreenState();
}

class _AppointmentDetailsScreenState
    extends ConsumerState<AppointmentDetailsScreen> {
  bool _isStartingSession = false;
  bool _isCompleting = false;
  bool _isCanceling = false;
  bool _isConfirming = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: const Text('Appointment Details'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: Column(
        children: [
          // Status Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: _getStatusColor(widget.appointment.status),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  _getStatusIcon(widget.appointment.status),
                  size: 48,
                  color: Colors.white,
                ),
                const SizedBox(height: 8),
                Text(
                  _getStatusText(widget.appointment.status),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailCard(
                    context,
                    'Customer',
                    widget.appointment.customerName,
                    Icons.person,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailCard(
                    context,
                    'Service',
                    widget.appointment.serviceName,
                    Icons.work,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailCard(
                    context,
                    'Scheduled Time',
                    _formatDateTime(widget.appointment.scheduledTime),
                    Icons.schedule,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailCard(
                    context,
                    'Duration',
                    '${widget.appointment.duration} minutes',
                    Icons.timer,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailCard(
                    context,
                    'Location',
                    widget.appointment.locationName ?? 'Primary Location',
                    Icons.location_on,
                  ),
                  if (widget.appointment.notes?.isNotEmpty == true) ...[
                    const SizedBox(height: 16),
                    _buildDetailCard(
                      context,
                      'Notes',
                      widget.appointment.notes!,
                      Icons.note,
                    ),
                  ],
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Actions
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: _buildActions(context),
          ),
        ],
      ),
    );
  }

  /// Build detail card widget
  Widget _buildDetailCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: AppColors.primary, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build actions based on appointment status
  Widget _buildActions(BuildContext context) {
    switch (widget.appointment.status) {
      case AppointmentStatus.pending:
        return _buildPendingActions(context);
      case AppointmentStatus.confirmed:
        return _buildConfirmedActions(context);
      case AppointmentStatus.inProgress:
        return _buildInProgressActions(context);
      default:
        return _buildDefaultActions(context);
    }
  }

  /// Actions for pending appointments
  Widget _buildPendingActions(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            // Confirm button
            Expanded(
              child: ElevatedButton(
                onPressed:
                    _isConfirming ? null : () => _onConfirmAppointment(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isConfirming
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Confirming...',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        )
                        : const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.check, size: 20),
                            SizedBox(width: 8),
                            Text('Accept', style: TextStyle(fontSize: 16)),
                          ],
                        ),
              ),
            ),
            const SizedBox(width: 16),
            // Cancel button
            Expanded(
              child: OutlinedButton(
                onPressed: _isCanceling ? null : () => _onCancel(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isCanceling
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.red,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Canceling...',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        )
                        : const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.cancel, size: 20),
                            SizedBox(width: 8),
                            Text('Cancel', style: TextStyle(fontSize: 16)),
                          ],
                        ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Actions for confirmed appointments
  Widget _buildConfirmedActions(BuildContext context) {
    return Column(
      children: [
        // Primary action - Start Session
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed:
                _isStartingSession ? null : () => _onStartSession(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child:
                _isStartingSession
                    ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('Starting...', style: TextStyle(fontSize: 16)),
                      ],
                    )
                    : const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.play_arrow, size: 20),
                        SizedBox(width: 8),
                        Text('Start Session', style: TextStyle(fontSize: 16)),
                      ],
                    ),
          ),
        ),
        const SizedBox(height: 12),
        // Secondary actions
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => _onReschedule(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange,
                  side: const BorderSide(color: Colors.orange),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.schedule, size: 18),
                    SizedBox(width: 6),
                    Text('Reschedule', style: TextStyle(fontSize: 14)),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton(
                onPressed: _isCanceling ? null : () => _onCancel(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                child:
                    _isCanceling
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 14,
                              height: 14,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.red,
                                ),
                              ),
                            ),
                            SizedBox(width: 6),
                            Text(
                              'Canceling...',
                              style: TextStyle(fontSize: 14),
                            ),
                          ],
                        )
                        : const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.cancel, size: 18),
                            SizedBox(width: 6),
                            Text('Cancel', style: TextStyle(fontSize: 14)),
                          ],
                        ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Actions for in-progress appointments
  Widget _buildInProgressActions(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () => _onContinueSession(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.play_arrow, size: 20),
                    SizedBox(width: 8),
                    Text('Continue Session', style: TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: OutlinedButton(
                onPressed: _isCompleting ? null : () => _onSetComplete(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue,
                  side: const BorderSide(color: Colors.blue),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isCompleting
                        ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.blue,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Completing...',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        )
                        : const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.check_circle, size: 20),
                            SizedBox(width: 8),
                            Text(
                              'Set Complete',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Default actions for other statuses
  Widget _buildDefaultActions(BuildContext context) {
    return const SizedBox.shrink(); // No actions for other statuses
  }

  /// Handle start session action - follows same process as QR code scanning
  Future<void> _onStartSession(BuildContext context) async {
    if (_isStartingSession) return;

    setState(() {
      _isStartingSession = true;
    });

    try {
      print(
        '[AppointmentDetails] Starting session for appointment: ${widget.appointment.id}',
      );

      // Update appointment status to InProgress (same as QR code scanning)
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            widget.appointment.id,
            AppointmentStatus.inProgress,
          );

      print(
        '[AppointmentDetails] Status updated successfully, navigating to service screen',
      );

      // Navigate to service screen (same as QR code scanning)
      if (mounted) {
        context.go('/service/${widget.appointment.id}');
      }
    } catch (e, stackTrace) {
      print('[AppointmentDetails] Error starting session: $e');
      print('[AppointmentDetails] Stack trace: $stackTrace');

      if (mounted) {
        String errorMessage = 'Failed to start session.';

        // Provide more specific error messages
        if (e.toString().contains('Network')) {
          errorMessage =
              'Network error. Please check your connection and try again.';
        } else if (e.toString().contains('STATUS_UPDATE_SUCCESS_NO_DATA')) {
          // This is actually a success case, continue to navigation
          print(
            '[AppointmentDetails] Status update succeeded, continuing to navigation',
          );
          context.go('/service/${widget.appointment.id}');
          return;
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isStartingSession = false;
        });
      }
    }
  }

  /// Handle reschedule action
  void _onReschedule(BuildContext context) {
    context.push(
      '/appointments/${widget.appointment.id}/edit',
      extra: widget.appointment,
    );
  }

  /// Handle cancel action
  void _onCancel(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cancel Appointment'),
            content: Text(
              'Are you sure you want to cancel the appointment with ${widget.appointment.customerName}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('No'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _confirmCancel(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Yes, Cancel'),
              ),
            ],
          ),
    );
  }

  /// Handle continue session action
  void _onContinueSession(BuildContext context) {
    context.push(
      '/service/${widget.appointment.id}',
      extra: widget.appointment,
    );
  }

  /// Handle confirm appointment action
  Future<void> _onConfirmAppointment(BuildContext context) async {
    if (_isConfirming) return;

    setState(() {
      _isConfirming = true;
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            widget.appointment.id,
            AppointmentStatus.confirmed,
          );

      if (mounted) {
        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Appointment confirmed for ${widget.appointment.customerName}',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to appointments screen and refresh
        context.go('/appointments');
      }
    } catch (e) {
      print('[AppointmentDetails] Error confirming appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to confirm appointment. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isConfirming = false;
        });
      }
    }
  }

  /// Handle set complete action
  void _onSetComplete(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Complete Appointment'),
            content: Text(
              'Mark the appointment with ${widget.appointment.customerName} as completed?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _confirmComplete(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Complete'),
              ),
            ],
          ),
    );
  }

  /// Confirm cancel action
  Future<void> _confirmCancel(BuildContext context) async {
    if (_isCanceling) return;

    setState(() {
      _isCanceling = true;
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            widget.appointment.id,
            AppointmentStatus.canceled,
          );

      if (mounted) {
        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Appointment with ${widget.appointment.customerName} canceled',
            ),
            backgroundColor: Colors.orange,
          ),
        );

        // Navigate back to appointments screen and refresh
        context.go('/appointments');
      }
    } catch (e) {
      print('[AppointmentDetails] Error canceling appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to cancel appointment. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCanceling = false;
        });
      }
    }
  }

  /// Confirm complete action
  Future<void> _confirmComplete(BuildContext context) async {
    if (_isCompleting) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            widget.appointment.id,
            AppointmentStatus.completed,
          );

      if (mounted) {
        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment completed successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to dashboard screen and refresh
        context.go('/dashboard');
      }
    } catch (e) {
      print('[AppointmentDetails] Error completing appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to complete appointment. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCompleting = false;
        });
      }
    }
  }

  /// Get status color
  Color _getStatusColor(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.confirmed:
        return Colors.green;
      case AppointmentStatus.pending:
        return Colors.orange;
      case AppointmentStatus.inProgress:
        return Colors.blue;
      case AppointmentStatus.completed:
        return Colors.teal;
      case AppointmentStatus.canceled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.confirmed:
        return Icons.check_circle;
      case AppointmentStatus.pending:
        return Icons.schedule;
      case AppointmentStatus.inProgress:
        return Icons.play_circle;
      case AppointmentStatus.completed:
        return Icons.task_alt;
      case AppointmentStatus.canceled:
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  /// Get status text
  String _getStatusText(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.confirmed:
        return 'Confirmed';
      case AppointmentStatus.pending:
        return 'Pending';
      case AppointmentStatus.inProgress:
        return 'In Progress';
      case AppointmentStatus.completed:
        return 'Completed';
      case AppointmentStatus.canceled:
        return 'Canceled';
      default:
        return 'Unknown';
    }
  }

  /// Format date time
  String _formatDateTime(DateTime dateTime) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    final month = months[dateTime.month - 1];
    final day = dateTime.day.toString().padLeft(2, '0');
    final year = dateTime.year;
    final hour =
        dateTime.hour == 0
            ? 12
            : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';

    return '$month $day, $year • $hour:$minute $period';
  }
}
