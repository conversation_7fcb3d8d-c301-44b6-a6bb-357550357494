import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../../appointments/providers/appointment_provider.dart';
import '../../appointments/models/appointment_models.dart';
import '../../../core/providers/app_providers.dart';

/// QR Scanner Screen
/// This screen will handle QR code scanning functionality
class QRScannerScreen extends ConsumerStatefulWidget {
  const QRScannerScreen({super.key});

  @override
  ConsumerState<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends ConsumerState<QRScannerScreen> {
  bool _isProcessing = false;
  bool _hasScanned = false; // Track if we've already scanned something
  MobileScannerController? _scannerController;

  @override
  void initState() {
    super.initState();
    _scannerController = MobileScannerController();
  }

  @override
  void dispose() {
    _scannerController?.dispose();
    super.dispose();
  }

  /// Stop and dispose camera properly
  Future<void> _stopCamera() async {
    try {
      await _scannerController?.stop();
      _scannerController?.dispose();
    } catch (e) {
      print('[QRScanner] Error stopping camera: $e');
    }
  }

  void _onDetect(BarcodeCapture capture) {
    // Don't process if we've already scanned something or are currently processing
    if (_hasScanned || _isProcessing) {
      return;
    }

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isNotEmpty) {
      final String? code = barcodes.first.rawValue;
      if (code != null) {
        setState(() {
          _hasScanned = true; // Mark as scanned to prevent further scanning
        });
        _handleQRCodeScanned(code);
      }
    }
  }

  /// Reset the scanner to allow scanning again
  void _resetScanner() {
    setState(() {
      _hasScanned = false;
      _isProcessing = false;
    });
  }

  /// Parse QR code in format: ${appointment.id}-${appointment.serviceName}
  /// Returns the appointment ID if valid, null otherwise
  String? _parseAppointmentQRCode(String qrData) {
    try {
      // Expected format: appointmentId-serviceName
      final parts = qrData.split('-');
      if (parts.length >= 2) {
        final appointmentId = parts[0];
        // Validate that the first part looks like an appointment ID
        if (appointmentId.isNotEmpty) {
          return appointmentId;
        }
      }
      return null;
    } catch (e) {
      print('[QRScanner] Error parsing QR code: $e');
      return null;
    }
  }

  /// Handle scanned QR code
  Future<void> _handleQRCodeScanned(String qrData) async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      print('[QRScanner] Scanned QR data: "$qrData"');

      final appointmentId = _parseAppointmentQRCode(qrData);
      print('[QRScanner] Parsed appointment ID: "$appointmentId"');

      if (appointmentId == null) {
        _showErrorDialog(
          'Invalid QR Code',
          'QR code format should be: appointmentId-serviceName\nScanned: "$qrData"',
        );
        return;
      }

      // Check authentication status first
      final jwtService = ref.read(jwtServiceProvider);
      final isAuthenticated = jwtService.isAuthenticated;
      print('[QRScanner] Authentication status: $isAuthenticated');

      if (!isAuthenticated) {
        throw Exception('User not authenticated. Please log in again.');
      }

      // First, verify the appointment exists
      print('[QRScanner] Verifying appointment exists: $appointmentId');
      Appointment? appointment;

      try {
        // Try direct lookup first
        final repository = ref.read(appointmentRepositoryProvider);
        appointment = await repository.getAppointment(appointmentId);
        print(
          '[QRScanner] Found appointment by ID: ${appointment.customerName} - ${appointment.serviceName}',
        );
      } catch (e) {
        print('[QRScanner] Direct appointment lookup failed: $e');

        // Fallback: Search through all appointments
        try {
          print('[QRScanner] Searching through all appointments...');
          await ref
              .read(appointmentNotifierProvider.notifier)
              .loadAppointments();
          final appointmentsAsync = ref.read(appointmentNotifierProvider);

          if (appointmentsAsync.hasValue) {
            final appointments = appointmentsAsync.value!;
            print('[QRScanner] Loaded ${appointments.length} appointments');

            // Try to find by ID or any other matching field
            appointment = appointments.firstWhere(
              (apt) =>
                  apt.id == appointmentId ||
                  apt.serviceId == appointmentId ||
                  apt.customerId == appointmentId,
              orElse: () => throw Exception('Appointment not found in list'),
            );

            print(
              '[QRScanner] Found appointment in list: ${appointment.customerName} - ${appointment.serviceName}',
            );
          } else {
            throw Exception('Failed to load appointments');
          }
        } catch (e2) {
          print('[QRScanner] Appointment search in list also failed: $e2');
          throw Exception('Appointment not found: $appointmentId');
        }
      }

      print('[QRScanner] Current status: ${appointment.status.name}');
      // Use the actual appointment ID for status update
      final actualAppointmentId = appointment.id;
      print(
        '[QRScanner] Using appointment ID for update: $actualAppointmentId',
      );

      print(
        '[QRScanner] Updating appointment status for ID: $actualAppointmentId',
      );
      print('[QRScanner] Status enum: ${AppointmentStatus.inProgress.name}');
      print('[QRScanner] Status will be sent as: InProgress (API format)');

      // Update appointment status to InProgress
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            actualAppointmentId,
            AppointmentStatus.inProgress,
          );

      print(
        '[QRScanner] Status updated successfully, navigating to service screen',
      );

      // Navigate to service screen and close QR scanner
      if (mounted) {
        // Stop and dispose camera before navigation
        await _stopCamera();

        // Check mounted again after async operation
        if (mounted) {
          // Use go instead of push to replace the current route and close QR scanner
          context.go('/service/$actualAppointmentId');
        }
      }
    } catch (e, stackTrace) {
      print('[QRScanner] Error processing QR code: $e');
      print('[QRScanner] Stack trace: $stackTrace');

      String errorMessage = 'Failed to process appointment QR code.';

      // Provide more specific error messages
      if (e.toString().contains('Appointment not found')) {
        errorMessage = 'Appointment not found. Please check the QR code.';
      } else if (e.toString().contains('Network')) {
        errorMessage =
            'Network error. Please check your connection and try again.';
      } else if (e.toString().contains('STATUS_UPDATE_SUCCESS_NO_DATA')) {
        // This is actually a success case, continue to navigation
        print('[QRScanner] Status update succeeded, continuing to navigation');
        if (mounted) {
          final appointmentId = _parseAppointmentQRCode(qrData);
          if (appointmentId != null) {
            await _stopCamera();
            if (mounted) {
              context.go('/service/$appointmentId');
            }
            return;
          }
        }
      }

      _showErrorDialog('Error', '$errorMessage\n\nDetails: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _resetScanner(); // Reset scanner to allow scanning again
                },
                child: const Text('Try Again'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Appointment QR'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.pop(),
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            // Scanner area
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(color: Colors.black),
                child: Stack(
                  children: [
                    // QR Scanner Camera View
                    MobileScanner(
                      controller: _scannerController,
                      onDetect: _onDetect,
                    ),

                    // Scanning overlay
                    Center(
                      child: Container(
                        width: 250,
                        height: 250,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: _hasScanned ? Colors.orange : Colors.green,
                            width: 4,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),

                    // Scanning status overlay
                    if (_hasScanned)
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.check_circle,
                                color: Colors.green,
                                size: 48,
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'QR Code Scanned',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Text(
                                'Processing...',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Instructions and controls
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              color: Colors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Scan Customer Appointment QR Code',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    'Position the QR code within the frame',
                    style: TextStyle(fontSize: 12, color: Colors.black54),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  // Control buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      // Reset scanner button
                      if (_hasScanned)
                        ElevatedButton.icon(
                          onPressed: _resetScanner,
                          icon: const Icon(Icons.refresh, size: 16),
                          label: const Text(
                            'Scan Again',
                            style: TextStyle(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                          ),
                        ),

                      // Test with existing appointment (only show if not scanned)
                      if (!_hasScanned)
                        ElevatedButton(
                          onPressed:
                              _isProcessing
                                  ? null
                                  : () {
                                    _handleQRCodeScanned('14-TestService');
                                  },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                          ),
                          child: const Text(
                            'Test ID 14',
                            style: TextStyle(fontSize: 11),
                          ),
                        ),

                      // Test with different format (only show if not scanned)
                      if (!_hasScanned)
                        ElevatedButton(
                          onPressed:
                              _isProcessing
                                  ? null
                                  : () {
                                    _handleQRCodeScanned(
                                      'appointment-123-service',
                                    );
                                  },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                          ),
                          child: const Text(
                            'Test Format',
                            style: TextStyle(fontSize: 11),
                          ),
                        ),
                    ],
                  ),

                  if (_isProcessing)
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
