import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/dashboard/models/dashboard_models.dart';

void main() {
  group('Dashboard Models Tests', () {
    group('BusinessMetrics', () {
      test('should create BusinessMetrics with all required fields', () {
        // Arrange & Act
        final businessMetrics = BusinessMetrics(
          todayRevenue: 1250.50,
          todayAppointments: 15,
          completedAppointments: 12,
          activeQueues: 3,
          totalCustomersToday: 45,
          customerSatisfaction: 4.5,
          revenueChange: 12.5,
        );

        // Assert
        expect(businessMetrics.todayRevenue, 1250.50);
        expect(businessMetrics.todayAppointments, 15);
        expect(businessMetrics.completedAppointments, 12);
        expect(businessMetrics.activeQueues, 3);
        expect(businessMetrics.totalCustomersToday, 45);
        expect(businessMetrics.customerSatisfaction, 4.5);
        expect(businessMetrics.revenueChange, 12.5);
      });

      test('should serialize to JSON correctly', () {
        // Arrange
        final businessMetrics = BusinessMetrics(
          todayRevenue: 1000.0,
          todayAppointments: 10,
          completedAppointments: 8,
          activeQueues: 2,
          totalCustomersToday: 30,
          customerSatisfaction: 4.2,
          revenueChange: 5.0,
        );

        // Act
        final json = businessMetrics.toJson();

        // Assert
        expect(json['todayRevenue'], 1000.0);
        expect(json['todayAppointments'], 10);
        expect(json['completedAppointments'], 8);
        expect(json['activeQueues'], 2);
        expect(json['totalCustomersToday'], 30);
        expect(json['customerSatisfaction'], 4.2);
        expect(json['revenueChange'], 5.0);
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'todayRevenue': 1500.75,
          'todayAppointments': 20,
          'completedAppointments': 18,
          'activeQueues': 4,
          'totalCustomersToday': 60,
          'customerSatisfaction': 4.8,
          'revenueChange': -2.3,
        };

        // Act
        final businessMetrics = BusinessMetrics.fromJson(json);

        // Assert
        expect(businessMetrics.todayRevenue, 1500.75);
        expect(businessMetrics.todayAppointments, 20);
        expect(businessMetrics.completedAppointments, 18);
        expect(businessMetrics.activeQueues, 4);
        expect(businessMetrics.totalCustomersToday, 60);
        expect(businessMetrics.customerSatisfaction, 4.8);
        expect(businessMetrics.revenueChange, -2.3);
      });
    });

    group('NotificationItem', () {
      test('should create NotificationItem with all fields', () {
        // Arrange & Act
        final notification = NotificationItem(
          id: 'notif_123',
          title: 'New Appointment',
          message: 'John Doe has booked an appointment',
          type: NotificationType.newCustomer,
          timestamp: DateTime(2024, 1, 15, 10, 30),
          isRead: false,
        );

        // Assert
        expect(notification.id, 'notif_123');
        expect(notification.title, 'New Appointment');
        expect(notification.message, 'John Doe has booked an appointment');
        expect(notification.type, NotificationType.newCustomer);
        expect(notification.timestamp, DateTime(2024, 1, 15, 10, 30));
        expect(notification.isRead, false);
      });

      test('should serialize to JSON correctly', () {
        // Arrange
        final notification = NotificationItem(
          id: 'notif_456',
          title: 'Reschedule Request',
          message: 'Customer wants to reschedule',
          type: NotificationType.rescheduleRequest,
          timestamp: DateTime(2024, 1, 15, 14, 45),
          isRead: true,
        );

        // Act
        final json = notification.toJson();

        // Assert
        expect(json['id'], 'notif_456');
        expect(json['title'], 'Reschedule Request');
        expect(json['message'], 'Customer wants to reschedule');
        expect(json['type'], 'reschedule_request');
        expect(json['timestamp'], '2024-01-15T14:45:00.000');
        expect(json['isRead'], true);
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'id': 'notif_789',
          'title': 'System Alert',
          'message': 'Queue capacity reached',
          'type': 'queue_alert',
          'timestamp': '2024-01-15T16:20:00.000',
          'isRead': false,
        };

        // Act
        final notification = NotificationItem.fromJson(json);

        // Assert
        expect(notification.id, 'notif_789');
        expect(notification.title, 'System Alert');
        expect(notification.message, 'Queue capacity reached');
        expect(notification.type, NotificationType.queueAlert);
        expect(notification.timestamp, DateTime(2024, 1, 15, 16, 20));
        expect(notification.isRead, false);
      });
    });

    group('QueueStatus', () {
      test('should create QueueStatus with all fields', () {
        // Arrange & Act
        final queueStatus = QueueStatus(
          queueId: 'queue_123',
          queueName: 'Main Queue',
          locationName: 'Main Location',
          waitingCount: 5,
          averageWaitTime: 15.0,
        );

        // Assert
        expect(queueStatus.queueId, 'queue_123');
        expect(queueStatus.queueName, 'Main Queue');
        expect(queueStatus.locationName, 'Main Location');
        expect(queueStatus.waitingCount, 5);
        expect(queueStatus.averageWaitTime, 15.0);
      });

      test('should handle inactive queue', () {
        // Arrange & Act
        final queueStatus = QueueStatus(
          queueId: 'queue_456',
          queueName: 'Secondary Queue',
          locationName: 'Secondary Location',
          waitingCount: 0,
          averageWaitTime: 0.0,
        );

        // Assert
        expect(queueStatus.waitingCount, 0);
        expect(queueStatus.averageWaitTime, 0.0);
      });
    });

    group('EmergencyQueueControl', () {
      test('should create EmergencyQueueControl with required fields', () {
        // Arrange
        final timestamp = DateTime(2024, 1, 15, 12, 0);

        // Act
        final control = EmergencyQueueControl(
          action: 'pause',
          reason: 'Emergency maintenance',
          timestamp: timestamp,
          estimatedDuration: 30,
        );

        // Assert
        expect(control.action, 'pause');
        expect(control.reason, 'Emergency maintenance');
        expect(control.timestamp, timestamp);
        expect(control.estimatedDuration, 30);
      });

      test('should create EmergencyQueueControl without optional duration', () {
        // Arrange
        final timestamp = DateTime(2024, 1, 15, 12, 0);

        // Act
        final control = EmergencyQueueControl(
          action: 'resume',
          reason: 'Maintenance completed',
          timestamp: timestamp,
        );

        // Assert
        expect(control.action, 'resume');
        expect(control.reason, 'Maintenance completed');
        expect(control.timestamp, timestamp);
        expect(control.estimatedDuration, null);
      });
    });

    group('QuickStats', () {
      test('should create QuickStats with all fields', () {
        // Arrange & Act
        final quickStats = QuickStats(
          currentWaitingCustomers: 12,
          unreadNotifications: 3,
          activeQueues: 8,
          todayRevenue: 2500.0,
        );

        // Assert
        expect(quickStats.currentWaitingCustomers, 12);
        expect(quickStats.unreadNotifications, 3);
        expect(quickStats.activeQueues, 8);
        expect(quickStats.todayRevenue, 2500.0);
      });

      test('should handle zero values', () {
        // Arrange & Act
        final quickStats = QuickStats(
          currentWaitingCustomers: 0,
          unreadNotifications: 0,
          activeQueues: 0,
          todayRevenue: 0.0,
        );

        // Assert
        expect(quickStats.currentWaitingCustomers, 0);
        expect(quickStats.unreadNotifications, 0);
        expect(quickStats.activeQueues, 0);
        expect(quickStats.todayRevenue, 0.0);
      });
    });

    group('DashboardError', () {
      test('should create DashboardError with all fields', () {
        // Arrange & Act
        final error = DashboardError(
          code: 'NETWORK_ERROR',
          message: 'Failed to connect to server',
          details: 'Connection timeout after 30 seconds',
        );

        // Assert
        expect(error.code, 'NETWORK_ERROR');
        expect(error.message, 'Failed to connect to server');
        expect(error.details, 'Connection timeout after 30 seconds');
      });

      test('should create DashboardError without optional details', () {
        // Arrange & Act
        final error = DashboardError(
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
        );

        // Assert
        expect(error.code, 'VALIDATION_ERROR');
        expect(error.message, 'Invalid input data');
        expect(error.details, null);
      });
    });
  });
}
