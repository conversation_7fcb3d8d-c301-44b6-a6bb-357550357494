import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/models/provider_category.dart';

void main() {
  group('Category Validation Tests', () {
    test('Child categories should be selectable by default', () {
      // Create a child category (has parentId)
      const childCategory = ProviderCategory(
        id: 101,
        name: 'Medical Clinics',
        description: 'General practice, family medicine',
        icon: '🩺',
        parentId: 1, // This makes it a child category
        // isSelectable defaults to true
      );

      expect(childCategory.isSelectable, isTrue);
      expect(childCategory.isChild, isTrue);
      expect(childCategory.isParent, isFalse);
    });

    test('Parent categories should not be selectable', () {
      // Create a parent category (no parentId, has children)
      const parentCategory = ProviderCategory(
        id: 1,
        name: 'Healthcare',
        description: 'Medical and health services',
        icon: '🏥',
        isSelectable: false,
        children: [
          ProviderCategory(
            id: 101,
            name: 'Medical Clinics',
            description: 'General practice, family medicine',
            icon: '🩺',
            parentId: 1,
          ),
        ],
      );

      expect(parentCategory.isSelectable, isFalse);
      expect(parentCategory.isChild, isFalse);
      expect(parentCategory.isParent, isTrue);
      expect(parentCategory.children.length, equals(1));
    });

    test('Hardcoded categories have correct selectable properties', () {
      final parentCategories = ProviderCategories.getParentCategories();
      final selectableCategories = ProviderCategories.getSelectableCategories();

      // All parent categories should not be selectable
      for (final parent in parentCategories) {
        expect(parent.isSelectable, isFalse, 
            reason: 'Parent category "${parent.name}" should not be selectable');
        expect(parent.isParent, isTrue,
            reason: 'Parent category "${parent.name}" should have children');
      }

      // All selectable categories should be child categories
      for (final selectable in selectableCategories) {
        expect(selectable.isSelectable, isTrue,
            reason: 'Selectable category "${selectable.name}" should be selectable');
        expect(selectable.isChild, isTrue,
            reason: 'Selectable category "${selectable.name}" should be a child category');
        expect(selectable.parentId, isNotNull,
            reason: 'Selectable category "${selectable.name}" should have a parentId');
      }

      // Verify we have selectable categories
      expect(selectableCategories.length, greaterThan(0),
          reason: 'Should have at least one selectable category');
    });

    test('Category validation logic works correctly', () {
      // Test the validation logic that would be used in the form
      String? validateCategory(ProviderCategory? category) {
        if (category == null) {
          return 'Please select a business category';
        }
        if (!category.isSelectable) {
          return 'Please select a specific business type';
        }
        return null;
      }

      // Test with null category
      expect(validateCategory(null), equals('Please select a business category'));

      // Test with non-selectable parent category
      const parentCategory = ProviderCategory(
        id: 1,
        name: 'Healthcare',
        description: 'Medical and health services',
        isSelectable: false,
      );
      expect(validateCategory(parentCategory), equals('Please select a specific business type'));

      // Test with selectable child category
      const childCategory = ProviderCategory(
        id: 101,
        name: 'Medical Clinics',
        description: 'General practice, family medicine',
        parentId: 1,
        isSelectable: true,
      );
      expect(validateCategory(childCategory), isNull);
    });

    test('API category parsing sets isSelectable correctly', () {
      // Test parent category from API (no parentId)
      final parentJson = {
        'id': 1,
        'name': 'Healthcare',
        'description': 'Medical and health services',
        'parentId': null,
      };
      final parentCategory = ProviderCategory.fromJson(parentJson);
      
      // Parent categories should not be selectable by default when parentId is null
      expect(parentCategory.isSelectable, isFalse);

      // Test child category from API (has parentId)
      final childJson = {
        'id': 101,
        'name': 'Medical Clinics',
        'description': 'General practice, family medicine',
        'parentId': 1,
      };
      final childCategory = ProviderCategory.fromJson(childJson);
      
      // Child categories should be selectable by default when parentId is not null
      expect(childCategory.isSelectable, isTrue);
    });
  });
}
