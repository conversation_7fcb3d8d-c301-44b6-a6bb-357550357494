import 'package:freezed_annotation/freezed_annotation.dart';
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/schedule_models.dart';
import '../../queues/models/queue_models.dart';

part 'onboarding_models.freezed.dart';
part 'onboarding_models.g.dart';

/// Represents the current step in the onboarding process
enum OnboardingStep {
  businessProfile,
  locationSetup,
  serviceCreation,
  scheduleSetup,
  queueManagement,
  summary,
  completed,
}

/// Business profile information collected during onboarding
@freezed
class BusinessProfile with _$BusinessProfile {
  const factory BusinessProfile({
    required String businessName,
    required String description,
    required int categoryId,
    required String categoryName,
    String? phone,
    String? website,
    String? logoUrl,
  }) = _BusinessProfile;

  factory BusinessProfile.fromJson(Map<String, dynamic> json) =>
      _$BusinessProfileFromJson(json);
}

/// Onboarding progress and state
@freezed
class OnboardingData with _$OnboardingData {
  const factory OnboardingData({
    @Default(OnboardingStep.businessProfile) OnboardingStep currentStep,
    @Default(0) int stepIndex,
    @Default(false) bool isCompleted,
    BusinessProfile? businessProfile,
    Location? primaryLocation,
    @Default([]) List<Service> services,
    @Default([]) List<Schedule> schedules,
    @Default([]) List<Queue> queues,
    @Default([]) List<QueueWithOpeningHours> queuesWithHours,
    DateTime? startedAt,
    DateTime? completedAt,
    DateTime? lastUpdatedAt,
  }) = _OnboardingData;

  factory OnboardingData.fromJson(Map<String, dynamic> json) =>
      _$OnboardingDataFromJson(json);
}

/// Onboarding state for UI management
@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(false) bool isLoading,
    @Default(false) bool isSaving,
    OnboardingData? data,
    String? error,
    @Default(false) bool canProceed,
    @Default(false) bool canGoBack,
  }) = _OnboardingState;
}

/// Step validation result
@freezed
class StepValidationResult with _$StepValidationResult {
  const factory StepValidationResult({
    @Default(false) bool isValid,
    @Default([]) List<String> errors,
    @Default([]) List<String> warnings,
  }) = _StepValidationResult;
}

/// Onboarding completion summary
@freezed
class OnboardingCompletion with _$OnboardingCompletion {
  const factory OnboardingCompletion({
    required BusinessProfile businessProfile,
    required Location primaryLocation,
    required List<Service> services,
    required List<Schedule> schedules,
    required List<Queue> queues,
    required DateTime completedAt,
    @Default(0) int totalSteps,
    @Default(Duration.zero) Duration totalTime,
  }) = _OnboardingCompletion;

  factory OnboardingCompletion.fromJson(Map<String, dynamic> json) =>
      _$OnboardingCompletionFromJson(json);
}

/// Extension methods for OnboardingStep
extension OnboardingStepExtension on OnboardingStep {
  /// Get the display title for the step
  String get title {
    switch (this) {
      case OnboardingStep.businessProfile:
        return 'Business Profile';
      case OnboardingStep.locationSetup:
        return 'Location Setup';
      case OnboardingStep.serviceCreation:
        return 'Services';
      case OnboardingStep.scheduleSetup:
        return 'Working Hours';
      case OnboardingStep.queueManagement:
        return 'Queue Setup';
      case OnboardingStep.summary:
        return 'Summary';
      case OnboardingStep.completed:
        return 'Completed';
    }
  }

  /// Get the description for the step
  String get description {
    switch (this) {
      case OnboardingStep.businessProfile:
        return 'Tell us about your business';
      case OnboardingStep.locationSetup:
        return 'Add your business location';
      case OnboardingStep.serviceCreation:
        return 'Define your services';
      case OnboardingStep.scheduleSetup:
        return 'Set your working hours';
      case OnboardingStep.queueManagement:
        return 'Configure your queues';
      case OnboardingStep.summary:
        return 'Review your setup';
      case OnboardingStep.completed:
        return 'Setup complete!';
    }
  }

  /// Get the icon for the step
  String get iconName {
    switch (this) {
      case OnboardingStep.businessProfile:
        return 'business';
      case OnboardingStep.locationSetup:
        return 'location_on';
      case OnboardingStep.serviceCreation:
        return 'room_service';
      case OnboardingStep.scheduleSetup:
        return 'schedule';
      case OnboardingStep.queueManagement:
        return 'queue';
      case OnboardingStep.summary:
        return 'summarize';
      case OnboardingStep.completed:
        return 'check_circle';
    }
  }

  /// Check if this is the first step
  bool get isFirst => this == OnboardingStep.businessProfile;

  /// Check if this is the last step
  bool get isLast => this == OnboardingStep.completed;

  /// Get the next step
  OnboardingStep? get next {
    final steps = OnboardingStep.values;
    final currentIndex = steps.indexOf(this);
    if (currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
    return null;
  }

  /// Get the previous step
  OnboardingStep? get previous {
    final steps = OnboardingStep.values;
    final currentIndex = steps.indexOf(this);
    if (currentIndex > 0) {
      return steps[currentIndex - 1];
    }
    return null;
  }

  /// Get step index (0-based)
  int get index => OnboardingStep.values.indexOf(this);

  /// Get total number of steps
  static int get totalSteps => OnboardingStep.values.length - 1; // Exclude completed
}

/// Queue with opening hours for onboarding
@freezed
class QueueWithOpeningHours with _$QueueWithOpeningHours {
  const factory QueueWithOpeningHours({
    required String title,
    required List<int> serviceIds,
    required Map<String, String> openingHours, // day_open/day_close format
    @Default(true) bool isActive,
  }) = _QueueWithOpeningHours;

  factory QueueWithOpeningHours.fromJson(Map<String, dynamic> json) =>
      _$QueueWithOpeningHoursFromJson(json);
}

/// Extension methods for OnboardingData
extension OnboardingDataExtension on OnboardingData {
  /// Calculate completion percentage
  double get completionPercentage {
    if (isCompleted) return 1.0;
    return stepIndex / OnboardingStep.totalSteps;
  }

  /// Check if a specific step is completed
  bool isStepCompleted(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.businessProfile:
        return businessProfile != null;
      case OnboardingStep.locationSetup:
        return primaryLocation != null;
      case OnboardingStep.serviceCreation:
        return services.isNotEmpty;
      case OnboardingStep.scheduleSetup:
        return schedules.isNotEmpty;
      case OnboardingStep.queueManagement:
        return queuesWithHours.isNotEmpty;
      case OnboardingStep.summary:
        return isCompleted;
      case OnboardingStep.completed:
        return isCompleted;
    }
  }

  /// Get the next incomplete step
  OnboardingStep? get nextIncompleteStep {
    for (final step in OnboardingStep.values) {
      if (step == OnboardingStep.completed) break;
      if (!isStepCompleted(step)) {
        return step;
      }
    }
    return null;
  }

  /// Check if onboarding can be completed
  bool get canComplete {
    return businessProfile != null &&
        primaryLocation != null &&
        services.isNotEmpty &&
        queuesWithHours.isNotEmpty;
  }

  /// Get duration since started
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }
}
