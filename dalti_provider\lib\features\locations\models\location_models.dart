import 'package:equatable/equatable.dart';
import '../../schedules/models/opening_hours_models.dart';

/// Location model based on the actual API response structure
class Location extends Equatable {
  final int id;
  final String name;
  final String? shortName;
  final String address;
  final String city;
  final String? country;
  final String? postalCode;
  final String? mobile;
  final bool isMobileHidden;
  final String? fax;
  final String? floor;
  final bool parking;
  final bool elevator;
  final bool handicapAccess;
  final double? latitude;
  final double? longitude;
  final String? timezone;
  final List<DayOpeningHours>? openingHours;

  const Location({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.isMobileHidden,
    required this.parking,
    required this.elevator,
    required this.handicapAccess,
    this.shortName,
    this.country,
    this.postalCode,
    this.mobile,
    this.fax,
    this.floor,
    this.latitude,
    this.longitude,
    this.timezone,
    this.openingHours,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'] as int,
      name: json['name'] as String,
      shortName: json['shortName'] as String?,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String?,
      postalCode: json['postalCode'] as String?,
      mobile: json['mobile'] as String?,
      isMobileHidden: json['isMobileHidden'] as bool? ?? false,
      fax: json['fax'] as String?,
      floor: json['floor'] as String?,
      parking: json['parking'] as bool? ?? false,
      elevator: json['elevator'] as bool? ?? false,
      handicapAccess: json['handicapAccess'] as bool? ?? false,
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      timezone: json['timezone'] as String?,
      openingHours:
          json['openingHours'] != null
              ? (json['openingHours'] as List<dynamic>)
                  .map(
                    (item) =>
                        DayOpeningHours.fromJson(item as Map<String, dynamic>),
                  )
                  .toList()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'shortName': shortName,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'mobile': mobile,
      'isMobileHidden': isMobileHidden,
      'fax': fax,
      'floor': floor,
      'parking': parking,
      'elevator': elevator,
      'handicapAccess': handicapAccess,
      'latitude': latitude,
      'longitude': longitude,
      'timezone': timezone,
      'openingHours': openingHours?.map((item) => item.toJson()).toList(),
    };
  }

  Location copyWith({
    int? id,
    String? name,
    String? shortName,
    String? address,
    String? city,
    String? country,
    String? postalCode,
    String? mobile,
    bool? isMobileHidden,
    String? fax,
    String? floor,
    bool? parking,
    bool? elevator,
    bool? handicapAccess,
    double? latitude,
    double? longitude,
    String? timezone,
    List<DayOpeningHours>? openingHours,
  }) {
    return Location(
      id: id ?? this.id,
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      mobile: mobile ?? this.mobile,
      isMobileHidden: isMobileHidden ?? this.isMobileHidden,
      fax: fax ?? this.fax,
      floor: floor ?? this.floor,
      parking: parking ?? this.parking,
      elevator: elevator ?? this.elevator,
      handicapAccess: handicapAccess ?? this.handicapAccess,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      timezone: timezone ?? this.timezone,
      openingHours: openingHours ?? this.openingHours,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    shortName,
    address,
    city,
    country,
    postalCode,
    mobile,
    isMobileHidden,
    fax,
    floor,
    parking,
    elevator,
    handicapAccess,
    latitude,
    longitude,
    timezone,
    openingHours,
  ];

  @override
  String toString() {
    return 'Location(id: $id, name: $name, address: $address, city: $city)';
  }
}

/// Enhanced Location model for onboarding with opening hours support
class EnhancedLocation extends Equatable {
  final int id;
  final String name;
  final String? shortName;
  final String address;
  final String city;
  final String country;
  final String timezone;
  final double? latitude;
  final double? longitude;
  final bool parking;
  final bool elevator;
  final bool handicapAccess;
  final OpeningHours? openingHours;
  final bool isMobileHidden;

  const EnhancedLocation({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.country,
    required this.timezone,
    this.shortName,
    this.latitude,
    this.longitude,
    this.parking = false,
    this.elevator = false,
    this.handicapAccess = false,
    this.openingHours,
    this.isMobileHidden = false,
  });

  factory EnhancedLocation.fromJson(Map<String, dynamic> json) {
    return EnhancedLocation(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String,
      shortName: json['shortName'] as String?,
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String? ?? 'Algeria',
      timezone: json['timezone'] as String? ?? 'Africa/Algiers',
      latitude: json['coordinates']?['latitude']?.toDouble(),
      longitude: json['coordinates']?['longitude']?.toDouble(),
      parking: json['parking'] as bool? ?? false,
      elevator: json['elevator'] as bool? ?? false,
      handicapAccess: json['handicapAccess'] as bool? ?? false,
      openingHours:
          json['openingHours'] != null
              ? OpeningHours.fromJson(json['openingHours'] as List<dynamic>)
              : null,
      isMobileHidden: json['isMobileHidden'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'shortName': shortName,
      'address': address,
      'city': city,
      'country': country,
      'timezone': timezone,
      'coordinates':
          (latitude != null && longitude != null)
              ? {'latitude': latitude, 'longitude': longitude}
              : null,
      'parking': parking,
      'elevator': elevator,
      'handicapAccess': handicapAccess,
      'openingHours': openingHours?.toJson(),
      'isMobileHidden': isMobileHidden,
    };
  }

  /// Convert to legacy Location model for backward compatibility
  Location toLegacyLocation() {
    return Location(
      id: id,
      name: name,
      address: address,
      city: city,
      isMobileHidden: isMobileHidden,
      parking: parking,
      elevator: elevator,
      handicapAccess: handicapAccess,
    );
  }

  /// Create from legacy Location model
  factory EnhancedLocation.fromLegacyLocation(
    Location location, {
    String? shortName,
    String country = 'Algeria',
    String timezone = 'Africa/Algiers',
    double? latitude,
    double? longitude,
    OpeningHours? openingHours,
  }) {
    return EnhancedLocation(
      id: location.id,
      name: location.name,
      shortName: shortName,
      address: location.address,
      city: location.city,
      country: country,
      timezone: timezone,
      latitude: latitude,
      longitude: longitude,
      parking: location.parking,
      elevator: location.elevator,
      handicapAccess: location.handicapAccess,
      openingHours: openingHours,
      isMobileHidden: location.isMobileHidden,
    );
  }

  EnhancedLocation copyWith({
    int? id,
    String? name,
    String? shortName,
    String? address,
    String? city,
    String? country,
    String? timezone,
    double? latitude,
    double? longitude,
    bool? parking,
    bool? elevator,
    bool? handicapAccess,
    OpeningHours? openingHours,
    bool? isMobileHidden,
  }) {
    return EnhancedLocation(
      id: id ?? this.id,
      name: name ?? this.name,
      shortName: shortName ?? this.shortName,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      timezone: timezone ?? this.timezone,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      parking: parking ?? this.parking,
      elevator: elevator ?? this.elevator,
      handicapAccess: handicapAccess ?? this.handicapAccess,
      openingHours: openingHours ?? this.openingHours,
      isMobileHidden: isMobileHidden ?? this.isMobileHidden,
    );
  }

  /// Check if location has coordinates
  bool get hasCoordinates => latitude != null && longitude != null;

  /// Check if location has opening hours
  bool get hasOpeningHours => openingHours != null;

  /// Get formatted address
  String get fullAddress => '$address, $city, $country';

  @override
  List<Object?> get props => [
    id,
    name,
    shortName,
    address,
    city,
    country,
    timezone,
    latitude,
    longitude,
    parking,
    elevator,
    handicapAccess,
    openingHours,
    isMobileHidden,
  ];

  @override
  String toString() {
    return 'EnhancedLocation(id: $id, name: $name, city: $city, country: $country)';
  }
}

/// Request model for creating a new location
class CreateLocationRequest extends Equatable {
  final String name;
  final String address;
  final String city;
  final String country;
  final String postalCode;
  final String timezone;
  final double latitude;
  final double longitude;
  final bool parking;
  final bool elevator;
  final bool handicapAccess;
  final List<Map<String, dynamic>>? openingHours;

  const CreateLocationRequest({
    required this.name,
    required this.address,
    required this.city,
    required this.country,
    required this.postalCode,
    required this.timezone,
    required this.latitude,
    required this.longitude,
    this.parking = false,
    this.elevator = false,
    this.handicapAccess = false,
    this.openingHours,
  });

  Map<String, dynamic> toJson() {
    final json = {
      'name': name,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'timezone': timezone,
      'latitude': latitude,
      'longitude': longitude,
      'parking': parking,
      'elevator': elevator,
      'handicapAccess': handicapAccess,
    };

    if (openingHours != null) {
      json['openingHours'] = openingHours!;
    }

    return json;
  }

  @override
  List<Object?> get props => [
    name,
    address,
    city,
    country,
    postalCode,
    timezone,
    latitude,
    longitude,
    parking,
    elevator,
    handicapAccess,
    openingHours,
  ];
}

/// Enhanced request model for creating a new location with opening hours
class CreateEnhancedLocationRequest extends Equatable {
  final String name;
  final String? shortName;
  final String address;
  final String city;
  final String country;
  final String timezone;
  final String? postalCode;
  final double? latitude;
  final double? longitude;
  final bool parking;
  final bool elevator;
  final bool handicapAccess;
  final OpeningHours openingHours;

  const CreateEnhancedLocationRequest({
    required this.name,
    required this.address,
    required this.city,
    required this.openingHours,
    this.shortName,
    this.country = 'Algeria',
    this.timezone = 'Africa/Algiers',
    this.postalCode,
    this.latitude,
    this.longitude,
    this.parking = false,
    this.elevator = false,
    this.handicapAccess = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'shortName': shortName,
      'address': address,
      'city': city,
      'country': country,
      'timezone': timezone,
      'postalCode': postalCode,
      'coordinates':
          (latitude != null && longitude != null)
              ? {'latitude': latitude, 'longitude': longitude}
              : null,
      'parking': parking,
      'elevator': elevator,
      'handicapAccess': handicapAccess,
      'openingHours': openingHours.toJson(),
    };
  }

  /// Convert to legacy CreateLocationRequest
  CreateLocationRequest toLegacyRequest({
    required String postalCode,
    required double latitude,
    required double longitude,
  }) {
    return CreateLocationRequest(
      name: name,
      address: address,
      city: city,
      country: country,
      postalCode: postalCode,
      timezone: timezone,
      latitude: latitude,
      longitude: longitude,
      parking: parking,
      elevator: elevator,
      handicapAccess: handicapAccess,
      openingHours: openingHours.toJson(),
    );
  }

  @override
  List<Object?> get props => [
    name,
    shortName,
    address,
    city,
    country,
    timezone,
    postalCode,
    latitude,
    longitude,
    parking,
    elevator,
    handicapAccess,
    openingHours,
  ];
}

/// Request model for updating an existing location
class UpdateLocationRequest extends Equatable {
  final String? name;
  final String? shortName;
  final String? address;
  final String? city;
  final String? country;
  final String? postalCode;
  final String? timezone;
  final String? mobile;
  final bool? isMobileHidden;
  final String? fax;
  final String? floor;
  final double? latitude;
  final double? longitude;
  final bool? parking;
  final bool? elevator;
  final bool? handicapAccess;
  final List<Map<String, dynamic>>? openingHours;

  const UpdateLocationRequest({
    this.name,
    this.shortName,
    this.address,
    this.city,
    this.country,
    this.postalCode,
    this.timezone,
    this.mobile,
    this.isMobileHidden,
    this.fax,
    this.floor,
    this.latitude,
    this.longitude,
    this.parking,
    this.elevator,
    this.handicapAccess,
    this.openingHours,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (name != null) json['name'] = name;
    if (shortName != null) json['shortName'] = shortName;
    if (address != null) json['address'] = address;
    if (city != null) json['city'] = city;
    if (country != null) json['country'] = country;
    if (postalCode != null) json['postalCode'] = postalCode;
    if (timezone != null) json['timezone'] = timezone;
    if (mobile != null) json['mobile'] = mobile;
    if (isMobileHidden != null) json['isMobileHidden'] = isMobileHidden;
    if (fax != null) json['fax'] = fax;
    if (floor != null) json['floor'] = floor;
    if (latitude != null) json['latitude'] = latitude;
    if (longitude != null) json['longitude'] = longitude;
    if (parking != null) json['parking'] = parking;
    if (elevator != null) json['elevator'] = elevator;
    if (handicapAccess != null) json['handicapAccess'] = handicapAccess;
    if (openingHours != null) json['openingHours'] = openingHours;

    return json;
  }

  @override
  List<Object?> get props => [
    name,
    shortName,
    address,
    city,
    country,
    postalCode,
    timezone,
    mobile,
    isMobileHidden,
    fax,
    floor,
    latitude,
    longitude,
    parking,
    elevator,
    handicapAccess,
    openingHours,
  ];
}

/// Response model for location operations
class LocationResponse extends Equatable {
  final bool success;
  final String message;
  final Location? location;
  final List<Location>? locations;

  const LocationResponse({
    required this.success,
    required this.message,
    this.location,
    this.locations,
  });

  factory LocationResponse.fromJson(Map<String, dynamic> json) {
    return LocationResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      location:
          json['data'] != null && json['data'] is Map
              ? Location.fromJson(json['data'] as Map<String, dynamic>)
              : null,
      locations:
          json['data'] != null && json['data'] is List
              ? (json['data'] as List)
                  .map(
                    (item) => Location.fromJson(item as Map<String, dynamic>),
                  )
                  .toList()
              : null,
    );
  }

  @override
  List<Object?> get props => [success, message, location, locations];
}
