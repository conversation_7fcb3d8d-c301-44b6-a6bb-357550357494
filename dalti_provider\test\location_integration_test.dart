import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/locations/services/location_integration_service.dart';

void main() {
  group('Location Integration Tests', () {
    test('LocationDependencies model works correctly', () {
      const dependencies = LocationDependencies(
        hasQueues: true,
        hasSchedules: false,
        queueCount: 3,
        scheduleCount: 0,
        canDelete: false,
      );

      expect(dependencies.hasQueues, isTrue);
      expect(dependencies.hasSchedules, isFalse);
      expect(dependencies.queueCount, equals(3));
      expect(dependencies.scheduleCount, equals(0));
      expect(dependencies.canDelete, isFalse);
    });

    test('LocationStats model works correctly', () {
      const stats = LocationStats(
        locationId: 1,
        totalQueues: 5,
        activeQueues: 3,
        totalSchedules: 7,
        activeSchedules: 5,
        hasActiveOperations: true,
      );

      expect(stats.locationId, equals(1));
      expect(stats.totalQueues, equals(5));
      expect(stats.activeQueues, equals(3));
      expect(stats.totalSchedules, equals(7));
      expect(stats.activeSchedules, equals(5));
      expect(stats.hasActiveOperations, isTrue);
    });

    test('LocationDependencies canDelete logic works correctly', () {
      // Can delete when no dependencies
      const noDependencies = LocationDependencies(
        hasQueues: false,
        hasSchedules: false,
        queueCount: 0,
        scheduleCount: 0,
        canDelete: true,
      );
      expect(noDependencies.canDelete, isTrue);

      // Cannot delete when has queues
      const hasQueues = LocationDependencies(
        hasQueues: true,
        hasSchedules: false,
        queueCount: 2,
        scheduleCount: 0,
        canDelete: false,
      );
      expect(hasQueues.canDelete, isFalse);

      // Cannot delete when has schedules
      const hasSchedules = LocationDependencies(
        hasQueues: false,
        hasSchedules: true,
        queueCount: 0,
        scheduleCount: 3,
        canDelete: false,
      );
      expect(hasSchedules.canDelete, isFalse);

      // Cannot delete when has both
      const hasBoth = LocationDependencies(
        hasQueues: true,
        hasSchedules: true,
        queueCount: 2,
        scheduleCount: 3,
        canDelete: false,
      );
      expect(hasBoth.canDelete, isFalse);
    });

    test('LocationStats hasActiveOperations logic works correctly', () {
      // Has operations when has queues
      const hasQueues = LocationStats(
        locationId: 1,
        totalQueues: 2,
        activeQueues: 1,
        totalSchedules: 0,
        activeSchedules: 0,
        hasActiveOperations: true,
      );
      expect(hasQueues.hasActiveOperations, isTrue);

      // Has operations when has schedules
      const hasSchedules = LocationStats(
        locationId: 1,
        totalQueues: 0,
        activeQueues: 0,
        totalSchedules: 3,
        activeSchedules: 2,
        hasActiveOperations: true,
      );
      expect(hasSchedules.hasActiveOperations, isTrue);

      // No operations when empty
      const noOperations = LocationStats(
        locationId: 1,
        totalQueues: 0,
        activeQueues: 0,
        totalSchedules: 0,
        activeSchedules: 0,
        hasActiveOperations: false,
      );
      expect(noOperations.hasActiveOperations, isFalse);
    });
  });
}
