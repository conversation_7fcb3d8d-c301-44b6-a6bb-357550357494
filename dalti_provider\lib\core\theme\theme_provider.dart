import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'app_theme.dart';

part 'theme_provider.g.dart';

/// Theme mode enumeration
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Theme state class
@immutable
class ThemeState {
  final AppThemeMode themeMode;
  final ThemeData lightTheme;
  final ThemeData darkTheme;

  const ThemeState({
    required this.themeMode,
    required this.lightTheme,
    required this.darkTheme,
  });

  ThemeState copyWith({
    AppThemeMode? themeMode,
    ThemeData? lightTheme,
    ThemeData? darkTheme,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      lightTheme: lightTheme ?? this.lightTheme,
      darkTheme: darkTheme ?? this.darkTheme,
    );
  }

  /// Get the current theme data based on theme mode and system brightness
  ThemeData getCurrentTheme(Brightness systemBrightness) {
    switch (themeMode) {
      case AppThemeMode.light:
        return lightTheme;
      case AppThemeMode.dark:
        return darkTheme;
      case AppThemeMode.system:
        return systemBrightness == Brightness.dark ? darkTheme : lightTheme;
    }
  }

  /// Get the current theme mode for MaterialApp
  ThemeMode get materialThemeMode {
    switch (themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ThemeState &&
        other.themeMode == themeMode &&
        other.lightTheme == lightTheme &&
        other.darkTheme == darkTheme;
  }

  @override
  int get hashCode {
    return themeMode.hashCode ^ lightTheme.hashCode ^ darkTheme.hashCode;
  }
}

/// Theme provider for managing app theme state
@riverpod
class ThemeNotifier extends _$ThemeNotifier {
  static const String _themeKey = 'app_theme_mode';
  late Box _settingsBox;

  @override
  ThemeState build() {
    _initializeHive();
    
    // Load saved theme mode or default to system
    final savedThemeMode = _loadThemeMode();
    
    return ThemeState(
      themeMode: savedThemeMode,
      lightTheme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
    );
  }

  /// Initialize Hive box for settings
  void _initializeHive() {
    try {
      _settingsBox = Hive.box('settings');
    } catch (e) {
      // If box doesn't exist, it will be created when first accessed
      print('[ThemeProvider] Settings box not yet initialized: $e');
    }
  }

  /// Load theme mode from storage
  AppThemeMode _loadThemeMode() {
    try {
      if (!Hive.isBoxOpen('settings')) {
        return AppThemeMode.system;
      }
      
      final savedMode = _settingsBox.get(_themeKey, defaultValue: 'system') as String;
      return AppThemeMode.values.firstWhere(
        (mode) => mode.name == savedMode,
        orElse: () => AppThemeMode.system,
      );
    } catch (e) {
      print('[ThemeProvider] Error loading theme mode: $e');
      return AppThemeMode.system;
    }
  }

  /// Save theme mode to storage
  Future<void> _saveThemeMode(AppThemeMode mode) async {
    try {
      if (!Hive.isBoxOpen('settings')) {
        await Hive.openBox('settings');
        _settingsBox = Hive.box('settings');
      }
      
      await _settingsBox.put(_themeKey, mode.name);
      print('[ThemeProvider] Theme mode saved: ${mode.name}');
    } catch (e) {
      print('[ThemeProvider] Error saving theme mode: $e');
    }
  }

  /// Set theme mode
  Future<void> setThemeMode(AppThemeMode mode) async {
    state = state.copyWith(themeMode: mode);
    await _saveThemeMode(mode);
  }

  /// Toggle between light and dark theme
  Future<void> toggleTheme() async {
    final newMode = state.themeMode == AppThemeMode.light 
        ? AppThemeMode.dark 
        : AppThemeMode.light;
    await setThemeMode(newMode);
  }

  /// Set to light theme
  Future<void> setLightTheme() async {
    await setThemeMode(AppThemeMode.light);
  }

  /// Set to dark theme
  Future<void> setDarkTheme() async {
    await setThemeMode(AppThemeMode.dark);
  }

  /// Set to system theme
  Future<void> setSystemTheme() async {
    await setThemeMode(AppThemeMode.system);
  }

  /// Check if current theme is dark
  bool isDarkMode(Brightness systemBrightness) {
    switch (state.themeMode) {
      case AppThemeMode.light:
        return false;
      case AppThemeMode.dark:
        return true;
      case AppThemeMode.system:
        return systemBrightness == Brightness.dark;
    }
  }

  /// Get theme mode display name
  String getThemeModeDisplayName() {
    switch (state.themeMode) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }
}

/// Provider for accessing current theme data
@riverpod
ThemeData currentTheme(CurrentThemeRef ref) {
  final themeState = ref.watch(themeNotifierProvider);
  // Default to light theme since we can't access MediaQuery here
  // The actual theme will be determined by MaterialApp based on themeMode
  return themeState.lightTheme;
}

/// Extension for easy theme access in widgets
extension ThemeExtension on BuildContext {
  /// Get current theme colors
  ColorScheme get colors => Theme.of(this).colorScheme;
  
  /// Get current text theme
  TextTheme get textTheme => Theme.of(this).textTheme;
  
  /// Check if current theme is dark
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
  
  /// Get primary color
  Color get primaryColor => colors.primary;
  
  /// Get secondary color
  Color get secondaryColor => colors.secondary;
  
  /// Get surface color
  Color get surfaceColor => colors.surface;
  
  /// Get background color
  Color get backgroundColor => colors.background;
  
  /// Get error color
  Color get errorColor => colors.error;
}
