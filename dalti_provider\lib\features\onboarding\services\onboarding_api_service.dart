import '../../../core/network/api_service.dart';
import '../../../core/network/http_client.dart';
import '../models/onboarding_models.dart';
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/schedule_models.dart';
import '../../queues/models/queue_models.dart';

/// API service for onboarding-related operations
class OnboardingApiService extends ApiService {
  OnboardingApiService(HttpClient httpClient) : super(httpClient);

  /// Submit completed onboarding data to backend
  Future<OnboardingCompletion> submitOnboarding(OnboardingData data) async {
    try {
      print('[OnboardingApiService] Submitting onboarding data');

      // Transform data to match the new API schema
      final payload = _transformOnboardingDataToApiSchema(data);

      final response = await httpClient.post(
        '/api/auth/provider/complete-setup',
        data: payload,
      );

      print('[OnboardingApiService] Onboarding submitted successfully');
      print('[OnboardingApiService] Response: ${response.data}');

      // Debug the data before creating completion
      print('[OnboardingApiService] Creating completion with:');
      print(
        '  - Business Profile: ${data.businessProfile != null ? "✅" : "❌"}',
      );
      print(
        '  - Primary Location: ${data.primaryLocation != null ? "✅" : "❌"}',
      );
      print('  - Services: ${data.services.length}');
      print('  - Schedules: ${data.schedules.length}');
      print('  - Queues: ${data.queues.length}');
      print('  - Queues with Hours: ${data.queuesWithHours.length}');

      // Create completion object from response
      // Convert queuesWithHours to Queue objects if queues list is empty
      final completionQueues =
          data.queues.isNotEmpty
              ? data.queues
              : data.queuesWithHours
                  .map(
                    (queueWithHours) => Queue(
                      id: 0, // Temporary ID
                      title: queueWithHours.title,
                      isActive: queueWithHours.isActive,
                      sProvidingPlaceId: 0, // Default location ID
                      services: [], // Empty services list for now
                    ),
                  )
                  .toList();

      print(
        '[OnboardingApiService] Using ${completionQueues.length} queues for completion',
      );

      return OnboardingCompletion(
        businessProfile: data.businessProfile!,
        primaryLocation: data.primaryLocation!,
        services: data.services,
        schedules: data.schedules,
        queues: completionQueues,
        completedAt: DateTime.now(),
        totalSteps: OnboardingStep.totalSteps,
        totalTime: data.duration ?? Duration.zero,
      );
    } catch (e) {
      print('[OnboardingApiService] Error submitting onboarding: $e');
      rethrow;
    }
  }

  /// Validate step data on server
  Future<StepValidationResult> validateStep(
    OnboardingStep step,
    Map<String, dynamic> data,
  ) async {
    try {
      print('[OnboardingApiService] Validating step: $step');

      final payload = {'step': step.name, 'data': data};

      final response = await httpClient.post(
        '/api/auth/providers/onboarding/validate-step',
        data: payload,
      );

      final result = StepValidationResult(
        isValid: response.data['isValid'] ?? false,
        errors: List<String>.from(response.data['errors'] ?? []),
        warnings: List<String>.from(response.data['warnings'] ?? []),
      );

      print('[OnboardingApiService] Step validation result: ${result.isValid}');
      return result;
    } catch (e) {
      print('[OnboardingApiService] Error validating step: $e');
      // Return a generic validation error
      return const StepValidationResult(
        isValid: false,
        errors: [
          'Unable to validate data. Please check your connection and try again.',
        ],
      );
    }
  }

  /// Get onboarding progress from server (if synced)
  Future<OnboardingData?> getOnboardingProgress() async {
    try {
      print('[OnboardingApiService] Getting onboarding progress from server');

      final response = await httpClient.get(
        '/api/auth/providers/onboarding/progress',
      );

      if (response.data == null) {
        print('[OnboardingApiService] No onboarding progress found on server');
        return null;
      }

      final data = OnboardingData.fromJson(response.data);
      print('[OnboardingApiService] Onboarding progress retrieved from server');
      return data;
    } catch (e) {
      print('[OnboardingApiService] Error getting onboarding progress: $e');
      return null;
    }
  }

  /// Sync onboarding progress to server
  Future<void> syncOnboardingProgress(OnboardingData data) async {
    try {
      print('[OnboardingApiService] Syncing onboarding progress to server');

      final payload = data.toJson();

      await httpClient.post(
        '/api/auth/providers/onboarding/sync',
        data: payload,
      );

      print('[OnboardingApiService] Onboarding progress synced to server');
    } catch (e) {
      print('[OnboardingApiService] Error syncing onboarding progress: $e');
      // Don't rethrow - sync is optional
    }
  }

  /// Check if user needs onboarding from server
  Future<bool> checkOnboardingStatus() async {
    try {
      print('[OnboardingApiService] Checking onboarding status from server');

      final response = await httpClient.get(
        '/api/auth/providers/onboarding/status',
      );
      print(response.data['data'].toString());
      final needsOnboarding = response.data['data']['needsOnboarding'] ?? true;
      print(
        '[OnboardingApiService] Server onboarding status - needs: $needsOnboarding',
      );

      return needsOnboarding;
    } catch (e) {
      print('[OnboardingApiService] Error checking onboarding status: $e');
      return true; // Assume needs onboarding on error
    }
  }

  /// Get onboarding configuration from server
  Future<Map<String, dynamic>> getOnboardingConfig() async {
    try {
      print('[OnboardingApiService] Getting onboarding configuration');

      final response = await httpClient.get(
        '/api/auth/providers/onboarding/config',
      );

      final config = response.data ?? {};
      print('[OnboardingApiService] Onboarding configuration retrieved');

      return config;
    } catch (e) {
      print('[OnboardingApiService] Error getting onboarding config: $e');
      // Return default configuration
      return {
        'steps': OnboardingStep.values.map((s) => s.name).toList(),
        'allowSkip': false,
        'requireAllSteps': true,
        'maxDuration': 3600, // 1 hour in seconds
      };
    }
  }

  /// Update business profile on server
  Future<void> updateBusinessProfile(BusinessProfile profile) async {
    try {
      print('[OnboardingApiService] Updating business profile on server');

      final payload = profile.toJson();

      await httpClient.put(
        '/api/auth/providers/profile/business',
        data: payload,
      );

      print('[OnboardingApiService] Business profile updated on server');
    } catch (e) {
      print('[OnboardingApiService] Error updating business profile: $e');
      rethrow;
    }
  }

  /// Get business categories for onboarding
  Future<List<Map<String, dynamic>>> getBusinessCategories() async {
    try {
      print('[OnboardingApiService] Getting business categories');

      final response = await httpClient.get('/api/auth/provider/categories');

      final categories = List<Map<String, dynamic>>.from(response.data ?? []);
      print(
        '[OnboardingApiService] Retrieved ${categories.length} business categories',
      );

      return categories;
    } catch (e) {
      print('[OnboardingApiService] Error getting business categories: $e');
      rethrow;
    }
  }

  /// Get Algerian cities for location setup
  Future<List<String>> getAlgerianCities() async {
    try {
      print('[OnboardingApiService] Getting Algerian cities');

      final response = await httpClient.get(
        '/api/auth/providers/cities/algeria',
      );

      final cities = List<String>.from(response.data ?? []);
      print(
        '[OnboardingApiService] Retrieved ${cities.length} Algerian cities',
      );

      return cities;
    } catch (e) {
      print('[OnboardingApiService] Error getting Algerian cities: $e');
      // Return default cities if API fails
      return [
        'Algiers',
        'Oran',
        'Constantine',
        'Annaba',
        'Blida',
        'Batna',
        'Djelfa',
        'Sétif',
        'Sidi Bel Abbès',
        'Biskra',
        'Tébessa',
        'El Oued',
        'Skikda',
        'Tiaret',
        'Béjaïa',
        'Tlemcen',
        'Ouargla',
        'Béchar',
        'Mostaganem',
        'Bordj Bou Arréridj',
        'Chlef',
        'Médéa',
        'El Tarf',
        'Jijel',
        'Relizane',
        'M\'Sila',
        'Saïda',
        'El Bayadh',
        'Ghardaïa',
        'Laghouat',
        'Khenchela',
        'Souk Ahras',
        'Naâma',
        'Aïn Defla',
        'Tissemsilt',
        'El Eulma',
        'Bordj El Kiffan',
        'Rouiba',
        'Dély Ibrahim',
        'Bab Ezzouar',
        'Dar El Beïda',
        'Birkhadem',
        'Draria',
        'Zeralda',
        'Saoula',
        'Ouled Fayet',
        'Chéraga',
        'Djasr Kasentina',
        'El Harrach',
        'Baraki',
        'Oued Smar',
        'Bachdjerrah',
        'Hussein Dey',
        'Kouba',
        'Boumerdès',
        'Thénia',
        'Bordj Menaïel',
        'Khemis Miliana',
        'Aflou',
        'Aïn Oussera',
        'Ksar El Boukhari',
        'Chettia',
        'Sour El Ghozlane',
        'Boufarik',
        'Larbaa',
        'Oued El Alleug',
        'Meftah',
        'Bouinan',
        'El Affroun',
        'Chiffa',
        'Hammam Melouane',
        'Ouled Yaïch',
        'Beni Tamou',
        'Soumaa',
        'Guerrouaou',
        'Tablat',
        'Boukadir',
        'Miliana',
        'Khemis Miliana',
        'El Attaf',
        'El Abadia',
        'Oued Fodda',
        'Ain Defla',
        'Djendel',
        'Barbouche',
        'Zeddine',
        'Hoceinia',
        'Tacheta Zegagha',
        'Sidi Lakhdar',
        'El Amra',
        'Bourached',
        'Ben Allal',
        'Rouina',
        'Djelida',
        'Bir Ould Khelifa',
        'Bathia',
      ];
    }
  }

  /// Transform onboarding data to match the new API schema
  Map<String, dynamic> _transformOnboardingDataToApiSchema(
    OnboardingData data,
  ) {
    // Validate required data
    if (data.businessProfile == null) {
      throw Exception('Business profile is required');
    }
    if (data.primaryLocation == null) {
      throw Exception('Primary location is required');
    }
    if (data.services.isEmpty) {
      throw Exception('At least one service is required');
    }
    // Check for queues in either format
    if (data.queues.isEmpty && data.queuesWithHours.isEmpty) {
      throw Exception('At least one queue is required');
    }

    return {
      'businessInfo': _transformBusinessInfo(data.businessProfile!),
      'locations': _transformLocations(data.primaryLocation!, data.schedules),
      'services': _transformServicesFromOnboardingData(data.services),
      'queues': _transformQueuesFromOnboardingData(data),
    };
  }

  /// Transform business profile to businessInfo schema
  Map<String, dynamic> _transformBusinessInfo(BusinessProfile profile) {
    // Create phones object - ensure at least one phone is provided
    final phones = <String, String>{};

    if (profile.mobile != null && profile.mobile!.isNotEmpty) {
      phones['mobile'] = profile.mobile!;
    }
    if (profile.landline != null && profile.landline!.isNotEmpty) {
      phones['landline'] = profile.landline!;
    }
    if (profile.fax != null && profile.fax!.isNotEmpty) {
      phones['fax'] = profile.fax!;
    }

    if (phones.isEmpty) {
      throw Exception('At least one phone number is required');
    }

    return {
      'businessName': profile.businessName,
      'bio': profile.description, // Map description to bio
      'shortName': profile.shortName,
      'logoUrl': profile.logoUrl,
      'phones': phones,
    };
  }

  /// Transform location and schedules to locations schema
  List<Map<String, dynamic>> _transformLocations(
    Location location,
    List<Schedule> schedules,
  ) {
    // Group schedules by location (assuming all schedules are for primary location)
    final openingHours = _transformSchedulesToOpeningHours(schedules);

    // Use actual coordinates from location, fallback to Algiers if not available
    final coordinates = {
      'latitude':
          location.latitude ?? 36.7538, // Fallback to Algiers coordinates
      'longitude': location.longitude ?? 3.0588,
    };

    return [
      {
        'name': location.name,
        'shortName': null, // TODO: Add shortName to Location model
        'country': 'Algeria', // Default for Dalti Provider
        'city': location.city,
        'timezone': 'Africa/Algiers', // Default timezone for Algeria
        'address': location.address,
        'parking': location.parking,
        'elevator': location.elevator,
        'handicapAccess': location.handicapAccess,
        'coordinates': coordinates, // Always provide coordinates object
        'openingHours': openingHours,
      },
    ];
  }

  /// Transform schedules to opening hours format
  List<Map<String, dynamic>> _transformSchedulesToOpeningHours(
    List<Schedule> schedules,
  ) {
    final dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];

    // Group schedules by day
    final schedulesByDay = <int, List<Schedule>>{};
    for (final schedule in schedules) {
      schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
    }

    // Create opening hours for each day
    final openingHours = <Map<String, dynamic>>[];
    for (int day = 0; day < 7; day++) {
      final daySchedules = schedulesByDay[day] ?? [];
      final hours =
          daySchedules
              .map(
                (schedule) => {
                  'timeFrom': schedule.startTime,
                  'timeTo': schedule.endTime,
                },
              )
              .toList();

      openingHours.add({
        'dayOfWeek': dayNames[day],
        'isActive':
            daySchedules.isNotEmpty && daySchedules.any((s) => s.isActive),
        'hours': hours,
      });
    }

    return openingHours;
  }

  /// Transform services from onboarding data to services schema
  List<Map<String, dynamic>> _transformServicesFromOnboardingData(
    List<Service> services,
  ) {
    return services
        .map(
          (service) => {
            'title': service.title,
            'duration': service.duration,
            'price': service.price,
            'pointsRequirements':
                service.pointsRequirements ?? 1, // Default to 1 if not set
            'isPublic': service.isPublic ?? true, // Use actual isPublic value
            'deliveryType':
                service.deliveryType ??
                'at_location', // Use actual delivery type
            'servedRegions':
                service.servedRegions ?? [], // Use actual served regions
          },
        )
        .toList();
  }

  /// Transform services to services schema (legacy method)
  List<Map<String, dynamic>> _transformServices(List<Service> services) {
    return services
        .map(
          (service) => {
            'title': service.title,
            'duration': service.duration,
            'price': service.price,
            'pointsRequirements':
                service.pointsRequirements ?? 1, // Default to 1 if not set
            'isPublic': service.isActive ?? true,
            'deliveryType':
                'at_location', // Default delivery type for existing services
            'servedRegions': <String>[], // Empty array for at_location services
          },
        )
        .toList();
  }

  /// Transform queues from onboarding data (handles both Queue and QueueWithOpeningHours)
  List<Map<String, dynamic>> _transformQueuesFromOnboardingData(
    OnboardingData data,
  ) {
    // Prefer queuesWithHours if available (current onboarding flow)
    if (data.queuesWithHours.isNotEmpty) {
      return _transformQueuesWithHours(data.queuesWithHours, data.services);
    }

    // Fallback to regular queues
    if (data.queues.isNotEmpty) {
      return _transformQueues(data.queues, data.queuesWithHours, data.services);
    }

    return [];
  }

  /// Transform QueueWithOpeningHours to queues schema
  List<Map<String, dynamic>> _transformQueuesWithHours(
    List<QueueWithOpeningHours> queuesWithHours,
    List<Service> services,
  ) {
    return queuesWithHours.asMap().entries.map((entry) {
      final queue = entry.value;
      return {
        'name': queue.title,
        'locationIndex': 0, // Always 0 since we only have one location
        'serviceIndices': _getServiceIndicesFromIds(queue.serviceIds, services),
        'customOpeningHours': _transformQueueOpeningHours(queue.openingHours),
      };
    }).toList();
  }

  /// Transform queues to queues schema (legacy method)
  List<Map<String, dynamic>> _transformQueues(
    List<Queue> queues,
    List<QueueWithOpeningHours> queuesWithHours,
    List<Service> services,
  ) {
    return queues
        .asMap()
        .entries
        .map(
          (entry) => {
            'name': entry.value.title,
            'locationIndex': 0, // Always 0 since we only have one location
            'serviceIndices': _getServiceIndicesForQueue(entry.value, services),
            'customOpeningHours': _getCustomOpeningHoursForQueue(
              entry.value,
              queuesWithHours,
            ),
          },
        )
        .toList();
  }

  /// Get service indices from service IDs (for QueueWithOpeningHours)
  List<int> _getServiceIndicesFromIds(
    List<int> serviceIds,
    List<Service> services,
  ) {
    final serviceIndices = <int>[];

    for (final serviceId in serviceIds) {
      final serviceIndex = services.indexWhere(
        (service) => service.id == serviceId,
      );
      if (serviceIndex != -1) {
        serviceIndices.add(serviceIndex);
      }
    }

    return serviceIndices;
  }

  /// Get service indices for a queue (legacy method)
  List<int> _getServiceIndicesForQueue(Queue queue, List<Service> services) {
    // Map queue's service IDs to indices in the services array
    final serviceIndices = <int>[];

    for (final queueService in queue.services) {
      final serviceIndex = services.indexWhere(
        (service) => service.id == queueService.serviceId,
      );
      if (serviceIndex != -1) {
        serviceIndices.add(serviceIndex);
      }
    }

    return serviceIndices;
  }

  /// Transform queue opening hours from QueueWithOpeningHours format
  List<Map<String, dynamic>>? _transformQueueOpeningHours(
    Map<String, String> openingHours,
  ) {
    if (openingHours.isEmpty) {
      return null; // Inherit from location
    }

    final dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    final customHours = <Map<String, dynamic>>[];

    for (int day = 0; day < 7; day++) {
      final dayName = dayNames[day].toLowerCase();
      final openKey = '${dayName}_open';
      final closeKey = '${dayName}_close';

      final openTime = openingHours[openKey];
      final closeTime = openingHours[closeKey];

      final hours = <Map<String, String>>[];
      if (openTime != null && closeTime != null) {
        hours.add({'timeFrom': openTime, 'timeTo': closeTime});
      }

      customHours.add({
        'dayOfWeek': dayNames[day],
        'isActive': hours.isNotEmpty,
        'hours': hours,
      });
    }

    return customHours;
  }

  /// Get custom opening hours for a queue
  List<Map<String, dynamic>>? _getCustomOpeningHoursForQueue(
    Queue queue,
    List<QueueWithOpeningHours> queuesWithHours,
  ) {
    // Find matching queue with hours
    final queueWithHours = queuesWithHours.firstWhere(
      (q) => q.title == queue.title,
      orElse:
          () => const QueueWithOpeningHours(
            title: '',
            serviceIds: [],
            openingHours: {},
          ),
    );

    if (queueWithHours.openingHours.isEmpty) {
      return null; // Inherit from location
    }

    // Transform queue opening hours to the expected format
    final dayNames = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];
    final customHours = <Map<String, dynamic>>[];

    for (int day = 0; day < 7; day++) {
      final dayName = dayNames[day].toLowerCase();
      final openKey = '${dayName}_open';
      final closeKey = '${dayName}_close';

      final openTime = queueWithHours.openingHours[openKey];
      final closeTime = queueWithHours.openingHours[closeKey];

      final hours = <Map<String, String>>[];
      if (openTime != null && closeTime != null) {
        hours.add({'timeFrom': openTime, 'timeTo': closeTime});
      }

      customHours.add({
        'dayOfWeek': dayNames[day],
        'isActive': hours.isNotEmpty,
        'hours': hours,
      });
    }

    return customHours;
  }
}
