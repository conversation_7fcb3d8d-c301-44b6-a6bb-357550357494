import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/storage/web_storage_service.dart';
import '../models/service_models.dart';
import '../repository/service_repository.dart';
import '../repository/service_repository_impl.dart';
import '../services/service_api_service.dart';
import 'dart:convert';

part 'service_provider.g.dart';

/// Service state
enum ServiceState { initial, loading, loaded, error }

/// Service data state
class ServiceData {
  final ServiceState state;
  final List<Service> services;
  final Service? selectedService;
  final String? error;
  final bool isLoading;

  const ServiceData({
    required this.state,
    this.services = const [],
    this.selectedService,
    this.error,
    this.isLoading = false,
  });

  ServiceData copyWith({
    ServiceState? state,
    List<Service>? services,
    Service? selectedService,
    String? error,
    bool? isLoading,
    bool clearSelectedService = false,
    bool clearError = false,
  }) {
    return ServiceData(
      state: state ?? this.state,
      services: services ?? this.services,
      selectedService:
          clearSelectedService
              ? null
              : (selectedService ?? this.selectedService),
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get hasError => state == ServiceState.error;
  bool get hasServices => services.isNotEmpty;
}

/// Service API service provider
@riverpod
ServiceApiService serviceApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return ServiceApiService(httpClient);
}

/// Service repository provider
@riverpod
ServiceRepository serviceRepository(Ref ref) {
  final apiService = ref.watch(serviceApiServiceProvider);
  return ServiceRepositoryImpl(apiService: apiService);
}

/// Service provider for managing service state
@riverpod
class ServiceNotifier extends _$ServiceNotifier {
  static const String _cacheKey = 'cached_services';
  static const String _cacheTimestampKey = 'cached_services_timestamp';
  static const Duration _cacheExpiry = Duration(minutes: 15);

  @override
  ServiceData build() {
    // Try to load cached data on initialization
    _loadCachedServices();
    return const ServiceData(state: ServiceState.initial);
  }

  /// Load cached services from storage
  void _loadCachedServices() {
    try {
      final cachedServicesJson = WebStorageService.getCache<String>(_cacheKey);
      final cachedTimestamp = WebStorageService.getCache<int>(
        _cacheTimestampKey,
      );

      if (cachedServicesJson != null && cachedTimestamp != null) {
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
        final now = DateTime.now();

        // Check if cache is still valid
        if (now.difference(cacheTime) < _cacheExpiry) {
          final List<dynamic> servicesJson = json.decode(cachedServicesJson);
          final services =
              servicesJson
                  .map((json) => Service.fromJson(json as Map<String, dynamic>))
                  .toList();

          print(
            '[ServiceProvider] Loaded ${services.length} services from cache',
          );

          state = state.copyWith(
            state: ServiceState.loaded,
            services: services,
          );
          return;
        } else {
          print('[ServiceProvider] Cache expired, will fetch fresh data');
        }
      }
    } catch (e) {
      print('[ServiceProvider] Error loading cached services: $e');
    }
  }

  /// Cache services to storage
  Future<void> _cacheServices(List<Service> services) async {
    try {
      final servicesJson = services.map((service) => service.toJson()).toList();
      final jsonString = json.encode(servicesJson);

      await WebStorageService.saveCache(_cacheKey, jsonString);
      await WebStorageService.saveCache(
        _cacheTimestampKey,
        DateTime.now().millisecondsSinceEpoch,
      );

      print('[ServiceProvider] Cached ${services.length} services');
    } catch (e) {
      print('[ServiceProvider] Error caching services: $e');
    }
  }

  /// Load all services
  Future<void> loadServices({
    bool? isActive,
    int? categoryId,
    String? search,
    bool forceRefresh = false,
  }) async {
    try {
      // Don't reload if already loaded and not forcing refresh
      if (state.state == ServiceState.loaded &&
          !forceRefresh &&
          search == null) {
        return;
      }

      state = state.copyWith(
        state: ServiceState.loading,
        isLoading: true,
        clearError: true,
      );

      print(
        '[ServiceProvider] Loading services (isActive: $isActive, categoryId: $categoryId, search: $search)',
      );

      final repository = ref.read(serviceRepositoryProvider);
      final services = await repository.getServices(
        isActive: isActive,
        categoryId: categoryId,
        search: search,
      );

      // Cache services if this is a general load (no filters)
      if (isActive == null && categoryId == null && search == null) {
        await _cacheServices(services);
      }

      state = state.copyWith(
        state: ServiceState.loaded,
        services: services,
        isLoading: false,
      );

      print(
        '[ServiceProvider] Successfully loaded ${services.length} services',
      );
    } catch (e) {
      print('[ServiceProvider] Error loading services: $e');

      state = state.copyWith(
        state: ServiceState.error,
        error: 'Failed to load services: $e',
        isLoading: false,
      );
    }
  }

  /// Select a service - Always fetch from API for fresh data
  Future<void> selectService(int serviceId) async {
    try {
      print(
        '[ServiceProvider] Selecting service ID: $serviceId - fetching from API',
      );

      state = state.copyWith(isLoading: true, clearError: true);

      final repository = ref.read(serviceRepositoryProvider);
      final service = await repository.getServiceById(serviceId);

      // Update the service in our local list if it exists
      final updatedServices =
          state.services.map((existingService) {
            return existingService.id == serviceId ? service : existingService;
          }).toList();

      // If service wasn't in the list, add it
      if (!state.services.any((s) => s.id == serviceId)) {
        updatedServices.add(service);
      }

      state = state.copyWith(
        services: updatedServices,
        selectedService: service,
        isLoading: false,
      );

      // Update cache with fresh data
      await _cacheServices(updatedServices);

      print(
        '[ServiceProvider] Successfully fetched and selected service: ${service.title}',
      );
    } catch (e) {
      print('[ServiceProvider] Error selecting service: $e');

      state = state.copyWith(
        error: 'Failed to select service: $e',
        isLoading: false,
      );
    }
  }

  /// Create a new service
  Future<bool> createService(CreateServiceRequest request) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print('[ServiceProvider] Creating service: ${request.title}');

      final repository = ref.read(serviceRepositoryProvider);
      final service = await repository.createService(request);

      // Add to current services list
      final updatedServices = [...state.services, service];

      // Update cache
      await _cacheServices(updatedServices);

      state = state.copyWith(
        services: updatedServices,
        selectedService: service,
        isLoading: false,
      );

      print(
        '[ServiceProvider] Successfully created service: ${service.title} (ID: ${service.id})',
      );
      return true;
    } catch (e) {
      print('[ServiceProvider] Error creating service: $e');

      state = state.copyWith(
        error: 'Failed to create service: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Update an existing service
  Future<bool> updateService(int id, UpdateServiceRequest request) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print('[ServiceProvider] Updating service ID: $id');

      final repository = ref.read(serviceRepositoryProvider);
      final updatedService = await repository.updateService(id, request);

      // Update in current services list
      final updatedServices =
          state.services.map((service) {
            return service.id == id ? updatedService : service;
          }).toList();

      // Update cache
      await _cacheServices(updatedServices);

      state = state.copyWith(
        services: updatedServices,
        selectedService:
            state.selectedService?.id == id
                ? updatedService
                : state.selectedService,
        isLoading: false,
      );

      print(
        '[ServiceProvider] Successfully updated service: ${updatedService.title}',
      );
      return true;
    } catch (e) {
      print('[ServiceProvider] Error updating service: $e');

      state = state.copyWith(
        error: 'Failed to update service: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Delete a service
  Future<bool> deleteService(int id) async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);

      print('[ServiceProvider] Deleting service ID: $id');

      final repository = ref.read(serviceRepositoryProvider);
      final success = await repository.deleteService(id);

      if (success) {
        // Remove from current services list
        final updatedServices =
            state.services.where((service) => service.id != id).toList();

        // Update cache
        await _cacheServices(updatedServices);

        state = state.copyWith(
          services: updatedServices,
          selectedService:
              state.selectedService?.id == id ? null : state.selectedService,
          isLoading: false,
        );

        print('[ServiceProvider] Successfully deleted service ID: $id');
      } else {
        throw Exception('Delete operation returned false');
      }

      return success;
    } catch (e) {
      print('[ServiceProvider] Error deleting service: $e');

      state = state.copyWith(
        error: 'Failed to delete service: $e',
        isLoading: false,
      );

      return false;
    }
  }

  /// Check if service can be deleted
  Future<bool> canDeleteService(int id) async {
    try {
      final repository = ref.read(serviceRepositoryProvider);
      final canDelete = await repository.canDeleteService(id);

      print('[ServiceProvider] Service $id can delete: $canDelete');
      return canDelete;
    } catch (e) {
      print('[ServiceProvider] Error checking if service can be deleted: $e');
      return true; // Assume we can try to delete
    }
  }

  /// Search services
  Future<void> searchServices(String query) async {
    if (query.isEmpty) {
      // If query is empty, reload all services
      await loadServices(forceRefresh: true);
      return;
    }

    await loadServices(search: query, forceRefresh: true);
  }

  /// Filter services by category
  Future<void> filterByCategory(int? categoryId) async {
    await loadServices(categoryId: categoryId, forceRefresh: true);
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Clear selected service
  void clearSelectedService() {
    state = state.copyWith(clearSelectedService: true);
  }

  /// Refresh services
  Future<void> refresh() async {
    await loadServices(forceRefresh: true);
  }

  /// Clear cache
  Future<void> clearCache() async {
    try {
      await WebStorageService.removeAuth(_cacheKey);
      await WebStorageService.removeAuth(_cacheTimestampKey);
      print('[ServiceProvider] Cache cleared');
    } catch (e) {
      print('[ServiceProvider] Error clearing cache: $e');
    }
  }

  /// Force refresh a single service from API
  Future<void> refreshService(int serviceId) async {
    try {
      print('[ServiceProvider] Force refreshing service ID: $serviceId');

      state = state.copyWith(isLoading: true, clearError: true);

      final repository = ref.read(serviceRepositoryProvider);
      final service = await repository.getServiceById(serviceId);

      // Update the service in our local list
      final updatedServices =
          state.services.map((existingService) {
            return existingService.id == serviceId ? service : existingService;
          }).toList();

      // If service wasn't in the list, add it
      if (!state.services.any((s) => s.id == serviceId)) {
        updatedServices.add(service);
      }

      state = state.copyWith(
        services: updatedServices,
        selectedService:
            state.selectedService?.id == serviceId
                ? service
                : state.selectedService,
        isLoading: false,
      );

      // Update cache with fresh data
      await _cacheServices(updatedServices);

      print(
        '[ServiceProvider] Successfully refreshed service: ${service.title}',
      );
    } catch (e) {
      print('[ServiceProvider] Error refreshing service: $e');

      state = state.copyWith(
        error: 'Failed to refresh service: $e',
        isLoading: false,
      );
    }
  }
}
