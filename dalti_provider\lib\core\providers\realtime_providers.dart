import 'dart:async';
import 'dart:developer' as developer;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/websocket_service.dart';
import '../../features/messages/models/message_models.dart';
import '../../features/messages/services/message_api_service.dart';
import '../../features/dashboard/models/dashboard_models.dart';
import '../../features/notifications/models/notification_models.dart'
    as notifications;
import 'app_providers.dart';

part 'realtime_providers.g.dart';

// Type alias for AppNotification to match the generated code
typedef AppNotification = notifications.NotificationItem;

/// WebSocket service provider (singleton)
@Riverpod(keepAlive: true)
WebSocketService webSocketService(Ref ref) {
  return WebSocketService.instance;
}

/// Initialized WebSocket service provider
@riverpod
Future<WebSocketService> initializedWebSocketService(Ref ref) async {
  final service = ref.watch(webSocketServiceProvider);
  final httpClient = ref.watch(httpClientProvider);
  final jwtService = ref.watch(jwtServiceProvider);

  await service.initialize(httpClient, jwtService: jwtService);
  return service;
}

/// Message API service provider
@riverpod
MessageApiService messageApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return MessageApiService(httpClient);
}

/// Real-time notification service provider
@Riverpod(keepAlive: true)
RealtimeNotificationService realtimeNotificationService(Ref ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  return RealtimeNotificationService(webSocketService);
}

/// WebSocket connection state provider
@riverpod
Stream<bool> webSocketConnectionState(Ref ref) {
  final service = ref.watch(webSocketServiceProvider);
  return service.connectionState;
}

/// Real-time conversations provider
@riverpod
Stream<List<Conversation>> realtimeConversations(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);
  final messageApiService = ref.watch(messageApiServiceProvider);

  // Initial load from API
  try {
    final response = await messageApiService.getConversations();
    if (response.success) {
      yield response.conversations;
    } else {
      yield [];
    }
  } catch (e) {
    yield [];
  }

  // Listen for real-time updates
  await for (final messageData in webSocketService.messageStream) {
    if (messageData['type'] == 'conversationStarted') {
      // Reload conversations when a new one is started
      try {
        final response = await messageApiService.getConversations();
        if (response.success) {
          yield response.conversations;
        }
      } catch (e) {
        // Continue with current state on error
      }
    } else if (messageData.containsKey('conversationId')) {
      // Update existing conversation with new message
      try {
        final response = await messageApiService.getConversations();
        if (response.success) {
          yield response.conversations;
        }
      } catch (e) {
        // Continue with current state on error
      }
    }
  }
}

/// Real-time messages provider for a specific conversation
@riverpod
Stream<List<Message>> realtimeMessages(Ref ref, String conversationId) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);
  final messageApiService = ref.watch(messageApiServiceProvider);

  // Join conversation room
  webSocketService.joinConversationRoom(conversationId);

  // Initial load from API
  try {
    final response = await messageApiService.getMessages(conversationId);
    if (response.success) {
      yield response.messages;
    } else {
      yield [];
    }
  } catch (e) {
    yield [];
  }

  // Listen for real-time updates
  await for (final messageData in webSocketService.messageStream) {
    if (messageData['conversationId']?.toString() == conversationId) {
      // Reload messages for this conversation
      try {
        final response = await messageApiService.getMessages(conversationId);
        if (response.success) {
          yield response.messages;
        }
      } catch (e) {
        // Continue with current state on error
      }
    }
  }
}

/// Real-time unread messages count provider
@riverpod
Stream<int> realtimeUnreadMessagesCount(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);
  final messageApiService = ref.watch(messageApiServiceProvider);

  // Initial count from API
  try {
    final response = await messageApiService.getConversations();
    if (response.success) {
      final unreadCount = response.conversations.fold<int>(
        0,
        (sum, conv) => sum + conv.unreadCount,
      );
      yield unreadCount;
    } else {
      yield 0;
    }
  } catch (e) {
    yield 0;
  }

  // Listen for real-time updates
  await for (final _ in webSocketService.messageStream) {
    // Recalculate unread count when new messages arrive
    try {
      final response = await messageApiService.getConversations();
      if (response.success) {
        final unreadCount = response.conversations.fold<int>(
          0,
          (sum, conv) => sum + conv.unreadCount,
        );
        yield unreadCount;
      }
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Real-time queue status provider for all queues
@riverpod
Stream<Map<String, QueueStatus>> realtimeAllQueues(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);

  // Initial empty state
  Map<String, QueueStatus> currentQueues = {};
  yield currentQueues;

  // Listen for real-time queue updates
  await for (final queueData in webSocketService.queueUpdateStream) {
    try {
      final queueStatus = QueueStatus.fromJson(queueData);
      currentQueues = {...currentQueues, queueStatus.queueId: queueStatus};
      yield currentQueues;
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Real-time queue status provider for a specific queue
@riverpod
Stream<QueueStatus> realtimeQueueStatus(Ref ref, String queueId) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);

  // Listen for real-time queue updates
  await for (final queueData in webSocketService.queueUpdateStream) {
    try {
      final queueStatus = QueueStatus.fromJson(queueData);
      if (queueStatus.queueId == queueId) {
        yield queueStatus;
      }
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Real-time notifications provider
@riverpod
Stream<List<AppNotification>> realtimeNotifications(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);

  // Initial empty state
  List<AppNotification> currentNotifications = [];
  yield currentNotifications;

  // Listen for real-time notification updates
  await for (final notificationData in webSocketService.notificationStream) {
    try {
      final notification = notifications.NotificationItem.fromJson(
        notificationData,
      );
      currentNotifications = [notification, ...currentNotifications];
      yield currentNotifications;
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Real-time new notification provider
@riverpod
Stream<AppNotification> realtimeNewNotification(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);

  // Listen for real-time notification updates
  await for (final notificationData in webSocketService.notificationStream) {
    try {
      final notification = notifications.NotificationItem.fromJson(
        notificationData,
      );
      yield notification;
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Real-time unread notifications count provider
@riverpod
Stream<int> realtimeUnreadNotificationsCount(Ref ref) async* {
  final webSocketService = ref.watch(webSocketServiceProvider);

  // Initial count
  int currentCount = 0;
  yield currentCount;

  // Listen for real-time notification updates
  await for (final notificationData in webSocketService.notificationStream) {
    try {
      final notification = notifications.NotificationItem.fromJson(
        notificationData,
      );
      if (!notification.isRead) {
        currentCount++;
        yield currentCount;
      }
    } catch (e) {
      // Continue with current state on error
    }
  }
}

/// Provider for initializing all real-time services
@riverpod
Future<void> initializeRealtimeServices(Ref ref) async {
  // Initialize WebSocket service
  await ref.read(initializedWebSocketServiceProvider.future);

  // Initialize notification service
  final notificationService = ref.read(realtimeNotificationServiceProvider);
  await notificationService.initialize();
}

/// Provider for managing conversation room subscriptions
@riverpod
class ConversationRoomManager extends _$ConversationRoomManager {
  @override
  Set<String> build() {
    return <String>{};
  }

  void joinRoom(String conversationId) {
    final webSocketService = ref.read(webSocketServiceProvider);
    webSocketService.joinConversationRoom(conversationId);
    state = {...state, conversationId};
  }

  void leaveRoom(String conversationId) {
    final webSocketService = ref.read(webSocketServiceProvider);
    webSocketService.leaveConversationRoom(conversationId);
    state = state.where((id) => id != conversationId).toSet();
  }
}

/// Provider for managing queue status requests
@riverpod
class QueueStatusManager extends _$QueueStatusManager {
  @override
  Set<String> build() {
    return <String>{};
  }

  void requestQueueStatus(String queueId) {
    final webSocketService = ref.read(webSocketServiceProvider);
    webSocketService.requestQueueStatus(queueId);
    state = {...state, queueId};
  }
}

/// Provider for real-time service health monitoring
@riverpod
class RealtimeServiceHealth extends _$RealtimeServiceHealth {
  @override
  Map<String, bool> build() {
    return {'websocket': false, 'notifications': false};
  }

  void updateWebSocketHealth(bool isHealthy) {
    state = {...state, 'websocket': isHealthy};
  }

  void updateNotificationHealth(bool isHealthy) {
    state = {...state, 'notifications': isHealthy};
  }
}

/// Provider for connection retry management
@riverpod
class ConnectionRetryManager extends _$ConnectionRetryManager {
  @override
  int build() {
    return 0;
  }

  void incrementRetryCount() {
    state = state + 1;
  }

  void resetRetryCount() {
    state = 0;
  }
}

/// Real-time notification service class
class RealtimeNotificationService {
  final WebSocketService _webSocketService;

  RealtimeNotificationService(this._webSocketService);

  Future<void> initialize() async {
    // Setup notification handling
    _webSocketService.notificationStream.listen((notificationData) {
      _handleRealtimeNotification(notificationData);
    });
  }

  void _handleRealtimeNotification(Map<String, dynamic> notificationData) {
    try {
      // Parse notification data
      final notification = notifications.NotificationItem.fromJson(
        notificationData,
      );

      // Show local notification if app is in background
      // Note: NotificationService.showLocalNotification method needs to be implemented
      // For now, we'll just log the notification
      developer.log(
        '[RealtimeNotificationService] 🔔 Received notification: ${notification.title}',
      );
    } catch (e) {
      developer.log(
        '[RealtimeNotificationService] ❌ Error handling notification: $e',
      );
    }
  }
}
