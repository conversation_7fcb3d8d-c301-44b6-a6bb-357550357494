import '../../../core/network/api_service.dart';
import '../models/queue_models.dart';

/// API service for queue management operations
class QueueApiService extends ApiService {
  QueueApiService(super.httpClient);

  /// Get all queues for the current provider
  Future<List<Queue>> getQueues({bool? isActive, int? locationId}) async {
    try {
      print(
        '[QueueApiService] Getting queues (isActive: $isActive, locationId: $locationId)',
      );

      final queryParams = <String, dynamic>{};
      if (isActive != null) queryParams['isActive'] = isActive;
      if (locationId != null) queryParams['locationId'] = locationId;

      final response = await httpClient.get(
        '/api/auth/providers/queues',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final queuesList = data['data'] as List;
          print('[QueueApiService] Raw queue data: $queuesList');

          final queues =
              queuesList.map((json) {
                print('[QueueApiService] Parsing queue: $json');
                try {
                  return Queue.fromJson(json as Map<String, dynamic>);
                } catch (e) {
                  print('[QueueApiService] Error parsing queue: $e');
                  rethrow;
                }
              }).toList();

          print(
            '[QueueApiService] Successfully retrieved ${queues.length} queues',
          );
          return queues;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception('Failed to get queues: ${response.statusCode}');
      }
    } catch (e) {
      print('[QueueApiService] Error getting queues: $e');
      rethrow;
    }
  }

  /// Get queue by ID
  Future<Queue> getQueueById(int id) async {
    try {
      print('[QueueApiService] Getting queue by ID: $id');

      // Note: API doesn't have a specific get-by-id endpoint, so we'll get all and filter
      final queues = await getQueues();
      final queue = queues.firstWhere(
        (q) => q.id == id,
        orElse: () => throw Exception('Queue not found with ID: $id'),
      );

      print('[QueueApiService] Successfully retrieved queue: ${queue.id}');
      return queue;
    } catch (e) {
      print('[QueueApiService] Error getting queue by ID: $e');
      rethrow;
    }
  }

  /// Create a new queue
  Future<Queue> createQueue(CreateQueueRequest request) async {
    try {
      print('[QueueApiService] Creating queue: ${request.toJson()}');

      final response = await httpClient.post(
        '/api/auth/providers/queues',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final queue = Queue.fromJson(data['data'] as Map<String, dynamic>);
          print('[QueueApiService] Successfully created queue: ${queue.id}');
          return queue;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception('Failed to create queue: ${response.statusCode}');
      }
    } catch (e) {
      print('[QueueApiService] Error creating queue: $e');
      rethrow;
    }
  }

  /// Create a new queue with opening hours
  Future<Queue> createQueueWithOpeningHours(
    Map<String, dynamic> requestData,
  ) async {
    print('[QueueApiService] Creating queue with opening hours: $requestData');

    final response = await httpClient.post(
      '/api/auth/providers/queues',
      data: requestData,
    );

    final data = response.data;
    if (data['success'] == true && data['data'] != null) {
      final queue = Queue.fromJson(data['data'] as Map<String, dynamic>);
      print(
        '[QueueApiService] Successfully created queue with opening hours: ${queue.id}',
      );
      return queue;
    } else {
      throw Exception(
        'Invalid response format: ${data['message'] ?? 'Unknown error'}',
      );
    }
  }

  /// Update an existing queue
  Future<Queue> updateQueue(int id, UpdateQueueRequest request) async {
    try {
      print('[QueueApiService] Updating queue $id: ${request.toJson()}');

      final response = await httpClient.put(
        '/api/auth/providers/queues/$id',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final queue = Queue.fromJson(data['data'] as Map<String, dynamic>);
          print('[QueueApiService] Successfully updated queue: ${queue.id}');
          return queue;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception('Failed to update queue: ${response.statusCode}');
      }
    } catch (e) {
      print('[QueueApiService] Error updating queue: $e');
      rethrow;
    }
  }

  /// Delete a queue
  Future<bool> deleteQueue(int id) async {
    try {
      print('[QueueApiService] Deleting queue: $id');

      final response = await httpClient.delete(
        '/api/auth/providers/queues/$id',
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          print('[QueueApiService] Successfully deleted queue: $id');
          return true;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else if (response.statusCode == 409) {
        throw Exception('Cannot delete queue: Queue has future appointments');
      } else {
        throw Exception('Failed to delete queue: ${response.statusCode}');
      }
    } catch (e) {
      print('[QueueApiService] Error deleting queue: $e');
      rethrow;
    }
  }

  /// Get queues for a specific location
  Future<List<Queue>> getQueuesByLocation(
    int locationId, {
    bool? isActive,
  }) async {
    try {
      print('[QueueApiService] Getting queues for location: $locationId');

      final queryParams = <String, dynamic>{};
      if (isActive != null) queryParams['isActive'] = isActive;

      final response = await httpClient.get(
        '/api/auth/providers/locations/$locationId/queues',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final queuesList = data['data'] as List;
          final queues =
              queuesList
                  .map((json) => Queue.fromJson(json as Map<String, dynamic>))
                  .toList();

          print(
            '[QueueApiService] Successfully retrieved ${queues.length} queues for location $locationId',
          );
          return queues;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception(
          'Failed to get queues for location: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[QueueApiService] Error getting queues for location: $e');
      rethrow;
    }
  }

  /// Get services assigned to a specific queue
  Future<List<QueueService>> getQueueServices(int queueId) async {
    try {
      print('[QueueApiService] Getting services for queue: $queueId');

      final response = await httpClient.get(
        '/api/auth/providers/queues/$queueId/services',
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final servicesList = data['data'] as List;
          final queueServices =
              servicesList
                  .map(
                    (json) =>
                        QueueService.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();

          print(
            '[QueueApiService] Successfully retrieved ${queueServices.length} services for queue $queueId',
          );
          return queueServices;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception('Failed to get queue services: ${response.statusCode}');
      }
    } catch (e) {
      print('[QueueApiService] Error getting queue services: $e');
      rethrow;
    }
  }

  /// Assign a service to a queue
  Future<QueueService> assignServiceToQueue(
    int queueId,
    AssignServiceRequest request,
  ) async {
    try {
      print(
        '[QueueApiService] Assigning service to queue $queueId: ${request.toJson()}',
      );

      final response = await httpClient.post(
        '/api/auth/providers/queues/$queueId/services',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final queueService = QueueService.fromJson(
            data['data'] as Map<String, dynamic>,
          );
          print('[QueueApiService] Successfully assigned service to queue');
          return queueService;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else {
        throw Exception(
          'Failed to assign service to queue: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[QueueApiService] Error assigning service to queue: $e');
      rethrow;
    }
  }

  /// Remove a service from a queue
  Future<bool> removeServiceFromQueue(int queueId, int serviceId) async {
    try {
      print(
        '[QueueApiService] Removing service $serviceId from queue $queueId',
      );

      final response = await httpClient.delete(
        '/api/auth/providers/queues/$queueId/services/$serviceId',
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          print('[QueueApiService] Successfully removed service from queue');
          return true;
        } else {
          throw Exception(
            'Invalid response format: ${data['message'] ?? 'Unknown error'}',
          );
        }
      } else if (response.statusCode == 409) {
        throw Exception('Cannot remove last service from queue');
      } else {
        throw Exception(
          'Failed to remove service from queue: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[QueueApiService] Error removing service from queue: $e');
      rethrow;
    }
  }

  /// Bulk assign services to a queue
  Future<List<QueueService>> bulkAssignServicesToQueue(
    int queueId,
    List<int> serviceIds,
  ) async {
    try {
      print(
        '[QueueApiService] Bulk assigning ${serviceIds.length} services to queue $queueId',
      );

      final queueServices = <QueueService>[];

      // Assign services one by one (API doesn't support bulk assignment)
      for (final serviceId in serviceIds) {
        try {
          final request = AssignServiceRequest(serviceId: serviceId);
          final queueService = await assignServiceToQueue(queueId, request);
          queueServices.add(queueService);
        } catch (e) {
          print('[QueueApiService] Failed to assign service $serviceId: $e');
          // Continue with other services
        }
      }

      print(
        '[QueueApiService] Successfully assigned ${queueServices.length}/${serviceIds.length} services',
      );
      return queueServices;
    } catch (e) {
      print('[QueueApiService] Error in bulk service assignment: $e');
      rethrow;
    }
  }

  /// Bulk remove services from a queue
  Future<bool> bulkRemoveServicesFromQueue(
    int queueId,
    List<int> serviceIds,
  ) async {
    try {
      print(
        '[QueueApiService] Bulk removing ${serviceIds.length} services from queue $queueId',
      );

      int successCount = 0;

      // Remove services one by one (API doesn't support bulk removal)
      for (final serviceId in serviceIds) {
        try {
          final success = await removeServiceFromQueue(queueId, serviceId);
          if (success) successCount++;
        } catch (e) {
          print('[QueueApiService] Failed to remove service $serviceId: $e');
          // Continue with other services
        }
      }

      print(
        '[QueueApiService] Successfully removed $successCount/${serviceIds.length} services',
      );
      return successCount == serviceIds.length;
    } catch (e) {
      print('[QueueApiService] Error in bulk service removal: $e');
      return false;
    }
  }
}
