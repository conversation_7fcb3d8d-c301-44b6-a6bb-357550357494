import 'package:flutter/material.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';

/// Base template for wizard pages
class WizardPage extends StatelessWidget {
  final OnboardingStep step;
  final String? title;
  final String? subtitle;
  final Widget content;
  final Widget? header;
  final Widget? footer;
  final EdgeInsetsGeometry? padding;
  final bool scrollable;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;

  const WizardPage({
    super.key,
    required this.step,
    required this.content,
    this.title,
    this.subtitle,
    this.header,
    this.footer,
    this.padding,
    this.scrollable = true,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final pageTitle = title ?? step.title;
    final pageSubtitle = subtitle ?? step.description;
    
    Widget body = Column(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header section
        if (header != null) ...[
          header!,
          const SizedBox(height: 16),
        ],
        
        // Title and subtitle
        if (pageTitle.isNotEmpty) ...[
          _buildTitle(context, pageTitle),
          const SizedBox(height: 8),
        ],
        
        if (pageSubtitle.isNotEmpty) ...[
          _buildSubtitle(context, pageSubtitle),
          const SizedBox(height: 24),
        ],
        
        // Main content
        if (scrollable)
          Flexible(child: content)
        else
          content,
        
        // Footer section
        if (footer != null) ...[
          const SizedBox(height: 16),
          footer!,
        ],
      ],
    );
    
    if (scrollable) {
      body = SingleChildScrollView(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: body,
      );
    } else {
      body = Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: body,
      );
    }
    
    return body;
  }

  Widget _buildTitle(BuildContext context, String title) {
    return Text(
      title,
      style: context.textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: context.colors.onSurface,
      ),
      textAlign: crossAxisAlignment == CrossAxisAlignment.center 
          ? TextAlign.center 
          : TextAlign.start,
    );
  }

  Widget _buildSubtitle(BuildContext context, String subtitle) {
    return Text(
      subtitle,
      style: context.textTheme.bodyLarge?.copyWith(
        color: context.colors.onSurfaceVariant,
      ),
      textAlign: crossAxisAlignment == CrossAxisAlignment.center 
          ? TextAlign.center 
          : TextAlign.start,
    );
  }
}

/// Wizard page with form layout
class WizardFormPage extends StatelessWidget {
  final OnboardingStep step;
  final String? title;
  final String? subtitle;
  final GlobalKey<FormState> formKey;
  final List<Widget> fields;
  final Widget? header;
  final Widget? footer;
  final EdgeInsetsGeometry? padding;
  final double fieldSpacing;

  const WizardFormPage({
    super.key,
    required this.step,
    required this.formKey,
    required this.fields,
    this.title,
    this.subtitle,
    this.header,
    this.footer,
    this.padding,
    this.fieldSpacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return WizardPage(
      step: step,
      title: title,
      subtitle: subtitle,
      header: header,
      footer: footer,
      padding: padding,
      content: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: _buildFormFields(),
        ),
      ),
    );
  }

  List<Widget> _buildFormFields() {
    final widgets = <Widget>[];
    
    for (int i = 0; i < fields.length; i++) {
      widgets.add(fields[i]);
      
      // Add spacing between fields (except after last field)
      if (i < fields.length - 1) {
        widgets.add(SizedBox(height: fieldSpacing));
      }
    }
    
    return widgets;
  }
}

/// Wizard page with card layout
class WizardCardPage extends StatelessWidget {
  final OnboardingStep step;
  final String? title;
  final String? subtitle;
  final List<Widget> cards;
  final Widget? header;
  final Widget? footer;
  final EdgeInsetsGeometry? padding;
  final double cardSpacing;
  final CrossAxisAlignment crossAxisAlignment;

  const WizardCardPage({
    super.key,
    required this.step,
    required this.cards,
    this.title,
    this.subtitle,
    this.header,
    this.footer,
    this.padding,
    this.cardSpacing = 12.0,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return WizardPage(
      step: step,
      title: title,
      subtitle: subtitle,
      header: header,
      footer: footer,
      padding: padding,
      crossAxisAlignment: crossAxisAlignment,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: crossAxisAlignment,
        children: _buildCards(),
      ),
    );
  }

  List<Widget> _buildCards() {
    final widgets = <Widget>[];
    
    for (int i = 0; i < cards.length; i++) {
      widgets.add(cards[i]);
      
      // Add spacing between cards (except after last card)
      if (i < cards.length - 1) {
        widgets.add(SizedBox(height: cardSpacing));
      }
    }
    
    return widgets;
  }
}

/// Wizard page with list layout
class WizardListPage extends StatelessWidget {
  final OnboardingStep step;
  final String? title;
  final String? subtitle;
  final List<Widget> items;
  final Widget? header;
  final Widget? footer;
  final Widget? emptyState;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const WizardListPage({
    super.key,
    required this.step,
    required this.items,
    this.title,
    this.subtitle,
    this.header,
    this.footer,
    this.emptyState,
    this.padding,
    this.shrinkWrap = true,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    Widget content;
    
    if (items.isEmpty && emptyState != null) {
      content = emptyState!;
    } else {
      content = ListView.separated(
        shrinkWrap: shrinkWrap,
        physics: physics ?? const NeverScrollableScrollPhysics(),
        itemCount: items.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemBuilder: (context, index) => items[index],
      );
    }
    
    return WizardPage(
      step: step,
      title: title,
      subtitle: subtitle,
      header: header,
      footer: footer,
      padding: padding,
      scrollable: !shrinkWrap,
      content: content,
    );
  }
}

/// Wizard page with centered content
class WizardCenteredPage extends StatelessWidget {
  final OnboardingStep step;
  final String? title;
  final String? subtitle;
  final Widget content;
  final Widget? header;
  final Widget? footer;
  final EdgeInsetsGeometry? padding;

  const WizardCenteredPage({
    super.key,
    required this.step,
    required this.content,
    this.title,
    this.subtitle,
    this.header,
    this.footer,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return WizardPage(
      step: step,
      title: title,
      subtitle: subtitle,
      header: header,
      footer: footer,
      padding: padding,
      scrollable: false,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      content: content,
    );
  }
}

/// Error state widget for wizard pages
class WizardErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;

  const WizardErrorState({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: 64,
              color: context.colors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Loading state widget for wizard pages
class WizardLoadingState extends StatelessWidget {
  final String? message;

  const WizardLoadingState({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message!,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
