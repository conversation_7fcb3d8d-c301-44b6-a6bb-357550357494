import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/services/widgets/color_picker_widget.dart';

void main() {
  group('Color Picker Fix Tests', () {
    testWidgets('Color picker should display single hash symbol', (WidgetTester tester) async {
      String selectedColor = '#FF5722';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                selectedColor = color;
              },
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Find the text input field
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Verify that the input shows only the hex part (without #)
      // The # should be shown as prefixText, not in the actual input value
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('FF5722')); // No # in the actual text

      // Verify that the prefix text is displayed
      expect(find.text('#'), findsOneWidget);
    });

    testWidgets('Color input should show correct character count', (WidgetTester tester) async {
      String selectedColor = '#FF5722';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                selectedColor = color;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the text input field
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Verify that the input field contains the correct hex value
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('FF5722'));

      // The counter should show "6/6" for a complete hex color
      expect(find.text('6/6'), findsOneWidget);
    });

    testWidgets('Selecting color from palette should update input field', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                selectedColor = color;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find a different color in the palette (Blue)
      final blueColorContainer = find.byWidgetPredicate(
        (widget) => widget is Container && 
                   widget.decoration is BoxDecoration &&
                   (widget.decoration as BoxDecoration).color == const Color(0xFF2196F3),
      );
      
      expect(blueColorContainer, findsOneWidget);

      // Tap on the blue color
      await tester.tap(blueColorContainer);
      await tester.pumpAndSettle();

      // Verify that the input field was updated
      final textField = find.byType(TextFormField);
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('2196F3')); // Blue hex without #
    });

    testWidgets('Typing in input field should update color', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      String? lastColorChanged;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                lastColorChanged = color;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the text input field and clear it
      final textField = find.byType(TextFormField);
      await tester.tap(textField);
      await tester.pumpAndSettle();

      // Clear the field and enter a new color
      await tester.enterText(textField, '00FF00'); // Green
      await tester.pumpAndSettle();

      // Verify that onColorChanged was called with the # prefix
      expect(lastColorChanged, equals('#00FF00'));
    });

    testWidgets('Color preview should show correct color', (WidgetTester tester) async {
      const selectedColor = '#FF5722'; // Deep Orange
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the color preview container
      final colorPreview = find.byWidgetPredicate(
        (widget) => widget is Container && 
                   widget.decoration is BoxDecoration &&
                   (widget.decoration as BoxDecoration).color == const Color(0xFFFF5722),
      );
      
      expect(colorPreview, findsOneWidget);

      // Verify the color text is displayed
      expect(find.text('#FF5722'), findsOneWidget);
    });

    testWidgets('Invalid hex input should show validation error', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ColorPickerWidget(
                selectedColor: selectedColor,
                onColorChanged: (color) {
                  selectedColor = color;
                },
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the text input field and enter invalid hex
      final textField = find.byType(TextFormField);
      await tester.tap(textField);
      await tester.pumpAndSettle();

      await tester.enterText(textField, 'GGGGGG'); // Invalid hex
      await tester.pumpAndSettle();

      // Trigger validation
      final form = tester.widget<Form>(find.byType(Form));
      final formState = form.key as GlobalKey<FormState>;
      formState.currentState?.validate();
      await tester.pumpAndSettle();

      // Should show validation error
      expect(find.text('Invalid hex color'), findsOneWidget);
    });

    test('Color parsing should handle different formats', () {
      // Test the color parsing logic
      const testCases = [
        ('#FF5722', Color(0xFFFF5722)),
        ('FF5722', Color(0xFFFF5722)),
        ('#2196F3', Color(0xFF2196F3)),
        ('2196F3', Color(0xFF2196F3)),
      ];

      for (final testCase in testCases) {
        final input = testCase.$1;
        final expected = testCase.$2;
        
        // Parse color similar to how ColorPickerWidget does it
        final hexValue = input.startsWith('#') ? input.substring(1) : input;
        final colorValue = int.tryParse(hexValue, radix: 16);
        final actualColor = colorValue != null ? Color(0xFF000000 | colorValue) : Colors.grey;
        
        expect(actualColor, equals(expected), 
            reason: 'Color parsing failed for input: $input');
      }
    });

    test('Hex validation should work correctly', () {
      // Test hex validation logic
      const validHexCodes = [
        'FF5722',
        '2196F3',
        '000000',
        'FFFFFF',
        'abcdef',
        'ABCDEF',
        '123456',
      ];

      const invalidHexCodes = [
        'GGGGGG',
        '12345',   // Too short
        '1234567', // Too long
        'XYZ123',
        '',
        'FF55ZZ',
      ];

      final hexPattern = RegExp(r'^[0-9A-Fa-f]{6}$');

      for (final validHex in validHexCodes) {
        expect(hexPattern.hasMatch(validHex), isTrue,
            reason: 'Valid hex code should pass validation: $validHex');
      }

      for (final invalidHex in invalidHexCodes) {
        expect(hexPattern.hasMatch(invalidHex), isFalse,
            reason: 'Invalid hex code should fail validation: $invalidHex');
      }
    });

    testWidgets('Color picker should update when selectedColor prop changes', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: Column(
                  children: [
                    ColorPickerWidget(
                      selectedColor: selectedColor,
                      onColorChanged: (color) {
                        setState(() {
                          selectedColor = color;
                        });
                      },
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          selectedColor = '#00FF00'; // Change to green
                        });
                      },
                      child: const Text('Change to Green'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Initially should show orange
      final textField = find.byType(TextFormField);
      TextFormField textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('FF5722'));

      // Tap the button to change color
      await tester.tap(find.text('Change to Green'));
      await tester.pumpAndSettle();

      // Should now show green
      textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('00FF00'));
    });
  });
}
