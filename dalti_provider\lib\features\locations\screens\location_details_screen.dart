import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/routing/app_routes.dart';
import '../models/location_models.dart';
import '../providers/location_provider.dart';
import '../widgets/location_info_card.dart';
import '../widgets/location_queues_section.dart';

class LocationDetailsScreen extends ConsumerStatefulWidget {
  final String locationId;

  const LocationDetailsScreen({super.key, required this.locationId});

  @override
  ConsumerState<LocationDetailsScreen> createState() =>
      _LocationDetailsScreenState();
}

class _LocationDetailsScreenState extends ConsumerState<LocationDetailsScreen> {
  Location? _location;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLocation();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh location data when screen gains focus
    _loadLocation();
  }

  Future<void> _loadLocation() async {
    final locationId = int.tryParse(widget.locationId);
    if (locationId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invalid location ID'),
            backgroundColor: Colors.red,
          ),
        );
        context.pop();
      }
      return;
    }

    setState(() => _isLoading = true);

    final location = await ref
        .read(locationNotifierProvider.notifier)
        .getLocationById(locationId);

    if (location != null && mounted) {
      setState(() {
        _location = location;
        _isLoading = false;
      });
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Location not found'),
          backgroundColor: Colors.red,
        ),
      );
      context.pop();
    }
  }

  void _onEditLocation() async {
    if (_location != null) {
      await context.push('/locations/${_location!.id}/edit');
      // Refresh data when returning from edit screen
      _loadLocation();
    }
  }

  Future<void> _onDeleteLocation() async {
    if (_location == null) return;

    // Check if location can be deleted
    final canDelete = await ref
        .read(locationNotifierProvider.notifier)
        .canDeleteLocation(_location!.id);

    if (!canDelete && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Cannot delete location. It has associated queues or schedules.',
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Show confirmation dialog
    if (!mounted) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Location'),
            content: Text(
              'Are you sure you want to delete "${_location!.name}"?\n\nThis action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final success = await ref
          .read(locationNotifierProvider.notifier)
          .deleteLocation(_location!.id);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Location "${_location!.name}" deleted successfully',
              ),
              backgroundColor: Colors.green,
            ),
          );
          context.pop(); // Go back to locations list
        } else {
          final locationData = ref.read(locationNotifierProvider);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(locationData.error ?? 'Failed to delete location'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _onAddQueue() {
    if (_location != null) {
      context.push('${AppRoutes.addQueue}?locationId=${_location!.id}');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Location Details')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_location == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Location Details')),
        body: const Center(child: Text('Location not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_location!.name),
        actions: [
          // Edit button
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit Location',
            onPressed: _onEditLocation,
          ),
          // Delete button
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'Delete Location',
            onPressed: _onDeleteLocation,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Location Information Card
            LocationInfoCard(location: _location!),

            const SizedBox(height: 24),

            // Queues Section
            LocationQueuesSection(
              locationId: _location!.id,
              onAddQueue: _onAddQueue,
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
