import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../auth/jwt_service.dart';
import '../network/http_client.dart';

/// WebSocket service for real-time communication using WebSocketChannel
class WebSocketService {
  static WebSocketService? _instance;
  static WebSocketService get instance => _instance ??= WebSocketService._();

  WebSocketService._();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  late final JwtService _jwtService;

  // Connection state
  bool _isConnected = false;
  bool _isConnecting = false;
  bool _shouldReconnect = true;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  // Event streams
  final StreamController<bool> _connectionStateController =
      StreamController<bool>.broadcast();
  final StreamController<Map<String, dynamic>> _messageController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _queueUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _notificationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _conversationController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _messageReadController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Getters for streams
  Stream<bool> get connectionState => _connectionStateController.stream;
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<Map<String, dynamic>> get queueUpdateStream =>
      _queueUpdateController.stream;
  Stream<Map<String, dynamic>> get notificationStream =>
      _notificationController.stream;
  Stream<Map<String, dynamic>> get conversationStream =>
      _conversationController.stream;
  Stream<Map<String, dynamic>> get messageReadStream =>
      _messageReadController.stream;

  bool get isConnected => _isConnected;

  /// Initialize WebSocket service
  Future<void> initialize(
    HttpClient httpClient, {
    JwtService? jwtService,
  }) async {
    developer.log('[WebSocketService] 🚀 Initializing WebSocket service...');
    print('[WebSocketService] 🚀 Initializing WebSocket service...');

    // Use provided JWT service or create new one
    if (jwtService != null) {
      _jwtService = jwtService;
      developer.log(
        '[WebSocketService] 🔑 Using provided JWT service instance',
      );
      print('[WebSocketService] 🔑 Using provided JWT service instance');
    } else {
      _jwtService = JwtService(httpClient);
      developer.log('[WebSocketService] 🔑 Created new JWT service instance');
      print('[WebSocketService] 🔑 Created new JWT service instance');
    }

    // Initialize JWT service if not already done
    try {
      await _jwtService.initialize();
      developer.log('[WebSocketService] ✅ JWT service initialized');
      print('[WebSocketService] ✅ JWT service initialized');
    } catch (e) {
      developer.log(
        '[WebSocketService] ❌ JWT service initialization failed: $e',
      );
      print('[WebSocketService] ❌ JWT service initialization failed: $e');
    }

    // Listen to authentication changes
    _jwtService.tokenStream.listen((token) {
      developer.log(
        '[WebSocketService] 🔄 Auth token changed - Token exists: ${token != null}',
      );
      print(
        '[WebSocketService] 🔄 Auth token changed - Token exists: ${token != null}',
      );

      if (token != null && token.isValid) {
        developer.log(
          '[WebSocketService] ✅ Valid token received - Connecting to WebSocket...',
        );
        print(
          '[WebSocketService] ✅ Valid token received - Connecting to WebSocket...',
        );
        print(
          '[WebSocketService] 🚀 ABOUT TO CALL connect() from token listener',
        );
        connect();
        print('[WebSocketService] 🚀 CALLED connect() from token listener');
      } else {
        developer.log(
          '[WebSocketService] ❌ Invalid or null token - Disconnecting...',
        );
        print('[WebSocketService] ❌ Invalid or null token - Disconnecting...');
        disconnect();
      }
    });

    // Check current authentication status
    final isAuthenticated = _jwtService.isAuthenticated;
    final currentToken = _jwtService.currentToken;
    developer.log(
      '[WebSocketService] 🔍 Current authentication status: $isAuthenticated',
    );
    developer.log(
      '[WebSocketService] 🔍 Current token exists: ${currentToken != null}',
    );

    // Auto-connect if already authenticated
    if (isAuthenticated) {
      developer.log(
        '[WebSocketService] 🔄 User already authenticated - Auto-connecting...',
      );
      print(
        '[WebSocketService] 🔄 User already authenticated - Auto-connecting...',
      );
      // Use Future.microtask to ensure the connection happens after initialization completes
      Future.microtask(() {
        print(
          '[WebSocketService] 🚀 ABOUT TO CALL connect() from auto-connect',
        );
        connect();
        print('[WebSocketService] 🚀 CALLED connect() from auto-connect');
      });
    } else {
      developer.log(
        '[WebSocketService] ⏳ User not authenticated - Waiting for authentication...',
      );
    }

    developer.log(
      '[WebSocketService] ✅ WebSocket service initialization complete',
    );
  }

  /// Connect to WebSocket server
  Future<void> connect() async {
    developer.log('[WebSocketService] CONNECT METHOD CALLED!');
    print('[WebSocketService] CONNECT METHOD CALLED!');
    developer.log('[WebSocketService] Platform check - kIsWeb: $kIsWeb');
    print('[WebSocketService] Platform check - kIsWeb: $kIsWeb');

    if (_isConnected || _isConnecting) {
      developer.log(
        '[WebSocketService] Already connected or connecting - Status: Connected=$_isConnected, Connecting=$_isConnecting',
      );
      print(
        '[WebSocketService] Already connected or connecting - Status: Connected=$_isConnected, Connecting=$_isConnecting',
      );
      return;
    }

    final token = _jwtService.currentToken?.accessToken;
    if (token == null) {
      developer.log(
        '[WebSocketService] ❌ No auth token available - Cannot connect',
      );
      return;
    }

    _isConnecting = true;
    final wsUrl = _getWebSocketUrl(token);
    developer.log('[WebSocketService] 🔄 Connecting to: $wsUrl');
    print('[WebSocketService] 🔄 Connecting to: $wsUrl');
    try {
      // Create WebSocket connection with Socket.IO URL
      developer.log('[WebSocketService] 🔗 Attempting to connect to: $wsUrl');
      print('[WebSocketService] 🔗 Attempting to connect to: $wsUrl');

      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      developer.log(
        '[WebSocketService] ⚙️ WebSocket channel created for Socket.IO',
      );
      print('[WebSocketService] ⚙️ WebSocket channel created for Socket.IO');

      // Listen to messages with timeout for Socket.IO handshake
      _subscription = _channel!.stream
          .timeout(
            const Duration(
              seconds: 30,
            ), // Increased timeout to 30 seconds for better debugging
            onTimeout: (sink) {
              developer.log(
                '[WebSocketService] ❌ Connection timeout after 30 seconds - no handshake received',
              );
              print(
                '[WebSocketService] ❌ Connection timeout after 30 seconds - no handshake received',
              );
              print(
                '[WebSocketService] 🔍 Expected Socket.IO open packet "0{" but received nothing',
              );
              if (!_isConnected) {
                _channel?.sink.close();
                _isConnecting = false;
                _scheduleReconnect();
              }
            },
          )
          .listen(
            _handleSocketIOMessage,
            onError: _handleError,
            onDone: _handleDisconnect,
          );

      developer.log('[WebSocketService] ⚙️ Socket.IO message listener set up');
      print('[WebSocketService] ⚙️ Socket.IO message listener set up');
      print('[WebSocketService] 🔍 Waiting for Socket.IO handshake...');
    } catch (e) {
      developer.log('[WebSocketService] ❌ Connection error: $e');
      print('[WebSocketService] ❌ Connection error: $e');
      _isConnecting = false;
      _scheduleReconnect();
    }
  }

  /// Get WebSocket URL with authentication
  String _getWebSocketUrl(String token) {
    // Use Socket.IO WebSocket URL format as per working example
    // Try different URL formats to debug connection issues

    // Test different formats to see which one works
    // Format 1: Standard Socket.IO format without sessionId in URL
    final baseUrl = 'wss://dapi-test.adscloud.org:8443/socket.io/';
    final params = 'EIO=4&transport=websocket';
    final fullUrl = '$baseUrl?$params';

    developer.log('[WebSocketService] 🔗 WebSocket URL: $fullUrl');
    print('[WebSocketService] 🔗 WebSocket URL: $fullUrl');
    print('[WebSocketService] 🔍 Token length: ${token.length}');
    print(
      '[WebSocketService] 🔍 Token preview: ${token.substring(0, token.length > 10 ? 10 : token.length)}...',
    );
    print(
      '[WebSocketService] 🔍 Note: SessionId will be sent after handshake, not in URL',
    );

    return fullUrl;
  }

  /// Handle Socket.IO messages following the working example
  void _handleSocketIOMessage(dynamic message) {
    if (message is String) {
      developer.log(
        '[WebSocketService] 📨 Socket.IO message received: ${message.substring(0, message.length > 100 ? 100 : message.length)}${message.length > 100 ? '...' : ''}',
      );
      print(
        '[WebSocketService] 📨 Socket.IO message received: ${message.substring(0, message.length > 100 ? 100 : message.length)}${message.length > 100 ? '...' : ''}',
      );

      if (message.startsWith('0{')) {
        // Socket.IO server open packet received
        developer.log(
          '[WebSocketService] ✅ Socket.IO server open packet received',
        );

        // Mark as connected when we receive the open packet
        if (!_isConnected) {
          _isConnected = true;
          _connectionStateController.add(true);
        }

        // Send authentication with sessionId
        final token = _jwtService.currentToken?.accessToken;
        if (token != null && token.isNotEmpty) {
          final authPayload = jsonEncode({"sessionId": token});
          final connectMessage = '40$authPayload';
          developer.log(
            '[WebSocketService] 🔑 Sending Socket.IO connect with auth: $connectMessage',
          );
          _channel!.sink.add(connectMessage);
        } else {
          developer.log(
            '[WebSocketService] ❌ No session ID available for authentication',
          );
          _handleError('No session ID for handshake');
        }
      } else if (message.startsWith('40')) {
        // Socket.IO namespace connection confirmed
        developer.log(
          '[WebSocketService] ✅ Socket.IO namespace connection confirmed',
        );

        _isConnecting = false;
        _reconnectAttempts = 0;

        developer.log(
          '[WebSocketService] ✅ Socket.IO handshake complete. Connection successful.',
        );

        // Join user room after successful handshake
        _joinUserRoom();

        // Request queue status if needed
        // _requestQueueStatus();
      } else if (message == '2') {
        // Socket.IO Ping received, send Pong
        developer.log(
          '[WebSocketService] 🏓 Socket.IO Ping received. Sending Pong (3).',
        );
        _channel!.sink.add('3');
      } else if (message.startsWith('42[')) {
        // Socket.IO Data message
        developer.log('[WebSocketService] 📨 Socket.IO Data received');

        // Ensure connection flags are set if data is received
        if (!_isConnected) {
          _isConnected = true;
          _connectionStateController.add(true);
        }
        if (_isConnecting) {
          _isConnecting = false;
          _reconnectAttempts = 0;
          developer.log(
            '[WebSocketService] ✅ Data received, marking connection as successful',
          );
        }

        // Parse the Socket.IO data message
        try {
          final dataContent = message.substring(2); // Remove '42' prefix
          final data = jsonDecode(dataContent);
          if (data is List && data.isNotEmpty) {
            final eventName = data[0];
            final eventData = data.length > 1 ? data[1] : null;

            developer.log('[WebSocketService] 🎯 Socket.IO event: $eventName');
            _handleSocketIOEvent(eventName, eventData);
          }
        } catch (e) {
          developer.log(
            '[WebSocketService] ❌ Error parsing Socket.IO data: $e',
          );
        }
      } else {
        developer.log(
          '[WebSocketService] ❓ Unknown Socket.IO message: $message',
        );
      }
    }
  }

  /// Handle Socket.IO events
  void _handleSocketIOEvent(String eventName, dynamic eventData) {
    developer.log('[WebSocketService] 🎯 Handling Socket.IO event: $eventName');

    switch (eventName) {
      case 'newMessage':
        developer.log('[WebSocketService] 💬 New message received');
        if (eventData != null) {
          _messageController.add(Map<String, dynamic>.from(eventData));
        }
        break;
      case 'conversationStarted':
        developer.log('[WebSocketService] 🆕 Conversation started');
        if (eventData != null) {
          _conversationController.add(Map<String, dynamic>.from(eventData));
        }
        break;
      case 'messageRead':
        developer.log('[WebSocketService] 👁️ Message read');
        if (eventData != null) {
          _messageReadController.add(Map<String, dynamic>.from(eventData));
        }
        break;
      case 'queueUpdate':
        developer.log('[WebSocketService] 📋 Queue update');
        if (eventData != null) {
          _queueUpdateController.add(Map<String, dynamic>.from(eventData));
        }
        break;
      case 'notification':
        developer.log('[WebSocketService] 🔔 Notification received');
        if (eventData != null) {
          _notificationController.add(Map<String, dynamic>.from(eventData));
        }
        break;
      default:
        developer.log(
          '[WebSocketService] ❓ Unknown Socket.IO event: $eventName',
        );
        developer.log('[WebSocketService] 📄 Event data: $eventData');
    }
  }

  /// Handle connection errors
  void _handleError(dynamic error) {
    developer.log('[WebSocketService] ❌ WebSocket error: $error');
    print('[WebSocketService] ❌ WebSocket error: $error');
    print('[WebSocketService] ❌ Error type: ${error.runtimeType}');
    if (error is Exception) {
      print('[WebSocketService] ❌ Exception details: ${error.toString()}');
    }
    _isConnected = false;
    _isConnecting = false;
    _connectionStateController.add(false);
    _scheduleReconnect();
  }

  /// Handle disconnection
  void _handleDisconnect() {
    developer.log('[WebSocketService] 🔌 WebSocket disconnected');
    print('[WebSocketService] 🔌 WebSocket disconnected');
    print(
      '[WebSocketService] 🔌 Was connected: $_isConnected, Was connecting: $_isConnecting',
    );
    _isConnected = false;
    _isConnecting = false;
    _connectionStateController.add(false);
    if (_shouldReconnect) {
      print('[WebSocketService] 🔄 Scheduling reconnect...');
      _scheduleReconnect();
    } else {
      print(
        '[WebSocketService] ⏹️ Not reconnecting (shouldReconnect: $_shouldReconnect)',
      );
    }
  }

  /// Disconnect from WebSocket server
  void disconnect() {
    developer.log(
      '[WebSocketService] 🔌 Disconnecting from WebSocket server...',
    );
    _shouldReconnect = false;
    _reconnectTimer?.cancel();

    _subscription?.cancel();
    _subscription = null;

    _channel?.sink.close();
    _channel = null;

    _isConnected = false;
    _isConnecting = false;
    _connectionStateController.add(false);

    developer.log('[WebSocketService] ✅ Disconnected successfully');
  }

  /// Join user room for receiving user-specific events
  void _joinUserRoom() {
    final userId = _jwtService.currentUserId;
    final token = _jwtService.currentToken;

    developer.log('[WebSocketService] 🏠 Attempting to join user room...');
    developer.log('[WebSocketService] 👤 User ID from JWT: $userId');
    developer.log('[WebSocketService] 🔑 Token payload: ${token?.payload}');
    developer.log('[WebSocketService] 🔗 Channel exists: ${_channel != null}');

    if (_channel != null && _isConnected) {
      // Try different user ID sources
      String? actualUserId = userId;

      // If no userId from JWT payload, try to get it from stored user data
      if (actualUserId == null) {
        developer.log(
          '[WebSocketService] 🔍 No userId in JWT, checking stored user data...',
        );
        // For now, we'll use a fallback approach
        // The backend might expect the sessionId as the user identifier
        actualUserId = token?.accessToken; // Use the session token as user ID
        developer.log('[WebSocketService] 🔄 Using session token as user ID');
      }

      if (actualUserId != null) {
        final roomName = 'user_$actualUserId';
        // Send Socket.IO emit message for joining room
        final joinMessage = '42${jsonEncode(['join', roomName])}';
        _channel!.sink.add(joinMessage);
        developer.log('[WebSocketService] 🏠 Joined user room: $roomName');
      } else {
        developer.log(
          '[WebSocketService] ❌ No user identifier available for room joining',
        );
      }
    } else {
      developer.log(
        '[WebSocketService] ❌ Channel not available for room joining',
      );
    }
  }

  /// Join conversation room
  void joinConversationRoom(String conversationId) {
    if (_channel != null && _isConnected) {
      final roomName = 'conversation:$conversationId';
      // Send Socket.IO emit message for joining room
      final joinMessage = '42${jsonEncode(['join', roomName])}';
      _channel!.sink.add(joinMessage);
      developer.log(
        '[WebSocketService] 💬 Joined conversation room: $roomName',
      );
    } else {
      developer.log(
        '[WebSocketService] ❌ Cannot join conversation room - channel: ${_channel != null}, connected: $_isConnected',
      );
    }
  }

  /// Leave conversation room
  void leaveConversationRoom(String conversationId) {
    if (_channel != null && _isConnected) {
      final roomName = 'conversation:$conversationId';
      // Send Socket.IO emit message for leaving room
      final leaveMessage = '42${jsonEncode(['leave', roomName])}';
      _channel!.sink.add(leaveMessage);
      developer.log('[WebSocketService] 🚪 Left conversation room: $roomName');
    } else {
      developer.log(
        '[WebSocketService] ❌ Cannot leave conversation room - channel: ${_channel != null}, connected: $_isConnected',
      );
    }
  }

  /// Request queue status
  void requestQueueStatus(String queueId) {
    if (_channel != null && _isConnected) {
      // Send Socket.IO emit message for requesting queue status
      final requestMessage =
          '42${jsonEncode([
            'requestQueueStatus',
            {'queueId': queueId},
          ])}';
      _channel!.sink.add(requestMessage);
      developer.log(
        '[WebSocketService] 🏃‍♂️ Requested queue status for: $queueId',
      );
    } else {
      developer.log(
        '[WebSocketService] ❌ Cannot request queue status - channel: ${_channel != null}, connected: $_isConnected',
      );
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      developer.log(
        '[WebSocketService] ❌ Max reconnection attempts reached ($_maxReconnectAttempts)',
      );
      return;
    }

    _reconnectAttempts++;
    developer.log(
      '[WebSocketService] 🔄 Scheduling reconnection attempt $_reconnectAttempts in ${_reconnectDelay.inSeconds}s',
    );

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      if (!_isConnected && _shouldReconnect) {
        developer.log(
          '[WebSocketService] 🔄 Executing scheduled reconnection attempt $_reconnectAttempts',
        );
        connect();
      } else {
        developer.log(
          '[WebSocketService] ⏹️ Skipping reconnection - Connected: $_isConnected, Should reconnect: $_shouldReconnect',
        );
      }
    });
  }

  /// Dispose resources
  void dispose() {
    developer.log('[WebSocketService] 🗑️ Disposing WebSocket service...');
    disconnect();
    _connectionStateController.close();
    _messageController.close();
    _queueUpdateController.close();
    _notificationController.close();
    _conversationController.close();
    _messageReadController.close();
    developer.log('[WebSocketService] ✅ WebSocket service disposed');
  }
}
