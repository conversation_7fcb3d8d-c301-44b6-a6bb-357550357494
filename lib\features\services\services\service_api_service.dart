import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../models/service_models.dart';

/// API service for service management operations
class ServiceApiService extends ApiService {
  ServiceApiService(super.httpClient);

  /// Get all services for the current provider
  Future<List<Service>> getServices({
    bool? isActive,
    int? categoryId,
    String? search,
  }) async {
    try {
      print('[ServiceApiService] Getting services (isActive: $isActive, categoryId: $categoryId, search: $search)');

      final queryParams = <String, dynamic>{};
      if (isActive != null) queryParams['isActive'] = isActive;
      if (categoryId != null) queryParams['categoryId'] = categoryId;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await httpClient.get(
        '/api/auth/providers/services',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> servicesJson = data['data'] as List<dynamic>;
          final services = servicesJson
              .map((json) => Service.fromJson(json as Map<String, dynamic>))
              .toList();

          print('[ServiceApiService] Successfully retrieved ${services.length} services');
          return services;
        } else {
          print('[ServiceApiService] API returned success=false or null data');
          return [];
        }
      } else {
        throw Exception('Failed to get services: ${response.statusCode}');
      }
    } catch (e) {
      print('[ServiceApiService] Error getting services: $e');
      rethrow;
    }
  }

  /// Get a specific service by ID
  Future<Service> getServiceById(int id) async {
    try {
      print('[ServiceApiService] Getting service by ID: $id');

      final response = await httpClient.get('/api/auth/providers/services/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final service = Service.fromJson(data['data'] as Map<String, dynamic>);
          print('[ServiceApiService] Successfully retrieved service: ${service.title}');
          return service;
        } else {
          throw Exception('Service not found or API returned invalid data');
        }
      } else {
        throw Exception('Failed to get service: ${response.statusCode}');
      }
    } catch (e) {
      print('[ServiceApiService] Error getting service by ID $id: $e');
      rethrow;
    }
  }

  /// Create a new service
  Future<Service> createService(CreateServiceRequest request) async {
    try {
      print('[ServiceApiService] Creating service: ${request.title}');

      final response = await httpClient.post(
        '/api/auth/providers/services',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final service = Service.fromJson(data['data'] as Map<String, dynamic>);
          print('[ServiceApiService] Successfully created service: ${service.title} (ID: ${service.id})');
          return service;
        } else {
          throw Exception('Failed to create service: Invalid response data');
        }
      } else {
        throw Exception('Failed to create service: ${response.statusCode}');
      }
    } catch (e) {
      print('[ServiceApiService] Error creating service: $e');
      rethrow;
    }
  }

  /// Update an existing service
  Future<Service> updateService(int id, UpdateServiceRequest request) async {
    try {
      print('[ServiceApiService] Updating service ID: $id');

      final response = await httpClient.put(
        '/api/auth/providers/services/$id',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true && data['data'] != null) {
          final service = Service.fromJson(data['data'] as Map<String, dynamic>);
          print('[ServiceApiService] Successfully updated service: ${service.title}');
          return service;
        } else {
          throw Exception('Failed to update service: Invalid response data');
        }
      } else {
        throw Exception('Failed to update service: ${response.statusCode}');
      }
    } catch (e) {
      print('[ServiceApiService] Error updating service ID $id: $e');
      rethrow;
    }
  }

  /// Delete a service
  Future<bool> deleteService(int id) async {
    try {
      print('[ServiceApiService] Deleting service ID: $id');

      final response = await httpClient.delete('/api/auth/providers/services/$id');

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['success'] == true) {
          print('[ServiceApiService] Successfully deleted service ID: $id');
          return true;
        } else {
          throw Exception('Failed to delete service: API returned success=false');
        }
      } else if (response.statusCode == 409) {
        // Conflict - Service has dependencies
        final data = response.data;
        final message = data['message'] ?? 'Service has dependencies and cannot be deleted';
        throw Exception('Cannot delete service: $message');
      } else {
        throw Exception('Failed to delete service: ${response.statusCode}');
      }
    } catch (e) {
      print('[ServiceApiService] Error deleting service ID $id: $e');
      rethrow;
    }
  }

  /// Check if a service can be deleted
  Future<bool> canDeleteService(int id) async {
    try {
      print('[ServiceApiService] Checking if service ID $id can be deleted');

      // For now, we'll assume all services can be deleted unless the delete operation fails
      // In the future, this could be a separate API endpoint
      return true;
    } catch (e) {
      print('[ServiceApiService] Error checking if service can be deleted: $e');
      return false;
    }
  }

  /// Get service statistics
  Future<Map<String, dynamic>> getServiceStats(int id) async {
    try {
      print('[ServiceApiService] Getting stats for service ID: $id');

      // This would be a separate endpoint in the future
      // For now, return empty stats
      return {
        'totalAppointments': 0,
        'totalRevenue': 0.0,
        'averageRating': 0.0,
        'lastBooking': null,
      };
    } catch (e) {
      print('[ServiceApiService] Error getting service stats: $e');
      return {};
    }
  }

  /// Search services by title
  Future<List<Service>> searchServices(String query, {int? limit}) async {
    try {
      print('[ServiceApiService] Searching services with query: "$query" (limit: $limit)');

      final services = await getServices(search: query);

      // Apply limit if specified
      final limitedServices = limit != null && services.length > limit
          ? services.take(limit).toList()
          : services;

      print('[ServiceApiService] Search returned ${limitedServices.length} services');
      return limitedServices;
    } catch (e) {
      print('[ServiceApiService] Error searching services: $e');
      rethrow;
    }
  }

  /// Get services by category
  Future<List<Service>> getServicesByCategory(int categoryId, {bool includeInactive = false}) async {
    try {
      print('[ServiceApiService] Getting services for category ID: $categoryId (includeInactive: $includeInactive)');

      final services = await getServices(
        categoryId: categoryId,
        isActive: includeInactive ? null : true,
      );

      print('[ServiceApiService] Found ${services.length} services in category $categoryId');
      return services;
    } catch (e) {
      print('[ServiceApiService] Error getting services by category: $e');
      rethrow;
    }
  }

  /// Get services available for online booking
  Future<List<Service>> getOnlineBookableServices() async {
    try {
      print('[ServiceApiService] Getting online bookable services');

      final allServices = await getServices(isActive: true);
      final onlineServices = allServices.where((service) => service.acceptOnline).toList();

      print('[ServiceApiService] Found ${onlineServices.length} online bookable services');
      return onlineServices;
    } catch (e) {
      print('[ServiceApiService] Error getting online bookable services: $e');
      rethrow;
    }
  }

  /// Get services that accept new customers
  Future<List<Service>> getNewCustomerServices() async {
    try {
      print('[ServiceApiService] Getting services that accept new customers');

      final allServices = await getServices(isActive: true);
      final newCustomerServices = allServices.where((service) => service.acceptNew).toList();

      print('[ServiceApiService] Found ${newCustomerServices.length} services accepting new customers');
      return newCustomerServices;
    } catch (e) {
      print('[ServiceApiService] Error getting new customer services: $e');
      rethrow;
    }
  }
}
