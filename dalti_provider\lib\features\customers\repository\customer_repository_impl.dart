import '../models/customer_models.dart';
import '../services/customer_api_service.dart';
import 'customer_repository.dart';

/// Concrete implementation of CustomerRepository with caching
class CustomerRepositoryImpl implements CustomerRepository {
  final CustomerApiService _apiService;

  // Cache for customers data
  List<Customer>? _cachedCustomers;
  DateTime? _lastCustomersFetch;
  CustomerStats? _cachedStats;
  DateTime? _lastStatsFetch;

  // Cache expiration times
  static const Duration _customersCacheExpiry = Duration(minutes: 5);
  static const Duration _statsCacheExpiry = Duration(minutes: 10);

  CustomerRepositoryImpl(this._apiService);

  @override
  Future<List<Customer>> getCustomers({
    CustomerSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print(
        '[CustomerRepository] Getting customers - page: $page, filters: ${filters?.searchQuery}',
      );

      // For filtered requests or pagination, always fetch from API
      if (filters != null || page > 1) {
        final response = await _apiService.getCustomers(
          filters: filters,
          page: page,
          pageSize: pageSize,
        );

        if (response.success) {
          return response.customers;
        } else {
          throw Exception(
            response.error?.message ?? 'Failed to fetch customers',
          );
        }
      }

      // For unfiltered first page, use cache if available and fresh
      if (_cachedCustomers != null && _isCustomersCacheFresh()) {
        print('[CustomerRepository] Returning cached customers');
        return _cachedCustomers!;
      }

      // Fetch from API and cache
      final response = await _apiService.getCustomers(
        page: page,
        pageSize: pageSize,
      );

      if (response.success) {
        _cachedCustomers = response.customers;
        _lastCustomersFetch = DateTime.now();
        print('[CustomerRepository] Customers cached successfully');
        return response.customers;
      } else {
        // Return cached data if available, even if stale
        if (_cachedCustomers != null) {
          print(
            '[CustomerRepository] API failed, returning stale cached customers',
          );
          return _cachedCustomers!;
        }
        throw Exception(response.error?.message ?? 'Failed to fetch customers');
      }
    } catch (e) {
      print('[CustomerRepository] Error getting customers: $e');

      // Return cached data if available
      if (_cachedCustomers != null) {
        print(
          '[CustomerRepository] Exception occurred, returning cached customers',
        );
        return _cachedCustomers!;
      }

      rethrow;
    }
  }

  @override
  Future<Customer> getCustomer(String customerId) async {
    try {
      print('[CustomerRepository] Getting customer: $customerId');

      // Check cache first
      if (_cachedCustomers != null) {
        final cachedCustomer =
            _cachedCustomers!
                .where((customer) => customer.id == customerId)
                .firstOrNull;

        if (cachedCustomer != null && _isCustomersCacheFresh()) {
          print('[CustomerRepository] Returning cached customer');
          return cachedCustomer;
        }
      }

      final response = await _apiService.getCustomer(customerId);

      if (response.success && response.customer != null) {
        // Update cache if we have the customer
        if (_cachedCustomers != null) {
          final index = _cachedCustomers!.indexWhere((c) => c.id == customerId);
          if (index != -1) {
            _cachedCustomers![index] = response.customer!;
          } else {
            _cachedCustomers!.add(response.customer!);
          }
        }

        return response.customer!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch customer');
      }
    } catch (e) {
      print('[CustomerRepository] Error getting customer: $e');
      rethrow;
    }
  }

  @override
  Future<Customer> createCustomer(CustomerRequest request) async {
    try {
      print(
        '[CustomerRepository] Creating customer: ${request.firstName} ${request.lastName}',
      );

      final response = await _apiService.createCustomer(request);

      if (response.success && response.customer != null) {
        // Add to cache
        if (_cachedCustomers != null) {
          _cachedCustomers!.insert(0, response.customer!);
        }

        // Invalidate stats cache
        _cachedStats = null;

        print('[CustomerRepository] Customer created and cached');
        return response.customer!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to create customer');
      }
    } catch (e) {
      print('[CustomerRepository] Error creating customer: $e');
      rethrow;
    }
  }

  @override
  Future<Customer> updateCustomer(
    String customerId,
    CustomerRequest request,
  ) async {
    try {
      print('[CustomerRepository] Updating customer: $customerId');

      final response = await _apiService.updateCustomer(customerId, request);

      if (response.success && response.customer != null) {
        // Update cache
        if (_cachedCustomers != null) {
          final index = _cachedCustomers!.indexWhere((c) => c.id == customerId);
          if (index != -1) {
            _cachedCustomers![index] = response.customer!;
          }
        }

        print('[CustomerRepository] Customer updated and cached');
        return response.customer!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to update customer');
      }
    } catch (e) {
      print('[CustomerRepository] Error updating customer: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteCustomer(String customerId) async {
    try {
      print('[CustomerRepository] Deleting customer: $customerId');

      final response = await _apiService.deleteCustomer(customerId);

      if (response.success) {
        // Remove from cache
        if (_cachedCustomers != null) {
          _cachedCustomers!.removeWhere((c) => c.id == customerId);
        }

        // Invalidate stats cache
        _cachedStats = null;

        print('[CustomerRepository] Customer deleted and removed from cache');
      } else {
        throw Exception(response.error?.message ?? 'Failed to delete customer');
      }
    } catch (e) {
      print('[CustomerRepository] Error deleting customer: $e');
      rethrow;
    }
  }

  @override
  Future<List<CustomerAppointment>> getCustomerAppointments(
    String customerId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print('[CustomerRepository] Getting customer appointments: $customerId');

      final response = await _apiService.getCustomerAppointments(
        customerId,
        page: page,
        pageSize: pageSize,
      );

      if (response.success) {
        return response.appointments;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to fetch customer appointments',
        );
      }
    } catch (e) {
      print('[CustomerRepository] Error getting customer appointments: $e');
      rethrow;
    }
  }

  @override
  Future<List<Customer>> searchCustomers(String query) async {
    try {
      print('[CustomerRepository] Searching customers: $query');

      final filters = CustomerSearchFilters(searchQuery: query);
      return await getCustomers(filters: filters);
    } catch (e) {
      print('[CustomerRepository] Error searching customers: $e');
      rethrow;
    }
  }

  @override
  Future<CustomerStats> getCustomerStats() async {
    try {
      print('[CustomerRepository] Getting customer stats');

      // Use cache if available and fresh
      if (_cachedStats != null && _isStatsCacheFresh()) {
        print('[CustomerRepository] Returning cached stats');
        return _cachedStats!;
      }

      final stats = await _apiService.getCustomerStats();
      _cachedStats = stats;
      _lastStatsFetch = DateTime.now();

      print('[CustomerRepository] Stats cached successfully');
      return stats;
    } catch (e) {
      print('[CustomerRepository] Error getting customer stats: $e');

      // Return cached stats if available
      if (_cachedStats != null) {
        print(
          '[CustomerRepository] Exception occurred, returning cached stats',
        );
        return _cachedStats!;
      }

      rethrow;
    }
  }

  @override
  Future<int> getCustomersCount({CustomerSearchFilters? filters}) async {
    try {
      final response = await _apiService.getCustomers(
        filters: filters,
        page: 1,
        pageSize: 1, // We only need the count
      );

      if (response.success) {
        return response.totalCount;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to get customers count',
        );
      }
    } catch (e) {
      print('[CustomerRepository] Error getting customers count: $e');
      rethrow;
    }
  }

  @override
  Future<bool> customerExistsByEmail(String email) async {
    try {
      final filters = CustomerSearchFilters(searchQuery: email);
      final customers = await getCustomers(filters: filters, pageSize: 1);
      return customers.any(
        (customer) => customer.email?.toLowerCase() == email.toLowerCase(),
      );
    } catch (e) {
      print('[CustomerRepository] Error checking customer by email: $e');
      return false;
    }
  }

  @override
  Future<bool> customerExistsByPhone(String phoneNumber) async {
    try {
      final filters = CustomerSearchFilters(searchQuery: phoneNumber);
      final customers = await getCustomers(filters: filters, pageSize: 1);
      return customers.any(
        (customer) =>
            customer.phoneNumber != null && customer.phoneNumber == phoneNumber,
      );
    } catch (e) {
      print('[CustomerRepository] Error checking customer by phone: $e');
      return false;
    }
  }

  @override
  Future<List<Customer>> getRecentCustomers({int limit = 10}) async {
    try {
      final filters = CustomerSearchFilters(
        sortBy: 'createdAt',
        ascending: false,
      );
      return await getCustomers(filters: filters, pageSize: limit);
    } catch (e) {
      print('[CustomerRepository] Error getting recent customers: $e');
      rethrow;
    }
  }

  @override
  Future<List<Customer>> getTopCustomers({int limit = 10}) async {
    try {
      final filters = CustomerSearchFilters(
        sortBy: 'totalSpent',
        ascending: false,
      );
      return await getCustomers(filters: filters, pageSize: limit);
    } catch (e) {
      print('[CustomerRepository] Error getting top customers: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    print('[CustomerRepository] Clearing cache');
    _cachedCustomers = null;
    _lastCustomersFetch = null;
    _cachedStats = null;
    _lastStatsFetch = null;
  }

  @override
  Future<void> refreshCustomers() async {
    print('[CustomerRepository] Refreshing customers');
    await clearCache();
    await getCustomers(); // This will fetch fresh data
  }

  /// Check if customers cache is still fresh
  bool _isCustomersCacheFresh() {
    if (_lastCustomersFetch == null) return false;
    return DateTime.now().difference(_lastCustomersFetch!) <
        _customersCacheExpiry;
  }

  /// Check if stats cache is still fresh
  bool _isStatsCacheFresh() {
    if (_lastStatsFetch == null) return false;
    return DateTime.now().difference(_lastStatsFetch!) < _statsCacheExpiry;
  }
}
