import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/dashboard_provider.dart';

/// Data refresh control panel for manual and automatic refresh options
class RefreshControlPanel extends ConsumerWidget {
  const RefreshControlPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(dashboardNotifierProvider);
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.refresh,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Data Refresh',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Last updated info
            if (dashboardState.lastUpdated != null)
              _buildLastUpdatedInfo(context, dashboardState.lastUpdated!),

            const SizedBox(height: 16),

            // Refresh buttons
            Row(
              children: [
                // Manual refresh button
                Expanded(
                  child: _RefreshButton(
                    icon: Icons.refresh,
                    label: 'Refresh Now',
                    isLoading: dashboardState.isRefreshing,
                    onPressed: dashboardState.isRefreshing 
                        ? null 
                        : () => _handleManualRefresh(ref),
                  ),
                ),
                const SizedBox(width: 12),

                // Auto-refresh toggle
                Expanded(
                  child: _AutoRefreshToggle(
                    isEnabled: true, // TODO: Get from settings
                    onToggle: (enabled) => _handleAutoRefreshToggle(ref, enabled),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Refresh status
            _buildRefreshStatus(context, dashboardState),
          ],
        ),
      ),
    );
  }

  /// Build last updated information
  Widget _buildLastUpdatedInfo(BuildContext context, DateTime lastUpdated) {
    final theme = Theme.of(context);
    final timeAgo = _formatTimeAgo(lastUpdated);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: theme.colorScheme.onSurfaceVariant,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Last updated: $timeAgo',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build refresh status indicator
  Widget _buildRefreshStatus(BuildContext context, DashboardState dashboardState) {
    final theme = Theme.of(context);

    if (dashboardState.isRefreshing) {
      return Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: theme.colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Refreshing data...',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      );
    }

    if (dashboardState.hasError) {
      return Row(
        children: [
          Icon(
            Icons.error_outline,
            color: theme.colorScheme.error,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Refresh failed: ${dashboardState.error}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          'Data is up to date',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.green,
          ),
        ),
      ],
    );
  }

  /// Handle manual refresh
  Future<void> _handleManualRefresh(WidgetRef ref) async {
    await ref.read(dashboardNotifierProvider.notifier).refreshDashboardData();
  }

  /// Handle auto-refresh toggle
  void _handleAutoRefreshToggle(WidgetRef ref, bool enabled) {
    if (enabled) {
      ref.read(dashboardNotifierProvider.notifier).startAutoRefresh();
    } else {
      ref.read(dashboardNotifierProvider.notifier).stopAutoRefresh();
    }
  }

  /// Format time ago string
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 30) {
      return 'Just now';
    } else if (difference.inMinutes < 1) {
      return '${difference.inSeconds}s ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

/// Refresh button widget
class _RefreshButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isLoading;
  final VoidCallback? onPressed;

  const _RefreshButton({
    required this.icon,
    required this.label,
    this.isLoading = false,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 48,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: theme.colorScheme.onPrimary,
                ),
              )
            : Icon(icon, size: 20),
        label: Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
        ),
      ),
    );
  }
}

/// Auto-refresh toggle widget
class _AutoRefreshToggle extends StatelessWidget {
  final bool isEnabled;
  final ValueChanged<bool>? onToggle;

  const _AutoRefreshToggle({
    required this.isEnabled,
    this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      height: 48,
      child: OutlinedButton.icon(
        onPressed: onToggle != null ? () => onToggle!(!isEnabled) : null,
        icon: Icon(
          isEnabled ? Icons.autorenew : Icons.pause,
          size: 20,
          color: isEnabled ? Colors.green : theme.colorScheme.onSurfaceVariant,
        ),
        label: Text(
          isEnabled ? 'Auto-Refresh' : 'Manual Only',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isEnabled ? Colors.green : theme.colorScheme.onSurfaceVariant,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: isEnabled ? Colors.green : theme.colorScheme.outline,
          ),
        ),
      ),
    );
  }
}
