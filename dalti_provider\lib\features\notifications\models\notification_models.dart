import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_models.freezed.dart';
part 'notification_models.g.dart';

@freezed
class NotificationItem with _$NotificationItem {
  const factory NotificationItem({
    required String id,
    required DateTime createdAt,
    required DateTime updatedAt,
    required String userId,
    required String type,
    required String title,
    required String message,
    required bool isRead,
    DateTime? readAt,
    String? link,
    String? actorId,
    NotificationActor? actor,
  }) = _NotificationItem;

  factory NotificationItem.fromJson(Map<String, dynamic> json) =>
      _$NotificationItemFromJson(json);
}

@freezed
class NotificationActor with _$NotificationActor {
  const factory NotificationActor({
    required String id,
    String? username,
    String? firstName,
    String? lastName,
  }) = _NotificationActor;

  factory NotificationActor.fromJson(Map<String, dynamic> json) =>
      _$NotificationActorFromJson(json);
}

// Notification types enum for better type safety
enum NotificationType {
  appointmentConfirmedCustomer('APPOINTMENT_CONFIRMED_CUSTOMER'),
  appointmentBookedSuccessCustomer('APPOINTMENT_BOOKED_SUCCESS_CUSTOMER'),
  appointmentCompletedCustomer('APPOINTMENT_COMPLETED_CUSTOMER'),
  appointmentCancelledCustomer('APPOINTMENT_CANCELLED_CUSTOMER'),
  appointmentRescheduledCustomer('APPOINTMENT_RESCHEDULED_CUSTOMER'),
  messageReceived('MESSAGE_RECEIVED'),
  systemNotification('SYSTEM_NOTIFICATION');

  const NotificationType(this.value);
  final String value;

  static NotificationType? fromString(String value) {
    for (NotificationType type in NotificationType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }
}

// Extension to get display properties for notification types
extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.appointmentConfirmedCustomer:
        return 'Appointment Confirmed';
      case NotificationType.appointmentBookedSuccessCustomer:
        return 'Appointment Booked';
      case NotificationType.appointmentCompletedCustomer:
        return 'Appointment Completed';
      case NotificationType.appointmentCancelledCustomer:
        return 'Appointment Cancelled';
      case NotificationType.appointmentRescheduledCustomer:
        return 'Appointment Rescheduled';
      case NotificationType.messageReceived:
        return 'New Message';
      case NotificationType.systemNotification:
        return 'System Notification';
    }
  }

  String get iconName {
    switch (this) {
      case NotificationType.appointmentConfirmedCustomer:
      case NotificationType.appointmentBookedSuccessCustomer:
      case NotificationType.appointmentRescheduledCustomer:
        return 'calendar_check';
      case NotificationType.appointmentCompletedCustomer:
        return 'check_circle';
      case NotificationType.appointmentCancelledCustomer:
        return 'cancel';
      case NotificationType.messageReceived:
        return 'message';
      case NotificationType.systemNotification:
        return 'info';
    }
  }
}
