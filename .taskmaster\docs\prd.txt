# Dalti Provider Mobile App - Product Requirements Document

## Project Overview
**App Name**: Dalti Provider
**Platform**: Flutter (iOS & Android)
**Target Users**: Service providers (doctors, barbers, mechanics, etc.) managing appointments and business operations
**Backend**: Complete REST API with 30+ endpoints (100% tested and ready)
**Authentication**: JWT-based with OTP registration

## Technical Requirements

### Core Technologies
- Flutter SDK (latest stable)
- State Management: Riverpod 3.0+ with code generation
- HTTP Client: Dio with custom interceptors
- Navigation: GoRouter
- UI Framework: Material Design 3
- Local Storage: Hive for offline data
- Architecture: Clean Architecture with repository pattern

### API Integration
- Base URL: https://dapi-test.adscloud.org:8443 (Development)
- Production URL: https://dapi.adscloud.org
- Authentication: Bearer token in Authorization header
- Special API Behaviors: Returns 500 for validation errors, 400 for auth errors
- 30+ fully tested endpoints across 8 domains

## Core Features

### 1. Authentication & Onboarding
- OTP-based registration (email and phone)
- Provider login with email/phone and password
- JWT token management and refresh
- Business setup wizard
- Profile completion flow

### 2. Dashboard & Overview
- Main dashboard with business overview
- Today's schedule and appointments
- Quick action buttons
- Notification center for reschedule requests

### 3. Business Management

#### Location Management
- View all business locations
- Create new locations with address and coordinates
- Edit location details and amenities
- Delete locations (with dependency checks)
- Location-based queue and schedule management

#### Service Management
- Service catalog with categories
- Create services with pricing and duration
- Edit service details and availability
- Service color coding and settings
- Delete services (with dependency checks)

#### Queue Management
- Queue overview by location
- Create and configure service queues
- Assign/remove services to queues
- Queue activation/deactivation
- Queue service assignment management

### 4. Schedule Management
- Weekly schedule grid view
- Set working hours by location and day
- Edit existing schedules
- Schedule conflict detection
- Multiple location schedule management

### 5. Customer Relationship Management
- Searchable customer directory
- Customer profile management
- Add new customer records
- Customer appointment history
- Customer contact information

### 6. Appointment Management
- Calendar view (daily/weekly/monthly)
- Appointment details and notes
- Create new appointments
- Update appointment status
- Appointment search and filtering

### 7. Reschedule Management
- Reschedule request inbox
- View reschedule request details
- Approve/reject reschedule requests
- Reschedule history tracking
- Customer notification management

## UI/UX Requirements

### Design System
- Material Design 3 with Dalti branding
- Consistent color scheme and typography
- Dark/Light theme support
- Responsive design for all screen sizes

### Navigation Structure
- Bottom Navigation: Dashboard, Calendar, Business, Customers, More
- Drawer Navigation: Profile, Schedules, Reschedules, Reports, Help, Logout
- Intuitive navigation flows between features

### Key Screens
1. Splash screen with app initialization
2. Login/Register with OTP verification
3. Onboarding wizard for new providers
4. Main dashboard with business overview
5. Calendar with appointment management
6. Business setup (locations, services, queues)
7. Customer management interface
8. Profile and settings

## Technical Implementation

### Project Structure
```
lib/
├── core/
│   ├── constants/
│   ├── errors/
│   ├── network/
│   └── utils/
├── features/
│   ├── auth/
│   ├── dashboard/
│   ├── profile/
│   ├── locations/
│   ├── services/
│   ├── queues/
│   ├── schedules/
│   ├── customers/
│   ├── appointments/
│   └── reschedules/
├── shared/
│   ├── widgets/
│   ├── models/
│   └── providers/
└── main.dart
```

### State Management
- Riverpod providers for each feature
- AsyncValue for API state management
- Repository pattern for data access
- Dependency injection with Riverpod

### API Integration
- Custom Dio interceptors for API quirks
- Proper error handling for API-specific behaviors
- JWT token management and refresh
- Offline data caching with Hive

## Success Criteria
- All 30+ API endpoints integrated
- Complete CRUD operations for all entities
- Intuitive user experience
- Fast loading times (<3 seconds)
- Offline capability for critical features
- Comprehensive error handling
- Clean, maintainable code architecture

## Development Phases
1. Foundation: Project setup, authentication, navigation, API client
2. Core Business: Profile, location, service management, dashboard
3. Operations: Queue, schedule, customer management, calendar
4. Advanced Features: Appointments, reschedules, notifications
5. Polish: UI/UX improvements, optimization, testing

## Quality Assurance
- Unit tests for business logic
- Widget tests for UI components
- Integration tests for API interactions
- Code quality checks and linting
- Performance optimization
- Accessibility compliance
