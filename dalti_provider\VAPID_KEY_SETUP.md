# VAPID Key Setup for Web FCM

## What is VAPID?
VAPID (Voluntary Application Server Identification) keys are required for web push notifications. They allow your web app to receive push notifications from Firebase Cloud Messaging.

## How to Generate VAPID Key

### Step 1: Go to Firebase Console
1. Open [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `dalti-3d06b`
3. Go to **Project Settings** (gear icon)

### Step 2: Navigate to Cloud Messaging
1. Click on the **Cloud Messaging** tab
2. Scroll down to **Web configuration**

### Step 3: Generate Web Push Certificates
1. In the **Web Push certificates** section
2. Click **Generate key pair** if no key exists
3. Copy the generated key (starts with `B` and is about 88 characters long)

### Step 4: Update the Code
Replace the placeholder VAPID key in the following files:

#### File: `lib/core/services/firebase_messaging_service.dart`
```dart
// Line ~274: Replace the placeholder
vapidKey: 'BKagOny0KF_2pCJQ3m....', // Replace with your actual VAPID key
```

#### File: `web/firebase-messaging-sw.js`
The service worker uses the Firebase config, so no additional VAPID key needed there.

## Example VAPID Key Format
```
BKagOny0KF_2pCJQ3mFfQRLQjdX8C9H5b2X1vZ9Y8W7V6U5T4S3R2Q1P0O9N8M7L6K5J4I3H2G1F0E9D8C7B6A5
```

## Testing Web Notifications

### Step 1: Build for Web
```bash
flutter build web --dart-define=FLUTTER_WEB_USE_SKIA=true
```

### Step 2: Serve Locally
```bash
cd build/web
python -m http.server 8000
# Or use any local web server
```

### Step 3: Test in Browser
1. Open `http://localhost:8000`
2. Allow notification permissions when prompted
3. Check browser console for FCM token
4. Test sending notifications from Firebase Console

## Troubleshooting

### Common Issues:
1. **VAPID key not working**: Make sure you copied the full key from Firebase Console
2. **Service worker not registering**: Check browser console for errors
3. **Notifications not showing**: Verify notification permissions are granted
4. **Token not generating**: Check Firebase configuration and VAPID key

### Browser Requirements:
- Chrome 50+
- Firefox 44+
- Safari 16+ (macOS 13+)
- Edge 17+

## Security Notes
- Keep your VAPID key secure
- Don't commit it to public repositories
- Consider using environment variables for production