import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_models.freezed.dart';
part 'dashboard_models.g.dart';

/// Business metrics for dashboard overview
@freezed
class BusinessMetrics with _$BusinessMetrics {
  const factory BusinessMetrics({
    @Default(0.0) double todayRevenue,
    @Default(0) int todayAppointments,
    @Default(0) int completedAppointments,
    @Default(0) int cancelledAppointments,
    @Default(0) int activeQueues,
    @Default(0) int totalCustomersToday,
    @Default(0.0) double customerSatisfaction,
    @Default(0.0) double averageWaitTime,
    @Default([]) List<double> weeklyRevenueData,
    @Default([]) List<int> weeklyAppointmentData,
    @Default(0.0) double revenueChange,
    @Default(0.0) double appointmentsChange,
  }) = _BusinessMetrics;

  factory BusinessMetrics.fromJson(Map<String, dynamic> json) =>
      _$BusinessMetricsFromJson(json);
}

/// Today's schedule and queue information
@freezed
class ScheduleData with _$ScheduleData {
  const factory ScheduleData({
    NextAppointment? nextAppointment,
    @Default([]) List<QueueStatus> queueStatuses,
    WorkingHours? todayHours,
    @Default(0) int totalAppointments,
    @Default(0) int completedAppointments,
    @Default(0) int upcomingAppointments,
    @Default(0) int cancelledAppointments,
  }) = _ScheduleData;

  factory ScheduleData.fromJson(Map<String, dynamic> json) =>
      _$ScheduleDataFromJson(json);
}

/// Next appointment information
@freezed
class NextAppointment with _$NextAppointment {
  const factory NextAppointment({
    required String id,
    required String customerName,
    required String serviceName,
    required DateTime scheduledTime,
    @Default(30) int estimatedDuration,
    @Default('confirmed') String status,
  }) = _NextAppointment;

  factory NextAppointment.fromJson(Map<String, dynamic> json) =>
      _$NextAppointmentFromJson(json);
}

/// Queue status information
@freezed
class QueueStatus with _$QueueStatus {
  const factory QueueStatus({
    required String queueId,
    required String queueName,
    required String locationName,
    @Default(0) int waitingCount,
    @Default(0.0) double averageWaitTime,
    @Default(true) bool isActive,
    DateTime? nextAvailableSlot,
  }) = _QueueStatus;

  factory QueueStatus.fromJson(Map<String, dynamic> json) =>
      _$QueueStatusFromJson(json);
}

/// Working hours for today
@freezed
class WorkingHours with _$WorkingHours {
  const factory WorkingHours({
    required String openTime,
    required String closeTime,
    @Default(true) bool isOpen,
    @Default([]) List<BreakTime> breakTimes,
  }) = _WorkingHours;

  factory WorkingHours.fromJson(Map<String, dynamic> json) =>
      _$WorkingHoursFromJson(json);
}

/// Break time information
@freezed
class BreakTime with _$BreakTime {
  const factory BreakTime({
    required String startTime,
    required String endTime,
    required String description,
  }) = _BreakTime;

  factory BreakTime.fromJson(Map<String, dynamic> json) =>
      _$BreakTimeFromJson(json);
}

/// Notification item
@freezed
class NotificationItem with _$NotificationItem {
  const factory NotificationItem({
    required String id,
    required NotificationType type,
    required String title,
    required String message,
    required DateTime timestamp,
    @Default(false) bool isRead,
    @Default('medium') String priority,
    Map<String, dynamic>? actionData,
  }) = _NotificationItem;

  factory NotificationItem.fromJson(Map<String, dynamic> json) =>
      _$NotificationItemFromJson(json);
}

/// Notification types
enum NotificationType {
  @JsonValue('reschedule_request')
  rescheduleRequest,
  @JsonValue('queue_alert')
  queueAlert,
  @JsonValue('new_customer')
  newCustomer,
  @JsonValue('appointment_reminder')
  appointmentReminder,
  @JsonValue('system_notification')
  systemNotification,
  @JsonValue('emergency_alert')
  emergencyAlert,
}

/// Quick stats for real-time updates
@freezed
class QuickStats with _$QuickStats {
  const factory QuickStats({
    @Default(0) int currentWaitingCustomers,
    @Default(0) int activeQueues,
    @Default(0.0) double todayRevenue,
    @Default(0) int unreadNotifications,
    @Default(0) int nextAppointmentIn, // minutes
    @Default(0.0) double averageWaitTime,
    DateTime? lastUpdated,
  }) = _QuickStats;

  factory QuickStats.fromJson(Map<String, dynamic> json) =>
      _$QuickStatsFromJson(json);
}

/// Complete dashboard data container
@freezed
class DashboardData with _$DashboardData {
  const factory DashboardData({
    BusinessMetrics? businessMetrics,
    ScheduleData? scheduleData,
    @Default([]) List<NotificationItem> notifications,
    QuickStats? quickStats,
    @Default(0) int unreadNotificationCount,
    DateTime? lastUpdated,
  }) = _DashboardData;

  factory DashboardData.fromJson(Map<String, dynamic> json) =>
      _$DashboardDataFromJson(json);
}

/// Dashboard API response wrapper for business metrics
@freezed
class BusinessMetricsResponse with _$BusinessMetricsResponse {
  const factory BusinessMetricsResponse({
    required bool success,
    BusinessMetrics? data,
    DashboardError? error,
  }) = _BusinessMetricsResponse;

  factory BusinessMetricsResponse.fromJson(Map<String, dynamic> json) =>
      _$BusinessMetricsResponseFromJson(json);
}

/// Dashboard API response wrapper for schedule data
@freezed
class ScheduleDataResponse with _$ScheduleDataResponse {
  const factory ScheduleDataResponse({
    required bool success,
    ScheduleData? data,
    DashboardError? error,
  }) = _ScheduleDataResponse;

  factory ScheduleDataResponse.fromJson(Map<String, dynamic> json) =>
      _$ScheduleDataResponseFromJson(json);
}

/// Dashboard API response wrapper for notifications
@freezed
class NotificationsResponse with _$NotificationsResponse {
  const factory NotificationsResponse({
    required bool success,
    @Default([]) List<NotificationItem> notifications,
    @Default(0) int unreadCount,
    @Default(0) int totalCount,
    DashboardError? error,
  }) = _NotificationsResponse;

  factory NotificationsResponse.fromJson(Map<String, dynamic> json) =>
      _$NotificationsResponseFromJson(json);
}

/// Dashboard API response wrapper for quick stats
@freezed
class QuickStatsResponse with _$QuickStatsResponse {
  const factory QuickStatsResponse({
    required bool success,
    QuickStats? data,
    DashboardError? error,
  }) = _QuickStatsResponse;

  factory QuickStatsResponse.fromJson(Map<String, dynamic> json) =>
      _$QuickStatsResponseFromJson(json);
}

/// Dashboard API response wrapper for basic operations
@freezed
class BasicResponse with _$BasicResponse {
  const factory BasicResponse({
    required bool success,
    String? message,
    DashboardError? error,
  }) = _BasicResponse;

  factory BasicResponse.fromJson(Map<String, dynamic> json) =>
      _$BasicResponseFromJson(json);
}

/// Dashboard API response wrapper for emergency queue control
@freezed
class EmergencyControlResponse with _$EmergencyControlResponse {
  const factory EmergencyControlResponse({
    required bool success,
    String? message,
    EmergencyQueueControlResponse? data,
    DashboardError? error,
  }) = _EmergencyControlResponse;

  factory EmergencyControlResponse.fromJson(Map<String, dynamic> json) =>
      _$EmergencyControlResponseFromJson(json);
}

/// Dashboard error information
@freezed
class DashboardError with _$DashboardError {
  const factory DashboardError({
    required String code,
    required String message,
    String? details,
  }) = _DashboardError;

  factory DashboardError.fromJson(Map<String, dynamic> json) =>
      _$DashboardErrorFromJson(json);
}

/// Emergency queue control request
@freezed
class EmergencyQueueControl with _$EmergencyQueueControl {
  const factory EmergencyQueueControl({
    required String action, // 'pause' or 'resume'
    required String reason,
    required DateTime timestamp,
    int? estimatedDuration, // minutes
  }) = _EmergencyQueueControl;

  factory EmergencyQueueControl.fromJson(Map<String, dynamic> json) =>
      _$EmergencyQueueControlFromJson(json);

  // toJson is automatically generated by freezed
}

/// Emergency queue control response
@freezed
class EmergencyQueueControlResponse with _$EmergencyQueueControlResponse {
  const factory EmergencyQueueControlResponse({
    required bool success,
    required String message,
    @Default(0) int affectedQueues,
    DateTime? estimatedResumeTime,
  }) = _EmergencyQueueControlResponse;

  factory EmergencyQueueControlResponse.fromJson(Map<String, dynamic> json) =>
      _$EmergencyQueueControlResponseFromJson(json);
}
