import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../providers/onboarding_provider.dart';

/// Business profile setup step
class BusinessProfileStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const BusinessProfileStep({
    super.key,
    required this.controller,
  });

  @override
  ConsumerState<BusinessProfileStep> createState() => _BusinessProfileStepState();
}

class _BusinessProfileStepState extends ConsumerState<BusinessProfileStep> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _phoneController = TextEditingController();
  final _websiteController = TextEditingController();
  
  int? _selectedCategoryId;
  String? _selectedCategoryName;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _businessNameController.dispose();
    _descriptionController.dispose();
    _phoneController.dispose();
    _websiteController.dispose();
    super.dispose();
  }

  /// Load existing business profile data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final profile = onboardingState.data?.businessProfile;
    
    if (profile != null) {
      _businessNameController.text = profile.businessName;
      _descriptionController.text = profile.description;
      _phoneController.text = profile.phone ?? '';
      _websiteController.text = profile.website ?? '';
      _selectedCategoryId = profile.categoryId;
      _selectedCategoryName = profile.categoryName;
      
      // Mark step as completed if data exists
      widget.controller.markStepCompleted(OnboardingStep.businessProfile, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(businessCategoriesProvider);
    
    return WizardFormPage(
      step: OnboardingStep.businessProfile,
      formKey: _formKey,
      fields: [
        // Business name field
        TextFormField(
          controller: _businessNameController,
          decoration: const InputDecoration(
            labelText: 'Business Name *',
            hintText: 'Enter your business name',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.business),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Business name is required';
            }
            if (value.trim().length < 2) {
              return 'Business name must be at least 2 characters';
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.words,
        ),

        // Business description field
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Business Description *',
            hintText: 'Describe what your business does',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Business description is required';
            }
            if (value.trim().length < 10) {
              return 'Description must be at least 10 characters';
            }
            return null;
          },
          onChanged: _onFieldChanged,
          textCapitalization: TextCapitalization.sentences,
        ),

        // Business category dropdown
        categoriesAsync.when(
          data: (categories) => DropdownButtonFormField<int>(
            value: _selectedCategoryId,
            decoration: const InputDecoration(
              labelText: 'Business Category *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.category),
            ),
            hint: const Text('Select your business category'),
            items: categories.map((category) {
              return DropdownMenuItem<int>(
                value: category['id'] as int,
                child: Text(category['name'] as String),
              );
            }).toList(),
            validator: (value) {
              if (value == null) {
                return 'Please select a business category';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {
                _selectedCategoryId = value;
                _selectedCategoryName = categories
                    .firstWhere((cat) => cat['id'] == value)['name'] as String;
              });
              _onFieldChanged('');
            },
          ),
          loading: () => const LinearProgressIndicator(),
          error: (error, stack) => Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.colors.errorContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Failed to load categories: $error',
              style: TextStyle(color: context.colors.onErrorContainer),
            ),
          ),
        ),

        // Phone number field (optional)
        TextFormField(
          controller: _phoneController,
          decoration: const InputDecoration(
            labelText: 'Phone Number',
            hintText: 'Enter your business phone number',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
          onChanged: _onFieldChanged,
        ),

        // Website field (optional)
        TextFormField(
          controller: _websiteController,
          decoration: const InputDecoration(
            labelText: 'Website',
            hintText: 'Enter your business website',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.language),
          ),
          keyboardType: TextInputType.url,
          onChanged: _onFieldChanged,
        ),

        // Info card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: context.colors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: context.colors.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'This information will be displayed to your customers and used to set up your business profile.',
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Handle field changes to validate and update state
  void _onFieldChanged(String value) {
    // Validate form and update controller state
    final isValid = _validateForm();
    widget.controller.markStepCompleted(OnboardingStep.businessProfile, isValid);
    
    if (isValid) {
      _saveBusinessProfile();
    }
  }

  /// Validate the form
  bool _validateForm() {
    return _businessNameController.text.trim().isNotEmpty &&
           _businessNameController.text.trim().length >= 2 &&
           _descriptionController.text.trim().isNotEmpty &&
           _descriptionController.text.trim().length >= 10 &&
           _selectedCategoryId != null;
  }

  /// Save business profile data
  void _saveBusinessProfile() {
    if (!_validateForm()) return;
    
    final profile = BusinessProfile(
      businessName: _businessNameController.text.trim(),
      description: _descriptionController.text.trim(),
      categoryId: _selectedCategoryId!,
      categoryName: _selectedCategoryName!,
      phone: _phoneController.text.trim().isNotEmpty 
          ? _phoneController.text.trim() 
          : null,
      website: _websiteController.text.trim().isNotEmpty 
          ? _websiteController.text.trim() 
          : null,
    );
    
    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveBusinessProfile(profile);
  }
}
