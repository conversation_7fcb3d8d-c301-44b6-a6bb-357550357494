import '../services/service_api_service.dart';
import '../models/service_models.dart';
import 'service_repository.dart';

/// Concrete implementation of ServiceRepository using API service
class ServiceRepositoryImpl implements ServiceRepository {
  final ServiceApiService _apiService;

  ServiceRepositoryImpl({
    required ServiceApiService apiService,
  }) : _apiService = apiService;

  @override
  Future<List<Service>> getServices({
    bool? isActive,
    int? categoryId,
    String? search,
  }) async {
    try {
      print('[ServiceRepository] Getting services (isActive: $isActive, categoryId: $categoryId, search: $search)');

      final services = await _apiService.getServices(
        isActive: isActive,
        categoryId: categoryId,
        search: search,
      );

      print('[ServiceRepository] Successfully retrieved ${services.length} services');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting services: $e');
      rethrow;
    }
  }

  @override
  Future<Service> getServiceById(int id) async {
    try {
      print('[ServiceRepository] Getting service by ID: $id');

      final service = await _apiService.getServiceById(id);

      print('[ServiceRepository] Successfully retrieved service: ${service.title}');
      return service;
    } catch (e) {
      print('[ServiceRepository] Error getting service by ID $id: $e');
      rethrow;
    }
  }

  @override
  Future<Service> createService(CreateServiceRequest request) async {
    try {
      print('[ServiceRepository] Creating service: ${request.title}');

      final service = await _apiService.createService(request);

      print('[ServiceRepository] Successfully created service: ${service.title} (ID: ${service.id})');
      return service;
    } catch (e) {
      print('[ServiceRepository] Error creating service: $e');
      rethrow;
    }
  }

  @override
  Future<Service> updateService(int id, UpdateServiceRequest request) async {
    try {
      print('[ServiceRepository] Updating service ID: $id');

      final service = await _apiService.updateService(id, request);

      print('[ServiceRepository] Successfully updated service: ${service.title}');
      return service;
    } catch (e) {
      print('[ServiceRepository] Error updating service ID $id: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deleteService(int id) async {
    try {
      print('[ServiceRepository] Deleting service ID: $id');

      final success = await _apiService.deleteService(id);

      print('[ServiceRepository] Service deletion result: $success');
      return success;
    } catch (e) {
      print('[ServiceRepository] Error deleting service ID $id: $e');
      rethrow;
    }
  }

  @override
  Future<bool> canDeleteService(int id) async {
    try {
      print('[ServiceRepository] Checking if service ID $id can be deleted');

      final canDelete = await _apiService.canDeleteService(id);

      print('[ServiceRepository] Service $id can delete: $canDelete');
      return canDelete;
    } catch (e) {
      print('[ServiceRepository] Error checking if service can be deleted: $e');
      // If we can't check, assume we can try to delete
      return true;
    }
  }

  @override
  Future<List<Service>> getServicesWithDetails() async {
    try {
      print('[ServiceRepository] Getting services with details');

      // For now, this is the same as getServices
      // In the future, this could include additional data like appointments, revenue, etc.
      final services = await _apiService.getServices();

      print('[ServiceRepository] Successfully retrieved ${services.length} services with details');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting services with details: $e');
      rethrow;
    }
  }

  @override
  Future<List<Service>> searchServices(String query, {int? limit}) async {
    try {
      print('[ServiceRepository] Searching services with query: "$query" (limit: $limit)');

      final services = await _apiService.searchServices(query, limit: limit);

      print('[ServiceRepository] Search returned ${services.length} services');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error searching services: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getServiceStats(int id) async {
    try {
      print('[ServiceRepository] Getting stats for service ID: $id');

      final stats = await _apiService.getServiceStats(id);

      print('[ServiceRepository] Successfully retrieved stats for service $id');
      return stats;
    } catch (e) {
      print('[ServiceRepository] Error getting service stats: $e');
      // Return empty stats on error
      return {};
    }
  }

  @override
  Future<List<Service>> getServicesByCategory(int categoryId, {bool includeInactive = false}) async {
    try {
      print('[ServiceRepository] Getting services for category ID: $categoryId (includeInactive: $includeInactive)');

      final services = await _apiService.getServicesByCategory(categoryId, includeInactive: includeInactive);

      print('[ServiceRepository] Found ${services.length} services in category $categoryId');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting services by category: $e');
      rethrow;
    }
  }

  @override
  Future<List<Service>> getPopularServices({int limit = 10, int timeRange = 30}) async {
    try {
      print('[ServiceRepository] Getting popular services (limit: $limit, timeRange: $timeRange days)');

      // For now, just return all active services
      // In the future, this would be based on appointment frequency
      final services = await _apiService.getServices(isActive: true);
      final limitedServices = services.take(limit).toList();

      print('[ServiceRepository] Returning ${limitedServices.length} popular services');
      return limitedServices;
    } catch (e) {
      print('[ServiceRepository] Error getting popular services: $e');
      rethrow;
    }
  }

  @override
  Future<List<Service>> getOnlineBookableServices() async {
    try {
      print('[ServiceRepository] Getting online bookable services');

      final services = await _apiService.getOnlineBookableServices();

      print('[ServiceRepository] Found ${services.length} online bookable services');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting online bookable services: $e');
      rethrow;
    }
  }

  @override
  Future<List<Service>> getNewCustomerServices() async {
    try {
      print('[ServiceRepository] Getting services that accept new customers');

      final services = await _apiService.getNewCustomerServices();

      print('[ServiceRepository] Found ${services.length} services accepting new customers');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting new customer services: $e');
      rethrow;
    }
  }

  @override
  Future<bool> bulkUpdateServiceStatus(List<int> serviceIds, bool isActive) async {
    try {
      print('[ServiceRepository] Bulk updating ${serviceIds.length} services to active: $isActive');

      // Update each service individually
      // In the future, this could be a single bulk API call
      for (final serviceId in serviceIds) {
        await _apiService.updateService(
          serviceId,
          UpdateServiceRequest(isActive: isActive),
        );
      }

      print('[ServiceRepository] Successfully bulk updated ${serviceIds.length} services');
      return true;
    } catch (e) {
      print('[ServiceRepository] Error bulk updating services: $e');
      return false;
    }
  }

  @override
  Future<List<Service>> getServicesByLocation(int locationId) async {
    try {
      print('[ServiceRepository] Getting services for location ID: $locationId');

      // This would require a separate API endpoint or integration with queue management
      // For now, return all active services
      final services = await _apiService.getServices(isActive: true);

      print('[ServiceRepository] Found ${services.length} services for location $locationId');
      return services;
    } catch (e) {
      print('[ServiceRepository] Error getting services by location: $e');
      rethrow;
    }
  }

  @override
  Future<Map<int, double>> getServicePricing(List<int> serviceIds) async {
    try {
      print('[ServiceRepository] Getting pricing for ${serviceIds.length} services');

      final Map<int, double> pricing = {};
      
      // Get pricing for each service
      for (final serviceId in serviceIds) {
        try {
          final service = await _apiService.getServiceById(serviceId);
          if (service.price != null) {
            pricing[serviceId] = service.price!;
          }
        } catch (e) {
          print('[ServiceRepository] Error getting pricing for service $serviceId: $e');
        }
      }

      print('[ServiceRepository] Retrieved pricing for ${pricing.length} services');
      return pricing;
    } catch (e) {
      print('[ServiceRepository] Error getting service pricing: $e');
      return {};
    }
  }

  @override
  Future<Service> updateServicePrice(int serviceId, double newPrice) async {
    try {
      print('[ServiceRepository] Updating price for service $serviceId to \$${newPrice.toStringAsFixed(2)}');

      final service = await _apiService.updateService(
        serviceId,
        UpdateServiceRequest(price: newPrice),
      );

      print('[ServiceRepository] Successfully updated price for service: ${service.title}');
      return service;
    } catch (e) {
      print('[ServiceRepository] Error updating service price: $e');
      rethrow;
    }
  }

  @override
  Future<List<Service>> getServicesByPointsRequirement(int minPoints) async {
    try {
      print('[ServiceRepository] Getting services requiring at least $minPoints points');

      final allServices = await _apiService.getServices(isActive: true);
      final filteredServices = allServices
          .where((service) => 
              service.pointsRequirements != null && 
              service.pointsRequirements! >= minPoints)
          .toList();

      print('[ServiceRepository] Found ${filteredServices.length} services requiring $minPoints+ points');
      return filteredServices;
    } catch (e) {
      print('[ServiceRepository] Error getting services by points requirement: $e');
      rethrow;
    }
  }

  @override
  Future<Service> cloneService(int serviceId, String newTitle) async {
    try {
      print('[ServiceRepository] Cloning service $serviceId with new title: $newTitle');

      // Get the original service
      final originalService = await _apiService.getServiceById(serviceId);

      // Create a new service with the same properties but different title
      final createRequest = CreateServiceRequest(
        title: newTitle,
        duration: originalService.duration,
        price: originalService.price ?? 0.0,
        categoryId: originalService.categoryId ?? 1, // Default category if not set
        description: originalService.description,
        color: originalService.color,
        acceptOnline: originalService.acceptOnline,
        acceptNew: originalService.acceptNew,
        notificationOn: originalService.notificationOn,
        pointsRequirements: originalService.pointsRequirements,
      );

      final clonedService = await _apiService.createService(createRequest);

      print('[ServiceRepository] Successfully cloned service: ${clonedService.title} (ID: ${clonedService.id})');
      return clonedService;
    } catch (e) {
      print('[ServiceRepository] Error cloning service: $e');
      rethrow;
    }
  }
}
