import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';

/// Widget displaying customer information in a card format
class CustomerInfoCard extends StatelessWidget {
  final Customer customer;

  const CustomerInfoCard({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Contact Information
        _buildInfoSection(
          title: 'Contact Information',
          icon: Icons.contact_phone,
          children: [
            _buildInfoRow(
              icon: Icons.email,
              label: 'Email',
              value: customer.email ?? 'No email',
            ),
            _buildInfoRow(
              icon: Icons.phone,
              label: 'Phone',
              value: customer.phoneNumber ?? 'No phone number',
            ),
            if (customer.dateOfBirth != null)
              _buildInfoRow(
                icon: Icons.cake,
                label: 'Date of Birth',
                value: DateFormat('MMM d, yyyy').format(customer.dateOfBirth!),
              ),
          ],
        ),

        const SizedBox(height: 16),

        // Address Information
        if (customer.address != null ||
            customer.city != null ||
            customer.wilaya != null)
          _buildInfoSection(
            title: 'Address Information',
            icon: Icons.location_on,
            children: [
              if (customer.address != null)
                _buildInfoRow(
                  icon: Icons.home,
                  label: 'Address',
                  value: customer.address!,
                ),
              if (customer.city != null)
                _buildInfoRow(
                  icon: Icons.location_city,
                  label: 'City',
                  value: customer.city!,
                ),
              if (customer.wilaya != null)
                _buildInfoRow(
                  icon: Icons.map,
                  label: 'Wilaya',
                  value: customer.wilaya!,
                ),
            ],
          ),

        const SizedBox(height: 16),

        // Customer Statistics
        _buildInfoSection(
          title: 'Customer Statistics',
          icon: Icons.analytics,
          children: [
            _buildInfoRow(
              icon: Icons.event,
              label: 'Total Appointments',
              value: customer.totalAppointments.toString(),
            ),
            _buildInfoRow(
              icon: Icons.attach_money,
              label: 'Total Spent',
              value: _formatCurrency(customer.totalSpent),
            ),
            if (customer.lastAppointmentDate != null)
              _buildInfoRow(
                icon: Icons.schedule,
                label: 'Last Appointment',
                value: DateFormat(
                  'MMM d, yyyy',
                ).format(customer.lastAppointmentDate!),
              ),
            _buildInfoRow(
              icon: Icons.person_outline,
              label: 'Customer Since',
              value: DateFormat('MMM d, yyyy').format(customer.createdAt),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Tags
        if (customer.tags.isNotEmpty)
          _buildInfoSection(
            title: 'Tags',
            icon: Icons.label,
            children: [
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: customer.tags.map((tag) => _buildTag(tag)).toList(),
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Notes
        if (customer.notes?.isNotEmpty == true)
          _buildInfoSection(
            title: 'Notes',
            icon: Icons.note,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  customer.notes!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(height: 1.4),
                ),
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Preferences
        if (customer.preferences != null)
          _buildInfoSection(
            title: 'Preferences',
            icon: Icons.settings,
            children: [
              _buildPreferenceRow(
                'Email Notifications',
                customer.preferences!.emailNotifications,
              ),
              _buildPreferenceRow(
                'SMS Notifications',
                customer.preferences!.smsNotifications,
              ),
              _buildPreferenceRow(
                'Appointment Reminders',
                customer.preferences!.appointmentReminders,
              ),
              _buildPreferenceRow(
                'Promotional Messages',
                customer.preferences!.promotionalMessages,
              ),
              if (customer.preferences!.preferredLanguage != null)
                _buildInfoRow(
                  icon: Icons.language,
                  label: 'Preferred Language',
                  value: customer.preferences!.preferredLanguage!,
                ),
              if (customer.preferences!.preferredContactMethod != null)
                _buildInfoRow(
                  icon: Icons.contact_support,
                  label: 'Preferred Contact Method',
                  value: customer.preferences!.preferredContactMethod!,
                ),
            ],
          ),
      ],
    );
  }

  Widget _buildInfoSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Builder(
      builder:
          (context) => Card(
            elevation: 1,
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        icon,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        title,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ...children,
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Builder(
      builder:
          (context) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  icon,
                  size: 18,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: Text(
                    label,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildPreferenceRow(String label, bool enabled) {
    return Builder(
      builder:
          (context) => Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Row(
              children: [
                Icon(
                  enabled ? Icons.check_circle : Icons.cancel,
                  size: 18,
                  color:
                      enabled
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 12),
                Text(label, style: Theme.of(context).textTheme.bodyMedium),
              ],
            ),
          ),
    );
  }

  Widget _buildTag(String tag) {
    return Builder(
      builder:
          (context) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Text(
              tag,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
    );
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: 'DA ', decimalDigits: 0);
    return formatter.format(amount);
  }
}
