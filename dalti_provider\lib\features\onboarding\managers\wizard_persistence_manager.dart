import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/wizard_persistence_models.dart';
import '../services/wizard_persistence_service.dart';
import '../services/wizard_auto_save_service.dart';
import '../services/wizard_recovery_service.dart';
import '../providers/enhanced_wizard_navigation_provider.dart';

/// Comprehensive manager for wizard persistence functionality
class WizardPersistenceManager {
  final WizardPersistenceService _persistenceService;
  final WizardAutoSaveService _autoSaveService;
  final WizardRecoveryService _recoveryService;
  final Ref _ref;

  bool _isInitialized = false;
  bool _recoveryShown = false;

  WizardPersistenceManager({
    required WizardPersistenceService persistenceService,
    required WizardAutoSaveService autoSaveService,
    required WizardRecoveryService recoveryService,
    required Ref ref,
  }) : _persistenceService = persistenceService,
       _autoSaveService = autoSaveService,
       _recoveryService = recoveryService,
       _ref = ref;

  /// Initialize the persistence manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('[WizardPersistenceManager] Initializing...');
      
      // Enable auto-save
      _autoSaveService.enable();
      
      // Cleanup old data
      await _persistenceService.cleanup();
      
      _isInitialized = true;
      print('[WizardPersistenceManager] Initialized successfully');
    } catch (e) {
      print('[WizardPersistenceManager] Error during initialization: $e');
    }
  }

  /// Check and handle recovery on app startup
  Future<bool> handleStartupRecovery(BuildContext context) async {
    if (_recoveryShown) return false;

    try {
      _recoveryShown = true;
      
      final recoveryInfo = await _recoveryService.checkRecoveryAvailable();
      if (recoveryInfo == null) {
        print('[WizardPersistenceManager] No recovery data available');
        return false;
      }

      print('[WizardPersistenceManager] Recovery data found: ${recoveryInfo.ageText}');
      
      // Show recovery dialog to user
      final shouldRecover = await _recoveryService.showRecoveryDialog(context, recoveryInfo);
      
      if (shouldRecover) {
        final result = await _recoveryService.recoverState(_ref);
        if (result.success) {
          _showRecoverySuccessMessage(context, result);
          return true;
        } else {
          _showRecoveryErrorMessage(context, result.error);
        }
      } else {
        // User declined recovery, clear saved state
        await _recoveryService.declineRecovery();
      }
      
      return false;
    } catch (e) {
      print('[WizardPersistenceManager] Error handling startup recovery: $e');
      return false;
    }
  }

  /// Save current wizard state immediately
  Future<PersistenceResult> saveNow({String? reason}) async {
    return await _autoSaveService.forceSave(reason: reason ?? 'manual_save');
  }

  /// Enable auto-save
  void enableAutoSave() {
    _autoSaveService.enable();
    print('[WizardPersistenceManager] Auto-save enabled');
  }

  /// Disable auto-save
  void disableAutoSave() {
    _autoSaveService.disable();
    print('[WizardPersistenceManager] Auto-save disabled');
  }

  /// Check if auto-save is enabled
  bool get isAutoSaveEnabled => _autoSaveService.isEnabled;

  /// Get last save time
  DateTime? get lastSaveTime => _autoSaveService.lastSaveTime;

  /// Clear all saved data
  Future<bool> clearAllData() async {
    try {
      final success = await _persistenceService.clearState();
      if (success) {
        print('[WizardPersistenceManager] All saved data cleared');
      }
      return success;
    } catch (e) {
      print('[WizardPersistenceManager] Error clearing data: $e');
      return false;
    }
  }

  /// Get storage statistics
  Future<StorageStats> getStorageStats() async {
    return await _persistenceService.getStats();
  }

  /// Check if saved data exists
  bool hasSavedData() {
    return _persistenceService.hasState();
  }

  /// Show save status to user
  void showSaveStatus(BuildContext context, PersistenceResult result) {
    if (result.success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text('Progress saved (${_formatDataSize(result.dataSize ?? 0)})'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text('Save failed: ${result.error}'),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// Show recovery success message
  void _showRecoverySuccessMessage(BuildContext context, RecoveryResult result) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.restore, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('Session restored to ${result.recoveredStep?.title ?? 'previous step'}'),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show recovery error message
  void _showRecoveryErrorMessage(BuildContext context, String? error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('Recovery failed: ${error ?? 'Unknown error'}'),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Format data size for display
  String _formatDataSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Dispose resources
  void dispose() {
    _autoSaveService.dispose();
    print('[WizardPersistenceManager] Disposed');
  }
}

/// Provider for wizard persistence manager
final wizardPersistenceManagerProvider = Provider<WizardPersistenceManager>((ref) {
  final persistenceService = WizardPersistenceServiceFactory.getInstance();
  final autoSaveService = ref.watch(wizardAutoSaveServiceProvider);
  final recoveryService = ref.watch(wizardRecoveryServiceProvider);
  
  final manager = WizardPersistenceManager(
    persistenceService: persistenceService,
    autoSaveService: autoSaveService,
    recoveryService: recoveryService,
    ref: ref,
  );

  // Initialize on creation
  Future.microtask(() => manager.initialize());

  // Dispose when provider is disposed
  ref.onDispose(() {
    manager.dispose();
  });

  return manager;
});

/// Widget that handles persistence initialization and recovery
class WizardPersistenceWrapper extends ConsumerStatefulWidget {
  final Widget child;
  final bool enableAutoRecovery;

  const WizardPersistenceWrapper({
    super.key,
    required this.child,
    this.enableAutoRecovery = true,
  });

  @override
  ConsumerState<WizardPersistenceWrapper> createState() => _WizardPersistenceWrapperState();
}

class _WizardPersistenceWrapperState extends ConsumerState<WizardPersistenceWrapper> {
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _initializePersistence();
  }

  Future<void> _initializePersistence() async {
    if (!widget.enableAutoRecovery) {
      setState(() {
        _initialized = true;
      });
      return;
    }

    try {
      final manager = ref.read(wizardPersistenceManagerProvider);
      await manager.initialize();
      
      if (mounted) {
        final recovered = await manager.handleStartupRecovery(context);
        if (recovered) {
          print('[WizardPersistenceWrapper] State recovered successfully');
        }
      }
    } catch (e) {
      print('[WizardPersistenceWrapper] Error during initialization: $e');
    } finally {
      if (mounted) {
        setState(() {
          _initialized = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_initialized) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Initializing...'),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}

/// Extension for easy persistence management
extension PersistenceManagerExtension on WidgetRef {
  /// Get persistence manager
  WizardPersistenceManager get persistenceManager => read(wizardPersistenceManagerProvider);

  /// Save wizard state now
  Future<PersistenceResult> saveWizardNow({String? reason}) => 
      persistenceManager.saveNow(reason: reason);

  /// Handle startup recovery
  Future<bool> handleWizardRecovery(BuildContext context) => 
      persistenceManager.handleStartupRecovery(context);

  /// Show save status
  void showSaveStatus(BuildContext context, PersistenceResult result) => 
      persistenceManager.showSaveStatus(context, result);

  /// Clear all wizard data
  Future<bool> clearWizardData() => persistenceManager.clearAllData();

  /// Get storage stats
  Future<StorageStats> getWizardStorageStats() => persistenceManager.getStorageStats();
}
