import '../../../core/network/api_service.dart';
import '../models/onboarding_models.dart';

/// API service for onboarding-related operations
class OnboardingApiService {
  final ApiService _apiService;

  OnboardingApiService({
    required ApiService apiService,
  }) : _apiService = apiService;

  /// Submit completed onboarding data to backend
  Future<OnboardingCompletion> submitOnboarding(OnboardingData data) async {
    try {
      print('[OnboardingApiService] Submitting onboarding data');

      // Prepare the submission payload
      final payload = {
        'businessProfile': data.businessProfile?.toJson(),
        'primaryLocation': data.primaryLocation?.toJson(),
        'services': data.services.map((s) => s.toJson()).toList(),
        'schedules': data.schedules.map((s) => s.toJson()).toList(),
        'queues': data.queues.map((q) => q.toJson()).toList(),
        'startedAt': data.startedAt?.toIso8601String(),
        'completedAt': DateTime.now().toIso8601String(),
      };

      final response = await _apiService.post(
        '/api/auth/providers/onboarding/submit',
        data: payload,
      );

      print('[OnboardingApiService] Onboarding submitted successfully');

      // Create completion object from response
      return OnboardingCompletion(
        businessProfile: data.businessProfile!,
        primaryLocation: data.primaryLocation!,
        services: data.services,
        schedules: data.schedules,
        queues: data.queues,
        completedAt: DateTime.now(),
        totalSteps: OnboardingStep.totalSteps,
        totalTime: data.duration ?? Duration.zero,
      );
    } catch (e) {
      print('[OnboardingApiService] Error submitting onboarding: $e');
      rethrow;
    }
  }

  /// Validate step data on server
  Future<StepValidationResult> validateStep(OnboardingStep step, Map<String, dynamic> data) async {
    try {
      print('[OnboardingApiService] Validating step: $step');

      final payload = {
        'step': step.name,
        'data': data,
      };

      final response = await _apiService.post(
        '/api/auth/providers/onboarding/validate-step',
        data: payload,
      );

      final result = StepValidationResult(
        isValid: response.data['isValid'] ?? false,
        errors: List<String>.from(response.data['errors'] ?? []),
        warnings: List<String>.from(response.data['warnings'] ?? []),
      );

      print('[OnboardingApiService] Step validation result: ${result.isValid}');
      return result;
    } catch (e) {
      print('[OnboardingApiService] Error validating step: $e');
      // Return a generic validation error
      return const StepValidationResult(
        isValid: false,
        errors: ['Unable to validate data. Please check your connection and try again.'],
      );
    }
  }

  /// Get onboarding progress from server (if synced)
  Future<OnboardingData?> getOnboardingProgress() async {
    try {
      print('[OnboardingApiService] Getting onboarding progress from server');

      final response = await _apiService.get('/api/auth/providers/onboarding/progress');

      if (response.data == null) {
        print('[OnboardingApiService] No onboarding progress found on server');
        return null;
      }

      final data = OnboardingData.fromJson(response.data);
      print('[OnboardingApiService] Onboarding progress retrieved from server');
      return data;
    } catch (e) {
      print('[OnboardingApiService] Error getting onboarding progress: $e');
      return null;
    }
  }

  /// Sync onboarding progress to server
  Future<void> syncOnboardingProgress(OnboardingData data) async {
    try {
      print('[OnboardingApiService] Syncing onboarding progress to server');

      final payload = data.toJson();

      await _apiService.post(
        '/api/auth/providers/onboarding/sync',
        data: payload,
      );

      print('[OnboardingApiService] Onboarding progress synced to server');
    } catch (e) {
      print('[OnboardingApiService] Error syncing onboarding progress: $e');
      // Don't rethrow - sync is optional
    }
  }

  /// Check if user needs onboarding from server
  Future<bool> checkOnboardingStatus() async {
    try {
      print('[OnboardingApiService] Checking onboarding status from server');

      final response = await _apiService.get('/api/auth/providers/onboarding/status');

      final needsOnboarding = response.data['needsOnboarding'] ?? true;
      print('[OnboardingApiService] Server onboarding status - needs: $needsOnboarding');
      
      return needsOnboarding;
    } catch (e) {
      print('[OnboardingApiService] Error checking onboarding status: $e');
      return true; // Assume needs onboarding on error
    }
  }

  /// Get onboarding configuration from server
  Future<Map<String, dynamic>> getOnboardingConfig() async {
    try {
      print('[OnboardingApiService] Getting onboarding configuration');

      final response = await _apiService.get('/api/auth/providers/onboarding/config');

      final config = response.data ?? {};
      print('[OnboardingApiService] Onboarding configuration retrieved');
      
      return config;
    } catch (e) {
      print('[OnboardingApiService] Error getting onboarding config: $e');
      // Return default configuration
      return {
        'steps': OnboardingStep.values.map((s) => s.name).toList(),
        'allowSkip': false,
        'requireAllSteps': true,
        'maxDuration': 3600, // 1 hour in seconds
      };
    }
  }

  /// Update business profile on server
  Future<void> updateBusinessProfile(BusinessProfile profile) async {
    try {
      print('[OnboardingApiService] Updating business profile on server');

      final payload = profile.toJson();

      await _apiService.put(
        '/api/auth/providers/profile/business',
        data: payload,
      );

      print('[OnboardingApiService] Business profile updated on server');
    } catch (e) {
      print('[OnboardingApiService] Error updating business profile: $e');
      rethrow;
    }
  }

  /// Get business categories for onboarding
  Future<List<Map<String, dynamic>>> getBusinessCategories() async {
    try {
      print('[OnboardingApiService] Getting business categories');

      final response = await _apiService.get('/api/auth/provider/categories');

      final categories = List<Map<String, dynamic>>.from(response.data ?? []);
      print('[OnboardingApiService] Retrieved ${categories.length} business categories');
      
      return categories;
    } catch (e) {
      print('[OnboardingApiService] Error getting business categories: $e');
      rethrow;
    }
  }

  /// Get Algerian cities for location setup
  Future<List<String>> getAlgerianCities() async {
    try {
      print('[OnboardingApiService] Getting Algerian cities');

      final response = await _apiService.get('/api/auth/providers/cities/algeria');

      final cities = List<String>.from(response.data ?? []);
      print('[OnboardingApiService] Retrieved ${cities.length} Algerian cities');
      
      return cities;
    } catch (e) {
      print('[OnboardingApiService] Error getting Algerian cities: $e');
      // Return default cities if API fails
      return [
        'Algiers', 'Oran', 'Constantine', 'Annaba', 'Blida', 'Batna', 'Djelfa',
        'Sétif', 'Sidi Bel Abbès', 'Biskra', 'Tébessa', 'El Oued', 'Skikda',
        'Tiaret', 'Béjaïa', 'Tlemcen', 'Ouargla', 'Béchar', 'Mostaganem',
        'Bordj Bou Arréridj', 'Chlef', 'Médéa', 'El Tarf', 'Jijel', 'Relizane',
        'M\'Sila', 'Saïda', 'El Bayadh', 'Ghardaïa', 'Laghouat', 'Khenchela',
        'Souk Ahras', 'Naâma', 'Aïn Defla', 'Tissemsilt', 'El Eulma',
        'Bordj El Kiffan', 'Rouiba', 'Dély Ibrahim', 'Bab Ezzouar',
        'Dar El Beïda', 'Birkhadem', 'Draria', 'Zeralda', 'Saoula',
        'Ouled Fayet', 'Chéraga', 'Djasr Kasentina', 'El Harrach',
        'Baraki', 'Oued Smar', 'Bachdjerrah', 'Hussein Dey', 'Kouba',
        'Boumerdès', 'Thénia', 'Bordj Menaïel', 'Khemis Miliana', 'Aflou',
        'Aïn Oussera', 'Ksar El Boukhari', 'Chettia', 'Sour El Ghozlane',
        'Boufarik', 'Larbaa', 'Oued El Alleug', 'Meftah', 'Bouinan',
        'El Affroun', 'Chiffa', 'Hammam Melouane', 'Ouled Yaïch',
        'Beni Tamou', 'Soumaa', 'Guerrouaou', 'Tablat', 'Boukadir',
        'Miliana', 'Khemis Miliana', 'El Attaf', 'El Abadia', 'Oued Fodda',
        'Ain Defla', 'Djendel', 'Barbouche', 'Zeddine', 'Hoceinia',
        'Tacheta Zegagha', 'Sidi Lakhdar', 'El Amra', 'Bourached',
        'Ben Allal', 'Rouina', 'Djelida', 'Bir Ould Khelifa', 'Bathia'
      ];
    }
  }
}
