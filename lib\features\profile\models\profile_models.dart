import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_models.freezed.dart';
part 'profile_models.g.dart';

/// Provider category information
@freezed
class ProfileCategory with _$ProfileCategory {
  const factory ProfileCategory({
    required int id,
    required String title,
  }) = _ProfileCategory;

  factory ProfileCategory.fromJson(Map<String, dynamic> json) =>
      _$ProfileCategoryFromJson(json);
}

/// Provider profile data
@freezed
class ProfileData with _$ProfileData {
  const factory ProfileData({
    required int id,
    required String userId,
    String? title,
    String? phone,
    String? presentation,
    required bool isVerified,
    required bool isSetupComplete,
    ProfileCategory? category,
    double? averageRating,
    required int totalReviews,
  }) = _ProfileData;

  factory ProfileData.fromJson(Map<String, dynamic> json) =>
      _$ProfileDataFromJson(json);
}

/// Profile update request
@freezed
class ProfileUpdateRequest with _$ProfileUpdateRequest {
  const factory ProfileUpdateRequest({
    String? title,
    String? phone,
    String? presentation,
    int? providerCategoryId,
  }) = _ProfileUpdateRequest;

  factory ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateRequestFromJson(json);
}

/// Profile API response wrapper
@freezed
class ProfileResponse with _$ProfileResponse {
  const factory ProfileResponse({
    required bool success,
    ProfileData? data,
    String? message,
  }) = _ProfileResponse;

  factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$ProfileResponseFromJson(json);
}

/// Profile error information
@freezed
class ProfileError with _$ProfileError {
  const factory ProfileError({
    required String code,
    required String message,
    String? details,
    List<String>? validationErrors,
  }) = _ProfileError;

  factory ProfileError.fromJson(Map<String, dynamic> json) =>
      _$ProfileErrorFromJson(json);
}

/// Profile operation result
@freezed
class ProfileResult with _$ProfileResult {
  const factory ProfileResult.success(ProfileData data) = ProfileSuccess;
  const factory ProfileResult.error(ProfileError error) = ProfileFailure;
}
