import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/appointment_models.dart';
import '../providers/calendar_config_provider.dart';
import 'calendar_config_dialog.dart';
import '../providers/appointment_provider.dart';

class TableCalendarWidget extends ConsumerStatefulWidget {
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;
  final Function(DateTime)? onAddAppointmentAtTime;

  const TableCalendarWidget({
    super.key,
    this.onAppointmentTap,
    this.onAddAppointment,
    this.onAddAppointmentAtTime,
  });

  @override
  ConsumerState<TableCalendarWidget> createState() =>
      _TableCalendarWidgetState();
}

class _TableCalendarWidgetState extends ConsumerState<TableCalendarWidget> {
  late DateTime _selectedDay;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();

    // Load appointments when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAppointments();
      _scrollToCurrentTime();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadAppointments() {
    final startDate = DateTime(
      _selectedDay.year,
      _selectedDay.month,
      _selectedDay.day,
    );
    final endDate = startDate.add(const Duration(days: 1));

    ref
        .read(appointmentNotifierProvider.notifier)
        .loadAppointments(
          filters: AppointmentSearchFilters(
            startDate: startDate,
            endDate: endDate,
          ),
        );
  }

  void _scrollToCurrentTime() {
    if (_isSameDay(DateTime.now(), _selectedDay)) {
      final now = DateTime.now();
      final hourHeight = 80.0;
      final minuteHeight = hourHeight / 60;
      final targetOffset =
          (now.hour * hourHeight) + (now.minute * minuteHeight) - 200;

      if (targetOffset > 0) {
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  void _changeDate(int days) {
    setState(() {
      _selectedDay = _selectedDay.add(Duration(days: days));
    });
    _loadAppointments();
  }

  void _showCalendarConfig() {
    final calendarConfig = ref.read(calendarConfigProvider);
    showCalendarConfigDialog(
      context: context,
      currentTimeSlotInterval: calendarConfig.timeSlotInterval,
      onTimeSlotIntervalChanged: (interval) {
        ref.read(calendarConfigProvider.notifier).setTimeSlotInterval(interval);
      },
    );
  }

  void _onTimeSlotTap(DateTime slotTime) {
    // Check if there's already an appointment at this time slot
    final appointmentsAsync = ref.read(appointmentNotifierProvider);
    appointmentsAsync.whenData((appointments) {
      final hasAppointmentAtSlot = appointments.any((appointment) {
        final appointmentStart = appointment.scheduledTime.toLocal();
        final appointmentEnd = appointmentStart.add(
          Duration(minutes: appointment.duration),
        );

        // Check if the slot time falls within any existing appointment
        return slotTime.isAfter(
              appointmentStart.subtract(Duration(minutes: 1)),
            ) &&
            slotTime.isBefore(appointmentEnd);
      });

      // Only open new appointment screen if slot is empty
      if (!hasAppointmentAtSlot) {
        if (widget.onAddAppointmentAtTime != null) {
          // Use the new callback that accepts preset time
          widget.onAddAppointmentAtTime!(slotTime);
        } else if (widget.onAddAppointment != null) {
          // Fallback to the old callback
          widget.onAddAppointment!();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Date navigation header
        _buildDateHeader(),

        // Timeline day view
        Expanded(child: _buildTimelineView()),
      ],
    );
  }

  Widget _buildDateHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => _changeDate(-1),
            icon: const Icon(Icons.chevron_left),
            tooltip: 'Previous day',
          ),
          Expanded(
            child: Center(
              child: Text(
                DateFormat('EEEE, MMMM d, y').format(_selectedDay),
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
              ),
            ),
          ),
          IconButton(
            onPressed: () => _changeDate(1),
            icon: const Icon(Icons.chevron_right),
            tooltip: 'Next day',
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _selectedDay = DateTime.now();
              });
              _loadAppointments();
              _scrollToCurrentTime();
            },
            icon: const Icon(Icons.today),
            tooltip: 'Today',
          ),
          IconButton(
            onPressed: _showCalendarConfig,
            icon: const Icon(Icons.settings),
            tooltip: 'Calendar Settings',
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineView() {
    final appointmentsValue = ref.watch(appointmentNotifierProvider);

    return appointmentsValue.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error loading appointments',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                TextButton(
                  onPressed: _loadAppointments,
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
      data: (appointments) {
        final dayAppointments =
            appointments
                .where((apt) => _isSameDay(apt.scheduledTime, _selectedDay))
                .toList();

        return _buildTimeGrid(dayAppointments);
      },
    );
  }

  Widget _buildTimeGrid(List<Appointment> appointments) {
    // Get calendar configuration
    final calendarConfig = ref.watch(calendarConfigProvider);
    final timeSlotInterval = calendarConfig.timeSlotInterval;

    // Calculate time slots based on interval
    final slotsPerHour = 60 ~/ timeSlotInterval;
    final totalSlots = 24 * slotsPerHour;

    // Dynamic hour height based on interval - smaller intervals need taller hours
    final minSlotHeight = 24.0; // Height needed for a normal appointment card
    final baseHourHeight = 80.0; // Base height for 60-minute intervals
    final hourHeight = (slotsPerHour * minSlotHeight).clamp(
      baseHourHeight,
      400.0,
    ); // Max 400px per hour to accommodate 5-minute intervals
    final slotHeight = hourHeight / slotsPerHour;

    // Calculate overlapping appointment columns
    final appointmentColumns = _calculateAppointmentColumns(appointments);

    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        children: [
          SizedBox(
            height: totalSlots * slotHeight, // Total height for all time slots
            child: Row(
              children: [
                // Time labels column
                SizedBox(
                  width: 60,
                  child: Column(
                    children: List.generate(totalSlots, (index) {
                      final hour = index ~/ slotsPerHour;
                      final minute = (index % slotsPerHour) * timeSlotInterval;

                      // Only show label for hour marks or major intervals
                      final showLabel = minute == 0 || timeSlotInterval >= 30;

                      return SizedBox(
                        height: slotHeight,
                        child:
                            showLabel
                                ? Center(
                                  child: Text(
                                    DateFormat('HH:mm').format(
                                      DateTime(2023, 1, 1, hour, minute),
                                    ),
                                    style: Theme.of(
                                      context,
                                    ).textTheme.labelSmall?.copyWith(
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurfaceVariant,
                                      fontSize:
                                          timeSlotInterval <= 10
                                              ? 10
                                              : null, // Smaller font for dense intervals
                                    ),
                                  ),
                                )
                                : null,
                      );
                    }),
                  ),
                ),

                // Divider
                Container(
                  width: 1,
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.3),
                ),

                // Appointments timeline column
                Expanded(
                  child: Stack(
                    children: [
                      // Time slot grid lines with click functionality
                      Column(
                        children: List.generate(totalSlots, (index) {
                          final hour = index ~/ slotsPerHour;
                          final minute =
                              (index % slotsPerHour) * timeSlotInterval;
                          final isHourMark = minute == 0;

                          // Calculate the exact time for this slot
                          final slotTime = DateTime(
                            _selectedDay.year,
                            _selectedDay.month,
                            _selectedDay.day,
                            hour,
                            minute,
                          );

                          return GestureDetector(
                            onTap: () => _onTimeSlotTap(slotTime),
                            child: Container(
                              height: slotHeight,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.outline.withValues(
                                      alpha:
                                          isHourMark
                                              ? 0.5
                                              : 0.25, // Much more visible lines
                                    ),
                                    width: isHourMark ? 1.0 : 0.5,
                                  ),
                                ),
                              ),
                              // Add subtle hover effect
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: Container(
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                              ),
                            ),
                          );
                        }),
                      ),

                      // Current time indicator (if today)
                      if (_isSameDay(DateTime.now(), _selectedDay))
                        _buildCurrentTimeIndicator(),

                      // Positioned appointments with overlap handling
                      ...appointments.map((appointment) {
                        final columnInfo = appointmentColumns[appointment];
                        return _buildPositionedAppointment(
                          appointment,
                          columnInfo!['column'] as int,
                          columnInfo['totalColumns'] as int,
                        );
                      }),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Add bottom padding to ensure we can scroll past the bottom navigation bar
          const SizedBox(height: 100), // Extra space at bottom
        ],
      ),
    );
  }

  /// Calculate column positions for overlapping appointments
  Map<Appointment, Map<String, int>> _calculateAppointmentColumns(
    List<Appointment> appointments,
  ) {
    // Sort appointments by start time, then by duration (longer first)
    final sortedAppointments = [...appointments];
    sortedAppointments.sort((a, b) {
      final timeCompare = a.scheduledTime.compareTo(b.scheduledTime);
      if (timeCompare != 0) return timeCompare;
      // If same start time, longer appointments first
      return b.duration.compareTo(a.duration);
    });

    final result = <Appointment, Map<String, int>>{};
    final List<List<Appointment>> columns = [];

    // Debug print
    print(
      '=== Calculating columns for ${appointments.length} appointments ===',
    );

    for (final appointment in sortedAppointments) {
      final appointmentStart = appointment.scheduledTime.toLocal();
      final appointmentEnd = appointmentStart.add(
        Duration(minutes: appointment.duration),
      );

      print(
        'Processing: ${appointment.customerName} ${DateFormat('HH:mm').format(appointmentStart)}-${DateFormat('HH:mm').format(appointmentEnd)}',
      );

      // Find the first column where this appointment doesn't overlap
      int columnIndex = -1;
      for (int i = 0; i < columns.length; i++) {
        bool hasOverlap = false;
        for (final existingAppointment in columns[i]) {
          final existingStart = existingAppointment.scheduledTime.toLocal();
          final existingEnd = existingStart.add(
            Duration(minutes: existingAppointment.duration),
          );

          // Check if appointments overlap visually (considering UI positioning)
          // Calculate visual positions for overlap detection using dynamic hour height
          final calendarConfig = ref.read(calendarConfigProvider);
          final timeSlotInterval = calendarConfig.timeSlotInterval;
          final slotsPerHour = 60 ~/ timeSlotInterval;
          final minSlotHeight = 24.0;
          final baseHourHeight = 80.0;
          final hourHeight = (slotsPerHour * minSlotHeight).clamp(
            baseHourHeight,
            400.0,
          );
          final minuteHeight = hourHeight / 60;

          final appointmentTopOffset =
              (appointmentStart.hour * hourHeight) +
              (appointmentStart.minute * minuteHeight);
          final appointmentHeight =
              (Duration(minutes: appointment.duration).inMinutes * minuteHeight)
                  .clamp(24.0, hourHeight * 1.5);
          final appointmentBottomOffset =
              appointmentTopOffset + appointmentHeight;

          final existingTopOffset =
              (existingStart.hour * hourHeight) +
              (existingStart.minute * minuteHeight);
          final existingHeight = (Duration(
                    minutes: existingAppointment.duration,
                  ).inMinutes *
                  minuteHeight)
              .clamp(24.0, hourHeight * 1.5);
          final existingBottomOffset = existingTopOffset + existingHeight;

          // Check for visual overlap with a small buffer (2px)
          if (appointmentTopOffset < existingBottomOffset + 2 &&
              appointmentBottomOffset > existingTopOffset - 2) {
            hasOverlap = true;
            print(
              '  Visual overlap with: ${existingAppointment.customerName} ${DateFormat('HH:mm').format(existingStart)}-${DateFormat('HH:mm').format(existingEnd)} in column $i',
            );
            print(
              '    Appointment: top=$appointmentTopOffset, bottom=$appointmentBottomOffset',
            );
            print(
              '    Existing: top=$existingTopOffset, bottom=$existingBottomOffset',
            );
            break;
          }
        }
        if (!hasOverlap) {
          columnIndex = i;
          break;
        }
      }

      // If no column found, create a new one
      if (columnIndex == -1) {
        columns.add([]);
        columnIndex = columns.length - 1;
        print('  Created new column $columnIndex');
      } else {
        print('  Assigned to existing column $columnIndex');
      }

      columns[columnIndex].add(appointment);
    }

    // Calculate total columns needed for each time slot using visual overlap
    for (final appointment in sortedAppointments) {
      final appointmentStart = appointment.scheduledTime.toLocal();

      // Calculate visual positions for this appointment
      const hourHeight = 80.0;
      const minuteHeight = hourHeight / 60;
      final appointmentTopOffset =
          (appointmentStart.hour * hourHeight) +
          (appointmentStart.minute * minuteHeight);
      final appointmentHeight =
          (Duration(minutes: appointment.duration).inMinutes * minuteHeight)
              .clamp(24.0, hourHeight * 1.5);
      final appointmentBottomOffset = appointmentTopOffset + appointmentHeight;

      // Find all appointments that visually overlap with this one
      int maxColumns = 1;
      for (final otherAppointment in sortedAppointments) {
        if (otherAppointment == appointment) continue;

        final otherStart = otherAppointment.scheduledTime.toLocal();
        final otherTopOffset =
            (otherStart.hour * hourHeight) + (otherStart.minute * minuteHeight);
        final otherHeight = (Duration(
                  minutes: otherAppointment.duration,
                ).inMinutes *
                minuteHeight)
            .clamp(24.0, hourHeight * 1.5);
        final otherBottomOffset = otherTopOffset + otherHeight;

        // Check for visual overlap with 2px buffer
        if (appointmentTopOffset < otherBottomOffset + 2 &&
            appointmentBottomOffset > otherTopOffset - 2) {
          maxColumns++;
        }
      }

      // Find which column this appointment is in
      int appointmentColumn = 0;
      for (int i = 0; i < columns.length; i++) {
        if (columns[i].contains(appointment)) {
          appointmentColumn = i;
          break;
        }
      }

      result[appointment] = {
        'column': appointmentColumn,
        'totalColumns': maxColumns,
      };

      print(
        'Final: ${appointment.customerName} -> Column $appointmentColumn of $maxColumns',
      );
    }

    print('=== Column calculation complete ===');
    return result;
  }

  Widget _buildCurrentTimeIndicator() {
    // Ensure we're using local time for the current time indicator
    final now = DateTime.now().toLocal();
    const hourHeight = 80.0;
    const minuteHeight = hourHeight / 60;
    final topOffset = (now.hour * hourHeight) + (now.minute * minuteHeight);

    return Positioned(
      top: topOffset,
      left: 8,
      right: 8,
      child: Container(
        height: 2,
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(1),
        ),
        child: Row(
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(child: Container(height: 2, color: Colors.red)),
          ],
        ),
      ),
    );
  }

  Widget _buildPositionedAppointment(
    Appointment appointment,
    int column,
    int totalColumns,
  ) {
    // Get calendar configuration for dynamic hour height
    final calendarConfig = ref.read(calendarConfigProvider);
    final timeSlotInterval = calendarConfig.timeSlotInterval;
    final slotsPerHour = 60 ~/ timeSlotInterval;
    final minSlotHeight = 24.0;
    final baseHourHeight = 80.0;
    final hourHeight = (slotsPerHour * minSlotHeight).clamp(
      baseHourHeight,
      400.0,
    );
    final minuteHeight = hourHeight / 60;
    const leftMargin = 8.0;
    const rightMargin = 8.0;
    const columnSpacing = 2.0;

    // Ensure we're working with local time for positioning
    final startTime = appointment.scheduledTime.toLocal();
    final topOffset =
        (startTime.hour * hourHeight) + (startTime.minute * minuteHeight);

    // Debug positioning
    print(
      'Positioning ${appointment.customerName}: ${DateFormat('HH:mm').format(startTime)} at topOffset: $topOffset, column: $column/$totalColumns',
    );

    // Calculate duration
    Duration duration = Duration(minutes: appointment.duration);

    // Simple height calculation - minimum 24px for readability
    final height = (duration.inMinutes * minuteHeight).clamp(
      24.0,
      hourHeight * 1.5,
    );

    // Calculate width and position for overlapping appointments
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth =
        screenWidth -
        60 -
        leftMargin -
        rightMargin -
        1; // Subtract time column width, margins, and divider

    // Ensure we don't create columns that are too narrow or push content off screen
    final minColumnWidth = 80.0; // Minimum width for readability
    final maxColumns = (availableWidth / minColumnWidth).floor().clamp(
      1,
      totalColumns,
    );
    final actualColumns = totalColumns > maxColumns ? maxColumns : totalColumns;

    final columnWidth =
        (availableWidth - (actualColumns - 1) * columnSpacing) / actualColumns;
    final adjustedColumn = column >= actualColumns ? actualColumns - 1 : column;
    final leftPosition =
        leftMargin + (adjustedColumn * (columnWidth + columnSpacing));

    // Debug width calculation
    print(
      'Width calc: screen=$screenWidth, available=$availableWidth, columnWidth=$columnWidth, leftPos=$leftPosition, cols=$actualColumns/$totalColumns',
    );

    return Positioned(
      top: topOffset,
      left: leftPosition,
      width: columnWidth,
      child: Container(
        height: height,
        margin: const EdgeInsets.only(bottom: 2),
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: _getAppointmentColor(appointment),
          borderRadius: BorderRadius.circular(8),
          border:
              totalColumns > 1
                  ? Border.all(color: Colors.white, width: 1.5)
                  : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () => widget.onAppointmentTap?.call(appointment),
          child: Center(
            child: Text(
              _buildAppointmentText(appointment),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 10,
              ),
              maxLines: height > 40 ? 2 : 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }

  String _buildAppointmentText(Appointment appointment) {
    // Ensure we're working with local time
    final startTime = appointment.scheduledTime.toLocal();
    final startTimeStr = DateFormat('HH:mm').format(startTime);

    // For completed appointments, show real end time if available
    if (appointment.status == AppointmentStatus.completed &&
        appointment.expectedEndTime != null) {
      // Ensure real end time is also in local time
      final endTime = appointment.expectedEndTime!.toLocal();
      final endTimeStr = DateFormat('HH:mm').format(endTime);
      return '$startTimeStr-$endTimeStr ${appointment.customerName} - ${appointment.serviceName}';
    }

    // For other appointments, show normal format
    return '$startTimeStr ${appointment.customerName} - ${appointment.serviceName}';
  }

  Color _getAppointmentColor(Appointment appointment) {
    switch (appointment.status) {
      case AppointmentStatus.scheduled:
        return Colors.blue;
      case AppointmentStatus.confirmed:
        return Colors.green;
      case AppointmentStatus.inProgress:
        return Colors.orange;
      case AppointmentStatus.completed:
        return Colors.purple;
      case AppointmentStatus.canceled:
        return Colors.red;
      case AppointmentStatus.noShow:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }
}
