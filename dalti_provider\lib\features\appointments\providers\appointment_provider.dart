import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/network/http_client.dart';
import '../../../core/providers/app_providers.dart';
import '../models/appointment_models.dart';
import '../repository/appointment_repository.dart';
import '../repository/appointment_repository_impl.dart';
import '../services/appointment_api_service.dart';

part 'appointment_provider.g.dart';

/// Provider for AppointmentApiService
@riverpod
AppointmentApiService appointmentApiService(AppointmentApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return AppointmentApiService(httpClient);
}

/// Provider for AppointmentRepository
@riverpod
AppointmentRepository appointmentRepository(AppointmentRepositoryRef ref) {
  final apiService = ref.watch(appointmentApiServiceProvider);
  return AppointmentRepositoryImpl(apiService);
}

/// Provider for appointments list with filters
@riverpod
class AppointmentNotifier extends _$AppointmentNotifier {
  @override
  AsyncValue<List<Appointment>> build() {
    return const AsyncValue.loading();
  }

  /// Load appointments with optional filters
  Future<void> loadAppointments({
    AppointmentSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  }) async {
    state = const AsyncValue.loading();

    try {
      print('[AppointmentNotifier] Loading appointments...');
      final repository = ref.read(appointmentRepositoryProvider);
      final appointments = await repository.getAppointments(
        filters: filters,
        page: page,
        pageSize: pageSize,
      );

      print(
        '[AppointmentNotifier] Loaded ${appointments.length} appointments successfully',
      );
      state = AsyncValue.data(appointments);
    } catch (error, stackTrace) {
      print('[AppointmentNotifier] Error loading appointments: $error');
      print('[AppointmentNotifier] Stack trace: $stackTrace');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh appointments
  Future<void> refreshAppointments() async {
    await loadAppointments();
  }

  /// Add new appointment
  Future<void> addAppointment(AppointmentRequest request) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final newAppointment = await repository.createAppointment(request);

      // Add to current state if loaded
      state.whenData((appointments) {
        state = AsyncValue.data([newAppointment, ...appointments]);
      });
    } catch (error) {
      rethrow;
    }
  }

  /// Add new appointment using the new API schema
  Future<Appointment> addNewAppointment(
    CreateAppointmentRequest request,
  ) async {
    try {
      final apiService = ref.read(appointmentApiServiceProvider);
      final response = await apiService.createNewAppointment(request);

      if (response.success && response.appointment != null) {
        final newAppointment = response.appointment!;

        // Add to current state if loaded
        state.whenData((appointments) {
          state = AsyncValue.data([newAppointment, ...appointments]);
        });

        return newAppointment;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to create appointment',
        );
      }
    } catch (error) {
      rethrow;
    }
  }

  /// Update appointment
  Future<void> updateAppointment(
    String appointmentId,
    UpdateAppointmentRequest request,
  ) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final updatedAppointment = await repository.updateAppointment(
        appointmentId,
        request,
      );

      // Update in current state if loaded
      state.whenData((appointments) {
        final updatedList =
            appointments.map((apt) {
              return apt.id == appointmentId ? updatedAppointment : apt;
            }).toList();
        state = AsyncValue.data(updatedList);
      });
    } catch (error) {
      rethrow;
    }
  }

  /// Update appointment status
  Future<void> updateAppointmentStatus(
    String appointmentId,
    AppointmentStatus status, {
    String? notes,
  }) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final updatedAppointment = await repository.updateAppointmentStatus(
        appointmentId,
        status,
        notes: notes,
      );

      // Update in current state if loaded
      state.whenData((appointments) {
        final updatedList =
            appointments.map((apt) {
              return apt.id == appointmentId ? updatedAppointment : apt;
            }).toList();
        state = AsyncValue.data(updatedList);
      });
    } catch (error) {
      // Check if this is the special case where status update succeeded but no data returned
      if (error.toString().contains('STATUS_UPDATE_SUCCESS_NO_DATA')) {
        // Status update was successful, just don't update local state
        // The UI will refresh the appointments list anyway
        print(
          '[AppointmentProvider] Status update successful, will refresh via UI',
        );
        return;
      }
      rethrow;
    }
  }

  /// Cancel appointment
  Future<void> cancelAppointment(String appointmentId, String reason) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      await repository.cancelAppointment(appointmentId, reason);

      // Update status in current state
      await updateAppointmentStatus(appointmentId, AppointmentStatus.canceled);
    } catch (error) {
      rethrow;
    }
  }

  /// Delete appointment
  Future<void> deleteAppointment(String appointmentId) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      await repository.deleteAppointment(appointmentId);

      // Remove from current state if loaded
      state.whenData((appointments) {
        final updatedList =
            appointments.where((apt) => apt.id != appointmentId).toList();
        state = AsyncValue.data(updatedList);
      });
    } catch (error) {
      rethrow;
    }
  }

  /// Extend appointment duration
  Future<Appointment> extendAppointment(
    String appointmentId,
    int extensionMinutes,
  ) async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final extendedAppointment = await repository.extendAppointment(
        appointmentId,
        extensionMinutes,
      );

      // Update in current state if loaded
      state.whenData((appointments) {
        final updatedList =
            appointments.map((apt) {
              return apt.id == appointmentId ? extendedAppointment : apt;
            }).toList();
        state = AsyncValue.data(updatedList);
      });

      return extendedAppointment;
    } catch (error) {
      rethrow;
    }
  }
}

/// Provider for single appointment
@riverpod
class SingleAppointmentNotifier extends _$SingleAppointmentNotifier {
  @override
  AsyncValue<Appointment?> build(String appointmentId) {
    loadAppointment();
    return const AsyncValue.loading();
  }

  /// Load appointment by ID
  Future<void> loadAppointment() async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final appointment = await repository.getAppointment(appointmentId);
      state = AsyncValue.data(appointment);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh appointment
  Future<void> refreshAppointment() async {
    state = const AsyncValue.loading();
    await loadAppointment();
  }
}

/// Provider for today's appointments
@riverpod
class TodaysAppointmentsNotifier extends _$TodaysAppointmentsNotifier {
  @override
  AsyncValue<List<Appointment>> build() {
    loadTodaysAppointments();
    return const AsyncValue.loading();
  }

  /// Load today's appointments
  Future<void> loadTodaysAppointments() async {
    try {
      print('[TodaysAppointmentsNotifier] Loading today\'s appointments...');
      final repository = ref.read(appointmentRepositoryProvider);
      final appointments = await repository.getTodaysAppointments();
      print(
        '[TodaysAppointmentsNotifier] Loaded ${appointments.length} appointments',
      );
      state = AsyncValue.data(appointments);
    } catch (error, stackTrace) {
      print('[TodaysAppointmentsNotifier] Error loading appointments: $error');
      print('[TodaysAppointmentsNotifier] Stack trace: $stackTrace');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh today's appointments
  Future<void> refreshTodaysAppointments() async {
    state = const AsyncValue.loading();
    await loadTodaysAppointments();
  }
}

/// Provider for upcoming appointments
@riverpod
class UpcomingAppointmentsNotifier extends _$UpcomingAppointmentsNotifier {
  @override
  AsyncValue<List<Appointment>> build() {
    loadUpcomingAppointments();
    return const AsyncValue.loading();
  }

  /// Load upcoming appointments (next 7 days)
  Future<void> loadUpcomingAppointments() async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final appointments = await repository.getUpcomingAppointments();
      state = AsyncValue.data(appointments);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh upcoming appointments
  Future<void> refreshUpcomingAppointments() async {
    state = const AsyncValue.loading();
    await loadUpcomingAppointments();
  }
}

/// Provider for appointment statistics
@riverpod
class AppointmentStatsNotifier extends _$AppointmentStatsNotifier {
  @override
  AsyncValue<AppointmentStats> build({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  }) {
    loadStats();
    return const AsyncValue.loading();
  }

  /// Load appointment statistics
  Future<void> loadStats() async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final stats = await repository.getAppointmentStats(
        startDate: startDate,
        endDate: endDate,
        locationId: locationId,
      );
      state = AsyncValue.data(stats);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh statistics
  Future<void> refreshStats() async {
    state = const AsyncValue.loading();
    await loadStats();
  }
}

/// Provider for available time slots
@riverpod
class TimeSlotNotifier extends _$TimeSlotNotifier {
  @override
  AsyncValue<List<TimeSlot>> build({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  }) {
    loadTimeSlots();
    return const AsyncValue.loading();
  }

  /// Load available time slots
  Future<void> loadTimeSlots() async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final slots = await repository.getAvailableTimeSlots(
        date: date,
        serviceId: serviceId,
        locationId: locationId,
        queueId: queueId,
      );
      state = AsyncValue.data(slots);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh time slots
  Future<void> refreshTimeSlots() async {
    state = const AsyncValue.loading();
    await loadTimeSlots();
  }
}

/// Provider for calendar view data
@riverpod
class CalendarViewNotifier extends _$CalendarViewNotifier {
  @override
  AsyncValue<List<CalendarViewData>> build({
    required DateTime startDate,
    required DateTime endDate,
  }) {
    loadCalendarData();
    return const AsyncValue.loading();
  }

  /// Load calendar view data
  Future<void> loadCalendarData() async {
    try {
      final repository = ref.read(appointmentRepositoryProvider);
      final calendarData = await repository.getCalendarViewData(
        startDate: startDate,
        endDate: endDate,
      );
      state = AsyncValue.data(calendarData);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Refresh calendar data
  Future<void> refreshCalendarData() async {
    state = const AsyncValue.loading();
    await loadCalendarData();
  }
}
