import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/service_provider.dart';
import '../widgets/service_card.dart';
import '../widgets/service_search_bar.dart';
import '../widgets/service_filter_chips.dart';
import '../models/service_models.dart';
import '../../../core/routing/app_routes.dart';

class ServicesScreen extends ConsumerStatefulWidget {
  const ServicesScreen({super.key});

  @override
  ConsumerState<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends ConsumerState<ServicesScreen>
    with WidgetsBindingObserver {
  final TextEditingController _searchController = TextEditingController();
  int? _selectedCategoryId;
  bool _showActiveOnly = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // Load services when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(serviceNotifierProvider.notifier).loadServices();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh services when app comes back to foreground
    if (state == AppLifecycleState.resumed) {
      _refreshServices();
    }
  }

  void _onBackPressed() {
    context.go(AppRoutes.dashboard);
  }

  @override
  Widget build(BuildContext context) {
    final serviceState = ref.watch(serviceNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: _onBackPressed,
          icon: const Icon(Icons.arrow_back),
          tooltip: 'Back to Dashboard',
        ),
        title: const Text('Services'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateServiceDialog(context),
            tooltip: 'Add Service',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshServices(),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filters
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Search bar
                ServiceSearchBar(
                  controller: _searchController,
                  onSearch: _onSearch,
                  onClear: _onClearSearch,
                ),
                const SizedBox(height: 12),

                // Filter chips
                ServiceFilterChips(
                  selectedCategoryId: _selectedCategoryId,
                  showActiveOnly: _showActiveOnly,
                  onCategoryChanged: _onCategoryFilterChanged,
                  onActiveFilterChanged: _onActiveFilterChanged,
                ),
              ],
            ),
          ),

          // Services list
          Expanded(child: _buildServicesList(serviceState)),
        ],
      ),
    );
  }

  Widget _buildServicesList(ServiceData serviceState) {
    if (serviceState.isLoading && serviceState.services.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading services...'),
          ],
        ),
      );
    }

    if (serviceState.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading services',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              serviceState.error ?? 'Unknown error occurred',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshServices,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (serviceState.services.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_center_outlined,
              size: 64,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No services found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _searchController.text.isNotEmpty
                  ? 'Try adjusting your search or filters'
                  : 'Create your first service to get started',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showCreateServiceDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Add Service'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshServices,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: serviceState.services.length,
        itemBuilder: (context, index) {
          final service = serviceState.services[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: ServiceCard(
              service: service,
              onTap: () => _onServiceTap(service),
              onEdit: () => _onEditService(service),
              onDelete: () => _onDeleteService(service),
            ),
          );
        },
      ),
    );
  }

  void _onSearch(String query) {
    ref.read(serviceNotifierProvider.notifier).searchServices(query);
  }

  void _onClearSearch() {
    _searchController.clear();
    ref.read(serviceNotifierProvider.notifier).searchServices('');
  }

  void _onCategoryFilterChanged(int? categoryId) {
    setState(() {
      _selectedCategoryId = categoryId;
    });
    ref.read(serviceNotifierProvider.notifier).filterByCategory(categoryId);
  }

  void _onActiveFilterChanged(bool showActiveOnly) {
    setState(() {
      _showActiveOnly = showActiveOnly;
    });
    ref
        .read(serviceNotifierProvider.notifier)
        .loadServices(
          isActive: showActiveOnly ? true : null,
          forceRefresh: true,
        );
  }

  void _onServiceTap(Service service) {
    // Navigate to service details
    context.push('/services/${service.id}');
  }

  void _onEditService(Service service) {
    // Navigate to edit service screen
    context.push('/services/${service.id}/edit');
  }

  Future<void> _onDeleteService(Service service) async {
    final confirmed = await _showDeleteConfirmationDialog(service);
    if (confirmed == true) {
      final success = await ref
          .read(serviceNotifierProvider.notifier)
          .deleteService(service.id);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Service "${service.title}" deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete service "${service.title}"'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<bool?> _showDeleteConfirmationDialog(Service service) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Service'),
            content: Text(
              'Are you sure you want to delete "${service.title}"?\n\n'
              'This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _showCreateServiceDialog(BuildContext context) {
    context.push('/services/create');
  }

  Future<void> _refreshServices() async {
    await ref.read(serviceNotifierProvider.notifier).refresh();
  }
}
