# Dalti Provider Onboarding Wizard Refactoring

## Project Overview
Refactor the existing onboarding wizard to improve user experience, enhance data collection, and align with the latest API requirements. The new wizard will provide a more intuitive flow with better organization of information and support for advanced features.

## Current State Analysis
The existing onboarding wizard has the following structure:
- BusinessProfile step (basic info)
- LocationSetup step (single location)
- ServiceCreation step (basic service info)
- ScheduleSetup step (separate schedule management)
- QueueManagement step (queue with hours)
- Summary step

## New Requirements

### Step 1: Welcome/Introduction
- Engaging welcome screen with "Let's setup your profile in few quick steps"
- Clear explanation of what will be accomplished
- "Start Setup" button to begin the process
- Progress indicator showing 5 steps total

### Step 2: Business Information
Enhanced business profile collection:
- Business name (required)
- Bio/description (required)
- Short name (optional)
- Logo upload/URL (optional)
- Multiple phone types:
  - Fixed line/landline
  - Fax number
  - Mobile number
  - At least one phone number required

### Step 3: Location Management
Comprehensive location setup with integrated opening hours:
- Country selection (default: Algeria)
- City selection (Algerian cities dropdown)
- Timezone (default: Africa/Algiers)
- Business address (required)
- GPS coordinates via current location/map pin (optional)
- Opening hours configuration:
  - Day-by-day schedule
  - Multiple time slots per day
  - Active/inactive days
- "Add Another Location" button for multiple locations support
- Location name and short name fields

### Step 4: Services Management
Enhanced service creation with delivery options:
- Service title (required)
- Duration in minutes (required)
- Price (optional)
- Points required for booking (required, minimum 1)
- Visibility: Private/Public for booking
- Delivery type selection:
  - "At my location" - uses business location
  - "At customer location" - requires region selection
  - "Both" - combination of above
- Region/Wilaya selection for customer location services
- "Add Service" button for multiple services
- Service categorization and color coding

### Step 5: Queue Management
Advanced queue configuration with location linking:
- Queue name/label (required)
- Service selection (multiple services per queue)
- Location linking (select from created locations)
- Opening hours options:
  - Inherit from linked location
  - Custom opening hours for queue
- Queue capacity and management settings
- "Add New Queue" button for multiple queues

### Step 6: Summary & Completion
- Review all configured information
- Edit capabilities for each section
- Final submission to API
- Success confirmation with next steps

## Technical Requirements

### Data Model Updates
- Enhance BusinessProfile model for multiple phone types
- Update Location model to include opening hours
- Enhance Service model for delivery types and regions
- Update Queue model for location linking
- Create OpeningHours model for reusable schedule management

### UI Components
- Create reusable OpeningHours component
- Develop PhoneNumbers input component
- Build DeliveryType selector with conditional region selection
- Create LocationSelector component for queue linking
- Design WelcomeStep component

### API Integration
- Update API transformation to handle new data structure
- Ensure backward compatibility during transition
- Validate all required fields are properly mapped
- Handle multiple locations, services, and queues

### Validation & Error Handling
- Comprehensive form validation for all steps
- Real-time validation feedback
- Cross-step validation (e.g., queues must have valid locations)
- Error recovery and user guidance

## Success Criteria
- Seamless user experience with intuitive flow
- All required data collected according to API schema
- Support for multiple locations, services, and queues
- Backward compatibility with existing data
- Comprehensive validation and error handling
- Mobile-responsive design
- Accessibility compliance

## Technical Constraints
- Must maintain existing API compatibility
- Should not break current user data
- Must work on web platform (Flutter Web)
- Should follow existing design system
- Must integrate with current authentication flow

## Implementation Phases
1. Data model updates and core infrastructure
2. Reusable UI component development
3. Step-by-step refactoring and enhancement
4. Integration testing and API validation
5. User acceptance testing and refinement

## Priority Features
- High: Core step refactoring and data collection
- Medium: Multiple locations and advanced queue management
- Low: Advanced customization and optional features

This refactoring will significantly improve the onboarding experience while maintaining the robust functionality required for the Dalti Provider platform.

## Project Overview
Refactor the existing onboarding wizard to improve user experience, enhance data collection, and align with the latest API requirements. The new wizard will provide a more intuitive flow with better organization of information and support for advanced features.

## Current State Analysis
The existing onboarding wizard has the following structure:
- BusinessProfile step (basic info)
- LocationSetup step (single location)
- ServiceCreation step (basic service info)
- ScheduleSetup step (separate schedule management)
- QueueManagement step (queue with hours)
- Summary step

## New Requirements

### Step 1: Welcome/Introduction
- Engaging welcome screen with "Let's setup your profile in few quick steps"
- Clear explanation of what will be accomplished
- "Start Setup" button to begin the process
- Progress indicator showing 5 steps total

### Step 2: Business Information
Enhanced business profile collection:
- Business name (required)
- Bio/description (required)
- Short name (optional)
- Logo upload/URL (optional)
- Multiple phone types:
  - Fixed line/landline
  - Fax number
  - Mobile number
  - At least one phone number required

### Step 3: Location Management
Comprehensive location setup with integrated opening hours:
- Country selection (default: Algeria)
- City selection (Algerian cities dropdown)
- Timezone (default: Africa/Algiers)
- Business address (required)
- GPS coordinates via current location/map pin (optional)
- Opening hours configuration:
  - Day-by-day schedule
  - Multiple time slots per day
  - Active/inactive days
- "Add Another Location" button for multiple locations support
- Location name and short name fields

### Step 4: Services Management
Enhanced service creation with delivery options:
- Service title (required)
- Duration in minutes (required)
- Price (optional)
- Points required for booking (required, minimum 1)
- Visibility: Private/Public for booking
- Delivery type selection:
  - "At my location" - uses business location
  - "At customer location" - requires region selection
  - "Both" - combination of above
- Region/Wilaya selection for customer location services
- "Add Service" button for multiple services
- Service categorization and color coding

### Step 5: Queue Management
Advanced queue configuration with location linking:
- Queue name/label (required)
- Service selection (multiple services per queue)
- Location linking (select from created locations)
- Opening hours options:
  - Inherit from linked location
  - Custom opening hours for queue
- Queue capacity and management settings
- "Add New Queue" button for multiple queues

### Step 6: Summary & Completion
- Review all configured information
- Edit capabilities for each section
- Final submission to API
- Success confirmation with next steps

## Technical Requirements

### Data Model Updates
- Enhance BusinessProfile model for multiple phone types
- Update Location model to include opening hours
- Enhance Service model for delivery types and regions
- Update Queue model for location linking
- Create OpeningHours model for reusable schedule management

### UI Components
- Create reusable OpeningHours component
- Develop PhoneNumbers input component
- Build DeliveryType selector with conditional region selection
- Create LocationSelector component for queue linking
- Design WelcomeStep component

### API Integration
- Update API transformation to handle new data structure
- Ensure backward compatibility during transition
- Validate all required fields are properly mapped
- Handle multiple locations, services, and queues

### Validation & Error Handling
- Comprehensive form validation for all steps
- Real-time validation feedback
- Cross-step validation (e.g., queues must have valid locations)
- Error recovery and user guidance

## Success Criteria
- Seamless user experience with intuitive flow
- All required data collected according to API schema
- Support for multiple locations, services, and queues
- Backward compatibility with existing data
- Comprehensive validation and error handling
- Mobile-responsive design
- Accessibility compliance

## Technical Constraints
- Must maintain existing API compatibility
- Should not break current user data
- Must work on web platform (Flutter Web)
- Should follow existing design system
- Must integrate with current authentication flow

## Implementation Phases
1. Data model updates and core infrastructure
2. Reusable UI component development
3. Step-by-step refactoring and enhancement
4. Integration testing and API validation
5. User acceptance testing and refinement

## Priority Features
- High: Core step refactoring and data collection
- Medium: Multiple locations and advanced queue management
- Low: Advanced customization and optional features

This refactoring will significantly improve the onboarding experience while maintaining the robust functionality required for the Dalti Provider platform.
