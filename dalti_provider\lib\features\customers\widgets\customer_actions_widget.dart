import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';
import '../providers/customer_provider.dart';
import '../services/customer_service.dart';

/// Widget providing quick actions for customer interactions
class CustomerActionsWidget extends ConsumerWidget {
  final Customer customer;
  final bool showLabels;
  final MainAxisAlignment alignment;

  const CustomerActionsWidget({
    super.key,
    required this.customer,
    this.showLabels = true,
    this.alignment = MainAxisAlignment.spaceEvenly,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerService = ref.watch(customerServiceProvider);

    return Row(
      mainAxisAlignment: alignment,
      children: [
        _buildActionButton(
          context: context,
          icon: Icons.phone,
          label: 'Call',
          color: Theme.of(context).colorScheme.primary,
          onPressed: () => _handleCall(context, customerService),
        ),
        _buildActionButton(
          context: context,
          icon: Icons.email,
          label: 'Email',
          color: Theme.of(context).colorScheme.secondary,
          onPressed: () => _handleEmail(context, customerService),
        ),
        _buildActionButton(
          context: context,
          icon: Icons.message,
          label: 'SMS',
          color: Theme.of(context).colorScheme.tertiary,
          onPressed: () => _handleSMS(context, customerService),
        ),
        _buildActionButton(
          context: context,
          icon: Icons.event,
          label: 'Book',
          color: Theme.of(context).colorScheme.primary,
          onPressed: () => _handleBookAppointment(context),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    if (showLabels) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            backgroundColor: color.withOpacity(0.1),
            radius: 24,
            child: IconButton(
              onPressed: onPressed,
              icon: Icon(icon, color: color),
              iconSize: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else {
      return IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: color),
        iconSize: 20,
        tooltip: label,
      );
    }
  }

  Future<void> _handleCall(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.callCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch phone app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _handleEmail(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.emailCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch email app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _handleSMS(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.smsCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch SMS app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _handleBookAppointment(BuildContext context) {
    // TODO: Navigate to appointment booking screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Appointment booking feature coming soon')),
    );
  }
}

/// Compact version of customer actions for list items
class CustomerActionsCompact extends ConsumerWidget {
  final Customer customer;

  const CustomerActionsCompact({super.key, required this.customer});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerService = ref.watch(customerServiceProvider);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: () => _handleCall(context, customerService),
          icon: const Icon(Icons.phone, size: 18),
          iconSize: 18,
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          tooltip: 'Call ${customer.firstName}',
        ),
        IconButton(
          onPressed: () => _handleEmail(context, customerService),
          icon: const Icon(Icons.email, size: 18),
          iconSize: 18,
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          tooltip: 'Email ${customer.firstName}',
        ),
        IconButton(
          onPressed: () => _handleSMS(context, customerService),
          icon: const Icon(Icons.message, size: 18),
          iconSize: 18,
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          tooltip: 'SMS ${customer.firstName}',
        ),
      ],
    );
  }

  Future<void> _handleCall(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.callCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch phone app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _handleEmail(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.emailCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch email app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _handleSMS(
    BuildContext context,
    CustomerService customerService,
  ) async {
    final success = await customerService.smsCustomer(customer);
    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not launch SMS app'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
}

/// Customer action sheet for more options
class CustomerActionSheet extends ConsumerWidget {
  final Customer customer;

  const CustomerActionSheet({super.key, required this.customer});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final customerService = ref.watch(customerServiceProvider);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      Theme.of(context).colorScheme.primaryContainer,
                  child: Text(
                    customerService.getInitials(customer),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customerService.getDisplayName(customer),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        customer.email ?? 'No email',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Actions
          _buildActionTile(
            context: context,
            icon: Icons.phone,
            title: 'Call Customer',
            subtitle:
                customer.phoneNumber != null
                    ? '+213 ${customer.phoneNumber}'
                    : 'No phone number',
            onTap: () async {
              Navigator.of(context).pop();
              await customerService.callCustomer(customer);
            },
          ),
          _buildActionTile(
            context: context,
            icon: Icons.email,
            title: 'Send Email',
            subtitle: customer.email ?? 'No email',
            onTap: () async {
              Navigator.of(context).pop();
              await customerService.emailCustomer(customer);
            },
          ),
          _buildActionTile(
            context: context,
            icon: Icons.message,
            title: 'Send SMS',
            subtitle:
                customer.phoneNumber != null
                    ? '+213 ${customer.phoneNumber}'
                    : 'No phone number',
            onTap: () async {
              Navigator.of(context).pop();
              await customerService.smsCustomer(customer);
            },
          ),
          _buildActionTile(
            context: context,
            icon: Icons.event,
            title: 'Book Appointment',
            subtitle: 'Schedule a new appointment',
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Navigate to appointment booking
            },
          ),
          _buildActionTile(
            context: context,
            icon: Icons.edit,
            title: 'Edit Customer',
            subtitle: 'Update customer information',
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Navigate to edit customer
            },
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.onPrimaryContainer,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
    );
  }
}
