import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/queue_provider.dart';
import '../models/queue_models.dart';
import '../widgets/queue_card.dart';
import '../widgets/queue_location_section.dart';
import '../../locations/providers/location_provider.dart';
import '../../../core/routing/app_routes.dart';

class QueuesScreen extends ConsumerStatefulWidget {
  const QueuesScreen({super.key});

  @override
  ConsumerState<QueuesScreen> createState() => _QueuesScreenState();
}

class _QueuesScreenState extends ConsumerState<QueuesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int? _selectedLocationId;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
      ref.read(queueNotifierProvider.notifier).loadQueues();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onBackPressed() {
    context.go(AppRoutes.dashboard);
  }

  @override
  Widget build(BuildContext context) {
    final queueState = ref.watch(queueNotifierProvider);
    final locationState = ref.watch(locationNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Queue Management'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _onBackPressed,
          tooltip: 'Back',
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'By Location', icon: Icon(Icons.location_on)),
            Tab(text: 'All Queues', icon: Icon(Icons.list)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(queueNotifierProvider.notifier).refreshQueues();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Location filter (only for "All Queues" tab)
          if (_tabController.index == 1 && locationState.locations.isNotEmpty)
            _buildLocationFilter(),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildLocationView(queueState, locationState),
                _buildAllQueuesView(queueState),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddQueueDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildLocationFilter() {
    final locationState = ref.watch(locationNotifierProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      child: DropdownButtonFormField<int?>(
        value: _selectedLocationId,
        decoration: const InputDecoration(
          labelText: 'Filter by Location',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.location_on),
        ),
        items: [
          const DropdownMenuItem<int?>(
            value: null,
            child: Text('All Locations'),
          ),
          ...locationState.locations.map((location) {
            return DropdownMenuItem<int?>(
              value: location.id,
              child: Text(location.name),
            );
          }),
        ],
        onChanged: (value) {
          setState(() {
            _selectedLocationId = value;
          });
          _filterQueues();
        },
      ),
    );
  }

  Widget _buildLocationView(QueueData queueState, locationState) {
    if (queueState.state == QueueState.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (queueState.state == QueueState.error) {
      return _buildErrorState(queueState.error);
    }

    final locations = locationState.locations;
    if (locations.isEmpty) {
      return _buildEmptyLocationsState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: locations.length,
      itemBuilder: (context, index) {
        final location = locations[index];
        final locationQueues = queueState.queuesByLocation[location.id] ?? [];

        return QueueLocationSection(
          location: location,
          queues: locationQueues,
          onQueueTap: _onQueueTap,
          onAddQueue: () => _showAddQueueDialog(locationId: location.id),
          onEditQueue: _editQueue,
          onDeleteQueue: _deleteQueue,
          onToggleStatus: _toggleQueueStatus,
        );
      },
    );
  }

  Widget _buildAllQueuesView(QueueData queueState) {
    if (queueState.state == QueueState.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (queueState.state == QueueState.error) {
      return _buildErrorState(queueState.error);
    }

    final filteredQueues = _getFilteredQueues(queueState.queues);

    if (filteredQueues.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredQueues.length,
      itemBuilder: (context, index) {
        final queue = filteredQueues[index];

        return QueueCard(
          queue: queue,
          onTap: () => _onQueueTap(queue),
          onEdit: () => _editQueue(queue),
          onDelete: () => _deleteQueue(queue),
          onToggleStatus: () => _toggleQueueStatus(queue),
        );
      },
    );
  }

  Widget _buildErrorState(String? error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          Text(
            'Error loading queues',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.read(queueNotifierProvider.notifier).refreshQueues();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyLocationsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.location_off, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No Locations Found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'Create locations first to manage queues.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => context.push('/locations'),
            icon: const Icon(Icons.add_location),
            label: const Text('Add Location'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.queue, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'No Queues Found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedLocationId != null
                ? 'No queues found for the selected location.'
                : 'Create your first queue to get started.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddQueueDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Queue'),
          ),
        ],
      ),
    );
  }

  List<Queue> _getFilteredQueues(List<Queue> queues) {
    var filtered = queues;

    // Filter by location
    if (_selectedLocationId != null) {
      filtered =
          filtered.where((q) => q.locationId == _selectedLocationId).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      filtered =
          filtered.where((q) {
            return q.title.toLowerCase().contains(_searchQuery.toLowerCase());
          }).toList();
    }

    return filtered;
  }

  void _filterQueues() {
    ref
        .read(queueNotifierProvider.notifier)
        .loadQueues(locationId: _selectedLocationId, forceRefresh: true);
  }

  void _onQueueTap(Queue queue) {
    // Navigate to queue details or edit
    _editQueue(queue);
  }

  void _showAddQueueDialog({int? locationId}) {
    context.push(
      '/queues/add${locationId != null ? '?locationId=$locationId' : ''}',
    );
  }

  Future<void> _editQueue(Queue queue) async {
    final shouldRefresh = await context.push<bool>(
      AppRoutes.editQueue.replaceAll(':id', queue.id.toString()),
      extra: {'queueId': queue.id, 'initialLocationId': queue.locationId},
    );

    // Refresh queues if form indicates changes were made
    if (shouldRefresh == true) {
      await ref
          .read(queueNotifierProvider.notifier)
          .loadQueues(forceRefresh: true);
    }
  }

  void _deleteQueue(Queue queue) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Queue'),
            content: Text(
              'Are you sure you want to delete the queue "${queue.title}"?\n\nThis action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref
                      .read(queueNotifierProvider.notifier)
                      .deleteQueue(queue.id);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _toggleQueueStatus(Queue queue) {
    ref
        .read(queueNotifierProvider.notifier)
        .toggleQueueStatus(queue.id, !queue.isActive);
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Queues'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Enter queue name...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _searchQuery = '';
                  });
                  Navigator.of(context).pop();
                },
                child: const Text('Clear'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }
}
