import 'package:geolocator/geolocator.dart';

/// Service for handling device location operations
class LocationService {
  /// Get current device location
  static Future<LocationResult> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationResult.error('Location services are disabled. Please enable location services in your device settings.');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return LocationResult.error('Location permissions are denied. Please grant location permission to use this feature.');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationResult.error('Location permissions are permanently denied. Please enable location permission in your device settings.');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationResult.success(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
      );
    } catch (e) {
      return LocationResult.error('Failed to get current location: ${e.toString()}');
    }
  }

  /// Check if location permissions are granted
  static Future<bool> hasLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  /// Request location permissions
  static Future<bool> requestLocationPermission() async {
    LocationPermission permission = await Geolocator.checkPermission();
    
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }
    
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  /// Open device location settings
  static Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  /// Get distance between two points in meters
  static double getDistanceBetween({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  /// Format coordinates for display
  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  /// Validate latitude value
  static bool isValidLatitude(double latitude) {
    return latitude >= -90.0 && latitude <= 90.0;
  }

  /// Validate longitude value
  static bool isValidLongitude(double longitude) {
    return longitude >= -180.0 && longitude <= 180.0;
  }
}

/// Result class for location operations
class LocationResult {
  final bool isSuccess;
  final double? latitude;
  final double? longitude;
  final double? accuracy;
  final String? error;

  const LocationResult._({
    required this.isSuccess,
    this.latitude,
    this.longitude,
    this.accuracy,
    this.error,
  });

  factory LocationResult.success({
    required double latitude,
    required double longitude,
    double? accuracy,
  }) {
    return LocationResult._(
      isSuccess: true,
      latitude: latitude,
      longitude: longitude,
      accuracy: accuracy,
    );
  }

  factory LocationResult.error(String error) {
    return LocationResult._(
      isSuccess: false,
      error: error,
    );
  }

  /// Get formatted coordinates string
  String get formattedCoordinates {
    if (latitude != null && longitude != null) {
      return LocationService.formatCoordinates(latitude!, longitude!);
    }
    return '';
  }

  /// Get accuracy description
  String get accuracyDescription {
    if (accuracy == null) return '';
    
    if (accuracy! <= 5) {
      return 'Very High Accuracy (±${accuracy!.toStringAsFixed(1)}m)';
    } else if (accuracy! <= 10) {
      return 'High Accuracy (±${accuracy!.toStringAsFixed(1)}m)';
    } else if (accuracy! <= 50) {
      return 'Good Accuracy (±${accuracy!.toStringAsFixed(1)}m)';
    } else {
      return 'Low Accuracy (±${accuracy!.toStringAsFixed(1)}m)';
    }
  }
}
