import 'package:dio/dio.dart';
import 'http_client.dart';
import '../auth/jwt_service.dart';
import '../../features/auth/models/auth_models.dart';
import '../../features/auth/models/provider_category.dart';

/// Base API service class
abstract class ApiService {
  final HttpClient httpClient;

  ApiService(this.httpClient);

  /// Handle API response and extract data
  T handleResponse<T>(
    Response<dynamic> response,
    T Function(dynamic) fromJson,
  ) {
    final data = response.data;

    if (data == null) {
      throw Exception('No data received from server');
    }

    return from<PERSON>son(data);
  }

  /// Handle API response for lists
  List<T> handleListResponse<T>(
    Response<dynamic> response,
    T Function(dynamic) fromJson,
  ) {
    final data = response.data;

    if (data == null) {
      throw Exception('No data received from server');
    }

    if (data is List) {
      return data.map((item) => fromJson(item)).toList();
    }

    // Handle paginated responses
    if (data is Map<String, dynamic>) {
      final items = data['data'] ?? data['items'] ?? data['results'];
      if (items is List) {
        return items.map((item) => fromJson(item)).toList();
      }
    }

    throw Exception('Invalid response format for list data');
  }
}

/// Example auth API service
class AuthApiService extends ApiService {
  final JwtService? _jwtService;

  AuthApiService(super.httpClient, [this._jwtService]);

  /// Provider login with identifier and password
  Future<AuthResponse> login(LoginRequest request) async {
    try {
      print('[AuthApiService] === STARTING LOGIN REQUEST ===');
      print(
        '[AuthApiService] Attempting provider login for: ${request.identifier}',
      );
      print('[AuthApiService] Request data: ${request.toJson()}');
      print(
        '[AuthApiService] Making POST request to: /api/auth/provider/login',
      );

      final response = await httpClient.post(
        '/api/auth/provider/login',
        data: request.toJson(),
      );

      print('[AuthApiService] === LOGIN RESPONSE RECEIVED ===');
      print('[AuthApiService] Login response status: ${response.statusCode}');
      print('[AuthApiService] Login response data: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Check if response contains sessionId (successful login)
        if (responseData.containsKey('sessionId') &&
            responseData['sessionId'] != null) {
          // Ensure success field is set
          responseData['success'] = true;
          if (!responseData.containsKey('message')) {
            responseData['message'] = 'Login successful';
          }

          final authResponse = AuthResponse.fromJson(responseData);

          // Store JWT token if service is available and login successful
          if (_jwtService != null &&
              authResponse.success &&
              authResponse.accessToken != null) {
            // Convert to format expected by JWT service
            final tokenData = {
              'access_token': authResponse.accessToken, // This is the sessionId
              'refresh_token': authResponse.refreshToken,
              'expires_in': 3600, // Default 1 hour if not provided
            };
            await _jwtService.storeTokenFromResponse(tokenData);
          }

          print('[AuthApiService] Login successful for: ${request.identifier}');
          return authResponse;
        } else {
          // Response doesn't contain sessionId, treat as error
          print('[AuthApiService] Login failed: No sessionId in response');
          return const AuthResponse(
            success: false,
            message: 'Invalid credentials. Please try again.',
          );
        }
      } else {
        print(
          '[AuthApiService] Login failed with status: ${response.statusCode}',
        );
        return const AuthResponse(
          success: false,
          message: 'Invalid credentials. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] === LOGIN ERROR ===');
      print('[AuthApiService] Login error type: ${e.runtimeType}');
      print('[AuthApiService] Login error details: $e');

      // Handle specific error types
      String errorMessage = 'Login failed. Please try again.';
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('cors') ||
          errorString.contains('cross-origin')) {
        errorMessage =
            'CORS error: API server needs to allow requests from localhost';
        print('[AuthApiService] 🚨 CORS ERROR DETECTED in login!');
      } else if (errorString.contains('401') || errorString.contains('400')) {
        errorMessage = 'Invalid identifier or password.';
      } else if (errorString.contains('network') ||
          errorString.contains('connection')) {
        errorMessage = 'Network error. Please check your connection.';
        print('[AuthApiService] 🚨 NETWORK ERROR DETECTED in login!');
      } else if (errorString.contains('timeout')) {
        errorMessage = 'Request timeout. API server is not responding.';
        print('[AuthApiService] 🚨 TIMEOUT ERROR DETECTED in login!');
      } else if (errorString.contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }

      return AuthResponse(success: false, message: errorMessage);
    }
  }

  /// Request email OTP for provider registration
  Future<EmailOtpResponse> requestEmailOtp(
    EmailOtpRequest request, {
    required int providerCategoryId,
    required String businessName,
    required String phone,
  }) async {
    try {
      print('[AuthApiService] Requesting email OTP for: ${request.email}');

      // Build the complete payload as expected by the API
      final payload = {
        ...request.toJson(),
        'providerCategoryId': providerCategoryId,
        'businessName': businessName,
        'phone': phone,
      };

      final response = await httpClient.post(
        '/api/auth/request-email-otp',
        data: payload,
      );

      print(
        '[AuthApiService] Email OTP response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        print(
          '[AuthApiService] Email OTP sent successfully for: ${request.email}',
        );
        return const EmailOtpResponse(
          success: true,
          message: 'OTP sent successfully. Please check your email.',
        );
      } else {
        print(
          '[AuthApiService] Email OTP failed with status: ${response.statusCode}',
        );
        return const EmailOtpResponse(
          success: false,
          message: 'Failed to send OTP. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Email OTP error: $e');

      // Handle specific error types
      String errorMessage = 'Failed to send OTP. Please try again.';
      if (e.toString().contains('400')) {
        errorMessage = 'Invalid email or registration data.';
      } else if (e.toString().contains('409')) {
        errorMessage = 'Email already exists. Please use a different email.';
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }

      return EmailOtpResponse(success: false, message: errorMessage);
    }
  }

  /// Complete provider registration with OTP verification
  Future<AuthResponse> completeProviderRegistration(
    ProviderRegistrationRequest request,
  ) async {
    try {
      print(
        '[AuthApiService] Completing provider registration for: ${request.email}',
      );

      final response = await httpClient.post(
        '/api/auth/provider/verify-otp-register',
        data: request.toJson(),
      );

      print(
        '[AuthApiService] Provider registration response status: ${response.statusCode}',
      );

      if ((response.statusCode == 200 || response.statusCode == 201) &&
          response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Check if response contains sessionId (successful registration)
        if (responseData.containsKey('sessionId') &&
            responseData['sessionId'] != null) {
          // Ensure success field is set
          responseData['success'] = true;
          if (!responseData.containsKey('message')) {
            responseData['message'] = 'Registration completed successfully.';
          }

          final authResponse = AuthResponse.fromJson(responseData);

          // Store JWT token if service is available and registration successful
          if (_jwtService != null &&
              authResponse.success &&
              authResponse.accessToken != null) {
            // Convert to format expected by JWT service
            final tokenData = {
              'access_token': authResponse.accessToken, // This is the sessionId
              'refresh_token': authResponse.refreshToken,
              'expires_in': 3600, // Default 1 hour if not provided
            };
            await _jwtService.storeTokenFromResponse(tokenData);
          }

          print(
            '[AuthApiService] Provider registration successful for: ${request.email}',
          );
          return authResponse;
        } else {
          // Response doesn't contain sessionId, treat as error
          print(
            '[AuthApiService] Provider registration failed: No sessionId in response',
          );
          return const AuthResponse(
            success: false,
            message: 'Registration failed. Please try again.',
          );
        }
      } else {
        print(
          '[AuthApiService] Provider registration failed with status: ${response.statusCode}',
        );
        return const AuthResponse(
          success: false,
          message: 'Invalid OTP or registration failed. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Provider registration error: $e');

      // Handle specific error types
      String errorMessage = 'Registration failed. Please try again.';
      if (e.toString().contains('400')) {
        errorMessage = 'Invalid OTP or registration data.';
      } else if (e.toString().contains('410')) {
        errorMessage = 'OTP has expired. Please request a new one.';
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }

      return AuthResponse(success: false, message: errorMessage);
    }
  }

  // Resend OTP functionality is handled by calling requestEmailOtp again

  /// Refresh token
  Future<AuthResponse> refreshToken({required String refreshToken}) async {
    try {
      print('[AuthApiService] Attempting token refresh');

      final response = await httpClient.post(
        '/api/auth/refresh-token',
        data: {'refreshToken': refreshToken},
      );

      print(
        '[AuthApiService] Token refresh response status: ${response.statusCode}',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Ensure success field is set
        responseData['success'] = true;
        if (!responseData.containsKey('message')) {
          responseData['message'] = 'Token refreshed successfully';
        }

        final authResponse = AuthResponse.fromJson(responseData);

        // Store new JWT token if service is available
        if (_jwtService != null &&
            authResponse.success &&
            authResponse.accessToken != null) {
          // Convert to format expected by JWT service
          final tokenData = {
            'access_token': authResponse.accessToken,
            'refresh_token': authResponse.refreshToken,
            'expires_in': 3600, // Default 1 hour if not provided
          };
          await _jwtService.storeTokenFromResponse(tokenData);
        }

        print('[AuthApiService] Token refresh successful');
        return authResponse;
      } else {
        print(
          '[AuthApiService] Token refresh failed with status: ${response.statusCode}',
        );
        return const AuthResponse(
          success: false,
          message: 'Token refresh failed. Please login again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Token refresh error: $e');

      // Handle specific error types
      String errorMessage = 'Token refresh failed. Please login again.';
      if (e.toString().contains('401') || e.toString().contains('403')) {
        errorMessage = 'Session expired. Please login again.';
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      }

      return AuthResponse(success: false, message: errorMessage);
    }
  }

  /// Logout
  Future<void> logout() async {
    await httpClient.post('/auth/logout');

    // Clear JWT token if service is available
    if (_jwtService != null) {
      await _jwtService.clearToken();
    }
  }

  /// Get current user profile
  Future<Map<String, dynamic>> getProfile() async {
    final response = await httpClient.get('/auth/profile');
    return handleResponse(response, (data) => data as Map<String, dynamic>);
  }

  /// Fetch provider categories from API
  Future<List<ProviderCategory>> getProviderCategories() async {
    try {
      print('[AuthApiService] Fetching provider categories from API');

      final response = await httpClient.get('/api/auth/provider/categories');

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        if (responseData is List) {
          // Parse categories from API response
          final categories =
              responseData
                  .map(
                    (categoryJson) => ProviderCategory.fromJson(
                      categoryJson as Map<String, dynamic>,
                    ),
                  )
                  .toList();

          print(
            '[AuthApiService] Successfully fetched ${categories.length} categories from API',
          );
          return categories;
        } else {
          print('[AuthApiService] Invalid categories response format');
          return [];
        }
      } else {
        print(
          '[AuthApiService] Failed to fetch categories with status: ${response.statusCode}',
        );
        return [];
      }
    } catch (e) {
      print('[AuthApiService] Error fetching categories: $e');
      return [];
    }
  }

  /// Request password reset OTP
  Future<PasswordResetResponse> requestPasswordResetOtp(
    PasswordResetRequest request,
  ) async {
    try {
      print(
        '[AuthApiService] Requesting password reset OTP for: ${request.email}',
      );

      final response = await httpClient.post(
        '/api/auth/request-password-reset-otp',
        data: request.toJson(),
      );

      print(
        '[AuthApiService] Password reset OTP response status: ${response.statusCode}',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Ensure success field is set
        responseData['success'] = true;

        final passwordResetResponse = PasswordResetResponse.fromJson(
          responseData,
        );
        print('[AuthApiService] Password reset OTP request successful');
        return passwordResetResponse;
      } else {
        print(
          '[AuthApiService] Password reset OTP request failed with status: ${response.statusCode}',
        );
        return const PasswordResetResponse(
          success: false,
          message: 'Failed to send password reset OTP. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Password reset OTP request error: $e');

      // Handle specific error types
      String errorMessage =
          'Failed to send password reset OTP. Please try again.';
      if (e.toString().contains('404')) {
        errorMessage = 'No account found with this email address.';
      } else if (e.toString().contains('400')) {
        errorMessage =
            'This account does not have password login enabled. Please contact support.';
      } else if (e.toString().contains('429')) {
        errorMessage =
            'Please wait before requesting another password reset OTP.';
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }

      return PasswordResetResponse(success: false, message: errorMessage);
    }
  }

  /// Verify password reset OTP and get reset token
  Future<PasswordResetOtpVerificationResponse> verifyPasswordResetOtp(
    PasswordResetOtpVerificationRequest request,
  ) async {
    try {
      print(
        '[AuthApiService] Verifying password reset OTP for: ${request.email}',
      );

      final response = await httpClient.post(
        '/api/auth/verify-password-reset-otp',
        data: request.toJson(),
      );

      print(
        '[AuthApiService] Password reset OTP verification response status: ${response.statusCode}',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Ensure success field is set
        responseData['success'] = true;

        final verificationResponse =
            PasswordResetOtpVerificationResponse.fromJson(responseData);
        print('[AuthApiService] Password reset OTP verification successful');
        return verificationResponse;
      } else {
        print(
          '[AuthApiService] Password reset OTP verification failed with status: ${response.statusCode}',
        );
        return const PasswordResetOtpVerificationResponse(
          success: false,
          message: 'Invalid OTP. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Password reset OTP verification error: $e');

      // Handle specific error types
      String errorMessage = 'Invalid OTP. Please try again.';
      if (e.toString().contains('400')) {
        if (e.toString().contains('expired')) {
          errorMessage =
              'OTP has expired. Please request a new password reset.';
        } else {
          errorMessage = 'Invalid OTP.';
        }
      } else if (e.toString().contains('404')) {
        errorMessage = 'No account found with this email address.';
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      }

      return PasswordResetOtpVerificationResponse(
        success: false,
        message: errorMessage,
      );
    }
  }

  /// Reset password with reset token
  Future<PasswordResetConfirmResponse> resetPassword(
    PasswordResetConfirmRequest request,
  ) async {
    try {
      print('[AuthApiService] Resetting password with token');

      final response = await httpClient.post(
        '/api/auth/reset-password',
        data: request.toJson(),
      );

      print(
        '[AuthApiService] Password reset response status: ${response.statusCode}',
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Ensure success field is set
        responseData['success'] = true;

        final resetResponse = PasswordResetConfirmResponse.fromJson(
          responseData,
        );
        print('[AuthApiService] Password reset successful');
        return resetResponse;
      } else {
        print(
          '[AuthApiService] Password reset failed with status: ${response.statusCode}',
        );
        return const PasswordResetConfirmResponse(
          success: false,
          message: 'Failed to reset password. Please try again.',
        );
      }
    } catch (e) {
      print('[AuthApiService] Password reset error: $e');

      // Handle specific error types
      String errorMessage = 'Failed to reset password. Please try again.';
      if (e.toString().contains('400')) {
        if (e.toString().contains('expired')) {
          errorMessage =
              'Reset token has expired. Please request a new password reset.';
        } else {
          errorMessage = 'Invalid or expired reset token.';
        }
      } else if (e.toString().contains('Network') ||
          e.toString().contains('Connection')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Failed to reset password. Please try again.';
      }

      return PasswordResetConfirmResponse(
        success: false,
        message: errorMessage,
      );
    }
  }
}

/// Example test API service for connectivity testing
class TestApiService extends ApiService {
  TestApiService(super.httpClient);

  /// Test API connectivity
  Future<Map<String, dynamic>> testConnection() async {
    final response = await httpClient.get('/health');
    return handleResponse(response, (data) => data as Map<String, dynamic>);
  }

  /// Test authenticated endpoint
  Future<Map<String, dynamic>> testAuth() async {
    final response = await httpClient.get('/auth/test');
    return handleResponse(response, (data) => data as Map<String, dynamic>);
  }
}
