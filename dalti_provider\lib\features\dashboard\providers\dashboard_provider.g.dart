// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'dashboard_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dashboardApiServiceHash() =>
    r'9c5da3a9207fc94060a7861e318d449937c5ab1b';

/// Provider for DashboardApiService
///
/// Copied from [dashboardApiService].
@ProviderFor(dashboardApiService)
final dashboardApiServiceProvider =
    AutoDisposeProvider<DashboardApiService>.internal(
  dashboardApiService,
  name: r'dashboardApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dashboardApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DashboardApiServiceRef = AutoDisposeProviderRef<DashboardApiService>;
String _$dashboardRepositoryHash() =>
    r'ddb8bdf74093d373ad16c076f03439adbd715704';

/// Provider for DashboardRepository
///
/// Copied from [dashboardRepository].
@ProviderFor(dashboardRepository)
final dashboardRepositoryProvider =
    AutoDisposeProvider<DashboardRepository>.internal(
  dashboardRepository,
  name: r'dashboardRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dashboardRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DashboardRepositoryRef = AutoDisposeProviderRef<DashboardRepository>;
String _$dashboardNotifierHash() => r'ef9f98af43e1d71665d965e9412cf364f27b2dc8';

/// Dashboard provider for managing dashboard state and auto-refresh
///
/// Copied from [DashboardNotifier].
@ProviderFor(DashboardNotifier)
final dashboardNotifierProvider =
    AutoDisposeNotifierProvider<DashboardNotifier, DashboardState>.internal(
  DashboardNotifier.new,
  name: r'dashboardNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$dashboardNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DashboardNotifier = AutoDisposeNotifier<DashboardState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
