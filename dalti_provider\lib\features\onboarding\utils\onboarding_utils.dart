import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/onboarding_provider.dart';
import '../models/onboarding_models.dart';

/// Utility functions for onboarding management
class OnboardingUtils {
  /// Check if user needs onboarding
  static Future<bool> needsOnboarding(WidgetRef ref) async {
    try {
      return await ref.read(needsOnboardingProvider.future);
    } catch (e) {
      print('[OnboardingUtils] Error checking onboarding status: $e');
      return false; // Assume onboarding is not needed on error
    }
  }

  /// Get onboarding progress percentage
  static double getProgressPercentage(OnboardingData? data) {
    if (data == null) return 0.0;
    return data.completionPercentage;
  }

  /// Get current step display name
  static String getCurrentStepName(OnboardingData? data) {
    if (data == null) return 'Not Started';
    return data.currentStep.title;
  }

  /// Check if onboarding is completed
  static bool isCompleted(OnboardingData? data) {
    return data?.isCompleted ?? false;
  }

  /// Get next incomplete step
  static OnboardingStep? getNextIncompleteStep(OnboardingData? data) {
    return data?.nextIncompleteStep;
  }

  /// Validate onboarding data completeness
  static Map<String, bool> validateCompleteness(OnboardingData? data) {
    if (data == null) {
      return {
        'businessProfile': false,
        'location': false,
        'services': false,
        'queues': false,
        'overall': false,
      };
    }

    return {
      'businessProfile': data.businessProfile != null,
      'location': data.primaryLocation != null,
      'services': data.services.isNotEmpty,
      'queues': data.queuesWithHours.isNotEmpty,
      'overall': data.canComplete,
    };
  }

  /// Get onboarding summary for display
  static Map<String, dynamic> getOnboardingSummary(OnboardingData? data) {
    if (data == null) {
      return {
        'status': 'Not Started',
        'progress': 0.0,
        'currentStep': 'Business Profile',
        'completedSteps': 0,
        'totalSteps': OnboardingStep.totalSteps,
        'canComplete': false,
      };
    }

    final completedSteps = OnboardingStep.values
        .where((step) => step != OnboardingStep.completed && data.isStepCompleted(step))
        .length;

    return {
      'status': data.isCompleted ? 'Completed' : 'In Progress',
      'progress': data.completionPercentage,
      'currentStep': data.currentStep.title,
      'completedSteps': completedSteps,
      'totalSteps': OnboardingStep.totalSteps,
      'canComplete': data.canComplete,
      'startedAt': data.startedAt?.toIso8601String(),
      'completedAt': data.completedAt?.toIso8601String(),
      'duration': data.duration?.inMinutes,
    };
  }

  /// Reset onboarding for testing or re-setup
  static Future<void> resetOnboarding(WidgetRef ref) async {
    try {
      await ref.read(onboardingNotifierProvider.notifier).resetOnboarding();
      print('[OnboardingUtils] Onboarding reset successfully');
    } catch (e) {
      print('[OnboardingUtils] Error resetting onboarding: $e');
      rethrow;
    }
  }

  /// Start onboarding process
  static Future<void> startOnboarding(WidgetRef ref) async {
    try {
      await ref.read(onboardingNotifierProvider.notifier).startOnboarding();
      print('[OnboardingUtils] Onboarding started successfully');
    } catch (e) {
      print('[OnboardingUtils] Error starting onboarding: $e');
      rethrow;
    }
  }

  /// Get step completion status
  static Map<OnboardingStep, bool> getStepCompletionStatus(OnboardingData? data) {
    final status = <OnboardingStep, bool>{};
    
    for (final step in OnboardingStep.values) {
      if (step == OnboardingStep.completed) continue;
      status[step] = data?.isStepCompleted(step) ?? false;
    }
    
    return status;
  }

  /// Get missing requirements for completion
  static List<String> getMissingRequirements(OnboardingData? data) {
    final missing = <String>[];
    
    if (data == null) {
      return ['Complete business setup to get started'];
    }
    
    if (data.businessProfile == null) {
      missing.add('Business profile information');
    }
    
    if (data.primaryLocation == null) {
      missing.add('Primary business location');
    }
    
    if (data.services.isEmpty) {
      missing.add('At least one service');
    }
    
    if (data.queuesWithHours.isEmpty) {
      missing.add('At least one queue with opening hours');
    }
    
    return missing;
  }

  /// Format duration for display
  static String formatDuration(Duration? duration) {
    if (duration == null) return 'Unknown';
    
    final minutes = duration.inMinutes;
    if (minutes < 60) {
      return '${minutes}m';
    }
    
    final hours = duration.inHours;
    final remainingMinutes = minutes % 60;
    
    if (remainingMinutes == 0) {
      return '${hours}h';
    }
    
    return '${hours}h ${remainingMinutes}m';
  }

  /// Get step icon
  static String getStepIcon(OnboardingStep step) {
    return step.iconName;
  }

  /// Check if step is accessible
  static bool isStepAccessible(OnboardingStep step, OnboardingData? data) {
    if (data == null) return step == OnboardingStep.businessProfile;
    
    // Business profile is always accessible
    if (step == OnboardingStep.businessProfile) return true;
    
    // Other steps require previous steps to be completed
    final stepIndex = step.index;
    for (int i = 0; i < stepIndex; i++) {
      final previousStep = OnboardingStep.values[i];
      if (previousStep == OnboardingStep.completed) continue;
      if (!data.isStepCompleted(previousStep)) {
        return false;
      }
    }
    
    return true;
  }
}
