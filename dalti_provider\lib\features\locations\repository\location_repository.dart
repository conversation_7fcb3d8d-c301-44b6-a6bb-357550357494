import '../models/location_models.dart';

/// Abstract repository interface for location management operations
abstract class LocationRepository {
  /// Get all locations for the current provider
  /// 
  /// [isActive] - Filter by active status (optional)
  /// [search] - Search term for location name or address (optional)
  /// 
  /// Returns list of locations
  Future<List<Location>> getLocations({
    bool? isActive,
    String? search,
  });

  /// Get a specific location by ID
  /// 
  /// [id] - Location ID
  /// 
  /// Returns location details
  Future<Location> getLocationById(int id);

  /// Create a new location
  /// 
  /// [request] - Location creation request with all required details
  /// 
  /// Returns the created location
  Future<Location> createLocation(CreateLocationRequest request);

  /// Update an existing location
  /// 
  /// [id] - Location ID to update
  /// [request] - Update request with modified fields
  /// 
  /// Returns the updated location
  Future<Location> updateLocation(int id, UpdateLocationRequest request);

  /// Delete a location
  /// 
  /// [id] - Location ID to delete
  /// 
  /// Returns success status
  /// Throws exception if location has dependencies (queues, schedules, etc.)
  Future<bool> deleteLocation(int id);

  /// Check if a location can be deleted
  /// 
  /// [id] - Location ID to check
  /// 
  /// Returns true if location can be safely deleted
  Future<bool> canDeleteLocation(int id);

  /// Get locations with their associated queues and services
  /// 
  /// Returns locations with extended information for management screens
  Future<List<Location>> getLocationsWithDetails();

  /// Search locations by name or address
  /// 
  /// [query] - Search query
  /// [limit] - Maximum number of results (optional)
  /// 
  /// Returns matching locations
  Future<List<Location>> searchLocations(String query, {int? limit});

  /// Get location statistics
  /// 
  /// [id] - Location ID
  /// 
  /// Returns statistics like number of queues, services, appointments, etc.
  Future<Map<String, dynamic>> getLocationStats(int id);
}
