import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';
import '../providers/onboarding_provider.dart';

/// Summary step showing all collected information
class SummaryStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const SummaryStep({
    super.key,
    required this.controller,
  });

  @override
  ConsumerState<SummaryStep> createState() => _SummaryStepState();
}

class _SummaryStepState extends ConsumerState<SummaryStep> {
  @override
  void initState() {
    super.initState();
    // Mark summary step as accessible when we reach it
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller.markStepCompleted(OnboardingStep.summary, true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final data = onboardingState.data;

    if (data == null) {
      return const WizardLoadingState(message: 'Loading summary...');
    }

    return WizardCardPage(
      step: OnboardingStep.summary,
      title: 'Review Your Setup',
      subtitle: 'Please review your business information before completing the setup.',
      cards: [
        // Business Profile Summary
        if (data.businessProfile != null)
          _buildSummaryCard(
            context,
            'Business Profile',
            Icons.business,
            [
              'Name: ${data.businessProfile!.businessName}',
              'Category: ${data.businessProfile!.categoryName}',
              'Description: ${data.businessProfile!.description}',
              if (data.businessProfile!.phone != null)
                'Phone: ${data.businessProfile!.phone}',
              if (data.businessProfile!.website != null)
                'Website: ${data.businessProfile!.website}',
            ],
          ),

        // Location Summary
        if (data.primaryLocation != null)
          _buildSummaryCard(
            context,
            'Primary Location',
            Icons.location_on,
            [
              'Name: ${data.primaryLocation!.name}',
              'City: ${data.primaryLocation!.city}',
              'Address: ${data.primaryLocation!.address}',
            ],
          ),

        // Services Summary
        if (data.services.isNotEmpty)
          _buildSummaryCard(
            context,
            'Services',
            Icons.room_service,
            [
              '${data.services.length} service(s) configured',
              ...data.services.take(3).map((s) => '• ${s.name}'),
              if (data.services.length > 3)
                '• ... and ${data.services.length - 3} more',
            ],
          ),

        // Queues Summary
        if (data.queuesWithHours.isNotEmpty)
          _buildSummaryCard(
            context,
            'Queue Management',
            Icons.queue,
            [
              '${data.queuesWithHours.length} queue(s) configured with opening hours',
              ...data.queuesWithHours.take(3).map((q) => '• ${q.title} (${q.serviceIds.length} services)'),
              if (data.queuesWithHours.length > 3)
                '• ... and ${data.queuesWithHours.length - 3} more',
            ],
          ),

        // Completion info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.colors.primaryContainer.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.colors.primary.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: context.colors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ready to Complete Setup',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: context.colors.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Your business is ready to go! You\'ve set up your profile, location, services, and queues with opening hours. Click "Complete Setup" to finish.',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colors.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    IconData icon,
    List<String> items,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: context.colors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                item,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }
}
