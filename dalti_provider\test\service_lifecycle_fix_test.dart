import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/services/models/service_models.dart';

void main() {
  group('Service Lifecycle Fix Tests', () {
    test('Service model serialization works correctly', () {
      final service = Service(
        id: 1,
        title: 'Test Service',
        duration: 60,
        price: 50.0,
        acceptOnline: true,
        acceptNew: true,
        notificationOn: true,
      );

      expect(service.id, equals(1));
      expect(service.title, equals('Test Service'));
      expect(service.formattedDuration, equals('1h'));
      expect(service.formattedPrice, equals('\$50.00'));
      
      // Test JSON serialization
      final json = service.toJson();
      expect(json['id'], equals(1));
      expect(json['title'], equals('Test Service'));
      
      // Test JSON deserialization
      final serviceFromJson = Service.fromJson(json);
      expect(serviceFromJson.id, equals(service.id));
      expect(serviceFromJson.title, equals(service.title));
    });

    test('UpdateServiceRequest handles partial updates correctly', () {
      final updateRequest = UpdateServiceRequest(
        title: 'Updated Service',
        price: 75.0,
      );

      expect(updateRequest.title, equals('Updated Service'));
      expect(updateRequest.price, equals(75.0));
      expect(updateRequest.duration, isNull);
      expect(updateRequest.hasUpdates, isTrue);

      final json = updateRequest.toJson();
      expect(json['title'], equals('Updated Service'));
      expect(json['price'], equals(75.0));
      expect(json.containsKey('duration'), isFalse);
    });

    test('CreateServiceRequest validation works correctly', () {
      final createRequest = CreateServiceRequest(
        title: 'New Service',
        duration: 45,
        price: 25.0,
        acceptOnline: true,
        acceptNew: false,
      );

      expect(createRequest.title, equals('New Service'));
      expect(createRequest.duration, equals(45));
      expect(createRequest.price, equals(25.0));
      expect(createRequest.acceptOnline, isTrue);
      expect(createRequest.acceptNew, isFalse);

      final json = createRequest.toJson();
      expect(json['title'], equals('New Service'));
      expect(json['acceptOnline'], isTrue);
      expect(json['acceptNew'], isFalse);
      // Verify categoryId is not included
      expect(json.containsKey('categoryId'), isFalse);
    });

    testWidgets('WidgetsBinding.addPostFrameCallback is available', (WidgetTester tester) async {
      // This test verifies that the addPostFrameCallback method is available
      // and can be used to delay provider modifications
      
      bool callbackExecuted = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Simulate the fix pattern used in EditServiceScreen and ServiceDetailsScreen
              WidgetsBinding.instance.addPostFrameCallback((_) {
                callbackExecuted = true;
              });
              
              return const Scaffold(
                body: Text('Test Widget'),
              );
            },
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pump();
      
      // Verify the callback was executed after the widget tree finished building
      expect(callbackExecuted, isTrue);
      
      // Verify the widget is displayed
      expect(find.text('Test Widget'), findsOneWidget);
    });

    test('Service equality and hashCode work correctly', () {
      final service1 = Service(id: 1, title: 'Service 1', duration: 60);
      final service2 = Service(id: 1, title: 'Different Title', duration: 30);
      final service3 = Service(id: 2, title: 'Service 1', duration: 60);

      // Services with same ID should be equal
      expect(service1, equals(service2));
      expect(service1.hashCode, equals(service2.hashCode));
      
      // Services with different IDs should not be equal
      expect(service1, isNot(equals(service3)));
      expect(service1.hashCode, isNot(equals(service3.hashCode)));
    });

    test('Service copyWith method works correctly', () {
      final originalService = Service(
        id: 1,
        title: 'Original',
        duration: 60,
        price: 50.0,
        acceptOnline: true,
      );

      final updatedService = originalService.copyWith(
        title: 'Updated',
        duration: 90,
      );

      expect(updatedService.id, equals(1)); // Unchanged
      expect(updatedService.title, equals('Updated')); // Changed
      expect(updatedService.duration, equals(90)); // Changed
      expect(updatedService.price, equals(50.0)); // Unchanged
      expect(updatedService.acceptOnline, isTrue); // Unchanged
    });

    test('Service formatted methods work correctly', () {
      // Test duration formatting
      final service30min = Service(id: 1, title: 'Test', duration: 30);
      expect(service30min.formattedDuration, equals('30min'));

      final service90min = Service(id: 2, title: 'Test', duration: 90);
      expect(service90min.formattedDuration, equals('1h 30min'));

      final service120min = Service(id: 3, title: 'Test', duration: 120);
      expect(service120min.formattedDuration, equals('2h'));

      // Test price formatting
      final serviceWithPrice = Service(id: 4, title: 'Test', duration: 30, price: 25.5);
      expect(serviceWithPrice.formattedPrice, equals('\$25.50'));

      final serviceWithoutPrice = Service(id: 5, title: 'Test', duration: 30);
      expect(serviceWithoutPrice.formattedPrice, equals('Price not set'));
    });

    test('Service validation methods work correctly', () {
      final completeService = Service(id: 1, title: 'Complete Service', duration: 60);
      expect(completeService.isComplete, isTrue);

      final incompleteService1 = Service(id: 2, title: '', duration: 60);
      expect(incompleteService1.isComplete, isFalse);

      final incompleteService2 = Service(id: 3, title: 'Service', duration: 0);
      expect(incompleteService2.isComplete, isFalse);
    });
  });
}
