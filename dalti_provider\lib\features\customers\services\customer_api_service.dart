import '../../../core/network/http_client.dart';
import '../models/customer_models.dart';

/// API service for customer-related operations
class CustomerApiService {
  final HttpClient _httpClient;

  CustomerApiService(this._httpClient);

  /// Get customers with optional filters and pagination
  Future<CustomersResponse> getCustomers({
    CustomerSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print('[CustomerApiService] Fetching customers - page: $page, pageSize: $pageSize');

      // Build query parameters
      final queryParams = <String, dynamic>{
        'page': page,
        'pageSize': pageSize,
      };

      if (filters != null) {
        if (filters.searchQuery?.isNotEmpty == true) {
          queryParams['search'] = filters.searchQuery;
        }
        if (filters.status != null) {
          queryParams['status'] = filters.status!.name;
        }
        if (filters.wilaya?.isNotEmpty == true) {
          queryParams['wilaya'] = filters.wilaya;
        }
        if (filters.createdAfter != null) {
          queryParams['createdAfter'] = filters.createdAfter!.toIso8601String();
        }
        if (filters.createdBefore != null) {
          queryParams['createdBefore'] = filters.createdBefore!.toIso8601String();
        }
        if (filters.minAppointments > 0) {
          queryParams['minAppointments'] = filters.minAppointments;
        }
        if (filters.minSpent > 0) {
          queryParams['minSpent'] = filters.minSpent;
        }
        if (filters.tags?.isNotEmpty == true) {
          queryParams['tags'] = filters.tags!.join(',');
        }
        queryParams['sortBy'] = filters.sortBy;
        queryParams['ascending'] = filters.ascending;
      }

      final response = await _httpClient.get(
        '/api/auth/providers/customers',
        queryParameters: queryParams,
      );

      print('[CustomerApiService] Customers response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[CustomerApiService] Raw customers response data: ${response.data}');

        // Handle the actual backend response structure
        final Map<String, dynamic> responseJson = response.data;
        final bool success = responseJson['success'] ?? false;

        if (success && responseJson['data'] != null) {
          final dataSection = responseJson['data'];
          final List<dynamic> customersJson = dataSection['data'] ?? [];
          final Map<String, dynamic> pagination = dataSection['pagination'] ?? {};

          // Parse customers
          final customers = customersJson.map((json) => Customer.fromJson(json)).toList();

          // Extract pagination info
          final totalCount = pagination['total'] ?? 0;
          final currentPage = pagination['page'] ?? 1;
          final pageSize = pagination['limit'] ?? 20;
          final totalPages = pagination['totalPages'] ?? 1;

          final responseData = CustomersResponse(
            success: true,
            customers: customers,
            totalCount: totalCount,
            currentPage: currentPage,
            pageSize: pageSize,
            totalPages: totalPages,
          );

          print('[CustomerApiService] Customers fetched successfully: ${customers.length} customers');
          return responseData;
        } else {
          print('[CustomerApiService] Backend returned success=false for customers');
          return CustomersResponse(
            success: false,
            error: CustomerError(
              code: 'BACKEND_ERROR',
              message: responseJson['message'] ?? 'Unknown error',
              details: 'Backend returned success=false',
            ),
          );
        }
      } else {
        print('[CustomerApiService] Customers fetch failed: ${response.statusCode}');
        return CustomersResponse(
          success: false,
          error: CustomerError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch customers',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Customers error: $e');
      return CustomersResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get a single customer by ID
  Future<CustomerResponse> getCustomer(String customerId) async {
    try {
      print('[CustomerApiService] Fetching customer: $customerId');

      final response = await _httpClient.get('/api/auth/providers/customers/$customerId');

      print('[CustomerApiService] Customer response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[CustomerApiService] Raw single customer response data: ${response.data}');

        // Handle the actual backend response structure
        final Map<String, dynamic> responseJson = response.data;
        final bool success = responseJson['success'] ?? false;

        if (success && responseJson['data'] != null) {
          final customer = Customer.fromJson(responseJson['data']);
          final responseData = CustomerResponse(
            success: true,
            customer: customer,
          );
          print('[CustomerApiService] Customer fetched successfully');
          return responseData;
        } else {
          print('[CustomerApiService] Backend returned success=false for single customer');
          return CustomerResponse(
            success: false,
            error: CustomerError(
              code: 'BACKEND_ERROR',
              message: responseJson['message'] ?? 'Unknown error',
              details: 'Backend returned success=false',
            ),
          );
        }
      } else {
        print('[CustomerApiService] Customer fetch failed: ${response.statusCode}');
        return CustomerResponse(
          success: false,
          error: CustomerError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch customer',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Customer error: $e');
      return CustomerResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Create a new customer
  Future<CustomerResponse> createCustomer(CustomerRequest request) async {
    try {
      print('[CustomerApiService] Creating customer: ${request.firstName} ${request.lastName}');

      final response = await _httpClient.post(
        '/api/auth/providers/customers',
        data: request.toBackendJson(),
      );

      print('[CustomerApiService] Create customer response: ${response.statusCode}');

      if (response.statusCode == 201) {
        print('[CustomerApiService] Raw response data: ${response.data}');

        // Handle the actual backend response structure
        final Map<String, dynamic> responseJson = response.data;
        final bool success = responseJson['success'] ?? false;

        if (success && responseJson['data'] != null) {
          final customer = Customer.fromJson(responseJson['data']);
          final responseData = CustomerResponse(
            success: true,
            customer: customer,
          );
          print('[CustomerApiService] Customer created successfully');
          return responseData;
        } else {
          print('[CustomerApiService] Backend returned success=false');
          return CustomerResponse(
            success: false,
            error: CustomerError(
              code: 'BACKEND_ERROR',
              message: responseJson['message'] ?? 'Unknown error',
              details: 'Backend returned success=false',
            ),
          );
        }
      } else {
        print('[CustomerApiService] Customer creation failed: ${response.statusCode}');
        return CustomerResponse(
          success: false,
          error: CustomerError(
            code: 'CREATE_ERROR',
            message: 'Failed to create customer',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Create customer error: $e');
      return CustomerResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Update an existing customer
  Future<CustomerResponse> updateCustomer(String customerId, CustomerRequest request) async {
    try {
      print('[CustomerApiService] Updating customer: $customerId');

      final response = await _httpClient.put(
        '/api/auth/providers/customers/$customerId',
        data: request.toBackendJson(),
      );

      print('[CustomerApiService] Update customer response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[CustomerApiService] Raw update response data: ${response.data}');

        // Handle the actual backend response structure
        final Map<String, dynamic> responseJson = response.data;
        final bool success = responseJson['success'] ?? false;

        if (success && responseJson['data'] != null) {
          final customer = Customer.fromJson(responseJson['data']);
          final responseData = CustomerResponse(
            success: true,
            customer: customer,
          );
          print('[CustomerApiService] Customer updated successfully');
          return responseData;
        } else {
          print('[CustomerApiService] Backend returned success=false for update');
          return CustomerResponse(
            success: false,
            error: CustomerError(
              code: 'BACKEND_ERROR',
              message: responseJson['message'] ?? 'Unknown error',
              details: 'Backend returned success=false',
            ),
          );
        }
      } else {
        print('[CustomerApiService] Customer update failed: ${response.statusCode}');
        return CustomerResponse(
          success: false,
          error: CustomerError(
            code: 'UPDATE_ERROR',
            message: 'Failed to update customer',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Update customer error: $e');
      return CustomerResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Delete a customer
  Future<CustomerResponse> deleteCustomer(String customerId) async {
    try {
      print('[CustomerApiService] Deleting customer: $customerId');

      final response = await _httpClient.delete('/api/auth/providers/customers/$customerId');

      print('[CustomerApiService] Delete customer response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[CustomerApiService] Customer deleted successfully');
        return const CustomerResponse(success: true);
      } else {
        print('[CustomerApiService] Customer deletion failed: ${response.statusCode}');
        return CustomerResponse(
          success: false,
          error: CustomerError(
            code: 'DELETE_ERROR',
            message: 'Failed to delete customer',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Delete customer error: $e');
      return CustomerResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get customer appointment history
  Future<CustomerAppointmentsResponse> getCustomerAppointments(
    String customerId, {
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print('[CustomerApiService] Fetching customer appointments: $customerId');

      final response = await _httpClient.get(
        '/api/auth/providers/customers/$customerId/appointments',
        queryParameters: {
          'page': page,
          'pageSize': pageSize,
        },
      );

      print('[CustomerApiService] Customer appointments response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = CustomerAppointmentsResponse.fromJson(response.data);
        print('[CustomerApiService] Customer appointments fetched successfully: ${responseData.appointments.length} appointments');
        return responseData;
      } else {
        print('[CustomerApiService] Customer appointments fetch failed: ${response.statusCode}');
        return CustomerAppointmentsResponse(
          success: false,
          error: CustomerError(
            code: 'FETCH_ERROR',
            message: 'Failed to fetch customer appointments',
            details: 'HTTP ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[CustomerApiService] Customer appointments error: $e');
      return CustomerAppointmentsResponse(
        success: false,
        error: CustomerError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get customer statistics
  Future<CustomerStats> getCustomerStats() async {
    try {
      print('[CustomerApiService] Fetching customer statistics');

      final response = await _httpClient.get('/api/auth/providers/customers/stats');

      print('[CustomerApiService] Customer stats response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final stats = CustomerStats.fromJson(response.data);
        print('[CustomerApiService] Customer stats fetched successfully');
        return stats;
      } else {
        print('[CustomerApiService] Customer stats fetch failed: ${response.statusCode}');
        return const CustomerStats();
      }
    } catch (e) {
      print('[CustomerApiService] Customer stats error: $e');
      return const CustomerStats();
    }
  }
}
