import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../locations/models/location_models.dart';
import '../../../../core/theme/theme_provider.dart';

/// Reusable widget for selecting a location for queues
/// Used in Queue management step
class LocationSelectorWidget extends ConsumerWidget {
  final List<EnhancedLocation> availableLocations;
  final int? selectedLocationId;
  final ValueChanged<int?> onLocationSelected;
  final String? title;
  final String? subtitle;
  final bool enabled;
  final bool isRequired;

  const LocationSelectorWidget({
    super.key,
    required this.availableLocations,
    required this.onLocationSelected,
    this.selectedLocationId,
    this.title,
    this.subtitle,
    this.enabled = true,
    this.isRequired = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            if (title != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: theme.colors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    title!,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isRequired) ...[
                    const SizedBox(width: 4),
                    Text(
                      '*',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colors.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
              const SizedBox(height: 16),
            ],

            // No locations available
            if (availableLocations.isEmpty) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colors.error.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_outlined,
                      color: theme.colors.error,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'No Locations Available',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colors.error,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Please add at least one location before creating queues.',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colors.error,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              // Location options
              ...availableLocations.map((location) {
                return _buildLocationOption(
                  context,
                  theme,
                  location,
                  selectedLocationId == location.id,
                );
              }).toList(),

              // Validation message
              if (isRequired && selectedLocationId == null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colors.error.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: theme.colors.error,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Please select a location for this queue.',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colors.error,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationOption(
    BuildContext context,
    ThemeData theme,
    EnhancedLocation location,
    bool isSelected,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: enabled ? () => onLocationSelected(location.id) : null,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected 
                  ? theme.colors.primary 
                  : theme.colors.outline.withOpacity(0.5),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected 
                ? theme.colors.primary.withOpacity(0.1)
                : null,
          ),
          child: Row(
            children: [
              // Radio button
              Radio<int>(
                value: location.id,
                groupValue: selectedLocationId,
                onChanged: enabled ? (value) => onLocationSelected(value) : null,
                activeColor: theme.colors.primary,
              ),
              
              const SizedBox(width: 12),
              
              // Location icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? theme.colors.primary.withOpacity(0.2)
                      : theme.colors.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected 
                        ? theme.colors.primary.withOpacity(0.3)
                        : theme.colors.outline.withOpacity(0.3),
                  ),
                ),
                child: Icon(
                  Icons.business,
                  color: isSelected 
                      ? theme.colors.primary 
                      : theme.colors.onSurface.withOpacity(0.7),
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Location details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Location name
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            location.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isSelected ? theme.colors.primary : null,
                            ),
                          ),
                        ),
                        if (location.shortName != null) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              location.shortName!,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colors.primary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Location address
                    Text(
                      location.fullAddress,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colors.onSurface.withOpacity(0.7),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Location features
                    Row(
                      children: [
                        if (location.parking) ...[
                          _buildFeatureChip(
                            theme,
                            Icons.local_parking,
                            'Parking',
                            isSelected,
                          ),
                          const SizedBox(width: 4),
                        ],
                        if (location.elevator) ...[
                          _buildFeatureChip(
                            theme,
                            Icons.elevator,
                            'Elevator',
                            isSelected,
                          ),
                          const SizedBox(width: 4),
                        ],
                        if (location.handicapAccess) ...[
                          _buildFeatureChip(
                            theme,
                            Icons.accessible,
                            'Accessible',
                            isSelected,
                          ),
                          const SizedBox(width: 4),
                        ],
                        if (location.hasOpeningHours) ...[
                          _buildFeatureChip(
                            theme,
                            Icons.schedule,
                            'Hours Set',
                            isSelected,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              // Selection indicator
              if (isSelected) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.check_circle,
                  color: theme.colors.primary,
                  size: 24,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(
    ThemeData theme,
    IconData icon,
    String label,
    bool isSelected,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isSelected 
            ? theme.colors.primary.withOpacity(0.1)
            : theme.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected 
              ? theme.colors.primary.withOpacity(0.3)
              : theme.colors.outline.withOpacity(0.3),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: isSelected 
                ? theme.colors.primary 
                : theme.colors.onSurface.withOpacity(0.6),
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              fontSize: 10,
              color: isSelected 
                  ? theme.colors.primary 
                  : theme.colors.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}
