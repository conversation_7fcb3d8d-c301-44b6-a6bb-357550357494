/// Provider category model with hierarchical support
class ProviderCategory {
  final int id;
  final String name;
  final String description;
  final String? icon;
  final int? parentId;
  final bool isSelectable;
  final List<ProviderCategory> children;

  const ProviderCategory({
    required this.id,
    required this.name,
    required this.description,
    this.icon,
    this.parentId,
    this.isSelectable = true,
    this.children = const [],
  });

  factory ProviderCategory.fromJson(Map<String, dynamic> json) {
    return ProviderCategory(
      id: json['id'] ?? 0,
      name: json['name'] ?? json['title'] ?? '', // API uses 'title' field
      description: json['description'] ?? '',
      icon: json['icon'],
      parentId: json['parentId'],
      isSelectable: json['isSelectable'] ?? (json['parentId'] != null), // Child categories are selectable by default
      children: (json['children'] as List<dynamic>?)
          ?.map((child) => ProviderCategory.fromJson(child as Map<String, dynamic>))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'parentId': parentId,
      'isSelectable': isSelectable,
      'children': children.map((child) => child.toJson()).toList(),
    };
  }

  /// Check if this category is a parent (has children)
  bool get isParent => children.isNotEmpty;

  /// Check if this category is a child (has parentId)
  bool get isChild => parentId != null;

  /// Get all selectable child categories recursively
  List<ProviderCategory> get selectableChildren {
    final List<ProviderCategory> result = [];
    for (final child in children) {
      if (child.isSelectable) {
        result.add(child);
      }
      result.addAll(child.selectableChildren);
    }
    return result;
  }

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProviderCategory &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Hierarchical provider categories with parent-child relationships
class ProviderCategories {
  static const List<ProviderCategory> categories = [
    // Healthcare - Parent Category
    ProviderCategory(
      id: 1,
      name: 'Healthcare',
      description: 'Medical and health services',
      icon: '🏥',
      isSelectable: false,
      children: [
        ProviderCategory(
          id: 101,
          name: 'Medical Clinics',
          description: 'General practice, family medicine',
          icon: '🩺',
          parentId: 1,
        ),
        ProviderCategory(
          id: 102,
          name: 'Dental Services',
          description: 'Dentists, orthodontists, oral surgery',
          icon: '🦷',
          parentId: 1,
        ),
        ProviderCategory(
          id: 103,
          name: 'Specialized Medicine',
          description: 'Cardiology, dermatology, neurology',
          icon: '🔬',
          parentId: 1,
        ),
        ProviderCategory(
          id: 104,
          name: 'Mental Health',
          description: 'Psychology, psychiatry, counseling',
          icon: '🧠',
          parentId: 1,
        ),
      ],
    ),

    // Beauty & Wellness - Parent Category
    ProviderCategory(
      id: 2,
      name: 'Beauty & Wellness',
      description: 'Beauty and personal care services',
      icon: '💅',
      isSelectable: false,
      children: [
        ProviderCategory(
          id: 201,
          name: 'Hair Salons',
          description: 'Hair cutting, styling, coloring',
          icon: '💇',
          parentId: 2,
        ),
        ProviderCategory(
          id: 202,
          name: 'Nail Services',
          description: 'Manicures, pedicures, nail art',
          icon: '💅',
          parentId: 2,
        ),
        ProviderCategory(
          id: 203,
          name: 'Spa Services',
          description: 'Massages, facials, body treatments',
          icon: '🧖',
          parentId: 2,
        ),
        ProviderCategory(
          id: 204,
          name: 'Fitness Centers',
          description: 'Gyms, personal training, yoga studios',
          icon: '💪',
          parentId: 2,
        ),
      ],
    ),

    // Professional Services - Parent Category
    ProviderCategory(
      id: 3,
      name: 'Professional Services',
      description: 'Business and professional services',
      icon: '💼',
      isSelectable: false,
      children: [
        ProviderCategory(
          id: 301,
          name: 'Legal Services',
          description: 'Law firms, legal consultations',
          icon: '⚖️',
          parentId: 3,
        ),
        ProviderCategory(
          id: 302,
          name: 'Financial Services',
          description: 'Banks, insurance, financial advisors',
          icon: '🏦',
          parentId: 3,
        ),
        ProviderCategory(
          id: 303,
          name: 'Consulting',
          description: 'Business consulting, management',
          icon: '📊',
          parentId: 3,
        ),
        ProviderCategory(
          id: 304,
          name: 'Accounting',
          description: 'Tax preparation, bookkeeping',
          icon: '🧮',
          parentId: 3,
        ),
      ],
    ),
  ];

  /// Get category by ID (searches both parent and child categories)
  static ProviderCategory? getCategoryById(int id) {
    // Search in parent categories
    for (final category in categories) {
      if (category.id == id) return category;

      // Search in child categories
      for (final child in category.children) {
        if (child.id == id) return child;
      }
    }
    return null;
  }

  /// Get all parent categories (top-level categories)
  static List<ProviderCategory> getParentCategories() {
    return List.from(categories);
  }

  /// Get all selectable child categories (flattened list)
  static List<ProviderCategory> getSelectableCategories() {
    final List<ProviderCategory> selectableCategories = [];
    for (final parent in categories) {
      selectableCategories.addAll(parent.selectableChildren);
    }
    return selectableCategories;
  }

  /// Get all categories (both parent and child, flattened)
  static List<ProviderCategory> getAllCategories() {
    final List<ProviderCategory> allCategories = [];
    for (final parent in categories) {
      allCategories.add(parent);
      allCategories.addAll(parent.children);
    }
    return allCategories;
  }

  /// Search categories by name (searches both parent and child categories)
  static List<ProviderCategory> searchCategories(String query) {
    if (query.isEmpty) return getSelectableCategories();

    final lowerQuery = query.toLowerCase();
    final List<ProviderCategory> results = [];

    for (final parent in categories) {
      // Search in parent
      if (parent.name.toLowerCase().contains(lowerQuery) ||
          parent.description.toLowerCase().contains(lowerQuery)) {
        if (parent.isSelectable) results.add(parent);
      }

      // Search in children
      for (final child in parent.children) {
        if (child.name.toLowerCase().contains(lowerQuery) ||
            child.description.toLowerCase().contains(lowerQuery)) {
          if (child.isSelectable) results.add(child);
        }
      }
    }

    return results;
  }

  /// Get children of a specific parent category
  static List<ProviderCategory> getChildrenOf(int parentId) {
    final parent = getCategoryById(parentId);
    return parent?.children ?? [];
  }

  /// Check if a category is selectable
  static bool isCategorySelectable(int id) {
    final category = getCategoryById(id);
    return category?.isSelectable ?? false;
  }
}
