import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/auth/models/provider_category.dart';

void main() {
  group('Hierarchical Category Dropdown Tests', () {

    test('Category hierarchy structure is correct', () {
      final parentCategories = ProviderCategories.getParentCategories();
      
      // Should have 3 parent categories
      expect(parentCategories.length, equals(3));
      
      // Check Healthcare parent
      final healthcare = parentCategories.firstWhere((cat) => cat.name == 'Healthcare');
      expect(healthcare.isSelectable, isFalse);
      expect(healthcare.children.length, equals(4));
      expect(healthcare.children.every((child) => child.isSelectable), isTrue);
      expect(healthcare.children.every((child) => child.parentId == healthcare.id), isTrue);
      
      // Check Beauty & Wellness parent
      final beauty = parentCategories.firstWhere((cat) => cat.name == 'Beauty & Wellness');
      expect(beauty.isSelectable, isFalse);
      expect(beauty.children.length, equals(4));
      
      // Check Professional Services parent
      final professional = parentCategories.firstWhere((cat) => cat.name == 'Professional Services');
      expect(professional.isSelectable, isFalse);
      expect(professional.children.length, equals(4));
    });

    test('Category search works correctly', () {
      // Search for medical-related categories
      final medicalCategories = ProviderCategories.searchCategories('medical');
      expect(medicalCategories.isNotEmpty, isTrue);
      expect(medicalCategories.every((cat) => cat.isSelectable), isTrue);
      
      // Search for dental categories
      final dentalCategories = ProviderCategories.searchCategories('dental');
      expect(dentalCategories.length, equals(1));
      expect(dentalCategories.first.name, equals('Dental Services'));
      
      // Search for hair categories
      final hairCategories = ProviderCategories.searchCategories('hair');
      expect(hairCategories.length, equals(1));
      expect(hairCategories.first.name, equals('Hair Salons'));
      
      // Empty search should return all selectable categories
      final allSelectable = ProviderCategories.searchCategories('');
      expect(allSelectable.length, equals(ProviderCategories.getSelectableCategories().length));
    });

    test('Category utility methods work correctly', () {
      // Test getCategoryById
      final medicalClinic = ProviderCategories.getCategoryById(101);
      expect(medicalClinic?.name, equals('Medical Clinics'));
      expect(medicalClinic?.parentId, equals(1));
      expect(medicalClinic?.isSelectable, isTrue);
      
      // Test getChildrenOf
      final healthcareChildren = ProviderCategories.getChildrenOf(1);
      expect(healthcareChildren.length, equals(4));
      expect(healthcareChildren.map((c) => c.name).toList(), contains('Medical Clinics'));
      expect(healthcareChildren.map((c) => c.name).toList(), contains('Dental Services'));
      
      // Test isCategorySelectable
      expect(ProviderCategories.isCategorySelectable(1), isFalse); // Healthcare parent
      expect(ProviderCategories.isCategorySelectable(101), isTrue); // Medical Clinics child
      expect(ProviderCategories.isCategorySelectable(999), isFalse); // Non-existent
    });

    test('Category model properties work correctly', () {
      final healthcare = ProviderCategories.getCategoryById(1)!;
      final medicalClinic = ProviderCategories.getCategoryById(101)!;
      
      // Test parent category properties
      expect(healthcare.isParent, isTrue);
      expect(healthcare.isChild, isFalse);
      expect(healthcare.isSelectable, isFalse);
      expect(healthcare.selectableChildren.length, equals(4));
      
      // Test child category properties
      expect(medicalClinic.isParent, isFalse);
      expect(medicalClinic.isChild, isTrue);
      expect(medicalClinic.isSelectable, isTrue);
      expect(medicalClinic.selectableChildren.length, equals(0));
      
      // Test equality
      final sameCategory = ProviderCategories.getCategoryById(101)!;
      expect(medicalClinic == sameCategory, isTrue);
      expect(medicalClinic.hashCode == sameCategory.hashCode, isTrue);
    });

    test('All selectable categories have valid parent relationships', () {
      final selectableCategories = ProviderCategories.getSelectableCategories();
      
      for (final category in selectableCategories) {
        // Every selectable category should be a child
        expect(category.isChild, isTrue);
        expect(category.parentId, isNotNull);
        expect(category.isSelectable, isTrue);
        
        // Parent should exist and be non-selectable
        final parent = ProviderCategories.getCategoryById(category.parentId!);
        expect(parent, isNotNull);
        expect(parent!.isSelectable, isFalse);
        expect(parent.isParent, isTrue);
      }
    });

    test('Category JSON serialization works correctly', () {
      final category = ProviderCategory(
        id: 999,
        name: 'Test Category',
        description: 'Test Description',
        icon: '🧪',
        parentId: 1,
        isSelectable: true,
        children: [
          ProviderCategory(
            id: 1000,
            name: 'Child Category',
            description: 'Child Description',
            parentId: 999,
          ),
        ],
      );
      
      final json = category.toJson();
      expect(json['id'], equals(999));
      expect(json['name'], equals('Test Category'));
      expect(json['parentId'], equals(1));
      expect(json['isSelectable'], equals(true));
      expect(json['children'], isA<List>());
      
      final fromJson = ProviderCategory.fromJson(json);
      expect(fromJson.id, equals(category.id));
      expect(fromJson.name, equals(category.name));
      expect(fromJson.parentId, equals(category.parentId));
      expect(fromJson.isSelectable, equals(category.isSelectable));
      expect(fromJson.children.length, equals(1));
    });
  });
}
