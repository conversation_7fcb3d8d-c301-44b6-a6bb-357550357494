import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/wizard_persistence_models.dart';
import '../models/onboarding_models.dart';
import '../providers/wizard_navigation_provider.dart';
import '../providers/onboarding_provider.dart';
import 'wizard_persistence_service.dart';

/// Service for recovering wizard state from persistence
class WizardRecoveryService {
  final WizardPersistenceService _persistenceService;
  final RecoveryOptions _options;

  WizardRecoveryService({
    required WizardPersistenceService persistenceService,
    RecoveryOptions? options,
  }) : _persistenceService = persistenceService,
       _options = options ?? const RecoveryOptions();

  /// Check if recovery is available
  Future<RecoveryInfo?> checkRecoveryAvailable() async {
    try {
      if (!_persistenceService.hasState()) {
        return null;
      }

      final state = await _persistenceService.loadState();
      if (state == null) {
        return null;
      }

      // Check if state is too old
      if (state.ageInHours > _options.maxRecoveryAgeHours) {
        print('[WizardRecovery] Saved state is too old (${state.ageInHours.toStringAsFixed(1)} hours)');
        return null;
      }

      // Validate state integrity if enabled
      if (_options.validateIntegrity && !_validateStateIntegrity(state)) {
        print('[WizardRecovery] State integrity validation failed');
        return null;
      }

      return RecoveryInfo(
        hasRecoverableState: true,
        savedAt: state.savedAt,
        currentStep: state.currentStep,
        ageInHours: state.ageInHours,
        dataSize: await _getStateSize(),
        stepCompletionStatus: state.stepCompletionStatus,
        sessionInfo: state.sessionInfo,
      );
    } catch (e) {
      print('[WizardRecovery] Error checking recovery availability: $e');
      return null;
    }
  }

  /// Recover wizard state
  Future<RecoveryResult> recoverState(WidgetRef ref) async {
    try {
      final state = await _persistenceService.loadState();
      if (state == null) {
        return const RecoveryResult(
          success: false,
          error: 'No saved state found',
        );
      }

      // Convert persisted state back to current state
      final (navigationState, onboardingData) = WizardStateConverter.toCurrentState(state);
      
      if (navigationState == null || onboardingData == null) {
        return const RecoveryResult(
          success: false,
          error: 'Invalid state data',
        );
      }

      // Restore navigation state
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      _restoreNavigationState(wizardNotifier, navigationState, state.stepCompletionStatus);

      // Restore onboarding data
      final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
      await _restoreOnboardingData(onboardingNotifier, onboardingData);

      print('[WizardRecovery] State recovered successfully');
      
      return RecoveryResult(
        success: true,
        recoveredStep: state.currentStep,
        recoveredAt: DateTime.now(),
        dataAge: state.ageInHours,
      );
    } catch (e) {
      print('[WizardRecovery] Error recovering state: $e');
      return RecoveryResult(
        success: false,
        error: 'Recovery failed: $e',
      );
    }
  }

  /// Show recovery dialog to user
  Future<bool> showRecoveryDialog(BuildContext context, RecoveryInfo info) async {
    if (!_options.showRecoveryDialog) {
      return _options.autoRestore;
    }

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => RecoveryDialog(info: info),
    ) ?? false;
  }

  /// Clear saved state (decline recovery)
  Future<void> declineRecovery() async {
    await _persistenceService.clearState();
    print('[WizardRecovery] Recovery declined, saved state cleared');
  }

  /// Validate state integrity
  bool _validateStateIntegrity(PersistedWizardState state) {
    try {
      // Check required fields
      if (state.onboardingData == null) {
        return false;
      }

      // Check step consistency
      if (!OnboardingStep.values.contains(state.currentStep)) {
        return false;
      }

      // Check step index consistency
      final allSteps = OnboardingStep.values.where((step) => step != OnboardingStep.completed).toList();
      if (state.currentStepIndex < 0 || state.currentStepIndex >= allSteps.length) {
        return false;
      }

      // Check if current step matches index
      if (allSteps[state.currentStepIndex] != state.currentStep) {
        return false;
      }

      return true;
    } catch (e) {
      print('[WizardRecovery] State integrity validation error: $e');
      return false;
    }
  }

  /// Get size of saved state
  Future<int> _getStateSize() async {
    try {
      final stats = await _persistenceService.getStats();
      return stats.totalSize;
    } catch (e) {
      return 0;
    }
  }

  /// Restore navigation state
  void _restoreNavigationState(
    WizardNavigationNotifier notifier,
    WizardNavigationState navigationState,
    Map<OnboardingStep, bool> stepCompletionStatus,
  ) {
    // Restore step completion status
    for (final entry in stepCompletionStatus.entries) {
      notifier.markStepCompleted(entry.key, entry.value);
    }

    // Navigate to the saved step
    notifier.goToStep(navigationState.currentStep);
  }

  /// Restore onboarding data
  Future<void> _restoreOnboardingData(
    OnboardingNotifier notifier,
    OnboardingData onboardingData,
  ) async {
    // Restore business profile
    if (onboardingData.businessProfile != null) {
      await notifier.saveBusinessProfile(onboardingData.businessProfile!);
    }

    // Restore primary location
    if (onboardingData.primaryLocation != null) {
      await notifier.savePrimaryLocation(onboardingData.primaryLocation!);
    }

    // Restore services
    if (onboardingData.services.isNotEmpty) {
      await notifier.saveServices(onboardingData.services);
    }

    // Restore queues
    if (onboardingData.queues.isNotEmpty) {
      await notifier.saveQueues(onboardingData.queues);
    }

    // Restore queues with location links
    if (onboardingData.queuesWithLocationLinks.isNotEmpty) {
      await notifier.saveQueuesWithLocationLinks(onboardingData.queuesWithLocationLinks);
    }
  }
}

/// Information about recoverable state
@immutable
class RecoveryInfo {
  final bool hasRecoverableState;
  final DateTime savedAt;
  final OnboardingStep currentStep;
  final double ageInHours;
  final int dataSize;
  final Map<OnboardingStep, bool> stepCompletionStatus;
  final SessionInfo? sessionInfo;

  const RecoveryInfo({
    required this.hasRecoverableState,
    required this.savedAt,
    required this.currentStep,
    required this.ageInHours,
    required this.dataSize,
    required this.stepCompletionStatus,
    this.sessionInfo,
  });

  /// Get human-readable age
  String get ageText {
    if (ageInHours < 1) {
      final minutes = (ageInHours * 60).round();
      return '$minutes minute${minutes != 1 ? 's' : ''} ago';
    } else if (ageInHours < 24) {
      final hours = ageInHours.round();
      return '$hours hour${hours != 1 ? 's' : ''} ago';
    } else {
      final days = (ageInHours / 24).round();
      return '$days day${days != 1 ? 's' : ''} ago';
    }
  }

  /// Get completion percentage
  double get completionPercentage {
    final completedSteps = stepCompletionStatus.values.where((completed) => completed).length;
    final totalSteps = OnboardingStep.values.length - 1; // Exclude completed step
    return completedSteps / totalSteps;
  }
}

/// Result of recovery operation
@immutable
class RecoveryResult {
  final bool success;
  final String? error;
  final OnboardingStep? recoveredStep;
  final DateTime? recoveredAt;
  final double? dataAge;

  const RecoveryResult({
    required this.success,
    this.error,
    this.recoveredStep,
    this.recoveredAt,
    this.dataAge,
  });
}

/// Dialog for asking user about recovery
class RecoveryDialog extends StatelessWidget {
  final RecoveryInfo info;

  const RecoveryDialog({
    super.key,
    required this.info,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.restore, color: Colors.blue),
          SizedBox(width: 8),
          Text('Restore Previous Session?'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'We found a previous onboarding session that was saved ${info.ageText}.',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          
          // Progress information
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Progress: ${(info.completionPercentage * 100).round()}% complete',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text('Current step: ${info.currentStep.title}'),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: info.completionPercentage,
                  backgroundColor: Colors.grey.withOpacity(0.3),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          const Text(
            'Would you like to continue where you left off, or start fresh?',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Start Fresh'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: const Text('Continue Previous'),
        ),
      ],
    );
  }
}

/// Provider for recovery service
final wizardRecoveryServiceProvider = Provider<WizardRecoveryService>((ref) {
  final persistenceService = WizardPersistenceServiceFactory.getInstance();
  return WizardRecoveryService(persistenceService: persistenceService);
});

/// Extension for easy recovery integration
extension RecoveryExtension on WidgetRef {
  /// Get recovery service
  WizardRecoveryService get recovery => read(wizardRecoveryServiceProvider);

  /// Check if recovery is available
  Future<RecoveryInfo?> checkRecovery() => recovery.checkRecoveryAvailable();

  /// Recover state
  Future<RecoveryResult> recoverState() => recovery.recoverState(this);

  /// Show recovery dialog
  Future<bool> showRecoveryDialog(BuildContext context, RecoveryInfo info) =>
      recovery.showRecoveryDialog(context, info);

  /// Decline recovery
  Future<void> declineRecovery() => recovery.declineRecovery();
}
