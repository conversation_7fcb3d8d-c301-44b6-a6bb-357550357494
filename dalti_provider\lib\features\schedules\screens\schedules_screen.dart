import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/schedule_provider.dart';
import '../models/schedule_models.dart';
import '../widgets/weekly_schedule_grid.dart';
import '../widgets/schedule_card.dart';
import '../../locations/providers/location_provider.dart';

class SchedulesScreen extends ConsumerStatefulWidget {
  const SchedulesScreen({super.key});

  @override
  ConsumerState<SchedulesScreen> createState() => _SchedulesScreenState();
}

class _SchedulesScreenState extends ConsumerState<SchedulesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int? _selectedLocationId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
      ref.read(scheduleNotifierProvider.notifier).loadSchedules();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final scheduleState = ref.watch(scheduleNotifierProvider);
    final locationState = ref.watch(locationNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Weekly View', icon: Icon(Icons.calendar_view_week)),
            Tab(text: 'List View', icon: Icon(Icons.list)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(scheduleNotifierProvider.notifier).refreshSchedules();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Location filter
          if (locationState.locations.isNotEmpty) _buildLocationFilter(),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildWeeklyView(scheduleState),
                _buildListView(scheduleState),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddScheduleDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildLocationFilter() {
    final locationState = ref.watch(locationNotifierProvider);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: DropdownButtonFormField<int?>(
        value: _selectedLocationId,
        decoration: const InputDecoration(
          labelText: 'Filter by Location',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.location_on),
        ),
        items: [
          const DropdownMenuItem<int?>(
            value: null,
            child: Text('All Locations'),
          ),
          ...locationState.locations.map((location) {
            return DropdownMenuItem<int?>(
              value: location.id,
              child: Text(location.name),
            );
          }),
        ],
        onChanged: (value) {
          setState(() {
            _selectedLocationId = value;
          });
          _filterSchedules();
        },
      ),
    );
  }

  Widget _buildWeeklyView(ScheduleData scheduleState) {
    if (scheduleState.state == ScheduleState.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (scheduleState.state == ScheduleState.error) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'Error loading schedules',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              scheduleState.error ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(scheduleNotifierProvider.notifier).refreshSchedules();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final filteredSchedules = _getFilteredSchedules(scheduleState.schedules);

    if (filteredSchedules.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: WeeklyScheduleGrid(
        schedules: filteredSchedules,
        onScheduleTap: _onScheduleTap,
        onAddSchedule: _showAddScheduleDialog,
      ),
    );
  }

  Widget _buildListView(ScheduleData scheduleState) {
    if (scheduleState.state == ScheduleState.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (scheduleState.state == ScheduleState.error) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'Error loading schedules',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              scheduleState.error ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(scheduleNotifierProvider.notifier).refreshSchedules();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final filteredSchedules = _getFilteredSchedules(scheduleState.schedules);

    if (filteredSchedules.isEmpty) {
      return _buildEmptyState();
    }

    // Group schedules by day for list view
    final groupedSchedules = <int, List<Schedule>>{};
    for (final schedule in filteredSchedules) {
      groupedSchedules.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 7, // 7 days of the week
      itemBuilder: (context, index) {
        final daySchedules = groupedSchedules[index] ?? [];
        final dayName = _getDayName(index);

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      dayName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${daySchedules.length} schedule${daySchedules.length != 1 ? 's' : ''}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              if (daySchedules.isEmpty)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Text(
                    'No schedules for this day',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              else
                ...daySchedules.map((schedule) {
                  return ScheduleCard(
                    schedule: schedule,
                    onTap: () => _onScheduleTap(schedule),
                    onEdit: () => _editSchedule(schedule),
                    onDelete: () => _deleteSchedule(schedule),
                  );
                }),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No Schedules Found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedLocationId != null
                ? 'No schedules found for the selected location.'
                : 'Create your first schedule to get started.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddScheduleDialog,
            icon: const Icon(Icons.add),
            label: const Text('Add Schedule'),
          ),
        ],
      ),
    );
  }

  List<Schedule> _getFilteredSchedules(List<Schedule> schedules) {
    if (_selectedLocationId == null) {
      return schedules;
    }
    return schedules.where((s) => s.locationId == _selectedLocationId).toList();
  }

  void _filterSchedules() {
    ref.read(scheduleNotifierProvider.notifier).loadSchedules(
      locationId: _selectedLocationId,
      forceRefresh: true,
    );
  }

  void _onScheduleTap(Schedule schedule) {
    // Navigate to schedule details or edit
    _editSchedule(schedule);
  }

  void _showAddScheduleDialog() {
    context.push('/schedules/add');
  }

  void _editSchedule(Schedule schedule) {
    context.push('/schedules/edit/${schedule.id}');
  }

  void _deleteSchedule(Schedule schedule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Schedule'),
        content: Text(
          'Are you sure you want to delete the schedule for ${schedule.dayName} (${schedule.timeRange})?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(scheduleNotifierProvider.notifier).deleteSchedule(schedule.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getDayName(int dayOfWeek) {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  }
}
