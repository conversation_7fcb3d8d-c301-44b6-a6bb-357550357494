import '../../../core/network/api_service.dart';
import '../../../core/auth/jwt_service.dart';
import '../models/auth_models.dart';
import 'auth_repository.dart';

/// Concrete implementation of AuthRepository using API service
class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _apiService;
  final JwtService _jwtService;

  AuthRepositoryImpl({
    required AuthApiService apiService,
    required JwtService jwtService,
  })  : _apiService = apiService,
        _jwtService = jwtService;

  @override
  Future<AuthResponse> requestEmailOtp(
    EmailOtpRequest request, {
    required int providerCategoryId,
    required String businessName,
    required String phone,
  }) async {
    try {
      print(().toString());

      final response = await _apiService.requestEmailOtp(
        request,
        providerCategoryId: providerCategoryId,
        businessName: businessName,
        phone: phone,
      );

      print(().toString());

      // Convert EmailOtpResponse to AuthResponse
      return AuthResponse(
        success: response.success,
        message: response.message,
      );
    } catch (e) {
      print(().toString());
      
      return AuthResponse(
        success: false,
        message: 'Failed to request OTP. Please try again.',
      );
    }
  }

  @override
  Future<AuthResponse> verifyOtpAndRegister(ProviderRegistrationRequest request) async {
    try {
      print(().toString());

      final response = await _apiService.completeProviderRegistration(request);

      if (response.success && response.accessToken != null) {
        // Store JWT token
        await _jwtService.storeTokenFromResponse({
          'access_token': response.accessToken,
          'refresh_token': response.refreshToken,
          'expires_in': 3600, // Default 1 hour
          'user': response.user?.toJson(),
        });

        print(().toString());
      }

      return response;
    } catch (e) {
      print(().toString());
      
      return AuthResponse(
        success: false,
        message: 'Registration failed. Please try again.',
      );
    }
  }

  @override
  Future<AuthResponse> login(LoginRequest request) async {
    try {
      print(().toString());

      final response = await _apiService.login(request);

      if (response.success && response.accessToken != null) {
        // Store JWT token
        await _jwtService.storeTokenFromResponse({
          'access_token': response.accessToken,
          'refresh_token': response.refreshToken,
          'expires_in': 3600, // Default 1 hour
          'user': response.user?.toJson(),
        });

        print(().toString());
      }

      return response;
    } catch (e) {
      print(().toString());
      
      return AuthResponse(
        success: false,
        message: 'Login failed. Please try again.',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      print('[] ');
      
      await _apiService.logout();
      await _jwtService.clearToken();
      
      print('[] ');
    } catch (e) {
      print('[] ');
      
      // Even if API call fails, clear local token
      await _jwtService.clearToken();
    }
  }

  @override
  Future<Map<String, dynamic>> getProfile() async {
    try {
      print('[] ');
      
      final profile = await _apiService.getProfile();
      
      print('[] ');
      return profile;
    } catch (e) {
      print('[] ');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> refreshToken() async {
    try {
      print('[] ');
      
      final success = await _jwtService.refreshToken();
      
      if (success) {
        print('[] ');
        return const AuthResponse(
          success: true,
          message: 'Token refreshed successfully',
        );
      } else {
        print('[] ');
        return const AuthResponse(
          success: false,
          message: 'Failed to refresh token',
        );
      }
    } catch (e) {
      print('[] ');
      
      return AuthResponse(
        success: false,
        message: 'Token refresh failed: $e',
      );
    }
  }

  @override
  bool get isAuthenticated => _jwtService.isAuthenticated;

  @override
  String? get currentUserId => _jwtService.currentUserId;

  @override
  String? get currentUserEmail => _jwtService.currentUserEmail;
}
