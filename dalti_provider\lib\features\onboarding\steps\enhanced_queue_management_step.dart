import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../components/queue/location_selector_widget.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../queues/models/queue_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/opening_hours_models.dart';

/// Enhanced Queue Management step with location selector integration
class EnhancedQueueManagementStep extends ConsumerStatefulWidget {
  const EnhancedQueueManagementStep({super.key});

  @override
  ConsumerState<EnhancedQueueManagementStep> createState() => _EnhancedQueueManagementStepState();
}

class _EnhancedQueueManagementStepState extends ConsumerState<EnhancedQueueManagementStep> {
  final List<QueueWithLocationLink> _queues = [];
  final _formKey = GlobalKey<FormState>();

  // Form controllers for adding new queue
  final _titleController = TextEditingController();

  List<int> _selectedServiceIds = [];
  bool _showAddForm = false;
  LocationSelectorData _locationData = const LocationSelectorData(
    selectedLocationIndex: 0,
    useCustomHours: false,
    customOpeningHours: null,
  );

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  /// Load existing queue data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final queues = onboardingState.data?.queuesWithLocationLinks ?? [];

    if (queues.isNotEmpty) {
      _queues.clear();
      _queues.addAll(queues);

      // Mark step as completed if queues exist
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.markStepCompleted(OnboardingStep.queueManagement, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ref.watch(themeProvider);
    final onboardingState = ref.watch(onboardingNotifierProvider);
    final availableServices = onboardingState.data?.services ?? [];
    final availableLocations = onboardingState.data?.locations ?? [];

    if (availableServices.isEmpty) {
      return _buildNoServicesView(theme);
    }

    if (availableLocations.isEmpty) {
      return _buildNoLocationsView(theme);
    }

    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Step header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colors.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.queue,
                    color: theme.colors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Queue Management',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Queues list
                    if (_queues.isNotEmpty) ...[
                      Text(
                        'Your Queues (${_queues.length})',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Queues list
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _queues.length,
                        separatorBuilder: (context, index) => const SizedBox(height: 8),
                        itemBuilder: (context, index) => _buildQueueCard(_queues[index], index, availableServices, availableLocations),
                      ),

                      const SizedBox(height: 24),
                    ],

                    // Add queue button or form
                    if (!_showAddForm) ...[
                      // Add queue button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showAddForm = true;
                              _selectedServiceIds.clear();
                            });
                          },
                          icon: const Icon(Icons.add),
                          label: Text(_queues.isEmpty ? 'Create Your First Queue' : 'Add Another Queue'),
                        ),
                      ),

                      if (_queues.isEmpty) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colors.primary.withOpacity(0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: theme.colors.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'Create at least one queue to organize your services. Each queue can be assigned to a location with custom hours.',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colors.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ] else ...[
                      // Add queue form
                      _buildAddQueueForm(theme, availableServices, availableLocations),
                    ],
                  ],
                ),
              ),
            ),
            
            // Navigation buttons
            WizardNavigationButtons(
              isValid: _queues.isNotEmpty,
              validationMessage: _queues.isEmpty ? 'Please create at least one queue' : null,
              onNext: () => _saveAndProceed(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoServicesView(ThemeData theme) {
    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.warning_amber_outlined,
                  size: 64,
                  color: theme.colors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'No Services Available',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You need to create at least one service before setting up queues. Please go back to the previous step.',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoLocationsView(ThemeData theme) {
    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.location_off,
                  size: 64,
                  color: theme.colors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'No Locations Available',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'You need to set up at least one location before creating queues. Please go back to the location setup step.',
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build queue card widget
  Widget _buildQueueCard(QueueWithLocationLink queue, int index, List<Service> availableServices, List<Location> availableLocations) {
    final theme = ref.watch(themeProvider);
    final assignedServices = availableServices.where((service) => queue.serviceIndices.contains(service.id)).toList();
    final location = availableLocations.isNotEmpty && queue.locationIndex < availableLocations.length 
        ? availableLocations[queue.locationIndex] 
        : null;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.queue,
                  color: theme.colors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    queue.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => _removeQueue(index),
                  icon: const Icon(Icons.delete_outline),
                  iconSize: 20,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Location info
            if (location != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    location.name,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // Assigned services
            if (assignedServices.isNotEmpty) ...[
              Text(
                'Assigned Services:',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: assignedServices.map((service) => Chip(
                  label: Text(
                    service.title,
                    style: theme.textTheme.bodySmall,
                  ),
                  backgroundColor: theme.colors.primary.withOpacity(0.1),
                )).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build add queue form
  Widget _buildAddQueueForm(ThemeData theme, List<Service> availableServices, List<Location> availableLocations) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form header
              Row(
                children: [
                  Icon(
                    Icons.add_circle_outline,
                    color: theme.colors.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Create New Queue',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _cancelAddQueue,
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Queue title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Queue Name *',
                  hintText: 'e.g., General Queue, VIP Queue, Walk-ins',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.queue),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Queue name is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Name must be at least 2 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Service selection
              Text(
                'Assign Services *',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Select which services will be available in this queue:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colors.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 12),

              // Services checkboxes
              ...availableServices.map((service) => CheckboxListTile(
                title: Text(service.title),
                subtitle: Text('${service.duration} min • ${service.price?.toStringAsFixed(0) ?? '0'} DA'),
                value: _selectedServiceIds.contains(service.id),
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedServiceIds.add(service.id);
                    } else {
                      _selectedServiceIds.remove(service.id);
                    }
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              )),

              if (_selectedServiceIds.isEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'Please select at least one service',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colors.error,
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Location selector using reusable component
              LocationSelectorWidget(
                initialData: _locationData,
                availableLocations: availableLocations,
                onChanged: _onLocationDataChanged,
                title: 'Queue Location & Hours',
                subtitle: 'Select location and configure operating hours for this queue',
              ),

              const SizedBox(height: 24),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _cancelAddQueue,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _selectedServiceIds.isNotEmpty ? _addQueue : null,
                      child: const Text('Create Queue'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle location data changes
  void _onLocationDataChanged(LocationSelectorData locationData) {
    setState(() {
      _locationData = locationData;
    });
  }

  /// Add new queue
  void _addQueue() {
    if (!_formKey.currentState!.validate() || _selectedServiceIds.isEmpty) {
      return;
    }

    final queue = QueueWithLocationLink(
      title: _titleController.text.trim(),
      serviceIndices: List.from(_selectedServiceIds),
      locationIndex: _locationData.selectedLocationIndex,
      customOpeningHours: _locationData.useCustomHours ? _locationData.customOpeningHours : null,
    );

    setState(() {
      _queues.add(queue);
      _showAddForm = false;
    });

    _clearForm();
    _updateStepCompletion();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text('Queue "${queue.title}" created successfully!'),
          ],
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Remove queue
  void _removeQueue(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Queue'),
        content: Text('Are you sure you want to remove "${_queues[index].title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _queues.removeAt(index);
              });
              _updateStepCompletion();
            },
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  /// Cancel add queue form
  void _cancelAddQueue() {
    setState(() {
      _showAddForm = false;
    });
    _clearForm();
  }

  /// Clear form fields
  void _clearForm() {
    _titleController.clear();
    setState(() {
      _selectedServiceIds.clear();
      _locationData = const LocationSelectorData(
        selectedLocationIndex: 0,
        useCustomHours: false,
        customOpeningHours: null,
      );
    });
  }

  /// Update step completion status
  void _updateStepCompletion() {
    final isValid = _queues.isNotEmpty;
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.markStepCompleted(OnboardingStep.queueManagement, isValid);
    
    if (isValid) {
      _saveQueuesData();
    }
  }

  /// Save queues data
  void _saveQueuesData() {
    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveQueuesWithLocationLinks(_queues);
  }

  /// Save and proceed to next step
  void _saveAndProceed() {
    if (_queues.isNotEmpty) {
      _saveQueuesData();
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.goToNextStep();
    }
  }
}
