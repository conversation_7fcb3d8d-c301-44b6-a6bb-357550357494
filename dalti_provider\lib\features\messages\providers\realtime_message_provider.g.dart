// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'realtime_message_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$realtimeUnreadCountHash() =>
    r'11f18d6fcb0f9648a0f03cb265e808542831120d';

/// Real-time unread messages count provider
///
/// Copied from [realtimeUnreadCount].
@ProviderFor(realtimeUnreadCount)
final realtimeUnreadCountProvider = AutoDisposeStreamProvider<int>.internal(
  realtimeUnreadCount,
  name: r'realtimeUnreadCountProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeUnreadCountHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RealtimeUnreadCountRef = AutoDisposeStreamProviderRef<int>;
String _$messageConnectionStatusHash() =>
    r'9fccd16f40586623c01b7e63eb1f9c2aadf3726d';

/// WebSocket connection status provider for messages
///
/// Copied from [messageConnectionStatus].
@ProviderFor(messageConnectionStatus)
final messageConnectionStatusProvider =
    AutoDisposeStreamProvider<bool>.internal(
  messageConnectionStatus,
  name: r'messageConnectionStatusProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageConnectionStatusHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MessageConnectionStatusRef = AutoDisposeStreamProviderRef<bool>;
String _$newMessageNotificationHash() =>
    r'efc0b29374d2ecff963586ed3c1f08d35be23bcc';

/// Provider for real-time message notifications
///
/// Copied from [newMessageNotification].
@ProviderFor(newMessageNotification)
final newMessageNotificationProvider =
    AutoDisposeStreamProvider<Message?>.internal(
  newMessageNotification,
  name: r'newMessageNotificationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$newMessageNotificationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NewMessageNotificationRef = AutoDisposeStreamProviderRef<Message?>;
String _$realtimeConversationsHash() =>
    r'56d49ab4bcac1a9663b1dc7aeb296359212845ba';

/// Enhanced conversations provider with real-time updates
///
/// Copied from [RealtimeConversations].
@ProviderFor(RealtimeConversations)
final realtimeConversationsProvider = AutoDisposeStreamNotifierProvider<
    RealtimeConversations, List<Conversation>>.internal(
  RealtimeConversations.new,
  name: r'realtimeConversationsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$realtimeConversationsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RealtimeConversations = AutoDisposeStreamNotifier<List<Conversation>>;
String _$realtimeMessagesHash() => r'f005d9272982e373fca2eda6e4f8a1011de313c9';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$RealtimeMessages
    extends BuildlessAutoDisposeStreamNotifier<List<Message>> {
  late final String conversationId;

  Stream<List<Message>> build(
    String conversationId,
  );
}

/// Enhanced messages provider for a specific conversation with real-time updates
///
/// Copied from [RealtimeMessages].
@ProviderFor(RealtimeMessages)
const realtimeMessagesProvider = RealtimeMessagesFamily();

/// Enhanced messages provider for a specific conversation with real-time updates
///
/// Copied from [RealtimeMessages].
class RealtimeMessagesFamily extends Family<AsyncValue<List<Message>>> {
  /// Enhanced messages provider for a specific conversation with real-time updates
  ///
  /// Copied from [RealtimeMessages].
  const RealtimeMessagesFamily();

  /// Enhanced messages provider for a specific conversation with real-time updates
  ///
  /// Copied from [RealtimeMessages].
  RealtimeMessagesProvider call(
    String conversationId,
  ) {
    return RealtimeMessagesProvider(
      conversationId,
    );
  }

  @override
  RealtimeMessagesProvider getProviderOverride(
    covariant RealtimeMessagesProvider provider,
  ) {
    return call(
      provider.conversationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'realtimeMessagesProvider';
}

/// Enhanced messages provider for a specific conversation with real-time updates
///
/// Copied from [RealtimeMessages].
class RealtimeMessagesProvider extends AutoDisposeStreamNotifierProviderImpl<
    RealtimeMessages, List<Message>> {
  /// Enhanced messages provider for a specific conversation with real-time updates
  ///
  /// Copied from [RealtimeMessages].
  RealtimeMessagesProvider(
    String conversationId,
  ) : this._internal(
          () => RealtimeMessages()..conversationId = conversationId,
          from: realtimeMessagesProvider,
          name: r'realtimeMessagesProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$realtimeMessagesHash,
          dependencies: RealtimeMessagesFamily._dependencies,
          allTransitiveDependencies:
              RealtimeMessagesFamily._allTransitiveDependencies,
          conversationId: conversationId,
        );

  RealtimeMessagesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.conversationId,
  }) : super.internal();

  final String conversationId;

  @override
  Stream<List<Message>> runNotifierBuild(
    covariant RealtimeMessages notifier,
  ) {
    return notifier.build(
      conversationId,
    );
  }

  @override
  Override overrideWith(RealtimeMessages Function() create) {
    return ProviderOverride(
      origin: this,
      override: RealtimeMessagesProvider._internal(
        () => create()..conversationId = conversationId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        conversationId: conversationId,
      ),
    );
  }

  @override
  AutoDisposeStreamNotifierProviderElement<RealtimeMessages, List<Message>>
      createElement() {
    return _RealtimeMessagesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RealtimeMessagesProvider &&
        other.conversationId == conversationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, conversationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RealtimeMessagesRef
    on AutoDisposeStreamNotifierProviderRef<List<Message>> {
  /// The parameter `conversationId` of this provider.
  String get conversationId;
}

class _RealtimeMessagesProviderElement
    extends AutoDisposeStreamNotifierProviderElement<RealtimeMessages,
        List<Message>> with RealtimeMessagesRef {
  _RealtimeMessagesProviderElement(super.provider);

  @override
  String get conversationId =>
      (origin as RealtimeMessagesProvider).conversationId;
}

String _$conversationManagerHash() =>
    r'0c0e3e8688a77f9ba564ad3fe9833af8f163667d';

/// Provider for managing conversation state
///
/// Copied from [ConversationManager].
@ProviderFor(ConversationManager)
final conversationManagerProvider = AutoDisposeNotifierProvider<
    ConversationManager, Map<String, bool>>.internal(
  ConversationManager.new,
  name: r'conversationManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationManager = AutoDisposeNotifier<Map<String, bool>>;
String _$messageSendingStateHash() =>
    r'864b5c332ce5c3078e9991f08ce9f493adb969dd';

/// Provider for message sending state
///
/// Copied from [MessageSendingState].
@ProviderFor(MessageSendingState)
final messageSendingStateProvider = AutoDisposeNotifierProvider<
    MessageSendingState, Map<String, bool>>.internal(
  MessageSendingState.new,
  name: r'messageSendingStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageSendingStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MessageSendingState = AutoDisposeNotifier<Map<String, bool>>;
String _$conversationSearchHash() =>
    r'acd9a7facb0d1312882521434b92ae212811f969';

/// Provider for conversation search functionality
///
/// Copied from [ConversationSearch].
@ProviderFor(ConversationSearch)
final conversationSearchProvider =
    AutoDisposeNotifierProvider<ConversationSearch, String>.internal(
  ConversationSearch.new,
  name: r'conversationSearchProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$conversationSearchHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ConversationSearch = AutoDisposeNotifier<String>;
String _$messageSearchHash() => r'065b7d897bbb6f8dc66802de8c0ba19b08563d65';

/// Provider for message search functionality
///
/// Copied from [MessageSearch].
@ProviderFor(MessageSearch)
final messageSearchProvider =
    AutoDisposeNotifierProvider<MessageSearch, Map<String, String>>.internal(
  MessageSearch.new,
  name: r'messageSearchProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$messageSearchHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MessageSearch = AutoDisposeNotifier<Map<String, String>>;
String _$typingIndicatorsHash() => r'6d918444758d09e9b5975a505e1832217ccdab6c';

/// Provider for conversation typing indicators
///
/// Copied from [TypingIndicators].
@ProviderFor(TypingIndicators)
final typingIndicatorsProvider = AutoDisposeNotifierProvider<TypingIndicators,
    Map<String, Set<String>>>.internal(
  TypingIndicators.new,
  name: r'typingIndicatorsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$typingIndicatorsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TypingIndicators = AutoDisposeNotifier<Map<String, Set<String>>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
