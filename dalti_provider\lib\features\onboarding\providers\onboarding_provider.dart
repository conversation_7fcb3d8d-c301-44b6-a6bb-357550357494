import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/local_storage_service.dart';
import '../models/onboarding_models.dart';
import '../repository/onboarding_repository.dart';
import '../repository/onboarding_repository_impl.dart';
import '../services/onboarding_api_service.dart';
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../schedules/models/schedule_models.dart';
import '../../queues/models/queue_models.dart';
import '../../auth/providers/auth_provider.dart';
import '../widgets/opening_hours_widget.dart' as opening_hours;
part 'onboarding_provider.g.dart';

/// Provider for OnboardingApiService
@riverpod
OnboardingApiService onboardingApiService(OnboardingApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return OnboardingApiService(httpClient);
}

/// Provider for OnboardingRepository
@riverpod
OnboardingRepository onboardingRepository(OnboardingRepositoryRef ref) {
  final apiService = ref.watch(onboardingApiServiceProvider);
  return OnboardingRepositoryImpl(apiService: apiService);
}

/// Provider to check if user needs onboarding
@riverpod
Future<bool> needsOnboarding(NeedsOnboardingRef ref) async {
  try {
    // Check localStorage first
    final isSkipped = await LocalStorageService.isOnboardingSkipped;
    final isCompleted = await LocalStorageService.isSetupCompleted;

    if (isSkipped || isCompleted) {
      print(
        '[needsOnboardingProvider] Onboarding skipped or setup completed (localStorage)',
      );
      return false;
    }

    // Check auth state for setup completion
    final authState = ref.watch(authNotifierProvider);
    final provider = authState.provider;

    // If provider exists and setup is complete, no onboarding needed
    if (provider != null && provider.isSetupComplete) {
      print('[needsOnboardingProvider] Setup completed (API)');
      await LocalStorageService.setSetupCompleted(true);
      return false;
    }

    // Check with server for additional validation
    final apiService = ref.watch(onboardingApiServiceProvider);
    final needsSetup = await apiService.checkOnboardingStatus();

    print(
      '[needsOnboardingProvider] Server says needs onboarding: $needsSetup',
    );
    return needsSetup;
  } catch (e) {
    print('[needsOnboardingProvider] Error checking onboarding status: $e');
    // On error, check localStorage as fallback
    return await LocalStorageService.needsOnboarding;
  }
}

/// Provider for onboarding state management
@riverpod
class OnboardingNotifier extends _$OnboardingNotifier {
  // Flag to prevent operations during completion
  bool _isCompletionInProgress = false;

  @override
  OnboardingState build() {
    // Return initial state and trigger async loading
    Future.microtask(() => _loadProgress());
    return const OnboardingState();
  }

  /// Check if completion is in progress
  bool get isCompletionInProgress => _isCompletionInProgress;

  /// Set completion in progress flag
  void setCompletionInProgress(bool inProgress) {
    _isCompletionInProgress = inProgress;
    print('[OnboardingProvider] Completion in progress: $inProgress');
  }

  /// Load existing onboarding progress
  Future<void> _loadProgress() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final repository = ref.read(onboardingRepositoryProvider);
      final data = await repository.loadProgress();

      if (data != null) {
        state = state.copyWith(
          isLoading: false,
          data: data,
          canProceed: _canProceedFromStep(data.currentStep, data),
          canGoBack: data.currentStep != OnboardingStep.businessProfile,
        );
      } else {
        // Initialize new onboarding
        final newData = OnboardingData(startedAt: DateTime.now());
        state = state.copyWith(
          isLoading: false,
          data: newData,
          canProceed: false,
          canGoBack: false,
        );
      }
    } catch (e) {
      print('[OnboardingProvider] Error loading progress: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load onboarding progress: $e',
      );
    }
  }

  /// Start or restart onboarding
  Future<void> startOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final repository = ref.read(onboardingRepositoryProvider);

      // Clear any existing progress
      await repository.clearProgress();

      // Create new onboarding data
      final newData = OnboardingData(startedAt: DateTime.now());

      // Save initial progress
      await repository.saveProgress(newData);

      state = state.copyWith(
        isLoading: false,
        data: newData,
        canProceed: false,
        canGoBack: false,
      );

      print('[OnboardingProvider] Onboarding started');
    } catch (e) {
      print('[OnboardingProvider] Error starting onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to start onboarding: $e',
      );
    }
  }

  /// Move to next step
  Future<void> nextStep() async {
    final currentData = state.data;
    if (currentData == null) return;

    final nextStep = currentData.currentStep.next;
    if (nextStep == null) return;

    await goToStep(nextStep);
  }

  /// Move to previous step
  Future<void> previousStep() async {
    final currentData = state.data;
    if (currentData == null) return;

    final previousStep = currentData.currentStep.previous;
    if (previousStep == null) return;

    await goToStep(previousStep);
  }

  /// Go to specific step
  Future<void> goToStep(OnboardingStep step) async {
    try {
      final currentData = state.data;
      if (currentData == null) return;

      state = state.copyWith(isLoading: true, error: null);

      final updatedData = currentData.copyWith(
        currentStep: step,
        stepIndex: step.index,
      );

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isLoading: false,
        data: updatedData,
        canProceed: _canProceedFromStep(step, updatedData),
        canGoBack: step != OnboardingStep.businessProfile,
      );

      print('[OnboardingProvider] Moved to step: $step');
    } catch (e) {
      print('[OnboardingProvider] Error going to step: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to navigate to step: $e',
      );
    }
  }

  /// Save business profile
  Future<void> saveBusinessProfile(BusinessProfile profile) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(businessProfile: profile);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);
      await repository.saveBusinessProfile(profile);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: true,
      );

      print('[OnboardingProvider] Business profile saved');
    } catch (e) {
      print('[OnboardingProvider] Error saving business profile: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save business profile: $e',
      );
    }
  }

  /// Save primary location
  Future<void> savePrimaryLocation(Location location) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(primaryLocation: location);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: true,
      );

      print('[OnboardingProvider] Primary location saved');
    } catch (e) {
      print('[OnboardingProvider] Error saving primary location: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save primary location: $e',
      );
    }
  }

  /// Save services
  Future<void> saveServices(List<Service> services) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(services: services);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: services.isNotEmpty,
      );

      print('[OnboardingProvider] Services saved: ${services.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving services: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save services: $e',
      );
    }
  }

  /// Save schedules
  Future<void> saveSchedules(List<Schedule> schedules) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(schedules: schedules);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: schedules.isNotEmpty,
      );

      print('[OnboardingProvider] Schedules saved: ${schedules.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving schedules: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save schedules: $e',
      );
    }
  }

  /// Convert opening hours from TimeSlot format to Schedule objects
  List<Schedule> convertOpeningHoursToSchedules(
    Map<String, List<opening_hours.TimeSlot>> openingHours,
    int locationId,
  ) {
    final schedules = <Schedule>[];

    // Map day names to dayOfWeek indices (0=Sunday, 6=Saturday)
    const dayNameToIndex = {
      'Sunday': 0,
      'Monday': 1,
      'Tuesday': 2,
      'Wednesday': 3,
      'Thursday': 4,
      'Friday': 5,
      'Saturday': 6,
    };

    for (final entry in openingHours.entries) {
      final dayName = entry.key;
      final timeSlots = entry.value;
      final dayOfWeek = dayNameToIndex[dayName];

      if (dayOfWeek == null || timeSlots.isEmpty) continue;

      // For now, use the first time slot for each day
      // TODO: Support multiple time slots per day if needed
      final firstTimeSlot = timeSlots.first;

      final schedule = Schedule(
        id: 0, // Will be assigned by the server
        dayOfWeek: dayOfWeek,
        startTime: _formatTimeOfDay(firstTimeSlot.from),
        endTime: _formatTimeOfDay(firstTimeSlot.to),
        isActive: true,
        locationId: locationId,
      );

      schedules.add(schedule);
    }

    return schedules;
  }

  /// Format TimeOfDay to HH:MM string format
  String _formatTimeOfDay(TimeOfDay timeOfDay) {
    final hour = timeOfDay.hour.toString().padLeft(2, '0');
    final minute = timeOfDay.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Save location with opening hours (combined method)
  Future<void> saveLocationWithOpeningHours(
    Location location,
    Map<String, List<opening_hours.TimeSlot>> openingHours,
  ) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      // First save the location
      await savePrimaryLocation(location);

      // Convert opening hours to schedules and save them
      final schedules = convertOpeningHoursToSchedules(
        openingHours,
        location.id,
      );
      await saveSchedules(schedules);

      print(
        '[OnboardingProvider] Location and opening hours saved successfully',
      );
    } catch (e) {
      print(
        '[OnboardingProvider] Error saving location with opening hours: $e',
      );
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save location with opening hours: $e',
      );
    }
  }

  /// Save queues with opening hours
  Future<void> saveQueuesWithHours(
    List<QueueWithOpeningHours> queuesWithHours,
  ) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(
        queuesWithHours: queuesWithHours,
      );

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: queuesWithHours.isNotEmpty,
      );

      print(
        '[OnboardingProvider] Queues with hours saved: ${queuesWithHours.length}',
      );
    } catch (e) {
      print('[OnboardingProvider] Error saving queues with hours: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save queues with hours: $e',
      );
    }
  }

  /// Save queues (legacy method for backward compatibility)
  Future<void> saveQueues(List<Queue> queues) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(queues: queues);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: queues.isNotEmpty,
      );

      print('[OnboardingProvider] Queues saved: ${queues.length}');
    } catch (e) {
      print('[OnboardingProvider] Error saving queues: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save queues: $e',
      );
    }
  }

  /// Save queues with location links
  Future<void> saveQueuesWithLocationLinks(
    List<QueueWithLocationLink> queuesWithLocationLinks,
  ) async {
    try {
      state = state.copyWith(isSaving: true, error: null);

      final currentData = state.data;
      if (currentData == null) return;

      final updatedData = currentData.copyWith(
        queuesWithLocationLinks: queuesWithLocationLinks,
      );

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(updatedData);

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
        canProceed: queuesWithLocationLinks.isNotEmpty,
      );

      print(
        '[OnboardingProvider] Queues with location links saved: ${queuesWithLocationLinks.length}',
      );
    } catch (e) {
      print('[OnboardingProvider] Error saving queues with location links: $e');
      state = state.copyWith(
        isSaving: false,
        error: 'Failed to save queues with location links: $e',
      );
    }
  }

  /// Complete onboarding
  Future<OnboardingCompletion?> completeOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final currentData = state.data;
      if (currentData == null) {
        print('[OnboardingProvider] ERROR: No onboarding data available');
        throw Exception('Cannot complete onboarding - no data available');
      }

      // Detailed validation logging
      print('[OnboardingProvider] Validation check:');
      print(
        '  - Business Profile: ${currentData.businessProfile != null ? "✅" : "❌"}',
      );
      print(
        '  - Primary Location: ${currentData.primaryLocation != null ? "✅" : "❌"}',
      );
      print(
        '  - Services: ${currentData.services.isNotEmpty ? "✅ (${currentData.services.length})" : "❌"}',
      );
      print(
        '  - Queues: ${currentData.queues.isNotEmpty ? "✅ (${currentData.queues.length})" : "❌"}',
      );
      print(
        '  - Queues with Hours: ${currentData.queuesWithHours.isNotEmpty ? "✅ (${currentData.queuesWithHours.length})" : "❌"}',
      );
      print(
        '  - Queues with Location Links: ${currentData.queuesWithLocationLinks.isNotEmpty ? "✅ (${currentData.queuesWithLocationLinks.length})" : "❌"}',
      );
      print('  - Can Complete: ${currentData.canComplete ? "✅" : "❌"}');

      if (!currentData.canComplete) {
        final missingItems = <String>[];
        if (currentData.businessProfile == null)
          missingItems.add('Business Profile');
        if (currentData.primaryLocation == null)
          missingItems.add('Primary Location');
        if (currentData.services.isEmpty) missingItems.add('Services');
        if (currentData.queuesWithHours.isEmpty)
          missingItems.add('Queues with Hours');

        final errorMsg =
            'Cannot complete onboarding - missing: ${missingItems.join(", ")}';
        print('[OnboardingProvider] ERROR: $errorMsg');
        throw Exception(errorMsg);
      }

      final repository = ref.read(onboardingRepositoryProvider);
      final completion = await repository.submitOnboarding(currentData);

      // Don't update state immediately - let the UI handle navigation first
      // The completion screen will handle the final state update
      state = state.copyWith(isLoading: false);

      print(
        '[OnboardingProvider] Onboarding completed successfully - API call done',
      );

      // Save completion data in background without updating state
      final completedData = currentData.copyWith(
        isCompleted: true,
        currentStep: OnboardingStep.completed,
        completedAt: DateTime.now(),
      );

      // Save progress in background
      repository.saveProgress(completedData).catchError((e) {
        print(
          '[OnboardingProvider] Warning: Failed to save completion progress: $e',
        );
      });

      return completion;
    } catch (e) {
      print('[OnboardingProvider] Error completing onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to complete onboarding: $e',
      );
      return null;
    }
  }

  /// Finalize onboarding completion (called from completion screen)
  Future<void> finalizeCompletion() async {
    try {
      final currentData = state.data;
      if (currentData == null) return;

      final completedData = currentData.copyWith(
        isCompleted: true,
        currentStep: OnboardingStep.completed,
        completedAt: DateTime.now(),
      );

      state = state.copyWith(
        data: completedData,
        canProceed: false,
        canGoBack: false,
      );

      print('[OnboardingProvider] Onboarding completion finalized');
    } catch (e) {
      print('[OnboardingProvider] Error finalizing completion: $e');
    }
  }

  /// Skip onboarding
  Future<void> skipOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final currentData =
          state.data ?? OnboardingData(startedAt: DateTime.now());

      // Update state to mark as skipped
      final skippedData = currentData.copyWith(
        isCompleted: true,
        currentStep: OnboardingStep.completed,
        completedAt: DateTime.now(),
        lastUpdatedAt: DateTime.now(),
      );

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.saveProgress(skippedData);

      state = state.copyWith(
        isLoading: false,
        data: skippedData,
        canProceed: false,
        canGoBack: false,
      );

      print('[OnboardingProvider] Onboarding skipped successfully');
    } catch (e) {
      print('[OnboardingProvider] Error skipping onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to skip onboarding: $e',
      );
    }
  }

  /// Validate current step
  Future<StepValidationResult> validateCurrentStep(
    Map<String, dynamic> data,
  ) async {
    try {
      final currentData = state.data;
      if (currentData == null) {
        return const StepValidationResult(
          isValid: false,
          errors: ['No onboarding data available'],
        );
      }

      final repository = ref.read(onboardingRepositoryProvider);
      return await repository.validateStep(currentData.currentStep, data);
    } catch (e) {
      print('[OnboardingProvider] Error validating step: $e');
      return StepValidationResult(
        isValid: false,
        errors: ['Validation failed: $e'],
      );
    }
  }

  /// Check if can proceed from current step (public method)
  bool canProceedFromStep(OnboardingStep step, OnboardingData data) {
    return _canProceedFromStep(step, data);
  }

  /// Check if can proceed from current step (private implementation)
  bool _canProceedFromStep(OnboardingStep step, OnboardingData data) {
    switch (step) {
      case OnboardingStep.welcome:
        return true; // Welcome step is always considered complete
      case OnboardingStep.businessProfile:
        return data.businessProfile != null;
      case OnboardingStep.locationSetup:
        // Enhanced validation for location step
        final location = data.primaryLocation;
        return location != null &&
            location.city.isNotEmpty &&
            location.address.isNotEmpty &&
            location.latitude != null &&
            location.longitude != null;
      case OnboardingStep.serviceCreation:
        return data.services.isNotEmpty;
      case OnboardingStep.queueManagement:
        return data.queues.isNotEmpty || data.queuesWithHours.isNotEmpty;
      case OnboardingStep.summary:
        return data.canComplete;
      case OnboardingStep.completed:
        return false;
    }
  }

  /// Reset onboarding
  Future<void> resetOnboarding() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final repository = ref.read(onboardingRepositoryProvider);
      await repository.resetOnboarding();

      await startOnboarding();

      print('[OnboardingProvider] Onboarding reset successfully');
    } catch (e) {
      print('[OnboardingProvider] Error resetting onboarding: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to reset onboarding: $e',
      );
    }
  }
}

/// Provider for business categories
@riverpod
Future<List<Map<String, dynamic>>> businessCategories(
  BusinessCategoriesRef ref,
) async {
  try {
    final apiService = ref.watch(onboardingApiServiceProvider);
    return await apiService.getBusinessCategories();
  } catch (e) {
    print('[OnboardingProvider] Error getting business categories: $e');
    return [];
  }
}

/// Provider for Algerian cities
@riverpod
Future<List<String>> algerianCities(AlgerianCitiesRef ref) async {
  try {
    final apiService = ref.watch(onboardingApiServiceProvider);
    return await apiService.getAlgerianCities();
  } catch (e) {
    print('[OnboardingProvider] Error getting Algerian cities: $e');
    return [];
  }
}
