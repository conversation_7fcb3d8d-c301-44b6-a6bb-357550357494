import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/app_colors.dart';
import '../models/customer_models.dart';
import '../providers/customer_provider.dart';
import '../widgets/customer_list_item.dart';
import '../widgets/customer_search_bar.dart';
import '../widgets/customer_filter_sheet.dart';

/// Customer directory screen showing list of customers with search and filter
class CustomerDirectoryScreen extends ConsumerStatefulWidget {
  const CustomerDirectoryScreen({super.key});

  @override
  ConsumerState<CustomerDirectoryScreen> createState() =>
      _CustomerDirectoryScreenState();
}

class _CustomerDirectoryScreenState
    extends ConsumerState<CustomerDirectoryScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load customers when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(customerNotifierProvider.notifier).loadCustomers();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      // Load more when 80% scrolled
      ref.read(customerNotifierProvider.notifier).loadMoreCustomers();
    }
  }

  void _onSearch(String query) {
    ref.read(customerNotifierProvider.notifier).searchCustomers(query);
  }

  void _onFilter() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => CustomerFilterSheet(
            onApplyFilters: (filters) {
              ref
                  .read(customerNotifierProvider.notifier)
                  .filterCustomers(filters);
            },
          ),
    );
  }

  Future<void> _onRefresh() async {
    await ref.read(customerNotifierProvider.notifier).refreshCustomers();
  }

  void _onAddCustomer() {
    context.push('/customers/new');
  }

  void _onCustomerTap(Customer customer) {
    context.push('/customers/${customer.id}');
  }

  void _onEditCustomer(Customer customer) {
    context.push('/customers/${customer.id}/edit');
  }

  void _onDeleteCustomer(Customer customer) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Customer'),
            content: Text(
              'Are you sure you want to delete ${customer.firstName} ${customer.lastName}? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _deleteCustomer(customer);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteCustomer(Customer customer) async {
    try {
      final repository = ref.read(customerRepositoryProvider);
      await repository.deleteCustomer(customer.id);

      // Remove from customer list
      ref.read(customerNotifierProvider.notifier).removeCustomer(customer.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Customer ${customer.firstName} ${customer.lastName} deleted successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete customer: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onCallCustomer(Customer customer) async {
    if (customer.phoneNumber == null || customer.phoneNumber!.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Customer has no phone number'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return;
    }

    final phoneUrl = Uri.parse('tel:+213${customer.phoneNumber}');
    if (await canLaunchUrl(phoneUrl)) {
      await launchUrl(phoneUrl);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Could not launch phone app'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onEmailCustomer(Customer customer) async {
    final emailUrl = Uri.parse('mailto:${customer.email}');
    if (await canLaunchUrl(emailUrl)) {
      await launchUrl(emailUrl);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Could not launch email app'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final customerState = ref.watch(customerNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        // title: const Text('Customers'),
        actions: [
          IconButton(
            onPressed: _onFilter,
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter customers',
          ),
          IconButton(
            onPressed: _onAddCustomer,
            icon: const Icon(Icons.person_add),
            tooltip: 'Add customer',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Container(
            // color: Theme.of(context).colorScheme.primary,
            color: Theme.of(context).colorScheme.surfaceVariant,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
              child: CustomerSearchBar(
                controller: _searchController,
                onSearch: _onSearch,
                onClear: () {
                  _searchController.clear();
                  ref.read(customerNotifierProvider.notifier).clearFilters();
                },
              ),
            ),
          ),

          // Customer count and filters info
          if (customerState.hasData || customerState.currentFilters != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              color: Theme.of(context).colorScheme.surfaceVariant,
              child: Row(
                children: [
                  Text(
                    '${customerState.totalCount} customers',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (customerState.currentFilters != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Filtered',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color:
                              Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                  const Spacer(),
                  if (customerState.currentFilters != null)
                    TextButton(
                      onPressed: () {
                        ref
                            .read(customerNotifierProvider.notifier)
                            .clearFilters();
                      },
                      child: Text(
                        'Clear filters',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                ],
              ),
            ),

          // Customer list
          Expanded(
            child: RefreshIndicator(
              onRefresh: _onRefresh,
              child: _buildCustomerList(customerState),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onAddCustomer,
        child: const Icon(Icons.person_add),
      ),
    );
  }

  Widget _buildCustomerList(CustomerState state) {
    if (state.isLoading && !state.hasData) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.hasError && !state.hasData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load customers',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.error ?? 'Unknown error occurred',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _onRefresh, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (!state.hasData) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            Text(
              'No customers found',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first customer to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _onAddCustomer,
              icon: const Icon(Icons.person_add),
              label: const Text('Add Customer'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      itemCount: state.customers.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.customers.length) {
          // Loading indicator for pagination
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final customer = state.customers[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: CustomerListItem(
            customer: customer,
            onTap: () => _onCustomerTap(customer),
            onEdit: () => _onEditCustomer(customer),
            onDelete: () => _onDeleteCustomer(customer),
            onCall: () => _onCallCustomer(customer),
            onEmail: () => _onEmailCustomer(customer),
            showActions: true,
          ),
        );
      },
    );
  }
}
