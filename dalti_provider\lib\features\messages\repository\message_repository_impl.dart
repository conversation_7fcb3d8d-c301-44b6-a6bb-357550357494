import 'dart:async';
import '../models/message_models.dart';
import '../services/message_api_service.dart';
import 'message_repository.dart';

/// Implementation of MessageRepository with caching and error handling
class MessageRepositoryImpl implements MessageRepository {
  final MessageApiService _apiService;

  // Cache for conversations
  final Map<String, List<Conversation>> _conversationsCache = {};
  final Map<String, DateTime> _conversationsCacheTimestamp = {};

  // Cache for messages by conversation ID
  final Map<String, List<Message>> _messagesCache = {};
  final Map<String, DateTime> _messagesCacheTimestamp = {};

  // Cache for unread count
  int? _unreadCount;
  DateTime? _unreadCountTimestamp;

  // Cache duration (5 minutes)
  static const Duration _cacheDuration = Duration(minutes: 5);

  // Stream controllers for real-time updates
  final StreamController<List<Conversation>> _conversationsController =
      StreamController<List<Conversation>>.broadcast();
  final Map<String, StreamController<List<Message>>> _messagesControllers = {};
  final StreamController<int> _unreadCountController =
      StreamController<int>.broadcast();

  MessageRepositoryImpl(this._apiService);

  @override
  Future<List<Conversation>> getConversations({
    String? searchQuery,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      // Create cache key based on search query and pagination
      final cacheKey = _createConversationsCacheKey(
        searchQuery,
        page,
        pageSize,
      );

      // Check cache first
      if (_isConversationsCacheValid(cacheKey)) {
        return _conversationsCache[cacheKey]!;
      }

      final response = await _apiService.getConversations(
        searchQuery: searchQuery,
        page: page,
        pageSize: pageSize,
      );

      if (response.success) {
        // Update cache
        _conversationsCache[cacheKey] = response.conversations;
        _conversationsCacheTimestamp[cacheKey] = DateTime.now();

        // Emit to stream
        _conversationsController.add(response.conversations);

        return response.conversations;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to fetch conversations',
        );
      }
    } catch (e) {
      print('[MessageRepository] Error fetching conversations: $e');
      rethrow;
    }
  }

  @override
  Future<Conversation> getConversation(String conversationId) async {
    try {
      final response = await _apiService.getConversation(conversationId);

      if (response.success && response.conversation != null) {
        // Update cache if conversation exists in any cached list
        _updateConversationInCache(response.conversation!);

        return response.conversation!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to fetch conversation',
        );
      }
    } catch (e) {
      print('[MessageRepository] Error fetching conversation: $e');
      rethrow;
    }
  }

  @override
  Future<Conversation> createConversation(
    CreateConversationRequest request,
  ) async {
    try {
      final response = await _apiService.createConversation(request);

      if (response.success && response.conversation != null) {
        // Clear conversations cache to force refresh
        _clearConversationsCache();

        return response.conversation!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to create conversation',
        );
      }
    } catch (e) {
      print('[MessageRepository] Error creating conversation: $e');
      rethrow;
    }
  }

  @override
  Future<List<Message>> getMessages(
    String conversationId, {
    String? searchQuery,
    int page = 1,
    int pageSize = 50,
  }) async {
    try {
      // Create cache key
      final cacheKey = _createMessagesCacheKey(
        conversationId,
        searchQuery,
        page,
        pageSize,
      );

      // Check cache first
      if (_isMessagesCacheValid(cacheKey)) {
        return _messagesCache[cacheKey]!;
      }

      final response = await _apiService.getMessages(
        conversationId,
        searchQuery: searchQuery,
        page: page,
        pageSize: pageSize,
      );

      if (response.success) {
        // Update cache
        _messagesCache[cacheKey] = response.messages;
        _messagesCacheTimestamp[cacheKey] = DateTime.now();

        // Emit to stream
        if (_messagesControllers.containsKey(conversationId)) {
          _messagesControllers[conversationId]!.add(response.messages);
        }

        return response.messages;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch messages');
      }
    } catch (e) {
      print('[MessageRepository] Error fetching messages: $e');
      rethrow;
    }
  }

  @override
  Future<Message> sendMessage(SendMessageRequest request) async {
    try {
      final response = await _apiService.sendMessage(request);

      if (response.success && response.message != null) {
        // Clear messages cache for this conversation to force refresh
        _clearMessagesCacheForConversation(request.conversationId);

        // Clear conversations cache to update last message
        _clearConversationsCache();

        return response.message!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to send message');
      }
    } catch (e) {
      print('[MessageRepository] Error sending message: $e');
      rethrow;
    }
  }

  @override
  Future<bool> markMessagesAsRead(
    String conversationId,
    List<String> messageIds,
  ) async {
    try {
      final result = await _apiService.markMessagesAsRead(
        conversationId,
        messageIds,
      );

      if (result) {
        // Clear caches to reflect read status changes
        _clearMessagesCacheForConversation(conversationId);
        _clearConversationsCache();
        _clearUnreadCountCache();
      }

      return result;
    } catch (e) {
      print('[MessageRepository] Error marking messages as read: $e');
      return false;
    }
  }

  @override
  Future<MarkAsReadResponse> markMessageAsRead(
    MarkAsReadRequest request,
  ) async {
    try {
      final response = await _apiService.markMessageAsRead(request);

      if (response.success) {
        // Clear caches to reflect read status changes
        _clearMessagesCacheForConversation(request.conversationId);
        _clearConversationsCache();
        _clearUnreadCountCache();
      }

      return response;
    } catch (e) {
      print('[MessageRepository] Error marking message as read: $e');
      return MarkAsReadResponse(
        success: false,
        error: MessageError(
          code: 'MARK_READ_ERROR',
          message: 'Error marking message as read',
          details: e.toString(),
        ),
      );
    }
  }

  @override
  Future<bool> archiveConversation(String conversationId) async {
    try {
      final result = await _apiService.archiveConversation(conversationId);

      if (result) {
        _clearConversationsCache();
      }

      return result;
    } catch (e) {
      print('[MessageRepository] Error archiving conversation: $e');
      return false;
    }
  }

  @override
  Future<bool> unarchiveConversation(String conversationId) async {
    try {
      // API doesn't support unarchive operation yet
      print(
        '[MessageRepository] Unarchive conversation not implemented in API',
      );
      return false;
    } catch (e) {
      print('[MessageRepository] Error unarchiving conversation: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteConversation(String conversationId) async {
    try {
      final result = await _apiService.deleteConversation(conversationId);

      if (result) {
        _clearConversationsCache();
        _clearMessagesCacheForConversation(conversationId);
      }

      return result;
    } catch (e) {
      print('[MessageRepository] Error deleting conversation: $e');
      return false;
    }
  }

  @override
  Future<int> getUnreadMessagesCount() async {
    try {
      // Check cache first
      if (_isUnreadCountCacheValid()) {
        return _unreadCount!;
      }

      final count = await _apiService.getUnreadMessagesCount();

      // Update cache
      _unreadCount = count;
      _unreadCountTimestamp = DateTime.now();

      // Emit to stream
      _unreadCountController.add(count);

      return count;
    } catch (e) {
      print('[MessageRepository] Error fetching unread count: $e');
      return 0;
    }
  }

  @override
  Future<List<Conversation>> searchConversations(String query) async {
    return getConversations(searchQuery: query);
  }

  @override
  Future<List<Message>> searchMessages(
    String query, {
    String? conversationId,
  }) async {
    if (conversationId != null) {
      return getMessages(conversationId, searchQuery: query);
    } else {
      // For global message search, we'd need a different API endpoint
      // For now, return empty list
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> getConversationStats() async {
    // This would require a specific API endpoint
    // For now, return basic stats from cached data
    return {
      'totalConversations':
          _conversationsCache.values.expand((list) => list).length,
      'cachedConversations': _conversationsCache.length,
      'cachedMessages': _messagesCache.length,
    };
  }

  @override
  Future<int> getConversationsCount({String? searchQuery}) async {
    // This would require a specific API endpoint or we can estimate from cached data
    final conversations = await getConversations(
      searchQuery: searchQuery,
      pageSize: 1,
    );
    return conversations.length;
  }

  @override
  Future<List<Conversation>> getRecentConversations({int limit = 10}) async {
    // Get recent conversations (simplified without filters)
    return getConversations(pageSize: limit);
  }

  @override
  Future<List<Conversation>> getConversationsWithUnreadMessages({
    int limit = 10,
  }) async {
    // Get conversations and filter for unread messages (simplified)
    final conversations = await getConversations(pageSize: limit);
    return conversations.where((conv) => conv.unreadCount > 0).toList();
  }

  @override
  Future<void> clearCache() async {
    _conversationsCache.clear();
    _conversationsCacheTimestamp.clear();
    _messagesCache.clear();
    _messagesCacheTimestamp.clear();
    _unreadCount = null;
    _unreadCountTimestamp = null;
  }

  @override
  Future<void> refreshConversations() async {
    _clearConversationsCache();
    await getConversations();
  }

  @override
  Stream<List<Conversation>> watchConversations() {
    return _conversationsController.stream;
  }

  @override
  Stream<List<Message>> watchMessages(String conversationId) {
    if (!_messagesControllers.containsKey(conversationId)) {
      _messagesControllers[conversationId] =
          StreamController<List<Message>>.broadcast();
    }
    return _messagesControllers[conversationId]!.stream;
  }

  @override
  Stream<int> watchUnreadMessagesCount() {
    return _unreadCountController.stream;
  }

  // Private helper methods
  String _createConversationsCacheKey(
    String? searchQuery,
    int page,
    int pageSize,
  ) {
    return 'conversations_${searchQuery ?? ''}_${page}_$pageSize';
  }

  String _createMessagesCacheKey(
    String conversationId,
    String? searchQuery,
    int page,
    int pageSize,
  ) {
    return 'messages_${conversationId}_${searchQuery ?? ''}_${page}_$pageSize';
  }

  bool _isConversationsCacheValid(String cacheKey) {
    if (!_conversationsCache.containsKey(cacheKey) ||
        !_conversationsCacheTimestamp.containsKey(cacheKey)) {
      return false;
    }

    final timestamp = _conversationsCacheTimestamp[cacheKey]!;
    return DateTime.now().difference(timestamp) < _cacheDuration;
  }

  bool _isMessagesCacheValid(String cacheKey) {
    if (!_messagesCache.containsKey(cacheKey) ||
        !_messagesCacheTimestamp.containsKey(cacheKey)) {
      return false;
    }

    final timestamp = _messagesCacheTimestamp[cacheKey]!;
    return DateTime.now().difference(timestamp) < _cacheDuration;
  }

  bool _isUnreadCountCacheValid() {
    if (_unreadCount == null || _unreadCountTimestamp == null) {
      return false;
    }

    return DateTime.now().difference(_unreadCountTimestamp!) < _cacheDuration;
  }

  void _updateConversationInCache(Conversation conversation) {
    for (final cacheKey in _conversationsCache.keys) {
      final conversations = _conversationsCache[cacheKey]!;
      final index = conversations.indexWhere((c) => c.id == conversation.id);
      if (index != -1) {
        conversations[index] = conversation;
      }
    }
  }

  void _clearConversationsCache() {
    _conversationsCache.clear();
    _conversationsCacheTimestamp.clear();
  }

  void _clearMessagesCacheForConversation(String conversationId) {
    final keysToRemove =
        _messagesCache.keys
            .where((key) => key.startsWith('messages_$conversationId'))
            .toList();

    for (final key in keysToRemove) {
      _messagesCache.remove(key);
      _messagesCacheTimestamp.remove(key);
    }
  }

  void _clearUnreadCountCache() {
    _unreadCount = null;
    _unreadCountTimestamp = null;
  }

  void dispose() {
    _conversationsController.close();
    for (final controller in _messagesControllers.values) {
      controller.close();
    }
    _messagesControllers.clear();
    _unreadCountController.close();
  }
}
