import 'dart:convert';
import 'dart:html' as html;
import 'dart:async';
import '../models/wizard_persistence_models.dart';
import '../models/onboarding_models.dart';
import '../providers/wizard_navigation_provider.dart';

/// Service for persisting and recovering wizard state
class WizardPersistenceService {
  static const String _defaultStorageKey = 'dalti_wizard_state_v1';
  static const String _configKey = 'dalti_wizard_config';
  static const String _statsKey = 'dalti_wizard_stats';
  
  final StorageConfig _config;
  final String _storageKey;
  
  WizardPersistenceService({
    StorageConfig? config,
    String? storageKey,
  }) : _config = config ?? const StorageConfig(),
       _storageKey = storageKey ?? _defaultStorageKey;

  /// Save wizard state to localStorage
  Future<PersistenceResult> saveState(PersistedWizardState state) async {
    try {
      // Check storage availability
      if (!_isStorageAvailable()) {
        return const PersistenceResult(
          success: false,
          error: 'localStorage is not available',
        );
      }

      // Serialize state to JSON
      final jsonData = state.toJson();
      final jsonString = json.encode(jsonData);
      
      // Check size limits
      final dataSize = jsonString.length;
      if (dataSize > _config.maxStorageSize) {
        return PersistenceResult(
          success: false,
          error: 'Data size ($dataSize bytes) exceeds maximum allowed (${_config.maxStorageSize} bytes)',
        );
      }

      // Compress if enabled
      String finalData = jsonString;
      if (_config.enableCompression) {
        finalData = _compressData(jsonString);
      }

      // Encrypt if enabled
      if (_config.enableEncryption) {
        finalData = _encryptData(finalData);
      }

      // Save to localStorage
      html.window.localStorage[_storageKey] = finalData;
      
      // Update statistics
      await _updateStats(dataSize);
      
      print('[WizardPersistence] State saved successfully (${dataSize} bytes)');
      
      return PersistenceResult(
        success: true,
        savedAt: DateTime.now(),
        dataSize: dataSize,
      );
    } catch (e) {
      print('[WizardPersistence] Error saving state: $e');
      return PersistenceResult(
        success: false,
        error: 'Failed to save state: $e',
      );
    }
  }

  /// Load wizard state from localStorage
  Future<PersistedWizardState?> loadState() async {
    try {
      // Check storage availability
      if (!_isStorageAvailable()) {
        print('[WizardPersistence] localStorage is not available');
        return null;
      }

      // Get data from localStorage
      final rawData = html.window.localStorage[_storageKey];
      if (rawData == null || rawData.isEmpty) {
        print('[WizardPersistence] No saved state found');
        return null;
      }

      // Decrypt if enabled
      String jsonString = rawData;
      if (_config.enableEncryption) {
        jsonString = _decryptData(jsonString);
      }

      // Decompress if enabled
      if (_config.enableCompression) {
        jsonString = _decompressData(jsonString);
      }

      // Parse JSON
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;
      final state = PersistedWizardState.fromJson(jsonData);

      // Validate state
      if (!_validateState(state)) {
        print('[WizardPersistence] Loaded state is invalid');
        return null;
      }

      // Check if migration is needed
      if (state.needsMigration) {
        final migratedState = await _migrateState(state);
        if (migratedState != null) {
          await saveState(migratedState);
          return migratedState;
        }
      }

      print('[WizardPersistence] State loaded successfully');
      return state;
    } catch (e) {
      print('[WizardPersistence] Error loading state: $e');
      return null;
    }
  }

  /// Clear saved wizard state
  Future<bool> clearState() async {
    try {
      if (!_isStorageAvailable()) {
        return false;
      }

      html.window.localStorage.remove(_storageKey);
      print('[WizardPersistence] State cleared successfully');
      return true;
    } catch (e) {
      print('[WizardPersistence] Error clearing state: $e');
      return false;
    }
  }

  /// Check if saved state exists
  bool hasState() {
    if (!_isStorageAvailable()) {
      return false;
    }
    
    final data = html.window.localStorage[_storageKey];
    return data != null && data.isNotEmpty;
  }

  /// Get storage statistics
  Future<StorageStats> getStats() async {
    try {
      final statsData = html.window.localStorage[_statsKey];
      if (statsData != null) {
        final jsonData = json.decode(statsData) as Map<String, dynamic>;
        return StorageStats.fromJson(jsonData);
      }
    } catch (e) {
      print('[WizardPersistence] Error loading stats: $e');
    }

    return const StorageStats(
      totalSize: 0,
      itemCount: 0,
    );
  }

  /// Cleanup old data
  Future<void> cleanup() async {
    try {
      if (!_isStorageAvailable()) {
        return;
      }

      final cutoffDate = DateTime.now().subtract(Duration(days: _config.cleanupAfterDays));
      
      // Check if current state is old
      final state = await loadState();
      if (state != null && state.savedAt.isBefore(cutoffDate)) {
        await clearState();
        print('[WizardPersistence] Cleaned up old state');
      }

      // Update cleanup timestamp
      await _updateStats(0, lastCleanup: DateTime.now());
    } catch (e) {
      print('[WizardPersistence] Error during cleanup: $e');
    }
  }

  /// Check if localStorage is available
  bool _isStorageAvailable() {
    try {
      final testKey = '__test_storage__';
      html.window.localStorage[testKey] = 'test';
      html.window.localStorage.remove(testKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Validate loaded state
  bool _validateState(PersistedWizardState state) {
    // Check if state is not expired
    if (state.isExpired) {
      print('[WizardPersistence] State is expired');
      return false;
    }

    // Check if state has required data
    if (state.onboardingData == null) {
      print('[WizardPersistence] State missing onboarding data');
      return false;
    }

    // Check if step is valid
    if (!OnboardingStep.values.contains(state.currentStep)) {
      print('[WizardPersistence] Invalid current step');
      return false;
    }

    return true;
  }

  /// Migrate state from older version
  Future<PersistedWizardState?> _migrateState(PersistedWizardState oldState) async {
    try {
      print('[WizardPersistence] Migrating state from version ${oldState.version} to 1');
      
      // For now, we only have version 1, so no migration needed
      // In the future, add migration logic here
      
      return oldState.copyWith(version: 1);
    } catch (e) {
      print('[WizardPersistence] Error migrating state: $e');
      return null;
    }
  }

  /// Update storage statistics
  Future<void> _updateStats(int dataSize, {DateTime? lastCleanup}) async {
    try {
      final currentStats = await getStats();
      final updatedStats = currentStats.copyWith(
        totalSize: dataSize,
        itemCount: hasState() ? 1 : 0,
        lastSaved: DateTime.now(),
        lastCleanup: lastCleanup ?? currentStats.lastCleanup,
        keys: hasState() ? [_storageKey] : [],
      );

      final jsonString = json.encode(updatedStats.toJson());
      html.window.localStorage[_statsKey] = jsonString;
    } catch (e) {
      print('[WizardPersistence] Error updating stats: $e');
    }
  }

  /// Compress data (placeholder implementation)
  String _compressData(String data) {
    // For now, just return the data as-is
    // In a real implementation, you might use a compression library
    return data;
  }

  /// Decompress data (placeholder implementation)
  String _decompressData(String data) {
    // For now, just return the data as-is
    return data;
  }

  /// Encrypt data (placeholder implementation)
  String _encryptData(String data) {
    // For now, just return the data as-is
    // In a real implementation, you would use proper encryption
    return data;
  }

  /// Decrypt data (placeholder implementation)
  String _decryptData(String data) {
    // For now, just return the data as-is
    return data;
  }
}

/// Factory for creating persistence service instances
class WizardPersistenceServiceFactory {
  static WizardPersistenceService? _instance;

  /// Get singleton instance
  static WizardPersistenceService getInstance({
    StorageConfig? config,
    String? storageKey,
  }) {
    _instance ??= WizardPersistenceService(
      config: config,
      storageKey: storageKey,
    );
    return _instance!;
  }

  /// Reset singleton (useful for testing)
  static void reset() {
    _instance = null;
  }
}

/// Helper class for creating persistence models from current state
class WizardStateConverter {
  /// Convert current wizard state to persistable format
  static PersistedWizardState fromCurrentState({
    required WizardNavigationState navigationState,
    required OnboardingData onboardingData,
    SessionInfo? sessionInfo,
    AutoSaveSettings? autoSaveSettings,
  }) {
    final navigationData = PersistenceModelFactory.createNavigationData(navigationState);
    
    return PersistenceModelFactory.create(
      currentStep: navigationState.currentStep,
      currentStepIndex: navigationState.currentStepIndex,
      stepCompletionStatus: navigationState.stepCompletionStatus,
      onboardingData: onboardingData,
      navigationData: navigationData,
      sessionInfo: sessionInfo,
      autoSaveSettings: autoSaveSettings,
    );
  }

  /// Convert persisted state back to current state format
  static (WizardNavigationState?, OnboardingData?) toCurrentState(PersistedWizardState persistedState) {
    final navigationState = persistedState.navigationData?.toNavigationState();
    final onboardingData = persistedState.onboardingData;
    
    return (navigationState, onboardingData);
  }
}
