import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../appointments/models/appointment_models.dart';
import '../../appointments/providers/appointment_provider.dart';

/// Widget that displays InProgress appointments with quick access to service sessions
class InProgressAppointmentsCard extends ConsumerStatefulWidget {
  const InProgressAppointmentsCard({super.key});

  @override
  ConsumerState<InProgressAppointmentsCard> createState() =>
      _InProgressAppointmentsCardState();
}

class _InProgressAppointmentsCardState
    extends ConsumerState<InProgressAppointmentsCard> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    // Load appointments when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInProgressAppointments();
      _startAutoRefresh();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// Start auto-refresh timer for InProgress appointments
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (mounted) {
        _loadInProgressAppointments();
      }
    });
  }

  /// Load appointments and filter for InProgress status
  Future<void> _loadInProgressAppointments() async {
    try {
      print('[InProgressAppointments] Loading appointments...');
      await ref.read(appointmentNotifierProvider.notifier).loadAppointments();
      print('[InProgressAppointments] Appointments loaded successfully');
    } catch (e, stackTrace) {
      print('[InProgressAppointments] Error loading appointments: $e');
      print('[InProgressAppointments] Stack trace: $stackTrace');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final appointmentsAsync = ref.watch(appointmentNotifierProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    Icons.play_circle_filled,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Active Service Sessions',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (appointmentsAsync.isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                else
                  IconButton(
                    onPressed: _loadInProgressAppointments,
                    icon: Icon(
                      Icons.refresh,
                      size: 20,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    tooltip: 'Refresh active sessions',
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                    padding: EdgeInsets.zero,
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Content
            appointmentsAsync.when(
              data: (appointments) {
                print(
                  '[InProgressAppointments] Total appointments loaded: ${appointments.length}',
                );
                for (final apt in appointments) {
                  print(
                    '[InProgressAppointments] - ${apt.customerName}: ${apt.status.name}',
                  );
                }

                // Filter for InProgress appointments
                final inProgressAppointments =
                    appointments
                        .where(
                          (apt) => apt.status == AppointmentStatus.inProgress,
                        )
                        .toList();

                print(
                  '[InProgressAppointments] Found ${inProgressAppointments.length} in-progress appointments',
                );

                if (inProgressAppointments.isEmpty) {
                  return _buildNoActiveSessionsState(context);
                }

                return _buildInProgressList(context, inProgressAppointments);
              },
              loading: () => _buildLoadingState(context),
              error: (error, stack) => _buildErrorState(context, error),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the list of InProgress appointments
  Widget _buildInProgressList(
    BuildContext context,
    List<Appointment> appointments,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Show count
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            '${appointments.length} active session${appointments.length == 1 ? '' : 's'}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.orange.shade700,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 12),

        // List of appointments
        ...appointments.map(
          (appointment) => _buildAppointmentTile(context, appointment),
        ),
      ],
    );
  }

  /// Build individual appointment tile
  Widget _buildAppointmentTile(BuildContext context, Appointment appointment) {
    final theme = Theme.of(context);
    final timeFormat = DateFormat('h:mm a');
    final startTimeText = timeFormat.format(appointment.scheduledTime);

    // Calculate session duration based on actual start time
    final now = DateTime.now();
    final actualStartTime =
        appointment.realAppointmentStartTime ?? appointment.scheduledTime;
    final duration = now.difference(actualStartTime);
    final durationText = _formatDuration(duration);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToServiceSession(context, appointment.id),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Customer avatar
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.orange.withValues(alpha: 0.2),
                  child: Text(
                    appointment.customerName.isNotEmpty
                        ? appointment.customerName[0].toUpperCase()
                        : 'C',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Appointment details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment.customerName,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${appointment.serviceName} • Started at $startTimeText',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'Running for $durationText',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.orange.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Action button
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.play_arrow, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Continue',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build no active sessions state
  Widget _buildNoActiveSessionsState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            'No Active Sessions',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'All appointments are completed or scheduled',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(20),
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(BuildContext context, Object error) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
          const SizedBox(height: 12),
          Text(
            'Failed to load active sessions',
            style: theme.textTheme.titleSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: _loadInProgressAppointments,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Navigate to service session screen
  void _navigateToServiceSession(BuildContext context, String appointmentId) {
    context.push('/service/$appointmentId');
  }

  /// Format duration to human readable string
  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}
