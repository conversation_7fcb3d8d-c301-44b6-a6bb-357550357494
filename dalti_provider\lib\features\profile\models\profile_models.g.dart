// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'profile_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProfileCategoryImpl _$$ProfileCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileCategoryImpl(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
    );

Map<String, dynamic> _$$ProfileCategoryImplToJson(
        _$ProfileCategoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
    };

_$ProfileDataImpl _$$ProfileDataImplFromJson(Map<String, dynamic> json) =>
    _$ProfileDataImpl(
      id: (json['id'] as num).toInt(),
      userId: json['userId'] as String,
      title: json['title'] as String?,
      phone: json['phone'] as String?,
      presentation: json['presentation'] as String?,
      isVerified: json['isVerified'] as bool,
      isSetupComplete: json['isSetupComplete'] as bool,
      category: json['category'] == null
          ? null
          : ProfileCategory.fromJson(json['category'] as Map<String, dynamic>),
      averageRating: (json['averageRating'] as num?)?.toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      profilePictureUrl: json['profilePictureUrl'] as String?,
      logoUrl: json['logoUrl'] as String?,
    );

Map<String, dynamic> _$$ProfileDataImplToJson(_$ProfileDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      if (instance.title case final value?) 'title': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.presentation case final value?) 'presentation': value,
      'isVerified': instance.isVerified,
      'isSetupComplete': instance.isSetupComplete,
      if (instance.category?.toJson() case final value?) 'category': value,
      if (instance.averageRating case final value?) 'averageRating': value,
      'totalReviews': instance.totalReviews,
      if (instance.profilePictureUrl case final value?)
        'profilePictureUrl': value,
      if (instance.logoUrl case final value?) 'logoUrl': value,
    };

_$ProfileUpdateRequestImpl _$$ProfileUpdateRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileUpdateRequestImpl(
      title: json['title'] as String?,
      phone: json['phone'] as String?,
      presentation: json['presentation'] as String?,
      providerCategoryId: (json['providerCategoryId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$ProfileUpdateRequestImplToJson(
        _$ProfileUpdateRequestImpl instance) =>
    <String, dynamic>{
      if (instance.title case final value?) 'title': value,
      if (instance.phone case final value?) 'phone': value,
      if (instance.presentation case final value?) 'presentation': value,
      if (instance.providerCategoryId case final value?)
        'providerCategoryId': value,
    };

_$ProfileResponseImpl _$$ProfileResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileResponseImpl(
      success: json['success'] as bool,
      data: json['data'] == null
          ? null
          : ProfileData.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$ProfileResponseImplToJson(
        _$ProfileResponseImpl instance) =>
    <String, dynamic>{
      'success': instance.success,
      if (instance.data?.toJson() case final value?) 'data': value,
      if (instance.message case final value?) 'message': value,
    };

_$ProfileErrorImpl _$$ProfileErrorImplFromJson(Map<String, dynamic> json) =>
    _$ProfileErrorImpl(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as String?,
      validationErrors: (json['validationErrors'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$ProfileErrorImplToJson(_$ProfileErrorImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      if (instance.details case final value?) 'details': value,
      if (instance.validationErrors case final value?)
        'validationErrors': value,
    };

_$ProfilePictureUploadResponseImpl _$$ProfilePictureUploadResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureUploadResponseImpl(
      uploadUrl: json['uploadUrl'] as String,
      uploadFields: json['uploadFields'] as Map<String, dynamic>,
      file: UploadFile.fromJson(json['file'] as Map<String, dynamic>),
      user: UploadUser.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProfilePictureUploadResponseImplToJson(
        _$ProfilePictureUploadResponseImpl instance) =>
    <String, dynamic>{
      'uploadUrl': instance.uploadUrl,
      'uploadFields': instance.uploadFields,
      'file': instance.file.toJson(),
      'user': instance.user.toJson(),
    };

_$UploadFileImpl _$$UploadFileImplFromJson(Map<String, dynamic> json) =>
    _$UploadFileImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      key: json['key'] as String,
    );

Map<String, dynamic> _$$UploadFileImplToJson(_$UploadFileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'key': instance.key,
    };

_$UploadUserImpl _$$UploadUserImplFromJson(Map<String, dynamic> json) =>
    _$UploadUserImpl(
      id: json['id'] as String,
      profilePictureId: json['profilePictureId'] as String,
    );

Map<String, dynamic> _$$UploadUserImplToJson(_$UploadUserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'profilePictureId': instance.profilePictureId,
    };

_$ProfilePictureDetailsImpl _$$ProfilePictureDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureDetailsImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      key: json['key'] as String,
      downloadUrl: json['downloadUrl'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$$ProfilePictureDetailsImplToJson(
        _$ProfilePictureDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'key': instance.key,
      'downloadUrl': instance.downloadUrl,
      'createdAt': instance.createdAt.toIso8601String(),
    };

_$ProfilePictureInfoImpl _$$ProfilePictureInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfilePictureInfoImpl(
      hasProfilePicture: json['hasProfilePicture'] as bool,
      profilePicture: json['profilePicture'] == null
          ? null
          : ProfilePictureDetails.fromJson(
              json['profilePicture'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ProfilePictureInfoImplToJson(
        _$ProfilePictureInfoImpl instance) =>
    <String, dynamic>{
      'hasProfilePicture': instance.hasProfilePicture,
      if (instance.profilePicture?.toJson() case final value?)
        'profilePicture': value,
    };
