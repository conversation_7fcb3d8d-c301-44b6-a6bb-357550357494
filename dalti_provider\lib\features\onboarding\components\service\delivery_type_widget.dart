import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Model for delivery type data
class DeliveryTypeData {
  final String deliveryType; // "at_location", "at_customer", "both"
  final List<String> servedRegions;

  const DeliveryTypeData({
    required this.deliveryType,
    required this.servedRegions,
  });

  DeliveryTypeData copyWith({
    String? deliveryType,
    List<String>? servedRegions,
  }) {
    return DeliveryTypeData(
      deliveryType: deliveryType ?? this.deliveryType,
      servedRegions: servedRegions ?? this.servedRegions,
    );
  }

  /// Check if delivery type requires region selection
  bool get requiresRegions {
    return deliveryType == 'at_customer' || deliveryType == 'both';
  }

  /// Check if delivery type is valid
  bool get isValid {
    if (!requiresRegions) return true;
    return servedRegions.isNotEmpty;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeliveryTypeData &&
        other.deliveryType == deliveryType &&
        _listEquals(other.servedRegions, servedRegions);
  }

  @override
  int get hashCode => deliveryType.hashCode ^ servedRegions.hashCode;

  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}

/// Reusable widget for managing service delivery types and served regions
/// Used in Service creation step
class DeliveryTypeWidget extends ConsumerStatefulWidget {
  final DeliveryTypeData? initialData;
  final ValueChanged<DeliveryTypeData> onChanged;
  final String? title;
  final String? subtitle;
  final bool enabled;

  const DeliveryTypeWidget({
    super.key,
    required this.onChanged,
    this.initialData,
    this.title,
    this.subtitle,
    this.enabled = true,
  });

  @override
  ConsumerState<DeliveryTypeWidget> createState() => _DeliveryTypeWidgetState();
}

class _DeliveryTypeWidgetState extends ConsumerState<DeliveryTypeWidget> {
  late DeliveryTypeData _deliveryData;
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredWilayas = [];
  bool _isDropdownOpen = false;

  // Algerian wilayas (provinces)
  static const List<String> _algerianWilayas = [
    'Adrar',
    'Chlef',
    'Laghouat',
    'Oum El Bouaghi',
    'Batna',
    'Béjaïa',
    'Biskra',
    'Béchar',
    'Blida',
    'Bouira',
    'Tamanrasset',
    'Tébessa',
    'Tlemcen',
    'Tiaret',
    'Tizi Ouzou',
    'Algiers',
    'Djelfa',
    'Jijel',
    'Sétif',
    'Saïda',
    'Skikda',
    'Sidi Bel Abbès',
    'Annaba',
    'Guelma',
    'Constantine',
    'Médéa',
    'Mostaganem',
    'M\'Sila',
    'Mascara',
    'Ouargla',
    'Oran',
    'El Bayadh',
    'Illizi',
    'Bordj Bou Arréridj',
    'Boumerdès',
    'El Tarf',
    'Tindouf',
    'Tissemsilt',
    'El Oued',
    'Khenchela',
    'Souk Ahras',
    'Tipaza',
    'Mila',
    'Aïn Defla',
    'Naâma',
    'Aïn Témouchent',
    'Ghardaïa',
    'Relizane',
  ];

  @override
  void initState() {
    super.initState();
    _deliveryData =
        widget.initialData ??
        const DeliveryTypeData(deliveryType: 'at_location', servedRegions: []);
    _filteredWilayas = List.from(_algerianWilayas);
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _filteredWilayas =
          _algerianWilayas
              .where(
                (wilaya) => wilaya.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ),
              )
              .toList();
    });
  }

  @override
  void didUpdateWidget(DeliveryTypeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialData != oldWidget.initialData) {
      _deliveryData =
          widget.initialData ??
          const DeliveryTypeData(
            deliveryType: 'at_location',
            servedRegions: [],
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            if (widget.title != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.local_shipping,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.title!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  widget.subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
              const SizedBox(height: 16),
            ],

            // Delivery type dropdown
            DropdownButtonFormField<String>(
              value: _deliveryData.deliveryType,
              decoration: const InputDecoration(
                labelText: 'Service Delivery Type *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.local_shipping),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'at_location',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.business, size: 18),
                      SizedBox(width: 8),
                      Text('At Business Location'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'at_customer',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.home, size: 18),
                      SizedBox(width: 8),
                      Text('At Customer Location'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'both',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.swap_horiz, size: 18),
                      SizedBox(width: 8),
                      Text('Both Options'),
                    ],
                  ),
                ),
              ],
              onChanged:
                  widget.enabled
                      ? (value) => _updateDeliveryType(value!)
                      : null,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please select a delivery type';
                }
                return null;
              },
            ),

            // Served regions (conditional)
            if (_deliveryData.requiresRegions) ...[
              const SizedBox(height: 24),
              _buildServedRegionsSection(theme),
            ],

            // Validation message
            if (_deliveryData.requiresRegions && !_deliveryData.isValid) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.error.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Please select at least one region where you provide service.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildServedRegionsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(Icons.location_on, color: theme.colorScheme.primary, size: 20),
            const SizedBox(width: 8),
            Text(
              'Served Regions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Text(
          'Select the regions where you provide service:',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),

        const SizedBox(height: 16),

        // Multi-select dropdown
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: theme.colorScheme.outline),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Column(
            children: [
              // Dropdown header
              InkWell(
                onTap:
                    widget.enabled
                        ? () {
                          setState(() {
                            _isDropdownOpen = !_isDropdownOpen;
                          });
                        }
                        : null,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 20,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _deliveryData.servedRegions.isEmpty
                              ? 'Select regions...'
                              : '${_deliveryData.servedRegions.length} region(s) selected',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color:
                                _deliveryData.servedRegions.isEmpty
                                    ? theme.colorScheme.onSurface.withOpacity(
                                      0.6,
                                    )
                                    : theme.colorScheme.onSurface,
                          ),
                        ),
                      ),
                      Icon(
                        _isDropdownOpen ? Icons.expand_less : Icons.expand_more,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ],
                  ),
                ),
              ),

              // Dropdown content
              if (_isDropdownOpen) ...[
                Divider(height: 1, color: theme.colorScheme.outline),
                Container(
                  constraints: const BoxConstraints(maxHeight: 300),
                  child: Column(
                    children: [
                      // Search field
                      Padding(
                        padding: const EdgeInsets.all(12),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search regions...',
                            prefixIcon: const Icon(Icons.search, size: 20),
                            suffixIcon:
                                _searchController.text.isNotEmpty
                                    ? IconButton(
                                      icon: const Icon(Icons.clear, size: 20),
                                      onPressed: () {
                                        _searchController.clear();
                                      },
                                    )
                                    : null,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            isDense: true,
                          ),
                        ),
                      ),

                      // Quick action buttons
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed:
                                    widget.enabled ? _selectAllRegions : null,
                                icon: const Icon(Icons.select_all, size: 16),
                                label: const Text('Select All'),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed:
                                    widget.enabled ? _clearAllRegions : null,
                                icon: const Icon(Icons.clear, size: 16),
                                label: const Text('Clear All'),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Regions list
                      Expanded(
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _filteredWilayas.length,
                          itemBuilder: (context, index) {
                            final wilaya = _filteredWilayas[index];
                            final isSelected = _deliveryData.servedRegions
                                .contains(wilaya);

                            return CheckboxListTile(
                              title: Text(wilaya),
                              value: isSelected,
                              onChanged:
                                  widget.enabled
                                      ? (value) => _toggleRegion(wilaya)
                                      : null,
                              dense: true,
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),

        // Selected regions display (when dropdown is closed)
        if (!_isDropdownOpen && _deliveryData.servedRegions.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children:
                _deliveryData.servedRegions.map((region) {
                  return Chip(
                    label: Text(region, style: theme.textTheme.bodySmall),
                    deleteIcon:
                        widget.enabled
                            ? const Icon(Icons.close, size: 16)
                            : null,
                    onDeleted:
                        widget.enabled ? () => _toggleRegion(region) : null,
                    backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                    side: BorderSide(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                    ),
                  );
                }).toList(),
          ),
        ],
      ],
    );
  }

  void _updateDeliveryType(String deliveryType) {
    setState(() {
      _deliveryData = _deliveryData.copyWith(
        deliveryType: deliveryType,
        // Clear regions if switching to at_location
        servedRegions:
            deliveryType == 'at_location' ? [] : _deliveryData.servedRegions,
      );
    });

    widget.onChanged(_deliveryData);
  }

  void _toggleRegion(String region) {
    final updatedRegions = List<String>.from(_deliveryData.servedRegions);

    if (updatedRegions.contains(region)) {
      updatedRegions.remove(region);
    } else {
      updatedRegions.add(region);
    }

    setState(() {
      _deliveryData = _deliveryData.copyWith(servedRegions: updatedRegions);
    });

    widget.onChanged(_deliveryData);
  }

  void _selectAllRegions() {
    setState(() {
      _deliveryData = _deliveryData.copyWith(
        servedRegions: List.from(_algerianWilayas),
      );
    });

    widget.onChanged(_deliveryData);
  }

  void _clearAllRegions() {
    setState(() {
      _deliveryData = _deliveryData.copyWith(servedRegions: []);
    });

    widget.onChanged(_deliveryData);
  }
}
