import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/onboarding_models.dart';
import '../controllers/wizard_controller.dart';
import '../widgets/wizard_page.dart';

/// Schedule setup step placeholder
class ScheduleSetupStep extends ConsumerStatefulWidget {
  final OnboardingWizardController controller;

  const ScheduleSetupStep({
    super.key,
    required this.controller,
  });

  @override
  ConsumerState<ScheduleSetupStep> createState() => _ScheduleSetupStepState();
}

class _ScheduleSetupStepState extends ConsumerState<ScheduleSetupStep> {
  @override
  Widget build(BuildContext context) {
    return WizardCenteredPage(
      step: OnboardingStep.scheduleSetup,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.schedule,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Schedule Setup',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'This step will be implemented in the next subtask.',
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              widget.controller.markStepCompleted(OnboardingStep.scheduleSetup, true);
            },
            child: const Text('Mark as Complete (Placeholder)'),
          ),
        ],
      ),
    );
  }
}
