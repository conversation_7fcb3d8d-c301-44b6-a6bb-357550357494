import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import '../providers/calendar_config_provider.dart';
import 'appointment_card.dart';
import 'calendar_header.dart';
import 'calendar_config_dialog.dart';

/// Daily calendar view showing appointments for a single day
class DailyCalendarView extends ConsumerStatefulWidget {
  final DateTime selectedDate;
  final Function(DateTime) onDateChanged;
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;

  const DailyCalendarView({
    super.key,
    required this.selectedDate,
    required this.onDateChanged,
    this.onAppointmentTap,
    this.onAddAppointment,
  });

  @override
  ConsumerState<DailyCalendarView> createState() => _DailyCalendarViewState();
}

class _DailyCalendarViewState extends ConsumerState<DailyCalendarView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentTime();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToCurrentTime() {
    if (!mounted) return;

    final now = DateTime.now();
    if (_isSameDay(now, widget.selectedDate)) {
      // Scroll to current hour
      final hourHeight = 80.0; // Height per hour slot
      final currentHour = now.hour;
      final scrollOffset =
          (currentHour - 2) * hourHeight; // Show 2 hours before current

      if (scrollOffset > 0) {
        _scrollController.animateTo(
          scrollOffset.clamp(0, _scrollController.position.maxScrollExtent),
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Calendar header
        CalendarHeader(
          currentDate: widget.selectedDate,
          viewType: 'day',
          onPreviousPressed: () => _changeDate(-1),
          onNextPressed: () => _changeDate(1),
          onTodayPressed: () => widget.onDateChanged(DateTime.now()),
          onViewChanged: (viewType) {
            // Handle view change in parent widget
          },
          onAddAppointment: widget.onAddAppointment,
          onConfigPressed: _showCalendarConfig,
        ),

        // Day view content
        Expanded(child: _buildDayView()),
      ],
    );
  }

  Widget _buildDayView() {
    // Get appointments for the selected date
    final appointmentsAsync = ref.watch(appointmentNotifierProvider);

    return appointmentsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load appointments',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed:
                      () =>
                          ref
                              .read(appointmentNotifierProvider.notifier)
                              .refreshAppointments(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
      data: (appointments) {
        // Filter appointments for the selected date
        final dayAppointments =
            appointments
                .where(
                  (apt) => _isSameDay(apt.scheduledTime, widget.selectedDate),
                )
                .toList();

        // Sort by time
        dayAppointments.sort(
          (a, b) => a.scheduledTime.compareTo(b.scheduledTime),
        );

        return _buildTimeGrid(dayAppointments);
      },
    );
  }

  Widget _buildTimeGrid(List<Appointment> appointments) {
    return Row(
      children: [
        // Time labels column
        SizedBox(
          width: 60,
          child: ListView.builder(
            controller: _scrollController,
            itemCount: 24, // 24 hours
            itemBuilder: (context, index) {
              final hour = index;
              return SizedBox(
                height: 80,
                child: Center(
                  child: Text(
                    DateFormat('HH:mm').format(DateTime(2023, 1, 1, hour)),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // Divider
        Container(
          width: 1,
          color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        ),

        // Appointments column
        Expanded(
          child: Stack(
            children: [
              // Hour grid lines
              ListView.builder(
                controller: ScrollController(), // Separate controller for grid
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 24,
                itemBuilder: (context, index) {
                  return Container(
                    height: 80,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Theme.of(
                            context,
                          ).colorScheme.outline.withOpacity(0.1),
                          width: 0.5,
                        ),
                      ),
                    ),
                  );
                },
              ),

              // Current time indicator (if today)
              if (_isSameDay(DateTime.now(), widget.selectedDate))
                _buildCurrentTimeIndicator(),

              // Appointments
              ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(8),
                itemCount: appointments.length,
                itemBuilder: (context, index) {
                  final appointment = appointments[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: AppointmentCard(
                      appointment: appointment,
                      onTap: () => widget.onAppointmentTap?.call(appointment),
                      onEdit: () => _handleEditAppointment(appointment),
                      onCancel: () => _handleCancelAppointment(appointment),
                      onComplete: () => _handleCompleteAppointment(appointment),
                    ),
                  );
                },
              ),

              // Empty state
              if (appointments.isEmpty)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.event_available,
                        size: 64,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No appointments scheduled',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap the + button to add an appointment',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentTimeIndicator() {
    final now = DateTime.now();
    final hourHeight = 80.0;
    final minuteHeight = hourHeight / 60;
    final topOffset = (now.hour * hourHeight) + (now.minute * minuteHeight);

    return Positioned(
      top: topOffset,
      left: 8,
      right: 8,
      child: TimeIndicator(currentTime: now),
    );
  }

  void _changeDate(int days) {
    final newDate = widget.selectedDate.add(Duration(days: days));
    widget.onDateChanged(newDate);
  }

  void _showCalendarConfig() {
    final calendarConfig = ref.read(calendarConfigProvider);
    showCalendarConfigDialog(
      context: context,
      currentTimeSlotInterval: calendarConfig.timeSlotInterval,
      onTimeSlotIntervalChanged: (interval) {
        ref.read(calendarConfigProvider.notifier).setTimeSlotInterval(interval);
      },
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  void _handleEditAppointment(Appointment appointment) {
    // TODO: Navigate to edit appointment screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Edit appointment: ${appointment.customerName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _handleCancelAppointment(Appointment appointment) {
    // TODO: Show cancel confirmation dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Cancel appointment: ${appointment.customerName}'),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  void _handleCompleteAppointment(Appointment appointment) {
    // TODO: Mark appointment as completed
    ref
        .read(appointmentNotifierProvider.notifier)
        .updateAppointmentStatus(appointment.id, AppointmentStatus.completed);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Appointment completed: ${appointment.customerName}'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
