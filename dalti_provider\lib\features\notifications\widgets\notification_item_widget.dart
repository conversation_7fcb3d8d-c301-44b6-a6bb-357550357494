import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/notification_models.dart';

class NotificationItemWidget extends StatelessWidget {
  final NotificationItem notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;

  const NotificationItemWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildIcon(),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildContent(),
                ),
                const SizedBox(width: 8),
                _buildTrailing(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    final notificationType = NotificationType.fromString(notification.type);
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getIconBackgroundColor(),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        _getIconData(notificationType),
        color: _getIconColor(),
        size: 20,
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
            Text(
              _formatTime(notification.createdAt),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          notification.message,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
            height: 1.3,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        if (notification.actor != null) ...[
          const SizedBox(height: 8),
          Text(
            'From: ${_getActorName(notification.actor!)}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTrailing() {
    return Column(
      children: [
        if (!notification.isRead)
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
          ),
        const SizedBox(height: 8),
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            color: Colors.grey[400],
            size: 18,
          ),
          onSelected: (value) {
            switch (value) {
              case 'mark_read':
                onMarkAsRead?.call();
                break;
              case 'delete':
                onDelete?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            if (!notification.isRead)
              const PopupMenuItem(
                value: 'mark_read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, size: 18),
                    SizedBox(width: 8),
                    Text('Mark as read'),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getIconData(NotificationType? type) {
    switch (type) {
      case NotificationType.appointmentConfirmedCustomer:
      case NotificationType.appointmentBookedSuccessCustomer:
        return Icons.event_available;
      case NotificationType.appointmentCompletedCustomer:
        return Icons.check_circle;
      case NotificationType.appointmentCancelledCustomer:
        return Icons.event_busy;
      case NotificationType.appointmentRescheduledCustomer:
        return Icons.schedule;
      case NotificationType.messageReceived:
        return Icons.message;
      case NotificationType.systemNotification:
      default:
        return Icons.notifications;
    }
  }

  Color _getIconBackgroundColor() {
    final notificationType = NotificationType.fromString(notification.type);
    
    switch (notificationType) {
      case NotificationType.appointmentConfirmedCustomer:
      case NotificationType.appointmentBookedSuccessCustomer:
        return Colors.green.withOpacity(0.1);
      case NotificationType.appointmentCompletedCustomer:
        return Colors.blue.withOpacity(0.1);
      case NotificationType.appointmentCancelledCustomer:
        return Colors.red.withOpacity(0.1);
      case NotificationType.appointmentRescheduledCustomer:
        return Colors.orange.withOpacity(0.1);
      case NotificationType.messageReceived:
        return Colors.purple.withOpacity(0.1);
      case NotificationType.systemNotification:
      default:
        return Colors.grey.withOpacity(0.1);
    }
  }

  Color _getIconColor() {
    final notificationType = NotificationType.fromString(notification.type);
    
    switch (notificationType) {
      case NotificationType.appointmentConfirmedCustomer:
      case NotificationType.appointmentBookedSuccessCustomer:
        return Colors.green;
      case NotificationType.appointmentCompletedCustomer:
        return Colors.blue;
      case NotificationType.appointmentCancelledCustomer:
        return Colors.red;
      case NotificationType.appointmentRescheduledCustomer:
        return Colors.orange;
      case NotificationType.messageReceived:
        return Colors.purple;
      case NotificationType.systemNotification:
      default:
        return Colors.grey;
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return DateFormat('HH:mm').format(dateTime);
    }
  }

  String _getActorName(NotificationActor actor) {
    if (actor.firstName != null && actor.lastName != null) {
      return '${actor.firstName} ${actor.lastName}';
    } else if (actor.firstName != null) {
      return actor.firstName!;
    } else if (actor.username != null) {
      return actor.username!;
    } else {
      return 'Unknown';
    }
  }
}
