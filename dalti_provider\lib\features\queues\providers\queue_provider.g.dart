// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'queue_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$queueApiServiceHash() => r'aae2c759f4047cf7f9974d83e1f0861ba317ed43';

/// Queue API service provider
///
/// Copied from [queueApiService].
@ProviderFor(queueApiService)
final queueApiServiceProvider = AutoDisposeProvider<QueueApiService>.internal(
  queueApiService,
  name: r'queueApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef QueueApiServiceRef = AutoDisposeProviderRef<QueueApiService>;
String _$queueRepositoryHash() => r'd2ae369fae00f258814f00c0d372cb496594948c';

/// Queue repository provider
///
/// Copied from [queueRepository].
@ProviderFor(queueRepository)
final queueRepositoryProvider = AutoDisposeProvider<QueueRepository>.internal(
  queueRepository,
  name: r'queueRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef QueueRepositoryRef = AutoDisposeProviderRef<QueueRepository>;
String _$queueNotifierHash() => r'abb255281eec37dbef985209a831184788e8b59e';

/// Queue provider for managing queue state
///
/// Copied from [QueueNotifier].
@ProviderFor(QueueNotifier)
final queueNotifierProvider =
    AutoDisposeNotifierProvider<QueueNotifier, QueueData>.internal(
  QueueNotifier.new,
  name: r'queueNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$queueNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$QueueNotifier = AutoDisposeNotifier<QueueData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
