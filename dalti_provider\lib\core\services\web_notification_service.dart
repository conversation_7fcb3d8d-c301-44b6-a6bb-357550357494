import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Web-specific notification service for handling in-app notifications
class WebNotificationService {
  static OverlayEntry? _currentNotification;
  static final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  /// Set the navigator key for navigation
  static void setNavigatorKey(GlobalKey<NavigatorState> key) {
    // This would be set from the main app
  }

  /// Show an in-app notification for web platform
  static void showInAppNotification({
    required String title,
    required String body,
    String? imageUrl,
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 5),
  }) {
    if (!kIsWeb) return;

    final context = _navigatorKey.currentContext;
    if (context == null) return;

    // Remove any existing notification
    _removeCurrentNotification();

    final overlay = Overlay.of(context);
    _currentNotification = OverlayEntry(
      builder: (context) => _WebNotificationWidget(
        title: title,
        body: body,
        imageUrl: imageUrl,
        onTap: onTap,
        onDismiss: _removeCurrentNotification,
      ),
    );

    overlay.insert(_currentNotification!);

    // Auto-dismiss after duration
    Future.delayed(duration, () {
      _removeCurrentNotification();
    });
  }

  /// Remove current notification
  static void _removeCurrentNotification() {
    _currentNotification?.remove();
    _currentNotification = null;
  }

  /// Request web notification permission
  static Future<bool> requestWebNotificationPermission() async {
    if (!kIsWeb) return false;

    try {
      // This would use dart:html in a real implementation
      // For now, we'll assume permission is granted
      return true;
    } catch (e) {
      print('[WebNotificationService] Error requesting permission: $e');
      return false;
    }
  }
}

/// Widget for displaying in-app notifications on web
class _WebNotificationWidget extends StatefulWidget {
  final String title;
  final String body;
  final String? imageUrl;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const _WebNotificationWidget({
    required this.title,
    required this.body,
    this.imageUrl,
    this.onTap,
    this.onDismiss,
  });

  @override
  State<_WebNotificationWidget> createState() => _WebNotificationWidgetState();
}

class _WebNotificationWidgetState extends State<_WebNotificationWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Positioned(
      top: 50,
      right: 20,
      child: SlideTransition(
        position: _slideAnimation,
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 350,
            constraints: const BoxConstraints(maxWidth: 350),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: InkWell(
              onTap: () {
                widget.onTap?.call();
                widget.onDismiss?.call();
              },
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon or image
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.notifications,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    
                    // Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.title,
                            style: theme.textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.body,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    
                    // Dismiss button
                    IconButton(
                      onPressed: widget.onDismiss,
                      icon: Icon(
                        Icons.close,
                        size: 18,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
