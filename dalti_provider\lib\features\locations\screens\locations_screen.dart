import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/routing/app_routes.dart';
import '../models/location_models.dart';
import '../providers/location_provider.dart';
import '../widgets/location_card.dart';
import '../widgets/location_search_bar.dart';

class LocationsScreen extends ConsumerStatefulWidget {
  const LocationsScreen({super.key});

  @override
  ConsumerState<LocationsScreen> createState() => _LocationsScreenState();
}

class _LocationsScreenState extends ConsumerState<LocationsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load locations when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    
    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchQuery == query) {
        ref.read(locationNotifierProvider.notifier).searchLocations(query);
      }
    });
  }

  void _onRefresh() {
    ref.read(locationNotifierProvider.notifier).refresh();
  }

  void _onAddLocation() {
    context.push(AppRoutes.addLocation);
  }

  void _onLocationTap(Location location) {
    context.push('/locations/${location.id}');
  }

  void _onLocationEdit(Location location) {
    context.push('/locations/${location.id}/edit');
  }

  Future<void> _onLocationDelete(Location location) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Location'),
        content: Text('Are you sure you want to delete "${location.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await ref.read(locationNotifierProvider.notifier).deleteLocation(location.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success 
                ? 'Location "${location.name}" deleted successfully'
                : 'Failed to delete location',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  void _onBackPressed() {
    context.go(AppRoutes.dashboard);
  }

  @override
  Widget build(BuildContext context) {
    final locationData = ref.watch(locationNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: _onBackPressed,
          icon: const Icon(Icons.arrow_back),
          tooltip: 'Back to Dashboard',
        ),
        title: const Text('Locations'),
        actions: [
          IconButton(
            onPressed: _onRefresh,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: LocationSearchBar(
              controller: _searchController,
              onChanged: _onSearchChanged,
              hintText: 'Search locations...',
            ),
          ),
          
          // Content
          Expanded(
            child: _buildContent(locationData),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _onAddLocation,
        tooltip: 'Add Location',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildContent(LocationData locationData) {
    if (locationData.isLoading && !locationData.hasLocations) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (locationData.hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading locations',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              locationData.error ?? 'Unknown error occurred',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _onRefresh,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (!locationData.hasLocations) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? 'No locations found' : 'No locations match your search',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isEmpty 
                ? 'Add your first business location to get started'
                : 'Try a different search term',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (_searchQuery.isEmpty)
              ElevatedButton(
                onPressed: _onAddLocation,
                child: const Text('Add Location'),
              ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async => _onRefresh(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: locationData.locations.length,
        itemBuilder: (context, index) {
          final location = locationData.locations[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: LocationCard(
              location: location,
              onTap: () => _onLocationTap(location),
              onEdit: () => _onLocationEdit(location),
              onDelete: () => _onLocationDelete(location),
            ),
          );
        },
      ),
    );
  }
}
