// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wizard_persistence_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PersistedWizardState _$PersistedWizardStateFromJson(Map<String, dynamic> json) {
  return _PersistedWizardState.fromJson(json);
}

/// @nodoc
mixin _$PersistedWizardState {
  /// Version of the persisted data structure for migration support
  int get version => throw _privateConstructorUsedError;

  /// Timestamp when the state was saved
  DateTime get savedAt => throw _privateConstructorUsedError;

  /// Timestamp when the wizard was started
  DateTime get startedAt => throw _privateConstructorUsedError;

  /// Current wizard step
  OnboardingStep get currentStep => throw _privateConstructorUsedError;

  /// Current step index
  int get currentStepIndex => throw _privateConstructorUsedError;

  /// Step completion status
  Map<OnboardingStep, bool> get stepCompletionStatus =>
      throw _privateConstructorUsedError;

  /// Complete onboarding data
  OnboardingData? get onboardingData => throw _privateConstructorUsedError;

  /// Navigation state
  WizardNavigationData? get navigationData =>
      throw _privateConstructorUsedError;

  /// Session information
  SessionInfo? get sessionInfo => throw _privateConstructorUsedError;

  /// Auto-save settings
  AutoSaveSettings get autoSaveSettings => throw _privateConstructorUsedError;

  /// Serializes this PersistedWizardState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PersistedWizardStateCopyWith<PersistedWizardState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PersistedWizardStateCopyWith<$Res> {
  factory $PersistedWizardStateCopyWith(PersistedWizardState value,
          $Res Function(PersistedWizardState) then) =
      _$PersistedWizardStateCopyWithImpl<$Res, PersistedWizardState>;
  @useResult
  $Res call(
      {int version,
      DateTime savedAt,
      DateTime startedAt,
      OnboardingStep currentStep,
      int currentStepIndex,
      Map<OnboardingStep, bool> stepCompletionStatus,
      OnboardingData? onboardingData,
      WizardNavigationData? navigationData,
      SessionInfo? sessionInfo,
      AutoSaveSettings autoSaveSettings});

  $WizardNavigationDataCopyWith<$Res>? get navigationData;
  $SessionInfoCopyWith<$Res>? get sessionInfo;
  $AutoSaveSettingsCopyWith<$Res> get autoSaveSettings;
}

/// @nodoc
class _$PersistedWizardStateCopyWithImpl<$Res,
        $Val extends PersistedWizardState>
    implements $PersistedWizardStateCopyWith<$Res> {
  _$PersistedWizardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? savedAt = null,
    Object? startedAt = null,
    Object? currentStep = null,
    Object? currentStepIndex = null,
    Object? stepCompletionStatus = null,
    Object? onboardingData = freezed,
    Object? navigationData = freezed,
    Object? sessionInfo = freezed,
    Object? autoSaveSettings = null,
  }) {
    return _then(_value.copyWith(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      savedAt: null == savedAt
          ? _value.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentStepIndex: null == currentStepIndex
          ? _value.currentStepIndex
          : currentStepIndex // ignore: cast_nullable_to_non_nullable
              as int,
      stepCompletionStatus: null == stepCompletionStatus
          ? _value.stepCompletionStatus
          : stepCompletionStatus // ignore: cast_nullable_to_non_nullable
              as Map<OnboardingStep, bool>,
      onboardingData: freezed == onboardingData
          ? _value.onboardingData
          : onboardingData // ignore: cast_nullable_to_non_nullable
              as OnboardingData?,
      navigationData: freezed == navigationData
          ? _value.navigationData
          : navigationData // ignore: cast_nullable_to_non_nullable
              as WizardNavigationData?,
      sessionInfo: freezed == sessionInfo
          ? _value.sessionInfo
          : sessionInfo // ignore: cast_nullable_to_non_nullable
              as SessionInfo?,
      autoSaveSettings: null == autoSaveSettings
          ? _value.autoSaveSettings
          : autoSaveSettings // ignore: cast_nullable_to_non_nullable
              as AutoSaveSettings,
    ) as $Val);
  }

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WizardNavigationDataCopyWith<$Res>? get navigationData {
    if (_value.navigationData == null) {
      return null;
    }

    return $WizardNavigationDataCopyWith<$Res>(_value.navigationData!, (value) {
      return _then(_value.copyWith(navigationData: value) as $Val);
    });
  }

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SessionInfoCopyWith<$Res>? get sessionInfo {
    if (_value.sessionInfo == null) {
      return null;
    }

    return $SessionInfoCopyWith<$Res>(_value.sessionInfo!, (value) {
      return _then(_value.copyWith(sessionInfo: value) as $Val);
    });
  }

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AutoSaveSettingsCopyWith<$Res> get autoSaveSettings {
    return $AutoSaveSettingsCopyWith<$Res>(_value.autoSaveSettings, (value) {
      return _then(_value.copyWith(autoSaveSettings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PersistedWizardStateImplCopyWith<$Res>
    implements $PersistedWizardStateCopyWith<$Res> {
  factory _$$PersistedWizardStateImplCopyWith(_$PersistedWizardStateImpl value,
          $Res Function(_$PersistedWizardStateImpl) then) =
      __$$PersistedWizardStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int version,
      DateTime savedAt,
      DateTime startedAt,
      OnboardingStep currentStep,
      int currentStepIndex,
      Map<OnboardingStep, bool> stepCompletionStatus,
      OnboardingData? onboardingData,
      WizardNavigationData? navigationData,
      SessionInfo? sessionInfo,
      AutoSaveSettings autoSaveSettings});

  @override
  $WizardNavigationDataCopyWith<$Res>? get navigationData;
  @override
  $SessionInfoCopyWith<$Res>? get sessionInfo;
  @override
  $AutoSaveSettingsCopyWith<$Res> get autoSaveSettings;
}

/// @nodoc
class __$$PersistedWizardStateImplCopyWithImpl<$Res>
    extends _$PersistedWizardStateCopyWithImpl<$Res, _$PersistedWizardStateImpl>
    implements _$$PersistedWizardStateImplCopyWith<$Res> {
  __$$PersistedWizardStateImplCopyWithImpl(_$PersistedWizardStateImpl _value,
      $Res Function(_$PersistedWizardStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? savedAt = null,
    Object? startedAt = null,
    Object? currentStep = null,
    Object? currentStepIndex = null,
    Object? stepCompletionStatus = null,
    Object? onboardingData = freezed,
    Object? navigationData = freezed,
    Object? sessionInfo = freezed,
    Object? autoSaveSettings = null,
  }) {
    return _then(_$PersistedWizardStateImpl(
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      savedAt: null == savedAt
          ? _value.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startedAt: null == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentStepIndex: null == currentStepIndex
          ? _value.currentStepIndex
          : currentStepIndex // ignore: cast_nullable_to_non_nullable
              as int,
      stepCompletionStatus: null == stepCompletionStatus
          ? _value._stepCompletionStatus
          : stepCompletionStatus // ignore: cast_nullable_to_non_nullable
              as Map<OnboardingStep, bool>,
      onboardingData: freezed == onboardingData
          ? _value.onboardingData
          : onboardingData // ignore: cast_nullable_to_non_nullable
              as OnboardingData?,
      navigationData: freezed == navigationData
          ? _value.navigationData
          : navigationData // ignore: cast_nullable_to_non_nullable
              as WizardNavigationData?,
      sessionInfo: freezed == sessionInfo
          ? _value.sessionInfo
          : sessionInfo // ignore: cast_nullable_to_non_nullable
              as SessionInfo?,
      autoSaveSettings: null == autoSaveSettings
          ? _value.autoSaveSettings
          : autoSaveSettings // ignore: cast_nullable_to_non_nullable
              as AutoSaveSettings,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PersistedWizardStateImpl implements _PersistedWizardState {
  const _$PersistedWizardStateImpl(
      {this.version = 1,
      required this.savedAt,
      required this.startedAt,
      required this.currentStep,
      required this.currentStepIndex,
      final Map<OnboardingStep, bool> stepCompletionStatus = const {},
      this.onboardingData,
      this.navigationData,
      this.sessionInfo,
      this.autoSaveSettings = const AutoSaveSettings()})
      : _stepCompletionStatus = stepCompletionStatus;

  factory _$PersistedWizardStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$PersistedWizardStateImplFromJson(json);

  /// Version of the persisted data structure for migration support
  @override
  @JsonKey()
  final int version;

  /// Timestamp when the state was saved
  @override
  final DateTime savedAt;

  /// Timestamp when the wizard was started
  @override
  final DateTime startedAt;

  /// Current wizard step
  @override
  final OnboardingStep currentStep;

  /// Current step index
  @override
  final int currentStepIndex;

  /// Step completion status
  final Map<OnboardingStep, bool> _stepCompletionStatus;

  /// Step completion status
  @override
  @JsonKey()
  Map<OnboardingStep, bool> get stepCompletionStatus {
    if (_stepCompletionStatus is EqualUnmodifiableMapView)
      return _stepCompletionStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_stepCompletionStatus);
  }

  /// Complete onboarding data
  @override
  final OnboardingData? onboardingData;

  /// Navigation state
  @override
  final WizardNavigationData? navigationData;

  /// Session information
  @override
  final SessionInfo? sessionInfo;

  /// Auto-save settings
  @override
  @JsonKey()
  final AutoSaveSettings autoSaveSettings;

  @override
  String toString() {
    return 'PersistedWizardState(version: $version, savedAt: $savedAt, startedAt: $startedAt, currentStep: $currentStep, currentStepIndex: $currentStepIndex, stepCompletionStatus: $stepCompletionStatus, onboardingData: $onboardingData, navigationData: $navigationData, sessionInfo: $sessionInfo, autoSaveSettings: $autoSaveSettings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PersistedWizardStateImpl &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.savedAt, savedAt) || other.savedAt == savedAt) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.currentStepIndex, currentStepIndex) ||
                other.currentStepIndex == currentStepIndex) &&
            const DeepCollectionEquality()
                .equals(other._stepCompletionStatus, _stepCompletionStatus) &&
            (identical(other.onboardingData, onboardingData) ||
                other.onboardingData == onboardingData) &&
            (identical(other.navigationData, navigationData) ||
                other.navigationData == navigationData) &&
            (identical(other.sessionInfo, sessionInfo) ||
                other.sessionInfo == sessionInfo) &&
            (identical(other.autoSaveSettings, autoSaveSettings) ||
                other.autoSaveSettings == autoSaveSettings));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      version,
      savedAt,
      startedAt,
      currentStep,
      currentStepIndex,
      const DeepCollectionEquality().hash(_stepCompletionStatus),
      onboardingData,
      navigationData,
      sessionInfo,
      autoSaveSettings);

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PersistedWizardStateImplCopyWith<_$PersistedWizardStateImpl>
      get copyWith =>
          __$$PersistedWizardStateImplCopyWithImpl<_$PersistedWizardStateImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PersistedWizardStateImplToJson(
      this,
    );
  }
}

abstract class _PersistedWizardState implements PersistedWizardState {
  const factory _PersistedWizardState(
      {final int version,
      required final DateTime savedAt,
      required final DateTime startedAt,
      required final OnboardingStep currentStep,
      required final int currentStepIndex,
      final Map<OnboardingStep, bool> stepCompletionStatus,
      final OnboardingData? onboardingData,
      final WizardNavigationData? navigationData,
      final SessionInfo? sessionInfo,
      final AutoSaveSettings autoSaveSettings}) = _$PersistedWizardStateImpl;

  factory _PersistedWizardState.fromJson(Map<String, dynamic> json) =
      _$PersistedWizardStateImpl.fromJson;

  /// Version of the persisted data structure for migration support
  @override
  int get version;

  /// Timestamp when the state was saved
  @override
  DateTime get savedAt;

  /// Timestamp when the wizard was started
  @override
  DateTime get startedAt;

  /// Current wizard step
  @override
  OnboardingStep get currentStep;

  /// Current step index
  @override
  int get currentStepIndex;

  /// Step completion status
  @override
  Map<OnboardingStep, bool> get stepCompletionStatus;

  /// Complete onboarding data
  @override
  OnboardingData? get onboardingData;

  /// Navigation state
  @override
  WizardNavigationData? get navigationData;

  /// Session information
  @override
  SessionInfo? get sessionInfo;

  /// Auto-save settings
  @override
  AutoSaveSettings get autoSaveSettings;

  /// Create a copy of PersistedWizardState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PersistedWizardStateImplCopyWith<_$PersistedWizardStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

WizardNavigationData _$WizardNavigationDataFromJson(Map<String, dynamic> json) {
  return _WizardNavigationData.fromJson(json);
}

/// @nodoc
mixin _$WizardNavigationData {
  OnboardingStep get currentStep => throw _privateConstructorUsedError;
  int get currentStepIndex => throw _privateConstructorUsedError;
  bool get canGoNext => throw _privateConstructorUsedError;
  bool get canGoPrevious => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  Map<OnboardingStep, bool> get stepCompletionStatus =>
      throw _privateConstructorUsedError;

  /// Serializes this WizardNavigationData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WizardNavigationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WizardNavigationDataCopyWith<WizardNavigationData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WizardNavigationDataCopyWith<$Res> {
  factory $WizardNavigationDataCopyWith(WizardNavigationData value,
          $Res Function(WizardNavigationData) then) =
      _$WizardNavigationDataCopyWithImpl<$Res, WizardNavigationData>;
  @useResult
  $Res call(
      {OnboardingStep currentStep,
      int currentStepIndex,
      bool canGoNext,
      bool canGoPrevious,
      bool isLoading,
      String? error,
      Map<OnboardingStep, bool> stepCompletionStatus});
}

/// @nodoc
class _$WizardNavigationDataCopyWithImpl<$Res,
        $Val extends WizardNavigationData>
    implements $WizardNavigationDataCopyWith<$Res> {
  _$WizardNavigationDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WizardNavigationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? currentStepIndex = null,
    Object? canGoNext = null,
    Object? canGoPrevious = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? stepCompletionStatus = null,
  }) {
    return _then(_value.copyWith(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentStepIndex: null == currentStepIndex
          ? _value.currentStepIndex
          : currentStepIndex // ignore: cast_nullable_to_non_nullable
              as int,
      canGoNext: null == canGoNext
          ? _value.canGoNext
          : canGoNext // ignore: cast_nullable_to_non_nullable
              as bool,
      canGoPrevious: null == canGoPrevious
          ? _value.canGoPrevious
          : canGoPrevious // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      stepCompletionStatus: null == stepCompletionStatus
          ? _value.stepCompletionStatus
          : stepCompletionStatus // ignore: cast_nullable_to_non_nullable
              as Map<OnboardingStep, bool>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WizardNavigationDataImplCopyWith<$Res>
    implements $WizardNavigationDataCopyWith<$Res> {
  factory _$$WizardNavigationDataImplCopyWith(_$WizardNavigationDataImpl value,
          $Res Function(_$WizardNavigationDataImpl) then) =
      __$$WizardNavigationDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OnboardingStep currentStep,
      int currentStepIndex,
      bool canGoNext,
      bool canGoPrevious,
      bool isLoading,
      String? error,
      Map<OnboardingStep, bool> stepCompletionStatus});
}

/// @nodoc
class __$$WizardNavigationDataImplCopyWithImpl<$Res>
    extends _$WizardNavigationDataCopyWithImpl<$Res, _$WizardNavigationDataImpl>
    implements _$$WizardNavigationDataImplCopyWith<$Res> {
  __$$WizardNavigationDataImplCopyWithImpl(_$WizardNavigationDataImpl _value,
      $Res Function(_$WizardNavigationDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of WizardNavigationData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentStep = null,
    Object? currentStepIndex = null,
    Object? canGoNext = null,
    Object? canGoPrevious = null,
    Object? isLoading = null,
    Object? error = freezed,
    Object? stepCompletionStatus = null,
  }) {
    return _then(_$WizardNavigationDataImpl(
      currentStep: null == currentStep
          ? _value.currentStep
          : currentStep // ignore: cast_nullable_to_non_nullable
              as OnboardingStep,
      currentStepIndex: null == currentStepIndex
          ? _value.currentStepIndex
          : currentStepIndex // ignore: cast_nullable_to_non_nullable
              as int,
      canGoNext: null == canGoNext
          ? _value.canGoNext
          : canGoNext // ignore: cast_nullable_to_non_nullable
              as bool,
      canGoPrevious: null == canGoPrevious
          ? _value.canGoPrevious
          : canGoPrevious // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      stepCompletionStatus: null == stepCompletionStatus
          ? _value._stepCompletionStatus
          : stepCompletionStatus // ignore: cast_nullable_to_non_nullable
              as Map<OnboardingStep, bool>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WizardNavigationDataImpl implements _WizardNavigationData {
  const _$WizardNavigationDataImpl(
      {required this.currentStep,
      required this.currentStepIndex,
      required this.canGoNext,
      required this.canGoPrevious,
      this.isLoading = false,
      this.error,
      final Map<OnboardingStep, bool> stepCompletionStatus = const {}})
      : _stepCompletionStatus = stepCompletionStatus;

  factory _$WizardNavigationDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$WizardNavigationDataImplFromJson(json);

  @override
  final OnboardingStep currentStep;
  @override
  final int currentStepIndex;
  @override
  final bool canGoNext;
  @override
  final bool canGoPrevious;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;
  final Map<OnboardingStep, bool> _stepCompletionStatus;
  @override
  @JsonKey()
  Map<OnboardingStep, bool> get stepCompletionStatus {
    if (_stepCompletionStatus is EqualUnmodifiableMapView)
      return _stepCompletionStatus;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_stepCompletionStatus);
  }

  @override
  String toString() {
    return 'WizardNavigationData(currentStep: $currentStep, currentStepIndex: $currentStepIndex, canGoNext: $canGoNext, canGoPrevious: $canGoPrevious, isLoading: $isLoading, error: $error, stepCompletionStatus: $stepCompletionStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WizardNavigationDataImpl &&
            (identical(other.currentStep, currentStep) ||
                other.currentStep == currentStep) &&
            (identical(other.currentStepIndex, currentStepIndex) ||
                other.currentStepIndex == currentStepIndex) &&
            (identical(other.canGoNext, canGoNext) ||
                other.canGoNext == canGoNext) &&
            (identical(other.canGoPrevious, canGoPrevious) ||
                other.canGoPrevious == canGoPrevious) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error) &&
            const DeepCollectionEquality()
                .equals(other._stepCompletionStatus, _stepCompletionStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentStep,
      currentStepIndex,
      canGoNext,
      canGoPrevious,
      isLoading,
      error,
      const DeepCollectionEquality().hash(_stepCompletionStatus));

  /// Create a copy of WizardNavigationData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WizardNavigationDataImplCopyWith<_$WizardNavigationDataImpl>
      get copyWith =>
          __$$WizardNavigationDataImplCopyWithImpl<_$WizardNavigationDataImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WizardNavigationDataImplToJson(
      this,
    );
  }
}

abstract class _WizardNavigationData implements WizardNavigationData {
  const factory _WizardNavigationData(
          {required final OnboardingStep currentStep,
          required final int currentStepIndex,
          required final bool canGoNext,
          required final bool canGoPrevious,
          final bool isLoading,
          final String? error,
          final Map<OnboardingStep, bool> stepCompletionStatus}) =
      _$WizardNavigationDataImpl;

  factory _WizardNavigationData.fromJson(Map<String, dynamic> json) =
      _$WizardNavigationDataImpl.fromJson;

  @override
  OnboardingStep get currentStep;
  @override
  int get currentStepIndex;
  @override
  bool get canGoNext;
  @override
  bool get canGoPrevious;
  @override
  bool get isLoading;
  @override
  String? get error;
  @override
  Map<OnboardingStep, bool> get stepCompletionStatus;

  /// Create a copy of WizardNavigationData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WizardNavigationDataImplCopyWith<_$WizardNavigationDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SessionInfo _$SessionInfoFromJson(Map<String, dynamic> json) {
  return _SessionInfo.fromJson(json);
}

/// @nodoc
mixin _$SessionInfo {
  /// Unique session ID
  String get sessionId => throw _privateConstructorUsedError;

  /// User ID if available
  String? get userId => throw _privateConstructorUsedError;

  /// Device/browser information
  String? get deviceInfo => throw _privateConstructorUsedError;

  /// Session expiration time
  DateTime? get expiresAt => throw _privateConstructorUsedError;

  /// Whether this is a guest session
  bool get isGuest => throw _privateConstructorUsedError;

  /// Last activity timestamp
  DateTime? get lastActivity => throw _privateConstructorUsedError;

  /// Serializes this SessionInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SessionInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SessionInfoCopyWith<SessionInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionInfoCopyWith<$Res> {
  factory $SessionInfoCopyWith(
          SessionInfo value, $Res Function(SessionInfo) then) =
      _$SessionInfoCopyWithImpl<$Res, SessionInfo>;
  @useResult
  $Res call(
      {String sessionId,
      String? userId,
      String? deviceInfo,
      DateTime? expiresAt,
      bool isGuest,
      DateTime? lastActivity});
}

/// @nodoc
class _$SessionInfoCopyWithImpl<$Res, $Val extends SessionInfo>
    implements $SessionInfoCopyWith<$Res> {
  _$SessionInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SessionInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? userId = freezed,
    Object? deviceInfo = freezed,
    Object? expiresAt = freezed,
    Object? isGuest = null,
    Object? lastActivity = freezed,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceInfo: freezed == deviceInfo
          ? _value.deviceInfo
          : deviceInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isGuest: null == isGuest
          ? _value.isGuest
          : isGuest // ignore: cast_nullable_to_non_nullable
              as bool,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionInfoImplCopyWith<$Res>
    implements $SessionInfoCopyWith<$Res> {
  factory _$$SessionInfoImplCopyWith(
          _$SessionInfoImpl value, $Res Function(_$SessionInfoImpl) then) =
      __$$SessionInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionId,
      String? userId,
      String? deviceInfo,
      DateTime? expiresAt,
      bool isGuest,
      DateTime? lastActivity});
}

/// @nodoc
class __$$SessionInfoImplCopyWithImpl<$Res>
    extends _$SessionInfoCopyWithImpl<$Res, _$SessionInfoImpl>
    implements _$$SessionInfoImplCopyWith<$Res> {
  __$$SessionInfoImplCopyWithImpl(
      _$SessionInfoImpl _value, $Res Function(_$SessionInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of SessionInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? userId = freezed,
    Object? deviceInfo = freezed,
    Object? expiresAt = freezed,
    Object? isGuest = null,
    Object? lastActivity = freezed,
  }) {
    return _then(_$SessionInfoImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceInfo: freezed == deviceInfo
          ? _value.deviceInfo
          : deviceInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      expiresAt: freezed == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isGuest: null == isGuest
          ? _value.isGuest
          : isGuest // ignore: cast_nullable_to_non_nullable
              as bool,
      lastActivity: freezed == lastActivity
          ? _value.lastActivity
          : lastActivity // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionInfoImpl implements _SessionInfo {
  const _$SessionInfoImpl(
      {required this.sessionId,
      this.userId,
      this.deviceInfo,
      this.expiresAt,
      this.isGuest = false,
      this.lastActivity});

  factory _$SessionInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionInfoImplFromJson(json);

  /// Unique session ID
  @override
  final String sessionId;

  /// User ID if available
  @override
  final String? userId;

  /// Device/browser information
  @override
  final String? deviceInfo;

  /// Session expiration time
  @override
  final DateTime? expiresAt;

  /// Whether this is a guest session
  @override
  @JsonKey()
  final bool isGuest;

  /// Last activity timestamp
  @override
  final DateTime? lastActivity;

  @override
  String toString() {
    return 'SessionInfo(sessionId: $sessionId, userId: $userId, deviceInfo: $deviceInfo, expiresAt: $expiresAt, isGuest: $isGuest, lastActivity: $lastActivity)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionInfoImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.deviceInfo, deviceInfo) ||
                other.deviceInfo == deviceInfo) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.isGuest, isGuest) || other.isGuest == isGuest) &&
            (identical(other.lastActivity, lastActivity) ||
                other.lastActivity == lastActivity));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sessionId, userId, deviceInfo,
      expiresAt, isGuest, lastActivity);

  /// Create a copy of SessionInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionInfoImplCopyWith<_$SessionInfoImpl> get copyWith =>
      __$$SessionInfoImplCopyWithImpl<_$SessionInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionInfoImplToJson(
      this,
    );
  }
}

abstract class _SessionInfo implements SessionInfo {
  const factory _SessionInfo(
      {required final String sessionId,
      final String? userId,
      final String? deviceInfo,
      final DateTime? expiresAt,
      final bool isGuest,
      final DateTime? lastActivity}) = _$SessionInfoImpl;

  factory _SessionInfo.fromJson(Map<String, dynamic> json) =
      _$SessionInfoImpl.fromJson;

  /// Unique session ID
  @override
  String get sessionId;

  /// User ID if available
  @override
  String? get userId;

  /// Device/browser information
  @override
  String? get deviceInfo;

  /// Session expiration time
  @override
  DateTime? get expiresAt;

  /// Whether this is a guest session
  @override
  bool get isGuest;

  /// Last activity timestamp
  @override
  DateTime? get lastActivity;

  /// Create a copy of SessionInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionInfoImplCopyWith<_$SessionInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AutoSaveSettings _$AutoSaveSettingsFromJson(Map<String, dynamic> json) {
  return _AutoSaveSettings.fromJson(json);
}

/// @nodoc
mixin _$AutoSaveSettings {
  /// Whether auto-save is enabled
  bool get enabled => throw _privateConstructorUsedError;

  /// Debounce delay in milliseconds
  int get debounceDelayMs => throw _privateConstructorUsedError;

  /// Save on step navigation
  bool get saveOnStepChange => throw _privateConstructorUsedError;

  /// Save on form field changes
  bool get saveOnFieldChange => throw _privateConstructorUsedError;

  /// Save on step completion
  bool get saveOnStepComplete => throw _privateConstructorUsedError;

  /// Maximum number of auto-save attempts
  int get maxRetries => throw _privateConstructorUsedError;

  /// Serializes this AutoSaveSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AutoSaveSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AutoSaveSettingsCopyWith<AutoSaveSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AutoSaveSettingsCopyWith<$Res> {
  factory $AutoSaveSettingsCopyWith(
          AutoSaveSettings value, $Res Function(AutoSaveSettings) then) =
      _$AutoSaveSettingsCopyWithImpl<$Res, AutoSaveSettings>;
  @useResult
  $Res call(
      {bool enabled,
      int debounceDelayMs,
      bool saveOnStepChange,
      bool saveOnFieldChange,
      bool saveOnStepComplete,
      int maxRetries});
}

/// @nodoc
class _$AutoSaveSettingsCopyWithImpl<$Res, $Val extends AutoSaveSettings>
    implements $AutoSaveSettingsCopyWith<$Res> {
  _$AutoSaveSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AutoSaveSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? debounceDelayMs = null,
    Object? saveOnStepChange = null,
    Object? saveOnFieldChange = null,
    Object? saveOnStepComplete = null,
    Object? maxRetries = null,
  }) {
    return _then(_value.copyWith(
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      debounceDelayMs: null == debounceDelayMs
          ? _value.debounceDelayMs
          : debounceDelayMs // ignore: cast_nullable_to_non_nullable
              as int,
      saveOnStepChange: null == saveOnStepChange
          ? _value.saveOnStepChange
          : saveOnStepChange // ignore: cast_nullable_to_non_nullable
              as bool,
      saveOnFieldChange: null == saveOnFieldChange
          ? _value.saveOnFieldChange
          : saveOnFieldChange // ignore: cast_nullable_to_non_nullable
              as bool,
      saveOnStepComplete: null == saveOnStepComplete
          ? _value.saveOnStepComplete
          : saveOnStepComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRetries: null == maxRetries
          ? _value.maxRetries
          : maxRetries // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AutoSaveSettingsImplCopyWith<$Res>
    implements $AutoSaveSettingsCopyWith<$Res> {
  factory _$$AutoSaveSettingsImplCopyWith(_$AutoSaveSettingsImpl value,
          $Res Function(_$AutoSaveSettingsImpl) then) =
      __$$AutoSaveSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool enabled,
      int debounceDelayMs,
      bool saveOnStepChange,
      bool saveOnFieldChange,
      bool saveOnStepComplete,
      int maxRetries});
}

/// @nodoc
class __$$AutoSaveSettingsImplCopyWithImpl<$Res>
    extends _$AutoSaveSettingsCopyWithImpl<$Res, _$AutoSaveSettingsImpl>
    implements _$$AutoSaveSettingsImplCopyWith<$Res> {
  __$$AutoSaveSettingsImplCopyWithImpl(_$AutoSaveSettingsImpl _value,
      $Res Function(_$AutoSaveSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AutoSaveSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
    Object? debounceDelayMs = null,
    Object? saveOnStepChange = null,
    Object? saveOnFieldChange = null,
    Object? saveOnStepComplete = null,
    Object? maxRetries = null,
  }) {
    return _then(_$AutoSaveSettingsImpl(
      enabled: null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
      debounceDelayMs: null == debounceDelayMs
          ? _value.debounceDelayMs
          : debounceDelayMs // ignore: cast_nullable_to_non_nullable
              as int,
      saveOnStepChange: null == saveOnStepChange
          ? _value.saveOnStepChange
          : saveOnStepChange // ignore: cast_nullable_to_non_nullable
              as bool,
      saveOnFieldChange: null == saveOnFieldChange
          ? _value.saveOnFieldChange
          : saveOnFieldChange // ignore: cast_nullable_to_non_nullable
              as bool,
      saveOnStepComplete: null == saveOnStepComplete
          ? _value.saveOnStepComplete
          : saveOnStepComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRetries: null == maxRetries
          ? _value.maxRetries
          : maxRetries // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AutoSaveSettingsImpl implements _AutoSaveSettings {
  const _$AutoSaveSettingsImpl(
      {this.enabled = true,
      this.debounceDelayMs = 2000,
      this.saveOnStepChange = true,
      this.saveOnFieldChange = true,
      this.saveOnStepComplete = true,
      this.maxRetries = 3});

  factory _$AutoSaveSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AutoSaveSettingsImplFromJson(json);

  /// Whether auto-save is enabled
  @override
  @JsonKey()
  final bool enabled;

  /// Debounce delay in milliseconds
  @override
  @JsonKey()
  final int debounceDelayMs;

  /// Save on step navigation
  @override
  @JsonKey()
  final bool saveOnStepChange;

  /// Save on form field changes
  @override
  @JsonKey()
  final bool saveOnFieldChange;

  /// Save on step completion
  @override
  @JsonKey()
  final bool saveOnStepComplete;

  /// Maximum number of auto-save attempts
  @override
  @JsonKey()
  final int maxRetries;

  @override
  String toString() {
    return 'AutoSaveSettings(enabled: $enabled, debounceDelayMs: $debounceDelayMs, saveOnStepChange: $saveOnStepChange, saveOnFieldChange: $saveOnFieldChange, saveOnStepComplete: $saveOnStepComplete, maxRetries: $maxRetries)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AutoSaveSettingsImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled) &&
            (identical(other.debounceDelayMs, debounceDelayMs) ||
                other.debounceDelayMs == debounceDelayMs) &&
            (identical(other.saveOnStepChange, saveOnStepChange) ||
                other.saveOnStepChange == saveOnStepChange) &&
            (identical(other.saveOnFieldChange, saveOnFieldChange) ||
                other.saveOnFieldChange == saveOnFieldChange) &&
            (identical(other.saveOnStepComplete, saveOnStepComplete) ||
                other.saveOnStepComplete == saveOnStepComplete) &&
            (identical(other.maxRetries, maxRetries) ||
                other.maxRetries == maxRetries));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, enabled, debounceDelayMs,
      saveOnStepChange, saveOnFieldChange, saveOnStepComplete, maxRetries);

  /// Create a copy of AutoSaveSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AutoSaveSettingsImplCopyWith<_$AutoSaveSettingsImpl> get copyWith =>
      __$$AutoSaveSettingsImplCopyWithImpl<_$AutoSaveSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AutoSaveSettingsImplToJson(
      this,
    );
  }
}

abstract class _AutoSaveSettings implements AutoSaveSettings {
  const factory _AutoSaveSettings(
      {final bool enabled,
      final int debounceDelayMs,
      final bool saveOnStepChange,
      final bool saveOnFieldChange,
      final bool saveOnStepComplete,
      final int maxRetries}) = _$AutoSaveSettingsImpl;

  factory _AutoSaveSettings.fromJson(Map<String, dynamic> json) =
      _$AutoSaveSettingsImpl.fromJson;

  /// Whether auto-save is enabled
  @override
  bool get enabled;

  /// Debounce delay in milliseconds
  @override
  int get debounceDelayMs;

  /// Save on step navigation
  @override
  bool get saveOnStepChange;

  /// Save on form field changes
  @override
  bool get saveOnFieldChange;

  /// Save on step completion
  @override
  bool get saveOnStepComplete;

  /// Maximum number of auto-save attempts
  @override
  int get maxRetries;

  /// Create a copy of AutoSaveSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AutoSaveSettingsImplCopyWith<_$AutoSaveSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PersistenceResult _$PersistenceResultFromJson(Map<String, dynamic> json) {
  return _PersistenceResult.fromJson(json);
}

/// @nodoc
mixin _$PersistenceResult {
  bool get success => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  DateTime? get savedAt => throw _privateConstructorUsedError;
  int? get dataSize => throw _privateConstructorUsedError;

  /// Serializes this PersistenceResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PersistenceResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PersistenceResultCopyWith<PersistenceResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PersistenceResultCopyWith<$Res> {
  factory $PersistenceResultCopyWith(
          PersistenceResult value, $Res Function(PersistenceResult) then) =
      _$PersistenceResultCopyWithImpl<$Res, PersistenceResult>;
  @useResult
  $Res call({bool success, String? error, DateTime? savedAt, int? dataSize});
}

/// @nodoc
class _$PersistenceResultCopyWithImpl<$Res, $Val extends PersistenceResult>
    implements $PersistenceResultCopyWith<$Res> {
  _$PersistenceResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PersistenceResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? error = freezed,
    Object? savedAt = freezed,
    Object? dataSize = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      savedAt: freezed == savedAt
          ? _value.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dataSize: freezed == dataSize
          ? _value.dataSize
          : dataSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PersistenceResultImplCopyWith<$Res>
    implements $PersistenceResultCopyWith<$Res> {
  factory _$$PersistenceResultImplCopyWith(_$PersistenceResultImpl value,
          $Res Function(_$PersistenceResultImpl) then) =
      __$$PersistenceResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, String? error, DateTime? savedAt, int? dataSize});
}

/// @nodoc
class __$$PersistenceResultImplCopyWithImpl<$Res>
    extends _$PersistenceResultCopyWithImpl<$Res, _$PersistenceResultImpl>
    implements _$$PersistenceResultImplCopyWith<$Res> {
  __$$PersistenceResultImplCopyWithImpl(_$PersistenceResultImpl _value,
      $Res Function(_$PersistenceResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of PersistenceResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? error = freezed,
    Object? savedAt = freezed,
    Object? dataSize = freezed,
  }) {
    return _then(_$PersistenceResultImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      savedAt: freezed == savedAt
          ? _value.savedAt
          : savedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dataSize: freezed == dataSize
          ? _value.dataSize
          : dataSize // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PersistenceResultImpl implements _PersistenceResult {
  const _$PersistenceResultImpl(
      {required this.success, this.error, this.savedAt, this.dataSize});

  factory _$PersistenceResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$PersistenceResultImplFromJson(json);

  @override
  final bool success;
  @override
  final String? error;
  @override
  final DateTime? savedAt;
  @override
  final int? dataSize;

  @override
  String toString() {
    return 'PersistenceResult(success: $success, error: $error, savedAt: $savedAt, dataSize: $dataSize)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PersistenceResultImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.savedAt, savedAt) || other.savedAt == savedAt) &&
            (identical(other.dataSize, dataSize) ||
                other.dataSize == dataSize));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, error, savedAt, dataSize);

  /// Create a copy of PersistenceResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PersistenceResultImplCopyWith<_$PersistenceResultImpl> get copyWith =>
      __$$PersistenceResultImplCopyWithImpl<_$PersistenceResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PersistenceResultImplToJson(
      this,
    );
  }
}

abstract class _PersistenceResult implements PersistenceResult {
  const factory _PersistenceResult(
      {required final bool success,
      final String? error,
      final DateTime? savedAt,
      final int? dataSize}) = _$PersistenceResultImpl;

  factory _PersistenceResult.fromJson(Map<String, dynamic> json) =
      _$PersistenceResultImpl.fromJson;

  @override
  bool get success;
  @override
  String? get error;
  @override
  DateTime? get savedAt;
  @override
  int? get dataSize;

  /// Create a copy of PersistenceResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PersistenceResultImplCopyWith<_$PersistenceResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RecoveryOptions _$RecoveryOptionsFromJson(Map<String, dynamic> json) {
  return _RecoveryOptions.fromJson(json);
}

/// @nodoc
mixin _$RecoveryOptions {
  /// Whether to show recovery dialog to user
  bool get showRecoveryDialog => throw _privateConstructorUsedError;

  /// Whether to auto-restore without asking
  bool get autoRestore => throw _privateConstructorUsedError;

  /// Maximum age of data to consider for recovery (in hours)
  int get maxRecoveryAgeHours => throw _privateConstructorUsedError;

  /// Whether to validate data integrity before recovery
  bool get validateIntegrity => throw _privateConstructorUsedError;

  /// Whether to migrate old data versions
  bool get enableMigration => throw _privateConstructorUsedError;

  /// Serializes this RecoveryOptions to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecoveryOptions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecoveryOptionsCopyWith<RecoveryOptions> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecoveryOptionsCopyWith<$Res> {
  factory $RecoveryOptionsCopyWith(
          RecoveryOptions value, $Res Function(RecoveryOptions) then) =
      _$RecoveryOptionsCopyWithImpl<$Res, RecoveryOptions>;
  @useResult
  $Res call(
      {bool showRecoveryDialog,
      bool autoRestore,
      int maxRecoveryAgeHours,
      bool validateIntegrity,
      bool enableMigration});
}

/// @nodoc
class _$RecoveryOptionsCopyWithImpl<$Res, $Val extends RecoveryOptions>
    implements $RecoveryOptionsCopyWith<$Res> {
  _$RecoveryOptionsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecoveryOptions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showRecoveryDialog = null,
    Object? autoRestore = null,
    Object? maxRecoveryAgeHours = null,
    Object? validateIntegrity = null,
    Object? enableMigration = null,
  }) {
    return _then(_value.copyWith(
      showRecoveryDialog: null == showRecoveryDialog
          ? _value.showRecoveryDialog
          : showRecoveryDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      autoRestore: null == autoRestore
          ? _value.autoRestore
          : autoRestore // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRecoveryAgeHours: null == maxRecoveryAgeHours
          ? _value.maxRecoveryAgeHours
          : maxRecoveryAgeHours // ignore: cast_nullable_to_non_nullable
              as int,
      validateIntegrity: null == validateIntegrity
          ? _value.validateIntegrity
          : validateIntegrity // ignore: cast_nullable_to_non_nullable
              as bool,
      enableMigration: null == enableMigration
          ? _value.enableMigration
          : enableMigration // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecoveryOptionsImplCopyWith<$Res>
    implements $RecoveryOptionsCopyWith<$Res> {
  factory _$$RecoveryOptionsImplCopyWith(_$RecoveryOptionsImpl value,
          $Res Function(_$RecoveryOptionsImpl) then) =
      __$$RecoveryOptionsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool showRecoveryDialog,
      bool autoRestore,
      int maxRecoveryAgeHours,
      bool validateIntegrity,
      bool enableMigration});
}

/// @nodoc
class __$$RecoveryOptionsImplCopyWithImpl<$Res>
    extends _$RecoveryOptionsCopyWithImpl<$Res, _$RecoveryOptionsImpl>
    implements _$$RecoveryOptionsImplCopyWith<$Res> {
  __$$RecoveryOptionsImplCopyWithImpl(
      _$RecoveryOptionsImpl _value, $Res Function(_$RecoveryOptionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecoveryOptions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showRecoveryDialog = null,
    Object? autoRestore = null,
    Object? maxRecoveryAgeHours = null,
    Object? validateIntegrity = null,
    Object? enableMigration = null,
  }) {
    return _then(_$RecoveryOptionsImpl(
      showRecoveryDialog: null == showRecoveryDialog
          ? _value.showRecoveryDialog
          : showRecoveryDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      autoRestore: null == autoRestore
          ? _value.autoRestore
          : autoRestore // ignore: cast_nullable_to_non_nullable
              as bool,
      maxRecoveryAgeHours: null == maxRecoveryAgeHours
          ? _value.maxRecoveryAgeHours
          : maxRecoveryAgeHours // ignore: cast_nullable_to_non_nullable
              as int,
      validateIntegrity: null == validateIntegrity
          ? _value.validateIntegrity
          : validateIntegrity // ignore: cast_nullable_to_non_nullable
              as bool,
      enableMigration: null == enableMigration
          ? _value.enableMigration
          : enableMigration // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecoveryOptionsImpl implements _RecoveryOptions {
  const _$RecoveryOptionsImpl(
      {this.showRecoveryDialog = true,
      this.autoRestore = false,
      this.maxRecoveryAgeHours = 24,
      this.validateIntegrity = true,
      this.enableMigration = true});

  factory _$RecoveryOptionsImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecoveryOptionsImplFromJson(json);

  /// Whether to show recovery dialog to user
  @override
  @JsonKey()
  final bool showRecoveryDialog;

  /// Whether to auto-restore without asking
  @override
  @JsonKey()
  final bool autoRestore;

  /// Maximum age of data to consider for recovery (in hours)
  @override
  @JsonKey()
  final int maxRecoveryAgeHours;

  /// Whether to validate data integrity before recovery
  @override
  @JsonKey()
  final bool validateIntegrity;

  /// Whether to migrate old data versions
  @override
  @JsonKey()
  final bool enableMigration;

  @override
  String toString() {
    return 'RecoveryOptions(showRecoveryDialog: $showRecoveryDialog, autoRestore: $autoRestore, maxRecoveryAgeHours: $maxRecoveryAgeHours, validateIntegrity: $validateIntegrity, enableMigration: $enableMigration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecoveryOptionsImpl &&
            (identical(other.showRecoveryDialog, showRecoveryDialog) ||
                other.showRecoveryDialog == showRecoveryDialog) &&
            (identical(other.autoRestore, autoRestore) ||
                other.autoRestore == autoRestore) &&
            (identical(other.maxRecoveryAgeHours, maxRecoveryAgeHours) ||
                other.maxRecoveryAgeHours == maxRecoveryAgeHours) &&
            (identical(other.validateIntegrity, validateIntegrity) ||
                other.validateIntegrity == validateIntegrity) &&
            (identical(other.enableMigration, enableMigration) ||
                other.enableMigration == enableMigration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, showRecoveryDialog, autoRestore,
      maxRecoveryAgeHours, validateIntegrity, enableMigration);

  /// Create a copy of RecoveryOptions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecoveryOptionsImplCopyWith<_$RecoveryOptionsImpl> get copyWith =>
      __$$RecoveryOptionsImplCopyWithImpl<_$RecoveryOptionsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecoveryOptionsImplToJson(
      this,
    );
  }
}

abstract class _RecoveryOptions implements RecoveryOptions {
  const factory _RecoveryOptions(
      {final bool showRecoveryDialog,
      final bool autoRestore,
      final int maxRecoveryAgeHours,
      final bool validateIntegrity,
      final bool enableMigration}) = _$RecoveryOptionsImpl;

  factory _RecoveryOptions.fromJson(Map<String, dynamic> json) =
      _$RecoveryOptionsImpl.fromJson;

  /// Whether to show recovery dialog to user
  @override
  bool get showRecoveryDialog;

  /// Whether to auto-restore without asking
  @override
  bool get autoRestore;

  /// Maximum age of data to consider for recovery (in hours)
  @override
  int get maxRecoveryAgeHours;

  /// Whether to validate data integrity before recovery
  @override
  bool get validateIntegrity;

  /// Whether to migrate old data versions
  @override
  bool get enableMigration;

  /// Create a copy of RecoveryOptions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecoveryOptionsImplCopyWith<_$RecoveryOptionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StorageConfig _$StorageConfigFromJson(Map<String, dynamic> json) {
  return _StorageConfig.fromJson(json);
}

/// @nodoc
mixin _$StorageConfig {
  /// Storage key prefix
  String get keyPrefix => throw _privateConstructorUsedError;

  /// Storage version for migration
  int get version => throw _privateConstructorUsedError;

  /// Whether to compress data
  bool get enableCompression => throw _privateConstructorUsedError;

  /// Whether to encrypt sensitive data
  bool get enableEncryption => throw _privateConstructorUsedError;

  /// Maximum storage size in bytes
  int get maxStorageSize => throw _privateConstructorUsedError; // 5MB default
  /// Cleanup old data after days
  int get cleanupAfterDays => throw _privateConstructorUsedError;

  /// Serializes this StorageConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StorageConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StorageConfigCopyWith<StorageConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StorageConfigCopyWith<$Res> {
  factory $StorageConfigCopyWith(
          StorageConfig value, $Res Function(StorageConfig) then) =
      _$StorageConfigCopyWithImpl<$Res, StorageConfig>;
  @useResult
  $Res call(
      {String keyPrefix,
      int version,
      bool enableCompression,
      bool enableEncryption,
      int maxStorageSize,
      int cleanupAfterDays});
}

/// @nodoc
class _$StorageConfigCopyWithImpl<$Res, $Val extends StorageConfig>
    implements $StorageConfigCopyWith<$Res> {
  _$StorageConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StorageConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? keyPrefix = null,
    Object? version = null,
    Object? enableCompression = null,
    Object? enableEncryption = null,
    Object? maxStorageSize = null,
    Object? cleanupAfterDays = null,
  }) {
    return _then(_value.copyWith(
      keyPrefix: null == keyPrefix
          ? _value.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      enableCompression: null == enableCompression
          ? _value.enableCompression
          : enableCompression // ignore: cast_nullable_to_non_nullable
              as bool,
      enableEncryption: null == enableEncryption
          ? _value.enableEncryption
          : enableEncryption // ignore: cast_nullable_to_non_nullable
              as bool,
      maxStorageSize: null == maxStorageSize
          ? _value.maxStorageSize
          : maxStorageSize // ignore: cast_nullable_to_non_nullable
              as int,
      cleanupAfterDays: null == cleanupAfterDays
          ? _value.cleanupAfterDays
          : cleanupAfterDays // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StorageConfigImplCopyWith<$Res>
    implements $StorageConfigCopyWith<$Res> {
  factory _$$StorageConfigImplCopyWith(
          _$StorageConfigImpl value, $Res Function(_$StorageConfigImpl) then) =
      __$$StorageConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String keyPrefix,
      int version,
      bool enableCompression,
      bool enableEncryption,
      int maxStorageSize,
      int cleanupAfterDays});
}

/// @nodoc
class __$$StorageConfigImplCopyWithImpl<$Res>
    extends _$StorageConfigCopyWithImpl<$Res, _$StorageConfigImpl>
    implements _$$StorageConfigImplCopyWith<$Res> {
  __$$StorageConfigImplCopyWithImpl(
      _$StorageConfigImpl _value, $Res Function(_$StorageConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of StorageConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? keyPrefix = null,
    Object? version = null,
    Object? enableCompression = null,
    Object? enableEncryption = null,
    Object? maxStorageSize = null,
    Object? cleanupAfterDays = null,
  }) {
    return _then(_$StorageConfigImpl(
      keyPrefix: null == keyPrefix
          ? _value.keyPrefix
          : keyPrefix // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      enableCompression: null == enableCompression
          ? _value.enableCompression
          : enableCompression // ignore: cast_nullable_to_non_nullable
              as bool,
      enableEncryption: null == enableEncryption
          ? _value.enableEncryption
          : enableEncryption // ignore: cast_nullable_to_non_nullable
              as bool,
      maxStorageSize: null == maxStorageSize
          ? _value.maxStorageSize
          : maxStorageSize // ignore: cast_nullable_to_non_nullable
              as int,
      cleanupAfterDays: null == cleanupAfterDays
          ? _value.cleanupAfterDays
          : cleanupAfterDays // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StorageConfigImpl implements _StorageConfig {
  const _$StorageConfigImpl(
      {this.keyPrefix = 'dalti_wizard',
      this.version = 1,
      this.enableCompression = false,
      this.enableEncryption = false,
      this.maxStorageSize = 5242880,
      this.cleanupAfterDays = 30});

  factory _$StorageConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$StorageConfigImplFromJson(json);

  /// Storage key prefix
  @override
  @JsonKey()
  final String keyPrefix;

  /// Storage version for migration
  @override
  @JsonKey()
  final int version;

  /// Whether to compress data
  @override
  @JsonKey()
  final bool enableCompression;

  /// Whether to encrypt sensitive data
  @override
  @JsonKey()
  final bool enableEncryption;

  /// Maximum storage size in bytes
  @override
  @JsonKey()
  final int maxStorageSize;
// 5MB default
  /// Cleanup old data after days
  @override
  @JsonKey()
  final int cleanupAfterDays;

  @override
  String toString() {
    return 'StorageConfig(keyPrefix: $keyPrefix, version: $version, enableCompression: $enableCompression, enableEncryption: $enableEncryption, maxStorageSize: $maxStorageSize, cleanupAfterDays: $cleanupAfterDays)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageConfigImpl &&
            (identical(other.keyPrefix, keyPrefix) ||
                other.keyPrefix == keyPrefix) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.enableCompression, enableCompression) ||
                other.enableCompression == enableCompression) &&
            (identical(other.enableEncryption, enableEncryption) ||
                other.enableEncryption == enableEncryption) &&
            (identical(other.maxStorageSize, maxStorageSize) ||
                other.maxStorageSize == maxStorageSize) &&
            (identical(other.cleanupAfterDays, cleanupAfterDays) ||
                other.cleanupAfterDays == cleanupAfterDays));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, keyPrefix, version,
      enableCompression, enableEncryption, maxStorageSize, cleanupAfterDays);

  /// Create a copy of StorageConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageConfigImplCopyWith<_$StorageConfigImpl> get copyWith =>
      __$$StorageConfigImplCopyWithImpl<_$StorageConfigImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StorageConfigImplToJson(
      this,
    );
  }
}

abstract class _StorageConfig implements StorageConfig {
  const factory _StorageConfig(
      {final String keyPrefix,
      final int version,
      final bool enableCompression,
      final bool enableEncryption,
      final int maxStorageSize,
      final int cleanupAfterDays}) = _$StorageConfigImpl;

  factory _StorageConfig.fromJson(Map<String, dynamic> json) =
      _$StorageConfigImpl.fromJson;

  /// Storage key prefix
  @override
  String get keyPrefix;

  /// Storage version for migration
  @override
  int get version;

  /// Whether to compress data
  @override
  bool get enableCompression;

  /// Whether to encrypt sensitive data
  @override
  bool get enableEncryption;

  /// Maximum storage size in bytes
  @override
  int get maxStorageSize; // 5MB default
  /// Cleanup old data after days
  @override
  int get cleanupAfterDays;

  /// Create a copy of StorageConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageConfigImplCopyWith<_$StorageConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

MigrationInfo _$MigrationInfoFromJson(Map<String, dynamic> json) {
  return _MigrationInfo.fromJson(json);
}

/// @nodoc
mixin _$MigrationInfo {
  int get fromVersion => throw _privateConstructorUsedError;
  int get toVersion => throw _privateConstructorUsedError;
  DateTime get migratedAt => throw _privateConstructorUsedError;
  List<String> get changes => throw _privateConstructorUsedError;
  bool? get dataLoss => throw _privateConstructorUsedError;

  /// Serializes this MigrationInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MigrationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MigrationInfoCopyWith<MigrationInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MigrationInfoCopyWith<$Res> {
  factory $MigrationInfoCopyWith(
          MigrationInfo value, $Res Function(MigrationInfo) then) =
      _$MigrationInfoCopyWithImpl<$Res, MigrationInfo>;
  @useResult
  $Res call(
      {int fromVersion,
      int toVersion,
      DateTime migratedAt,
      List<String> changes,
      bool? dataLoss});
}

/// @nodoc
class _$MigrationInfoCopyWithImpl<$Res, $Val extends MigrationInfo>
    implements $MigrationInfoCopyWith<$Res> {
  _$MigrationInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MigrationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromVersion = null,
    Object? toVersion = null,
    Object? migratedAt = null,
    Object? changes = null,
    Object? dataLoss = freezed,
  }) {
    return _then(_value.copyWith(
      fromVersion: null == fromVersion
          ? _value.fromVersion
          : fromVersion // ignore: cast_nullable_to_non_nullable
              as int,
      toVersion: null == toVersion
          ? _value.toVersion
          : toVersion // ignore: cast_nullable_to_non_nullable
              as int,
      migratedAt: null == migratedAt
          ? _value.migratedAt
          : migratedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      changes: null == changes
          ? _value.changes
          : changes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dataLoss: freezed == dataLoss
          ? _value.dataLoss
          : dataLoss // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MigrationInfoImplCopyWith<$Res>
    implements $MigrationInfoCopyWith<$Res> {
  factory _$$MigrationInfoImplCopyWith(
          _$MigrationInfoImpl value, $Res Function(_$MigrationInfoImpl) then) =
      __$$MigrationInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int fromVersion,
      int toVersion,
      DateTime migratedAt,
      List<String> changes,
      bool? dataLoss});
}

/// @nodoc
class __$$MigrationInfoImplCopyWithImpl<$Res>
    extends _$MigrationInfoCopyWithImpl<$Res, _$MigrationInfoImpl>
    implements _$$MigrationInfoImplCopyWith<$Res> {
  __$$MigrationInfoImplCopyWithImpl(
      _$MigrationInfoImpl _value, $Res Function(_$MigrationInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of MigrationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromVersion = null,
    Object? toVersion = null,
    Object? migratedAt = null,
    Object? changes = null,
    Object? dataLoss = freezed,
  }) {
    return _then(_$MigrationInfoImpl(
      fromVersion: null == fromVersion
          ? _value.fromVersion
          : fromVersion // ignore: cast_nullable_to_non_nullable
              as int,
      toVersion: null == toVersion
          ? _value.toVersion
          : toVersion // ignore: cast_nullable_to_non_nullable
              as int,
      migratedAt: null == migratedAt
          ? _value.migratedAt
          : migratedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      changes: null == changes
          ? _value._changes
          : changes // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dataLoss: freezed == dataLoss
          ? _value.dataLoss
          : dataLoss // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MigrationInfoImpl implements _MigrationInfo {
  const _$MigrationInfoImpl(
      {required this.fromVersion,
      required this.toVersion,
      required this.migratedAt,
      final List<String> changes = const [],
      this.dataLoss})
      : _changes = changes;

  factory _$MigrationInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$MigrationInfoImplFromJson(json);

  @override
  final int fromVersion;
  @override
  final int toVersion;
  @override
  final DateTime migratedAt;
  final List<String> _changes;
  @override
  @JsonKey()
  List<String> get changes {
    if (_changes is EqualUnmodifiableListView) return _changes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_changes);
  }

  @override
  final bool? dataLoss;

  @override
  String toString() {
    return 'MigrationInfo(fromVersion: $fromVersion, toVersion: $toVersion, migratedAt: $migratedAt, changes: $changes, dataLoss: $dataLoss)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MigrationInfoImpl &&
            (identical(other.fromVersion, fromVersion) ||
                other.fromVersion == fromVersion) &&
            (identical(other.toVersion, toVersion) ||
                other.toVersion == toVersion) &&
            (identical(other.migratedAt, migratedAt) ||
                other.migratedAt == migratedAt) &&
            const DeepCollectionEquality().equals(other._changes, _changes) &&
            (identical(other.dataLoss, dataLoss) ||
                other.dataLoss == dataLoss));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fromVersion, toVersion,
      migratedAt, const DeepCollectionEquality().hash(_changes), dataLoss);

  /// Create a copy of MigrationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MigrationInfoImplCopyWith<_$MigrationInfoImpl> get copyWith =>
      __$$MigrationInfoImplCopyWithImpl<_$MigrationInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MigrationInfoImplToJson(
      this,
    );
  }
}

abstract class _MigrationInfo implements MigrationInfo {
  const factory _MigrationInfo(
      {required final int fromVersion,
      required final int toVersion,
      required final DateTime migratedAt,
      final List<String> changes,
      final bool? dataLoss}) = _$MigrationInfoImpl;

  factory _MigrationInfo.fromJson(Map<String, dynamic> json) =
      _$MigrationInfoImpl.fromJson;

  @override
  int get fromVersion;
  @override
  int get toVersion;
  @override
  DateTime get migratedAt;
  @override
  List<String> get changes;
  @override
  bool? get dataLoss;

  /// Create a copy of MigrationInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MigrationInfoImplCopyWith<_$MigrationInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StorageStats _$StorageStatsFromJson(Map<String, dynamic> json) {
  return _StorageStats.fromJson(json);
}

/// @nodoc
mixin _$StorageStats {
  int get totalSize => throw _privateConstructorUsedError;
  int get itemCount => throw _privateConstructorUsedError;
  DateTime? get lastSaved => throw _privateConstructorUsedError;
  DateTime? get lastCleanup => throw _privateConstructorUsedError;
  List<String> get keys => throw _privateConstructorUsedError;

  /// Serializes this StorageStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StorageStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StorageStatsCopyWith<StorageStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StorageStatsCopyWith<$Res> {
  factory $StorageStatsCopyWith(
          StorageStats value, $Res Function(StorageStats) then) =
      _$StorageStatsCopyWithImpl<$Res, StorageStats>;
  @useResult
  $Res call(
      {int totalSize,
      int itemCount,
      DateTime? lastSaved,
      DateTime? lastCleanup,
      List<String> keys});
}

/// @nodoc
class _$StorageStatsCopyWithImpl<$Res, $Val extends StorageStats>
    implements $StorageStatsCopyWith<$Res> {
  _$StorageStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StorageStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalSize = null,
    Object? itemCount = null,
    Object? lastSaved = freezed,
    Object? lastCleanup = freezed,
    Object? keys = null,
  }) {
    return _then(_value.copyWith(
      totalSize: null == totalSize
          ? _value.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastSaved: freezed == lastSaved
          ? _value.lastSaved
          : lastSaved // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastCleanup: freezed == lastCleanup
          ? _value.lastCleanup
          : lastCleanup // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      keys: null == keys
          ? _value.keys
          : keys // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StorageStatsImplCopyWith<$Res>
    implements $StorageStatsCopyWith<$Res> {
  factory _$$StorageStatsImplCopyWith(
          _$StorageStatsImpl value, $Res Function(_$StorageStatsImpl) then) =
      __$$StorageStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalSize,
      int itemCount,
      DateTime? lastSaved,
      DateTime? lastCleanup,
      List<String> keys});
}

/// @nodoc
class __$$StorageStatsImplCopyWithImpl<$Res>
    extends _$StorageStatsCopyWithImpl<$Res, _$StorageStatsImpl>
    implements _$$StorageStatsImplCopyWith<$Res> {
  __$$StorageStatsImplCopyWithImpl(
      _$StorageStatsImpl _value, $Res Function(_$StorageStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of StorageStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalSize = null,
    Object? itemCount = null,
    Object? lastSaved = freezed,
    Object? lastCleanup = freezed,
    Object? keys = null,
  }) {
    return _then(_$StorageStatsImpl(
      totalSize: null == totalSize
          ? _value.totalSize
          : totalSize // ignore: cast_nullable_to_non_nullable
              as int,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastSaved: freezed == lastSaved
          ? _value.lastSaved
          : lastSaved // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastCleanup: freezed == lastCleanup
          ? _value.lastCleanup
          : lastCleanup // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      keys: null == keys
          ? _value._keys
          : keys // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StorageStatsImpl implements _StorageStats {
  const _$StorageStatsImpl(
      {required this.totalSize,
      required this.itemCount,
      this.lastSaved,
      this.lastCleanup,
      final List<String> keys = const []})
      : _keys = keys;

  factory _$StorageStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$StorageStatsImplFromJson(json);

  @override
  final int totalSize;
  @override
  final int itemCount;
  @override
  final DateTime? lastSaved;
  @override
  final DateTime? lastCleanup;
  final List<String> _keys;
  @override
  @JsonKey()
  List<String> get keys {
    if (_keys is EqualUnmodifiableListView) return _keys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_keys);
  }

  @override
  String toString() {
    return 'StorageStats(totalSize: $totalSize, itemCount: $itemCount, lastSaved: $lastSaved, lastCleanup: $lastCleanup, keys: $keys)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageStatsImpl &&
            (identical(other.totalSize, totalSize) ||
                other.totalSize == totalSize) &&
            (identical(other.itemCount, itemCount) ||
                other.itemCount == itemCount) &&
            (identical(other.lastSaved, lastSaved) ||
                other.lastSaved == lastSaved) &&
            (identical(other.lastCleanup, lastCleanup) ||
                other.lastCleanup == lastCleanup) &&
            const DeepCollectionEquality().equals(other._keys, _keys));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalSize, itemCount, lastSaved,
      lastCleanup, const DeepCollectionEquality().hash(_keys));

  /// Create a copy of StorageStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageStatsImplCopyWith<_$StorageStatsImpl> get copyWith =>
      __$$StorageStatsImplCopyWithImpl<_$StorageStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StorageStatsImplToJson(
      this,
    );
  }
}

abstract class _StorageStats implements StorageStats {
  const factory _StorageStats(
      {required final int totalSize,
      required final int itemCount,
      final DateTime? lastSaved,
      final DateTime? lastCleanup,
      final List<String> keys}) = _$StorageStatsImpl;

  factory _StorageStats.fromJson(Map<String, dynamic> json) =
      _$StorageStatsImpl.fromJson;

  @override
  int get totalSize;
  @override
  int get itemCount;
  @override
  DateTime? get lastSaved;
  @override
  DateTime? get lastCleanup;
  @override
  List<String> get keys;

  /// Create a copy of StorageStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageStatsImplCopyWith<_$StorageStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
