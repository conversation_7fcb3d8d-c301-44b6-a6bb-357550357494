/// Profile completion response from API
class ProfileCompletionResponse {
  final bool success;
  final ProfileCompletionData data;
  final String message;

  const ProfileCompletionResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ProfileCompletionResponse.fromJson(Map<String, dynamic> json) {
    return ProfileCompletionResponse(
      success: json['success'] as bool,
      data: ProfileCompletionData.fromJson(
        json['data'] as Map<String, dynamic>,
      ),
      message: json['message'] as String,
    );
  }
}

/// Main profile completion data
class ProfileCompletionData {
  final int overallPercentage;
  final bool overallCompleted;
  final ProfileCompletionBreakdown breakdown;
  final List<String> nextSteps;
  final List<String> criticalMissing;

  const ProfileCompletionData({
    required this.overallPercentage,
    required this.overallCompleted,
    required this.breakdown,
    required this.nextSteps,
    required this.criticalMissing,
  });

  factory ProfileCompletionData.fromJson(Map<String, dynamic> json) {
    return ProfileCompletionData(
      overallPercentage: json['overallPercentage'] as int,
      overallCompleted: json['overallCompleted'] as bool,
      breakdown: ProfileCompletionBreakdown.fromJson(
        json['breakdown'] as Map<String, dynamic>,
      ),
      nextSteps:
          (json['nextSteps'] as List<dynamic>).map((e) => e as String).toList(),
      criticalMissing:
          (json['criticalMissing'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
    );
  }
}

/// Breakdown of different completion categories
class ProfileCompletionBreakdown {
  final ProfileCompletionItem profilePicture;
  final ProfileCompletionItem providerInfo;
  final ProfileCompletionItem providingPlaces;
  final ProfileCompletionItem services;
  final ProfileCompletionItem queues;

  const ProfileCompletionBreakdown({
    required this.profilePicture,
    required this.providerInfo,
    required this.providingPlaces,
    required this.services,
    required this.queues,
  });

  factory ProfileCompletionBreakdown.fromJson(Map<String, dynamic> json) {
    return ProfileCompletionBreakdown(
      profilePicture: ProfileCompletionItem.fromJson(
        json['profilePicture'] as Map<String, dynamic>,
      ),
      providerInfo: ProfileCompletionItem.fromJson(
        json['providerInfo'] as Map<String, dynamic>,
      ),
      providingPlaces: ProfileCompletionItem.fromJson(
        json['providingPlaces'] as Map<String, dynamic>,
      ),
      services: ProfileCompletionItem.fromJson(
        json['services'] as Map<String, dynamic>,
      ),
      queues: ProfileCompletionItem.fromJson(
        json['queues'] as Map<String, dynamic>,
      ),
    );
  }
}

/// Individual completion item
class ProfileCompletionItem {
  final bool completed;
  final int percentage;
  final String details;
  final Map<String, bool>? requiredFields;
  final int? count;
  final int? validPlaces;
  final int? activeQueues;

  const ProfileCompletionItem({
    required this.completed,
    required this.percentage,
    required this.details,
    this.requiredFields,
    this.count,
    this.validPlaces,
    this.activeQueues,
  });

  factory ProfileCompletionItem.fromJson(Map<String, dynamic> json) {
    return ProfileCompletionItem(
      completed: json['completed'] as bool,
      percentage: json['percentage'] as int,
      details: json['details'] as String,
      requiredFields:
          json['requiredFields'] != null
              ? Map<String, bool>.from(json['requiredFields'] as Map)
              : null,
      count: json['count'] as int?,
      validPlaces: json['validPlaces'] as int?,
      activeQueues: json['activeQueues'] as int?,
    );
  }
}
