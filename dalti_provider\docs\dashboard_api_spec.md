# Dashboard API Specification

## Base URL
```
https://api.dalti.app/api/provider/dashboard
```

## Authentication
All endpoints require Bearer token authentication with provider session ID.

```http
Authorization: Bearer {sessionId}
```

## Endpoints

### 1. Get Business Overview
**GET** `/overview`

Returns key business metrics and performance indicators.

#### Response
```json
{
  "success": true,
  "data": {
    "todayRevenue": 1250.00,
    "todayAppointments": 12,
    "completedAppointments": 8,
    "cancelledAppointments": 1,
    "activeQueues": 3,
    "totalCustomersToday": 15,
    "customerSatisfaction": 4.8,
    "averageWaitTime": 12.5,
    "weeklyTrends": {
      "revenue": [800, 950, 1100, 1250, 1400, 1200, 1250],
      "appointments": [8, 10, 12, 12, 15, 11, 12]
    },
    "monthlyComparison": {
      "revenueChange": 15.2,
      "appointmentsChange": 8.5
    }
  }
}
```

### 2. Get Today's Schedule
**GET** `/schedule/today`

Returns today's appointments and queue status.

#### Response
```json
{
  "success": true,
  "data": {
    "nextAppointment": {
      "id": "apt_123",
      "customerName": "Ahmed Hassan",
      "serviceName": "Consultation",
      "scheduledTime": "2025-06-16T14:30:00Z",
      "estimatedDuration": 30,
      "status": "confirmed"
    },
    "queueStatuses": [
      {
        "queueId": "queue_1",
        "queueName": "General Consultation",
        "locationName": "Main Clinic",
        "waitingCount": 3,
        "averageWaitTime": 15,
        "isActive": true,
        "nextAvailableSlot": "2025-06-16T15:00:00Z"
      }
    ],
    "todayHours": {
      "openTime": "08:00",
      "closeTime": "18:00",
      "isOpen": true,
      "breakTimes": [
        {
          "startTime": "12:00",
          "endTime": "13:00",
          "description": "Lunch Break"
        }
      ]
    },
    "appointmentsSummary": {
      "total": 15,
      "completed": 8,
      "upcoming": 6,
      "cancelled": 1
    }
  }
}
```

### 3. Get Notifications
**GET** `/notifications`

Returns recent notifications and alerts.

#### Query Parameters
- `limit` (optional): Number of notifications to return (default: 10, max: 50)
- `unreadOnly` (optional): Return only unread notifications (default: false)

#### Response
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "notif_123",
        "type": "reschedule_request",
        "title": "Reschedule Request",
        "message": "Ahmed Hassan requested to reschedule appointment from 2:30 PM to 4:00 PM",
        "timestamp": "2025-06-16T13:45:00Z",
        "isRead": false,
        "priority": "medium",
        "actionData": {
          "appointmentId": "apt_123",
          "originalTime": "2025-06-16T14:30:00Z",
          "requestedTime": "2025-06-16T16:00:00Z"
        }
      },
      {
        "id": "notif_124",
        "type": "queue_alert",
        "title": "Long Wait Time Alert",
        "message": "General Consultation queue has 5+ customers waiting (avg wait: 25 min)",
        "timestamp": "2025-06-16T13:30:00Z",
        "isRead": true,
        "priority": "high",
        "actionData": {
          "queueId": "queue_1",
          "waitingCount": 5,
          "averageWaitTime": 25
        }
      }
    ],
    "unreadCount": 3,
    "totalCount": 15
  }
}
```

### 4. Get Quick Stats
**GET** `/quick-stats`

Returns real-time counters for dashboard widgets.

#### Response
```json
{
  "success": true,
  "data": {
    "currentWaitingCustomers": 8,
    "activeQueues": 3,
    "todayRevenue": 1250.00,
    "unreadNotifications": 3,
    "nextAppointmentIn": 45,
    "averageWaitTime": 12.5,
    "lastUpdated": "2025-06-16T13:50:00Z"
  }
}
```

### 5. Mark Notification as Read
**PATCH** `/notifications/{notificationId}/read`

Marks a specific notification as read.

#### Response
```json
{
  "success": true,
  "message": "Notification marked as read"
}
```

### 6. Emergency Queue Control
**POST** `/queues/emergency-control`

Pause or resume all queues for emergency situations.

#### Request Body
```json
{
  "action": "pause", // or "resume"
  "reason": "Emergency situation",
  "estimatedDuration": 30 // minutes
}
```

#### Response
```json
{
  "success": true,
  "message": "All queues paused successfully",
  "data": {
    "affectedQueues": 3,
    "estimatedResumeTime": "2025-06-16T14:30:00Z"
  }
}
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "DASHBOARD_ERROR",
    "message": "Unable to fetch dashboard data",
    "details": "Database connection timeout"
  }
}
```

### Common Error Codes
- `UNAUTHORIZED`: Invalid or expired session token
- `DASHBOARD_ERROR`: General dashboard data fetching error
- `QUEUE_ERROR`: Queue-related operation failed
- `NOTIFICATION_ERROR`: Notification operation failed
- `RATE_LIMIT_EXCEEDED`: Too many requests

## Rate Limits
- **Overview endpoint**: 12 requests per minute
- **Schedule endpoint**: 120 requests per minute (for real-time updates)
- **Notifications endpoint**: 60 requests per minute
- **Quick stats endpoint**: 360 requests per minute (for frequent polling)

## Caching Strategy
- **Overview data**: Cache for 5 minutes
- **Schedule data**: Cache for 30 seconds
- **Notifications**: No caching (real-time)
- **Quick stats**: Cache for 10 seconds

## WebSocket Support (Future Enhancement)
Real-time updates will be available via WebSocket connection:

```
wss://api.dalti.app/ws/provider/dashboard/{providerId}
```

### WebSocket Events
- `queue_update`: Queue status changed
- `new_appointment`: New appointment booked
- `notification`: New notification received
- `emergency_alert`: Emergency situation alert

---

*This API specification supports the Dalti Provider Dashboard implementation and provides all necessary endpoints for real-time business management.*
