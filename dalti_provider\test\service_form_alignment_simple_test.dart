import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/services/widgets/service_form.dart';

void main() {
  group('ServiceForm Alignment Simple Tests', () {
    testWidgets('Duration and Price fields should have consistent structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the Row containing duration and price fields
      final row = find.byWidgetPredicate(
        (widget) => widget is Row && 
                   widget.children.length >= 3 && // Two Expanded + SizedBox
                   widget.children.any((child) => child is Expanded),
      );
      expect(row, findsOneWidget);

      // Verify Row has crossAxisAlignment.start for proper alignment
      final rowWidget = tester.widget<Row>(row);
      expect(rowWidget.crossAxisAlignment, equals(CrossAxisAlignment.start));

      // Find both Column widgets (duration and price)
      final columns = find.descendant(
        of: row,
        matching: find.byType(Column),
      );
      expect(columns, findsNWidgets(2)); // Should find exactly 2 columns

      // Verify both columns have the same crossAxisAlignment
      final columnWidgets = tester.widgetList<Column>(columns).toList();
      expect(columnWidgets.length, equals(2));
      
      for (final column in columnWidgets) {
        expect(column.crossAxisAlignment, equals(CrossAxisAlignment.start));
      }
    });

    testWidgets('Duration and Price fields should have consistent label structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find duration label
      expect(find.text('Duration (minutes) *'), findsOneWidget);
      
      // Find price label
      expect(find.text('Price (\$) *'), findsOneWidget);

      // Verify both labels are Text widgets
      final durationLabel = find.text('Duration (minutes) *');
      final priceLabel = find.text('Price (\$) *');
      
      expect(durationLabel, findsOneWidget);
      expect(priceLabel, findsOneWidget);
    });

    testWidgets('Duration and Price input fields should be properly aligned', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the DropdownButtonFormField (duration)
      final durationField = find.byType(DropdownButtonFormField<int>);
      expect(durationField, findsOneWidget);

      // Find all TextFormFields and identify the price field by its position
      final textFields = find.byType(TextFormField);
      expect(textFields, findsAtLeastNWidgets(1));

      // Get the render boxes to check alignment
      final durationRenderBox = tester.renderObject(durationField) as RenderBox;
      
      // Find the first TextFormField that appears after the duration field
      final textFieldWidgets = tester.widgetList<TextFormField>(textFields);
      bool foundPriceField = false;
      
      for (int i = 0; i < textFieldWidgets.length; i++) {
        final textFieldFinder = textFields.at(i);
        final textFieldRenderBox = tester.renderObject(textFieldFinder) as RenderBox;
        
        final durationPosition = durationRenderBox.localToGlobal(Offset.zero);
        final textFieldPosition = textFieldRenderBox.localToGlobal(Offset.zero);
        
        // Check if this text field is horizontally aligned with duration field
        // (same Y coordinate) and to the right of it (greater X coordinate)
        if ((durationPosition.dy - textFieldPosition.dy).abs() < 5.0 && 
            textFieldPosition.dx > durationPosition.dx) {
          foundPriceField = true;
          
          // The Y coordinates should be the same (or very close)
          expect((durationPosition.dy - textFieldPosition.dy).abs(), lessThan(5.0),
              reason: 'Duration and Price fields should be aligned horizontally');
          break;
        }
      }
      
      expect(foundPriceField, isTrue, 
          reason: 'Should find a price field aligned with duration field');
    });

    testWidgets('Both fields should have consistent spacing structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find all SizedBox widgets with height 8 (spacing between label and field)
      final spacingBoxes = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.height == 8.0,
      );
      
      // Should find at least 2 (one for duration, one for price)
      expect(spacingBoxes, findsAtLeastNWidgets(2));

      // Find the SizedBox with width 16 (spacing between fields)
      final horizontalSpacing = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.width == 16.0,
      );
      expect(horizontalSpacing, findsOneWidget);
    });

    testWidgets('Input field decorations should be consistent', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the DropdownButtonFormField
      final durationField = find.byType(DropdownButtonFormField<int>);
      final durationWidget = tester.widget<DropdownButtonFormField<int>>(durationField);

      // Find any TextFormField (price field)
      final textFields = find.byType(TextFormField);
      expect(textFields, findsAtLeastNWidgets(1));

      // Both should have OutlineInputBorder
      expect(durationWidget.decoration?.border, isA<OutlineInputBorder>());
      
      // We can't directly access TextFormField decoration, but we can verify
      // that the fields are rendered consistently by checking their presence
      expect(durationField, findsOneWidget);
      expect(textFields, findsAtLeastNWidgets(1));
    });

    testWidgets('Form should display all required elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify all essential form elements are present
      expect(find.text('Service Title *'), findsOneWidget);
      expect(find.text('Description'), findsOneWidget);
      expect(find.text('Duration (minutes) *'), findsOneWidget);
      expect(find.text('Price (\$) *'), findsOneWidget);
      expect(find.text('Points Required'), findsOneWidget);
      expect(find.text('Service Settings'), findsOneWidget);
      
      // Verify input fields are present
      expect(find.byType(TextFormField), findsAtLeastNWidgets(3)); // Title, Description, Price, Points
      expect(find.byType(DropdownButtonFormField<int>), findsOneWidget); // Duration
    });

    testWidgets('Fields should maintain structure after interaction', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Interact with the form
      final titleField = find.byType(TextFormField).first;
      await tester.tap(titleField);
      await tester.enterText(titleField, 'Test Service');
      await tester.pumpAndSettle();

      // Verify structure is still intact
      expect(find.text('Duration (minutes) *'), findsOneWidget);
      expect(find.text('Price (\$) *'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<int>), findsOneWidget);
      
      // Verify the Row structure is still there
      final row = find.byWidgetPredicate(
        (widget) => widget is Row && 
                   widget.children.length >= 3 &&
                   widget.children.any((child) => child is Expanded),
      );
      expect(row, findsOneWidget);
    });

    test('ServiceForm layout structure should be logical', () {
      // Test the logical structure expectations
      
      // Both duration and price fields should follow the same pattern:
      // Row(crossAxisAlignment: start) -> [
      //   Expanded(Column -> Text + SizedBox + InputField),
      //   SizedBox(width: 16),
      //   Expanded(Column -> Text + SizedBox + InputField)
      // ]
      
      const expectedRowStructure = [
        'Row with crossAxisAlignment.start',
        '  Expanded(Duration Column)',
        '  SizedBox(width: 16)',
        '  Expanded(Price Column)',
      ];

      const expectedColumnStructure = [
        'Column with crossAxisAlignment.start',
        '  Text (label)',
        '  SizedBox(height: 8)',
        '  Input Field',
      ];

      // This test documents the expected structure
      expect(expectedRowStructure.length, equals(4));
      expect(expectedColumnStructure.length, equals(4));
      
      // Verify structure elements
      expect(expectedRowStructure[0], contains('crossAxisAlignment.start'));
      expect(expectedColumnStructure[1], contains('Text (label)'));
      expect(expectedColumnStructure[2], contains('SizedBox(height: 8)'));
    });
  });
}
