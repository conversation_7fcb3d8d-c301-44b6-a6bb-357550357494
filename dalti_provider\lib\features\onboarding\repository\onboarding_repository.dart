import '../models/onboarding_models.dart';

/// Abstract repository interface for onboarding data management
abstract class OnboardingRepository {
  /// Save onboarding progress to local storage
  Future<void> saveProgress(OnboardingData data);

  /// Load onboarding progress from local storage
  Future<OnboardingData?> loadProgress();

  /// Clear onboarding progress from local storage
  Future<void> clearProgress();

  /// Check if onboarding is completed
  Future<bool> isOnboardingCompleted();

  /// Mark onboarding as completed
  Future<void> markAsCompleted();

  /// Save business profile step
  Future<void> saveBusinessProfile(BusinessProfile profile);

  /// Load business profile step
  Future<BusinessProfile?> loadBusinessProfile();

  /// Save current step
  Future<void> saveCurrentStep(OnboardingStep step);

  /// Load current step
  Future<OnboardingStep?> loadCurrentStep();

  /// Get onboarding statistics
  Future<Map<String, dynamic>> getOnboardingStats();

  /// Submit completed onboarding data to backend
  Future<OnboardingCompletion> submitOnboarding(OnboardingData data);

  /// Validate step data
  Future<StepValidationResult> validateStep(OnboardingStep step, Map<String, dynamic> data);

  /// Check if user needs onboarding
  Future<bool> needsOnboarding();

  /// Reset onboarding state (for testing or re-onboarding)
  Future<void> resetOnboarding();
}
