import '../models/auth_models.dart';

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Request OTP for email verification during registration
  /// 
  /// [request] - Email OTP request with user details
  /// [providerCategoryId] - Category ID for provider registration
  /// [businessName] - Business name for provider
  /// [phone] - Phone number for provider
  /// 
  /// Returns [AuthResponse] indicating success/failure
  Future<AuthResponse> requestEmailOtp(
    EmailOtpRequest request, {
    required int providerCategoryId,
    required String businessName,
    required String phone,
  });

  /// Verify OTP and complete provider registration
  /// 
  /// [request] - Provider registration request with OTP and user details
  /// 
  /// Returns [AuthResponse] with authentication tokens and user data
  Future<AuthResponse> verifyOtpAndRegister(ProviderRegistrationRequest request);

  /// Login with email/phone and password
  /// 
  /// [request] - Login request with identifier and password
  /// 
  /// Returns [AuthResponse] with authentication tokens and user data
  Future<AuthResponse> login(LoginRequest request);

  /// Logout current user
  /// 
  /// Clears authentication tokens and session
  Future<void> logout();

  /// Get current user profile
  /// 
  /// Returns user profile data
  Future<Map<String, dynamic>> getProfile();

  /// Refresh authentication token
  /// 
  /// Returns [AuthResponse] with new tokens
  Future<AuthResponse> refreshToken();

  /// Check if user is currently authenticated
  /// 
  /// Returns true if user has valid authentication token
  bool get isAuthenticated;

  /// Get current user ID
  /// 
  /// Returns current user ID if authenticated, null otherwise
  String? get currentUserId;

  /// Get current user email
  /// 
  /// Returns current user email if authenticated, null otherwise
  String? get currentUserEmail;
}
