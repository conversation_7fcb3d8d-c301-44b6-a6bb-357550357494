import 'package:flutter/material.dart';
import '../models/schedule_models.dart';

class WeeklyScheduleGrid extends StatelessWidget {
  final List<Schedule> schedules;
  final Function(Schedule) onScheduleTap;
  final VoidCallback onAddSchedule;

  const WeeklyScheduleGrid({
    super.key,
    required this.schedules,
    required this.onScheduleTap,
    required this.onAddSchedule,
  });

  @override
  Widget build(BuildContext context) {
    // Group schedules by day of week
    final schedulesByDay = <int, List<Schedule>>{};
    for (final schedule in schedules) {
      schedulesByDay.putIfAbsent(schedule.dayOfWeek, () => []).add(schedule);
    }

    // Sort schedules within each day by start time
    for (final daySchedules in schedulesByDay.values) {
      daySchedules.sort((a, b) => a.startTime.compareTo(b.startTime));
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_view_week,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Weekly Schedule',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: onAddSchedule,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Schedule'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildWeeklyGrid(context, schedulesByDay),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyGrid(BuildContext context, Map<int, List<Schedule>> schedulesByDay) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header row with day names
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: days.map((day) {
                return Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Text(
                      day,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Schedule rows
          SizedBox(
            height: 300, // Fixed height for the grid
            child: Row(
              children: List.generate(7, (dayIndex) {
                final daySchedules = schedulesByDay[dayIndex] ?? [];
                
                return Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: dayIndex < 6 
                            ? BorderSide(color: Colors.grey.shade300)
                            : BorderSide.none,
                      ),
                    ),
                    child: _buildDayColumn(context, dayIndex, daySchedules),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayColumn(BuildContext context, int dayIndex, List<Schedule> daySchedules) {
    if (daySchedules.isEmpty) {
      return _buildEmptyDayColumn(context, dayIndex);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: daySchedules.map((schedule) {
          return _buildScheduleBlock(context, schedule);
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyDayColumn(BuildContext context, int dayIndex) {
    return InkWell(
      onTap: onAddSchedule,
      child: Container(
        height: double.infinity,
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle_outline,
              color: Colors.grey.shade400,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Add Schedule',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleBlock(BuildContext context, Schedule schedule) {
    final isToday = schedule.isToday;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: isToday 
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        child: InkWell(
          onTap: () => onScheduleTap(schedule),
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(
                color: isToday 
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade300,
                width: isToday ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: isToday 
                          ? Theme.of(context).primaryColor
                          : Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        schedule.startTime,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isToday 
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        schedule.endTime,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
                if (schedule.location != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 12,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          schedule.location!.name,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade500,
                            fontSize: 10,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
                if (isToday) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      'TODAY',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class WeeklyScheduleSummary extends StatelessWidget {
  final List<Schedule> schedules;

  const WeeklyScheduleSummary({
    super.key,
    required this.schedules,
  });

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weekly Summary',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Hours',
                    '${stats['totalHours'].toStringAsFixed(1)}h',
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Active Days',
                    '${stats['activeDays']}/7',
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Schedules',
                    '${stats['totalSchedules']}',
                    Icons.event,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Map<String, dynamic> _calculateStats() {
    final activeDays = schedules.map((s) => s.dayOfWeek).toSet().length;
    double totalHours = 0;

    for (final schedule in schedules) {
      final startMinutes = _timeToMinutes(schedule.startTime);
      final endMinutes = _timeToMinutes(schedule.endTime);
      totalHours += (endMinutes - startMinutes) / 60.0;
    }

    return {
      'totalHours': totalHours,
      'activeDays': activeDays,
      'totalSchedules': schedules.length,
    };
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  }
}
