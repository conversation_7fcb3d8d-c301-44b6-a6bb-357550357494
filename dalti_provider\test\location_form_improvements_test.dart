import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/locations/constants/algerian_cities.dart';
import 'package:dalti_provider/features/locations/services/location_service.dart';
import 'package:dalti_provider/features/locations/widgets/location_form_fields.dart';

void main() {
  group('Location Form Improvements Tests', () {
    group('Algerian Cities', () {
      test('should contain all 58 Algerian cities', () {
        expect(AlgerianCities.cities.length, equals(58));
        
        // Verify some major cities are included
        expect(AlgerianCities.cities, contains('Alger'));
        expect(AlgerianCities.cities, contains('Oran'));
        expect(AlgerianCities.cities, contains('Constantine'));
        expect(AlgerianCities.cities, contains('Annaba'));
        expect(AlgerianCities.cities, contains('Blida'));
      });

      test('should provide sorted cities', () {
        final sortedCities = AlgerianCities.sortedCities;
        expect(sortedCities.length, equals(AlgerianCities.cities.length));
        
        // Verify it's actually sorted
        for (int i = 1; i < sortedCities.length; i++) {
          expect(sortedCities[i-1].compareTo(sortedCities[i]), lessThanOrEqualTo(0));
        }
      });

      test('should validate city names correctly', () {
        expect(AlgerianCities.isValidCity('Alger'), isTrue);
        expect(AlgerianCities.isValidCity('alger'), isTrue); // Case insensitive
        expect(AlgerianCities.isValidCity('ALGER'), isTrue); // Case insensitive
        expect(AlgerianCities.isValidCity('Paris'), isFalse); // Not Algerian city
        expect(AlgerianCities.isValidCity(''), isFalse); // Empty string
      });

      test('should format city names correctly', () {
        expect(AlgerianCities.getFormattedCityName('alger'), equals('Alger'));
        expect(AlgerianCities.getFormattedCityName('ORAN'), equals('Oran'));
        expect(AlgerianCities.getFormattedCityName('constantine'), equals('Constantine'));
        expect(AlgerianCities.getFormattedCityName('Paris'), isNull);
      });

      test('should provide popular cities list', () {
        final popularCities = AlgerianCities.popularCities;
        expect(popularCities.length, greaterThan(0));
        expect(popularCities, contains('Alger'));
        expect(popularCities, contains('Oran'));
        expect(popularCities, contains('Constantine'));
        
        // All popular cities should be valid
        for (final city in popularCities) {
          expect(AlgerianCities.isValidCity(city), isTrue);
        }
      });
    });

    group('Location Service', () {
      test('should validate latitude correctly', () {
        expect(LocationService.isValidLatitude(0), isTrue);
        expect(LocationService.isValidLatitude(90), isTrue);
        expect(LocationService.isValidLatitude(-90), isTrue);
        expect(LocationService.isValidLatitude(45.5), isTrue);
        expect(LocationService.isValidLatitude(-45.5), isTrue);
        
        expect(LocationService.isValidLatitude(91), isFalse);
        expect(LocationService.isValidLatitude(-91), isFalse);
        expect(LocationService.isValidLatitude(180), isFalse);
      });

      test('should validate longitude correctly', () {
        expect(LocationService.isValidLongitude(0), isTrue);
        expect(LocationService.isValidLongitude(180), isTrue);
        expect(LocationService.isValidLongitude(-180), isTrue);
        expect(LocationService.isValidLongitude(90.5), isTrue);
        expect(LocationService.isValidLongitude(-90.5), isTrue);
        
        expect(LocationService.isValidLongitude(181), isFalse);
        expect(LocationService.isValidLongitude(-181), isFalse);
        expect(LocationService.isValidLongitude(360), isFalse);
      });

      test('should format coordinates correctly', () {
        expect(LocationService.formatCoordinates(36.7538, 3.0588), 
               equals('36.753800, 3.058800'));
        expect(LocationService.formatCoordinates(-36.7538, -3.0588), 
               equals('-36.753800, -3.058800'));
        expect(LocationService.formatCoordinates(0, 0), 
               equals('0.000000, 0.000000'));
      });

      test('should calculate distance between points', () {
        // Distance between Algiers and Oran (approximately 432 km)
        final algiersLat = 36.7538;
        final algiersLng = 3.0588;
        final oranLat = 35.6969;
        final oranLng = -0.6331;
        
        final distance = LocationService.getDistanceBetween(
          startLatitude: algiersLat,
          startLongitude: algiersLng,
          endLatitude: oranLat,
          endLongitude: oranLng,
        );
        
        // Should be approximately 432 km (432,000 meters)
        expect(distance, greaterThan(400000)); // At least 400 km
        expect(distance, lessThan(500000)); // Less than 500 km
      });
    });

    group('Location Result', () {
      test('should create success result correctly', () {
        final result = LocationResult.success(
          latitude: 36.7538,
          longitude: 3.0588,
          accuracy: 5.0,
        );
        
        expect(result.isSuccess, isTrue);
        expect(result.latitude, equals(36.7538));
        expect(result.longitude, equals(3.0588));
        expect(result.accuracy, equals(5.0));
        expect(result.error, isNull);
        expect(result.formattedCoordinates, equals('36.753800, 3.058800'));
      });

      test('should create error result correctly', () {
        final result = LocationResult.error('Location permission denied');
        
        expect(result.isSuccess, isFalse);
        expect(result.latitude, isNull);
        expect(result.longitude, isNull);
        expect(result.accuracy, isNull);
        expect(result.error, equals('Location permission denied'));
        expect(result.formattedCoordinates, equals(''));
      });

      test('should provide accuracy descriptions', () {
        final highAccuracy = LocationResult.success(
          latitude: 36.7538,
          longitude: 3.0588,
          accuracy: 3.0,
        );
        expect(highAccuracy.accuracyDescription, contains('Very High Accuracy'));
        
        final goodAccuracy = LocationResult.success(
          latitude: 36.7538,
          longitude: 3.0588,
          accuracy: 25.0,
        );
        expect(goodAccuracy.accuracyDescription, contains('Good Accuracy'));
        
        final lowAccuracy = LocationResult.success(
          latitude: 36.7538,
          longitude: 3.0588,
          accuracy: 100.0,
        );
        expect(lowAccuracy.accuracyDescription, contains('Low Accuracy'));
      });
    });

    testWidgets('City field should display as dropdown with Algerian cities', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationFormFields.cityField(
              controller: controller,
            ),
          ),
        ),
      );

      // Find the dropdown
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
      
      // Tap to open dropdown
      await tester.tap(find.byType(DropdownButtonFormField<String>));
      await tester.pumpAndSettle();

      // Should find Algerian cities in the dropdown
      expect(find.text('Alger'), findsOneWidget);
      expect(find.text('Oran'), findsOneWidget);
      expect(find.text('Constantine'), findsOneWidget);
    });

    testWidgets('Country field should be disabled and show Algeria', (WidgetTester tester) async {
      final controller = TextEditingController();
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationFormFields.countryField(
              controller: controller,
            ),
          ),
        ),
      );

      // Find the text field
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);
      
      // Verify it's disabled
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.enabled, isFalse);
      
      // Verify controller has Algeria
      expect(controller.text, equals('Algeria'));
      
      // Should show lock icon
      expect(find.byIcon(Icons.lock), findsOneWidget);
    });

    testWidgets('Coordinates widget should display current location button', (WidgetTester tester) async {
      final latController = TextEditingController();
      final lngController = TextEditingController();
      bool locationChanged = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LocationFormFields.coordinatesWidget(
              latitudeController: latController,
              longitudeController: lngController,
              onLocationChanged: () => locationChanged = true,
            ),
          ),
        ),
      );

      // Should find the current location button
      expect(find.text('Use Current Location'), findsOneWidget);
      expect(find.byIcon(Icons.my_location), findsOneWidget);
      
      // Should find disabled coordinate input fields
      final textFields = find.byType(TextFormField);
      expect(textFields, findsNWidgets(2)); // Latitude and longitude
      
      // Verify fields are disabled (read-only)
      final latField = tester.widget<TextFormField>(textFields.first);
      final lngField = tester.widget<TextFormField>(textFields.last);
      expect(latField.enabled, isFalse);
      expect(lngField.enabled, isFalse);
    });

    test('Algeria should be the default country for location forms', () {
      // This test documents the business requirement
      const expectedCountry = 'Algeria';
      const businessReason = 'We only operate in Algeria initially';
      
      expect(expectedCountry, equals('Algeria'));
      expect(businessReason, contains('Algeria'));
      
      // Verify Algeria is a valid country name
      expect(expectedCountry.length, greaterThan(2));
      expect(expectedCountry, isNot(equals('')));
    });

    test('Location form should support all Algerian administrative divisions', () {
      // Algeria has 58 wilayas (provinces/administrative divisions)
      const expectedWilayaCount = 58;
      
      expect(AlgerianCities.cities.length, equals(expectedWilayaCount));
      
      // Verify major economic centers are included
      const majorCities = ['Alger', 'Oran', 'Constantine', 'Annaba'];
      for (final city in majorCities) {
        expect(AlgerianCities.cities, contains(city),
            reason: '$city should be included as a major Algerian city');
      }
    });
  });
}
