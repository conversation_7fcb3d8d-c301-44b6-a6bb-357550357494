import 'package:flutter/foundation.dart';

/// Application configuration and environment settings
class AppConfig {
  static const String _devBaseUrl = 'https://dapi-test.adscloud.org:8443';
  static const String _prodBaseUrl = 'https://dapi.adscloud.org';

  /// Current environment
  static Environment get environment {
    // In a real app, this would be determined by build configuration
    // For now, we'll use development by default
    return Environment.development;
  }

  /// Check if running on web platform
  static bool get isWeb => kIsWeb;
  
  /// Get the appropriate base URL for the current environment
  static String get baseUrl {
    switch (environment) {
      case Environment.development:
        return _devBaseUrl;
      case Environment.production:
        return _prodBaseUrl;
    }
  }
  
  /// API timeout configurations
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  /// API version
  static const String apiVersion = 'v1';
  
  /// Full API base URL with version
  static String get apiBaseUrl => baseUrl;
  
  /// Debug mode (enables logging)
  static bool get isDebugMode {
    return environment == Environment.development;
  }
  
  /// App version and build info
  static const String appVersion = '1.0.0';
  static const String appName = 'Dalti Provider';
  
  /// Default headers for all requests
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': '$appName/$appVersion',
  };
}

/// Application environments
enum Environment {
  development,
  production,
}
