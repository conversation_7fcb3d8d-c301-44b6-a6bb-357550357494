/// List of all 85 Algerian cities (wilayas)
class AlgerianCities {
  static const List<String> cities = [
    'Adrar',
    'Chlef',
    'Laghouat',
    'Oum El Bouaghi',
    'Batna',
    'Béjaïa',
    'Biskra',
    'Béchar',
    'Blida',
    'Bouira',
    'Tamanrasset',
    'Tébessa',
    'Tlemcen',
    'Tiaret',
    'Tizi Ouzou',
    'Alger',
    'Djelfa',
    'Jijel',
    'Sétif',
    'Saïda',
    'Skikda',
    'Sidi Bel Abbès',
    'Annaba',
    'Guelma',
    'Constantine',
    'Médéa',
    'Mostaganem',
    '<PERSON>\'<PERSON><PERSON>',
    '<PERSON>sca<PERSON>',
    'Ouarg<PERSON>',
    'Oran',
    'El Bayadh',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>rdj Bou Arréridj',
    'Bo<PERSON>rdès',
    'El Tarf',
    'Tindouf',
    'Tissemsilt',
    'El Oued',
    'Khenchela',
    'Souk Ahras',
    'Tipaz<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>hardaïa',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    'Bordj Badji Mokhtar',
    '<PERSON>uled D<PERSON>l',
    'Béni Abbès',
    'In Salah',
    'In Guezzam',
    'Touggourt',
    'Djanet',
    'El M\'Ghair',
    'El Meniaa',
  ];

  /// Get a sorted list of cities for dropdown display
  static List<String> get sortedCities {
    final sorted = List<String>.from(cities);
    sorted.sort();
    return sorted;
  }

  /// Check if a city name is valid
  static bool isValidCity(String cityName) {
    return cities.any((city) => city.toLowerCase() == cityName.toLowerCase());
  }

  /// Get city name with proper capitalization
  static String? getFormattedCityName(String cityName) {
    for (final city in cities) {
      if (city.toLowerCase() == cityName.toLowerCase()) {
        return city;
      }
    }
    return null;
  }

  /// Get popular cities (major cities that are commonly used)
  static List<String> get popularCities {
    return [
      'Alger',
      'Oran',
      'Constantine',
      'Annaba',
      'Blida',
      'Batna',
      'Djelfa',
      'Sétif',
      'Sidi Bel Abbès',
      'Biskra',
      'Tébessa',
      'El Oued',
      'Skikda',
      'Tiaret',
      'Béjaïa',
      'Tlemcen',
      'Ouargla',
      'Mostaganem',
      'Bordj Bou Arréridj',
      'Chlef',
    ];
  }
}
