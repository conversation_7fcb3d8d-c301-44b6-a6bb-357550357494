import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Quick actions panel for common provider tasks
class QuickActionsPanel extends ConsumerWidget {
  const QuickActionsPanel({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Quick Actions',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Actions grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _QuickActionButton(
                  icon: Icons.room_service,
                  label: 'New Service',
                  color: Colors.blue,
                  onTap: () => _handleAction(context, 'new_service'),
                ),
                _QuickActionButton(
                  icon: Icons.queue,
                  label: 'New Queue',
                  color: Colors.orange,
                  onTap: () => _handleAction(context, 'new_queue'),
                ),
                _QuickActionButton(
                  icon: Icons.calendar_today,
                  label: 'New Appointment',
                  color: Colors.green,
                  onTap: () => _handleAction(context, 'new_appointment'),
                ),
                _QuickActionButton(
                  icon: Icons.person_add,
                  label: 'New Customer',
                  color: Colors.purple,
                  onTap: () => _handleAction(context, 'new_customer'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Handle quick action tap
  void _handleAction(BuildContext context, String action) {
    switch (action) {
      case 'new_service':
        context.push('/services/create');
        break;
      case 'new_queue':
        context.push('/queues/add');
        break;
      case 'new_appointment':
        context.push('/appointments/add');
        break;
      case 'new_customer':
        context.push('/customers/new');
        break;
    }
  }
}

/// Individual quick action button widget
class _QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback? onTap;

  const _QuickActionButton({
    required this.icon,
    required this.label,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: color.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
