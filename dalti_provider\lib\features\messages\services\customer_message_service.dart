import '../../customers/models/customer_models.dart';
import '../../customers/repository/customer_repository.dart';
import '../models/message_models.dart';

/// Service for integrating customer data with messages
class CustomerMessageService {
  final CustomerRepository _customerRepository;

  CustomerMessageService(this._customerRepository);

  /// Get customer information for a conversation
  Future<Customer?> getCustomerForConversation(String customerId) async {
    try {
      return await _customerRepository.getCustomer(customerId);
    } catch (e) {
      print('[CustomerMessageService] Error getting customer: $e');
      return null;
    }
  }

  /// Search customers for starting new conversations
  Future<List<Customer>> searchCustomersForConversation(String query) async {
    try {
      return await _customerRepository.searchCustomers(query);
    } catch (e) {
      print('[CustomerMessageService] Error searching customers: $e');
      return [];
    }
  }

  /// Get recent customers for quick conversation start
  Future<List<Customer>> getRecentCustomers({int limit = 10}) async {
    try {
      return await _customerRepository.getRecentCustomers(limit: limit);
    } catch (e) {
      print('[CustomerMessageService] Error getting recent customers: $e');
      return [];
    }
  }

  /// Get customer display name for messages
  String getCustomerDisplayName(Customer customer) {
    return '${customer.firstName} ${customer.lastName}'.trim();
  }

  /// Get customer initials for avatar
  String getCustomerInitials(Customer customer) {
    final firstName = customer.firstName.trim();
    final lastName = customer.lastName.trim();
    
    if (firstName.isEmpty && lastName.isEmpty) return '?';
    
    if (firstName.isNotEmpty && lastName.isNotEmpty) {
      return '${firstName[0]}${lastName[0]}'.toUpperCase();
    } else if (firstName.isNotEmpty) {
      return firstName[0].toUpperCase();
    } else {
      return lastName[0].toUpperCase();
    }
  }

  /// Check if customer can receive messages
  bool canCustomerReceiveMessages(Customer customer) {
    // Check if customer has a phone number or email
    return customer.phoneNumber?.isNotEmpty == true || 
           customer.email?.isNotEmpty == true;
  }

  /// Get customer contact info for conversation
  String? getCustomerContactInfo(Customer customer) {
    if (customer.phoneNumber?.isNotEmpty == true) {
      return customer.phoneNumber;
    } else if (customer.email?.isNotEmpty == true) {
      return customer.email;
    }
    return null;
  }

  /// Create conversation metadata from customer
  Map<String, dynamic> createConversationMetadata(Customer customer) {
    return {
      'customerId': customer.id,
      'customerName': getCustomerDisplayName(customer),
      'customerPhone': customer.phoneNumber,
      'customerEmail': customer.email,
      'customerCity': customer.city,
      'customerWilaya': customer.wilaya,
      'totalAppointments': customer.totalAppointments,
      'totalSpent': customer.totalSpent,
      'lastAppointmentDate': customer.lastAppointmentDate?.toIso8601String(),
      'customerStatus': customer.status.name,
      'createdAt': DateTime.now().toIso8601String(),
    };
  }

  /// Extract customer info from conversation
  CustomerInfo extractCustomerInfoFromConversation(Conversation conversation) {
    return CustomerInfo(
      id: conversation.customerId,
      name: conversation.customerName,
      phone: conversation.customerPhone,
      avatar: conversation.customerAvatar,
    );
  }

  /// Validate customer for conversation creation
  ValidationResult validateCustomerForConversation(Customer customer) {
    if (!canCustomerReceiveMessages(customer)) {
      return ValidationResult(
        isValid: false,
        error: 'Customer must have a phone number or email to receive messages',
      );
    }

    if (customer.status == CustomerStatus.blocked) {
      return ValidationResult(
        isValid: false,
        error: 'Cannot start conversation with blocked customer',
      );
    }

    if (customer.status == CustomerStatus.inactive) {
      return ValidationResult(
        isValid: false,
        error: 'Customer account is inactive',
      );
    }

    return ValidationResult(isValid: true);
  }
}

/// Customer info extracted from conversation
class CustomerInfo {
  final String id;
  final String name;
  final String? phone;
  final String? avatar;

  const CustomerInfo({
    required this.id,
    required this.name,
    this.phone,
    this.avatar,
  });
}

/// Validation result for customer operations
class ValidationResult {
  final bool isValid;
  final String? error;

  const ValidationResult({
    required this.isValid,
    this.error,
  });
}
