import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/services/widgets/service_form.dart';
import 'package:dalti_provider/features/services/models/service_models.dart';

void main() {
  group('ServiceForm Alignment Tests', () {
    testWidgets('Duration and Price fields should have consistent structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the Row containing duration and price fields
      final row = find.byWidgetPredicate(
        (widget) => widget is Row && 
                   widget.children.length >= 3 && // Two Expanded + SizedBox
                   widget.children.any((child) => child is Expanded),
      );
      expect(row, findsOneWidget);

      // Verify Row has crossAxisAlignment.start for proper alignment
      final rowWidget = tester.widget<Row>(row);
      expect(rowWidget.crossAxisAlignment, equals(CrossAxisAlignment.start));

      // Find both Column widgets (duration and price)
      final columns = find.descendant(
        of: row,
        matching: find.byType(Column),
      );
      expect(columns, findsNWidgets(2)); // Should find exactly 2 columns

      // Verify both columns have the same crossAxisAlignment
      final columnWidgets = tester.widgetList<Column>(columns).toList();
      expect(columnWidgets.length, equals(2));
      
      for (final column in columnWidgets) {
        expect(column.crossAxisAlignment, equals(CrossAxisAlignment.start));
      }
    });

    testWidgets('Duration and Price fields should have consistent label structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find duration label
      expect(find.text('Duration (minutes) *'), findsOneWidget);
      
      // Find price label
      expect(find.text('Price (\$) *'), findsOneWidget);

      // Verify both labels are Text widgets with consistent styling
      final durationLabel = find.text('Duration (minutes) *');
      final priceLabel = find.text('Price (\$) *');
      
      expect(durationLabel, findsOneWidget);
      expect(priceLabel, findsOneWidget);
    });

    testWidgets('Duration and Price input fields should be properly aligned', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the DropdownButtonFormField (duration)
      final durationField = find.byType(DropdownButtonFormField<int>);
      expect(durationField, findsOneWidget);

      // Find the TextFormField for price (should be the one with prefixText)
      final priceField = find.byWidgetPredicate(
        (widget) => widget is TextFormField &&
                   widget.decoration?.prefixText == '\$ ',
      );
      expect(priceField, findsOneWidget);

      // Get the render boxes to check alignment
      final durationRenderBox = tester.renderObject(durationField) as RenderBox;
      final priceRenderBox = tester.renderObject(priceField) as RenderBox;

      // Both fields should have the same top position (Y coordinate)
      // since they're in the same row with consistent structure
      final durationPosition = durationRenderBox.localToGlobal(Offset.zero);
      final pricePosition = priceRenderBox.localToGlobal(Offset.zero);

      // The Y coordinates should be the same (or very close due to floating point precision)
      expect((durationPosition.dy - pricePosition.dy).abs(), lessThan(1.0),
          reason: 'Duration and Price fields should be aligned horizontally');
    });

    testWidgets('Both fields should have consistent spacing structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find all SizedBox widgets with height 8 (spacing between label and field)
      final spacingBoxes = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.height == 8.0,
      );
      
      // Should find at least 2 (one for duration, one for price)
      expect(spacingBoxes, findsAtLeastNWidgets(2));

      // Find the SizedBox with width 16 (spacing between fields)
      final horizontalSpacing = find.byWidgetPredicate(
        (widget) => widget is SizedBox && widget.width == 16.0,
      );
      expect(horizontalSpacing, findsOneWidget);
    });

    testWidgets('Input field decorations should be consistent', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the DropdownButtonFormField
      final durationField = find.byType(DropdownButtonFormField<int>);
      final durationWidget = tester.widget<DropdownButtonFormField<int>>(durationField);

      // Find the price TextFormField
      final priceField = find.byWidgetPredicate(
        (widget) => widget is TextFormField &&
                   widget.decoration?.prefixText == '\$ ',
      );
      final priceWidget = tester.widget<TextFormField>(priceField);

      // Both should have OutlineInputBorder
      expect(durationWidget.decoration?.border, isA<OutlineInputBorder>());
      expect(priceWidget.decoration?.border, isA<OutlineInputBorder>());
    });

    testWidgets('Form validation should work for both fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ServiceForm(
                onCreateService: (request) {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the form
      final form = find.byType(Form);
      expect(form, findsOneWidget);

      // Try to validate without filling required fields
      final formWidget = tester.widget<Form>(form);
      final formState = formWidget.key as GlobalKey<FormState>?;
      
      if (formState != null) {
        final isValid = formState.currentState?.validate() ?? false;
        await tester.pumpAndSettle();

        // Should show validation errors for both duration and price
        // (Duration might not show error if it has a default value)
        expect(find.text('Price is required'), findsOneWidget);
      }
    });

    testWidgets('Fields should maintain alignment with different content', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ServiceForm(
              onCreateService: (request) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Enter text in the price field
      final priceField = find.byWidgetPredicate(
        (widget) => widget is TextFormField &&
                   widget.decoration?.prefixText == '\$ ',
      );
      
      await tester.tap(priceField);
      await tester.enterText(priceField, '50.00');
      await tester.pumpAndSettle();

      // Change duration selection
      final durationField = find.byType(DropdownButtonFormField<int>);
      await tester.tap(durationField);
      await tester.pumpAndSettle();

      // Select a different duration (if dropdown opens)
      final dropdownItem = find.text('30min').last;
      if (tester.any(dropdownItem)) {
        await tester.tap(dropdownItem);
        await tester.pumpAndSettle();
      }

      // Verify fields are still aligned after content changes
      final durationRenderBox = tester.renderObject(durationField) as RenderBox;
      final priceRenderBox = tester.renderObject(priceField) as RenderBox;

      final durationPosition = durationRenderBox.localToGlobal(Offset.zero);
      final pricePosition = priceRenderBox.localToGlobal(Offset.zero);

      expect((durationPosition.dy - pricePosition.dy).abs(), lessThan(1.0),
          reason: 'Fields should remain aligned after content changes');
    });

    test('ServiceForm structure should be consistent', () {
      // Test the logical structure of the form layout
      
      // Both fields should follow the same pattern:
      // Column -> Text (label) -> SizedBox (spacing) -> Input Field
      
      const expectedStructure = [
        'Column',
        '  Text (label)',
        '  SizedBox (height: 8)',
        '  Input Field (DropdownButtonFormField or TextFormField)',
      ];

      // This test documents the expected structure
      expect(expectedStructure.length, equals(4));
      expect(expectedStructure[0], equals('Column'));
      expect(expectedStructure[1], contains('Text (label)'));
      expect(expectedStructure[2], contains('SizedBox (height: 8)'));
      expect(expectedStructure[3], contains('Input Field'));
    });
  });
}
