import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/onboarding_models.dart';

/// Navigation state for the wizard
class WizardNavigationState {
  final OnboardingStep currentStep;
  final int currentStepIndex;
  final bool canGoNext;
  final bool canGoPrevious;
  final bool isLoading;
  final String? error;
  final Map<OnboardingStep, bool> stepCompletionStatus;

  const WizardNavigationState({
    required this.currentStep,
    required this.currentStepIndex,
    required this.canGoNext,
    required this.canGoPrevious,
    this.isLoading = false,
    this.error,
    this.stepCompletionStatus = const {},
  });

  WizardNavigationState copyWith({
    OnboardingStep? currentStep,
    int? currentStepIndex,
    bool? canGoNext,
    bool? canGoPrevious,
    bool? isLoading,
    String? error,
    Map<OnboardingStep, bool>? stepCompletionStatus,
  }) {
    return WizardNavigationState(
      currentStep: currentStep ?? this.currentStep,
      currentStepIndex: currentStepIndex ?? this.currentStepIndex,
      canGoNext: canGoNext ?? this.canGoNext,
      canGoPrevious: canGoPrevious ?? this.canGoPrevious,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      stepCompletionStatus: stepCompletionStatus ?? this.stepCompletionStatus,
    );
  }

  /// Get total number of steps
  int get totalSteps => OnboardingStep.totalSteps;

  /// Get progress percentage (0.0 to 1.0)
  double get progress {
    if (totalSteps == 0) return 0.0;
    return (currentStepIndex + 1) / totalSteps;
  }

  /// Check if a specific step is completed
  bool isStepCompleted(OnboardingStep step) {
    return stepCompletionStatus[step] ?? false;
  }

  /// Get list of all steps (excluding completed)
  List<OnboardingStep> get allSteps {
    return OnboardingStep.values.where((step) => step != OnboardingStep.completed).toList();
  }
}

/// Wizard navigation provider
class WizardNavigationNotifier extends StateNotifier<WizardNavigationState> {
  WizardNavigationNotifier() : super(
    const WizardNavigationState(
      currentStep: OnboardingStep.welcome,
      currentStepIndex: 0,
      canGoNext: true, // Welcome step can always proceed
      canGoPrevious: false, // Can't go back from welcome
    ),
  );

  /// Navigate to the next step
  void goToNextStep() {
    if (!state.canGoNext || state.isLoading) return;

    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    if (currentIndex < allSteps.length - 1) {
      final nextStep = allSteps[currentIndex + 1];
      final nextIndex = currentIndex + 1;
      
      _updateNavigationState(
        currentStep: nextStep,
        currentStepIndex: nextIndex,
      );
    }
  }

  /// Navigate to the previous step
  void goToPreviousStep() {
    if (!state.canGoPrevious || state.isLoading) return;

    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    if (currentIndex > 0) {
      final previousStep = allSteps[currentIndex - 1];
      final previousIndex = currentIndex - 1;
      
      _updateNavigationState(
        currentStep: previousStep,
        currentStepIndex: previousIndex,
      );
    }
  }

  /// Jump to a specific step
  void goToStep(OnboardingStep step) {
    if (state.isLoading) return;

    final allSteps = state.allSteps;
    final stepIndex = allSteps.indexOf(step);
    
    if (stepIndex >= 0) {
      _updateNavigationState(
        currentStep: step,
        currentStepIndex: stepIndex,
      );
    }
  }

  /// Mark a step as completed
  void markStepCompleted(OnboardingStep step, bool isCompleted) {
    final updatedCompletionStatus = Map<OnboardingStep, bool>.from(state.stepCompletionStatus);
    updatedCompletionStatus[step] = isCompleted;
    
    state = state.copyWith(
      stepCompletionStatus: updatedCompletionStatus,
    );
    
    // Update navigation capabilities based on current step completion
    _updateNavigationCapabilities();
  }

  /// Set loading state
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }

  /// Set error state
  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  /// Reset wizard to welcome step
  void resetWizard() {
    state = const WizardNavigationState(
      currentStep: OnboardingStep.welcome,
      currentStepIndex: 0,
      canGoNext: true,
      canGoPrevious: false,
    );
  }

  /// Complete the wizard
  void completeWizard() {
    _updateNavigationState(
      currentStep: OnboardingStep.completed,
      currentStepIndex: -1,
    );
  }

  /// Skip the onboarding wizard
  void skipWizard() {
    _updateNavigationState(
      currentStep: OnboardingStep.completed,
      currentStepIndex: -1,
    );
  }

  /// Update navigation state and capabilities
  void _updateNavigationState({
    required OnboardingStep currentStep,
    required int currentStepIndex,
  }) {
    state = state.copyWith(
      currentStep: currentStep,
      currentStepIndex: currentStepIndex,
    );
    
    _updateNavigationCapabilities();
  }

  /// Update navigation capabilities based on current state
  void _updateNavigationCapabilities() {
    final allSteps = state.allSteps;
    final currentIndex = state.currentStepIndex;
    
    // Can go previous if not on first step and not completed
    final canGoPrevious = currentIndex > 0 && state.currentStep != OnboardingStep.completed;
    
    // Can go next based on current step logic
    bool canGoNext = false;
    
    switch (state.currentStep) {
      case OnboardingStep.welcome:
        canGoNext = true; // Welcome step can always proceed
        break;
      case OnboardingStep.businessProfile:
        canGoNext = state.isStepCompleted(OnboardingStep.businessProfile);
        break;
      case OnboardingStep.locationSetup:
        canGoNext = state.isStepCompleted(OnboardingStep.locationSetup);
        break;
      case OnboardingStep.serviceCreation:
        canGoNext = state.isStepCompleted(OnboardingStep.serviceCreation);
        break;
      case OnboardingStep.queueManagement:
        canGoNext = state.isStepCompleted(OnboardingStep.queueManagement);
        break;
      case OnboardingStep.summary:
        canGoNext = true; // Summary can proceed to completion
        break;
      case OnboardingStep.completed:
        canGoNext = false; // No next step after completion
        break;
    }
    
    // Can't go next if on last step (summary)
    if (currentIndex >= allSteps.length - 1) {
      canGoNext = false;
    }
    
    state = state.copyWith(
      canGoNext: canGoNext,
      canGoPrevious: canGoPrevious,
    );
  }

  /// Get step validation requirements
  bool isStepValid(OnboardingStep step) {
    switch (step) {
      case OnboardingStep.welcome:
        return true; // Welcome step is always valid
      case OnboardingStep.businessProfile:
        return state.isStepCompleted(OnboardingStep.businessProfile);
      case OnboardingStep.locationSetup:
        return state.isStepCompleted(OnboardingStep.locationSetup);
      case OnboardingStep.serviceCreation:
        return state.isStepCompleted(OnboardingStep.serviceCreation);
      case OnboardingStep.queueManagement:
        return state.isStepCompleted(OnboardingStep.queueManagement);
      case OnboardingStep.summary:
        return true; // Summary step shows completion status
      case OnboardingStep.completed:
        return true;
    }
  }
}

/// Provider for wizard navigation
final wizardNavigationProvider = StateNotifierProvider<WizardNavigationNotifier, WizardNavigationState>(
  (ref) => WizardNavigationNotifier(),
);

/// Convenience providers for specific navigation states
final currentStepProvider = Provider<OnboardingStep>((ref) {
  return ref.watch(wizardNavigationProvider).currentStep;
});

final canGoNextProvider = Provider<bool>((ref) {
  return ref.watch(wizardNavigationProvider).canGoNext;
});

final canGoPreviousProvider = Provider<bool>((ref) {
  return ref.watch(wizardNavigationProvider).canGoPrevious;
});

final wizardProgressProvider = Provider<double>((ref) {
  return ref.watch(wizardNavigationProvider).progress;
});

final isWizardLoadingProvider = Provider<bool>((ref) {
  return ref.watch(wizardNavigationProvider).isLoading;
});

final wizardErrorProvider = Provider<String?>((ref) {
  return ref.watch(wizardNavigationProvider).error;
});
