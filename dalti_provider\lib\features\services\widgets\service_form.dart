import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/service_models.dart';
import 'color_picker_widget.dart';
import '../../onboarding/components/service/delivery_type_widget.dart';
import '../../onboarding/models/onboarding_models.dart';

class ServiceForm extends ConsumerStatefulWidget {
  final Service? initialService;
  final Function(CreateServiceRequest) onCreateService;
  final Function(int, UpdateServiceRequest)? onUpdateService;
  final bool isEditing;

  const ServiceForm({
    super.key,
    this.initialService,
    required this.onCreateService,
    this.onUpdateService,
    this.isEditing = false,
  });

  @override
  ConsumerState<ServiceForm> createState() => ServiceFormState();
}

class ServiceFormState extends ConsumerState<ServiceForm> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _pointsController = TextEditingController();

  int _duration = 30; // Default 30 minutes to match onboarding
  String _selectedColor = '#2196F3'; // Default blue
  bool _acceptOnline = true;
  bool _acceptNew = true;
  bool _notificationOn = true;
  bool _isActive = true;
  bool _isPublic = true; // New field for public/private

  // Delivery type data
  DeliveryTypeData _deliveryData = const DeliveryTypeData(
    deliveryType: 'at_location',
    servedRegions: [],
  );

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.initialService != null) {
      final service = widget.initialService!;
      _titleController.text = service.title;
      _descriptionController.text = service.description ?? '';
      _priceController.text = service.price?.toString() ?? '';
      _pointsController.text = service.pointsRequirements?.toString() ?? '1';
      _duration = service.duration;
      // Ensure color always has # prefix
      final serviceColor = service.color ?? '#2196F3';
      _selectedColor =
          serviceColor.startsWith('#') ? serviceColor : '#$serviceColor';
      _acceptOnline = service.acceptOnline;
      _acceptNew = service.acceptNew;
      _notificationOn = service.notificationOn;
      _isActive = service.isActive ?? true;
      _isPublic = service.isPublic ?? true;

      // Initialize delivery data
      _deliveryData = DeliveryTypeData(
        deliveryType: service.serviceDeliveryType ?? 'at_location',
        servedRegions: service.servedRegions ?? [],
      );
    } else {
      // Set default points for new service
      _pointsController.text = '1';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _pointsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service Information Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.room_service, color: theme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Service Information',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Service title
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Service Title *',
                      hintText: 'e.g., Consultation, Haircut, Massage',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.room_service),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Service title is required';
                      }
                      if (value.trim().length < 2) {
                        return 'Title must be at least 2 characters';
                      }
                      return null;
                    },
                    textCapitalization: TextCapitalization.words,
                  ),

                  const SizedBox(height: 16),

                  // Service description
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      hintText: 'Describe your service',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 2,
                    textCapitalization: TextCapitalization.sentences,
                  ),

                  const SizedBox(height: 16),

                  // Duration and Price row
                  Row(
                    children: [
                      // Duration
                      Expanded(
                        child: DropdownButtonFormField<int>(
                          value: _duration,
                          decoration: const InputDecoration(
                            labelText: 'Duration *',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.schedule),
                          ),
                          items:
                              [5, 10, 15, 30, 45, 60, 90, 120, 180, 240]
                                  .map(
                                    (duration) => DropdownMenuItem(
                                      value: duration,
                                      child: Text('$duration min'),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            setState(() {
                              _duration = value ?? 30;
                            });
                          },
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Price
                      Expanded(
                        child: TextFormField(
                          controller: _priceController,
                          decoration: const InputDecoration(
                            labelText: 'Price (DA) *',
                            hintText: '0',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.attach_money),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                              RegExp(r'^\d+\.?\d{0,2}'),
                            ),
                          ],
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Price is required';
                            }
                            final price = double.tryParse(value);
                            if (price == null || price < 0) {
                              return 'Enter valid price';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Points requirement
                  TextFormField(
                    controller: _pointsController,
                    decoration: const InputDecoration(
                      labelText: 'Credit Points Required *',
                      hintText:
                          'Credits needed to book this service (minimum 1)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.account_balance_wallet),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Credit points requirement is required';
                      }
                      final points = int.tryParse(value);
                      if (points == null || points < 1) {
                        return 'Credit points must be a positive number';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Delivery Type Card
          DeliveryTypeWidget(
            initialData: _deliveryData,
            onChanged: _onDeliveryDataChanged,
            title: 'Service Delivery',
            subtitle: 'Where do you provide this service?',
          ),

          const SizedBox(height: 16),

          // Appearance Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.palette, color: theme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Appearance',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Color picker
                  ColorPickerWidget(
                    selectedColor: _selectedColor,
                    onColorChanged: (color) {
                      setState(() {
                        _selectedColor = color;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Service Options Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.settings, color: theme.primaryColor),
                      const SizedBox(width: 8),
                      Text(
                        'Service Options',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  CheckboxListTile(
                    title: const Text('Public Service'),
                    subtitle: const Text('Visible to all customers'),
                    value: _isPublic,
                    onChanged: (value) {
                      setState(() {
                        _isPublic = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Accept Online Bookings'),
                    subtitle: const Text('Allow customers to book online'),
                    value: _acceptOnline,
                    onChanged: (value) {
                      setState(() {
                        _acceptOnline = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Accept New Customers'),
                    subtitle: const Text('Allow new customers to book'),
                    value: _acceptNew,
                    onChanged: (value) {
                      setState(() {
                        _acceptNew = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: const Text('Get notified for new bookings'),
                    value: _notificationOn,
                    onChanged: (value) {
                      setState(() {
                        _notificationOn = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  if (widget.isEditing)
                    CheckboxListTile(
                      title: const Text('Active'),
                      subtitle: const Text('Service is available for booking'),
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value ?? true;
                        });
                      },
                      controlAffinity: ListTileControlAffinity.leading,
                      contentPadding: EdgeInsets.zero,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle delivery data changes
  void _onDeliveryDataChanged(DeliveryTypeData deliveryData) {
    setState(() {
      _deliveryData = deliveryData;
    });
  }

  bool validateAndSave() {
    if (!_formKey.currentState!.validate()) {
      return false;
    }

    // Validate delivery data
    if (!_deliveryData.isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please select served regions for customer location services',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    final price = double.tryParse(_priceController.text) ?? 0.0;
    final points = int.tryParse(_pointsController.text) ?? 1;

    if (widget.isEditing && widget.onUpdateService != null) {
      final updateRequest = UpdateServiceRequest(
        title: _titleController.text.trim(),
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        duration: _duration,
        price: price,
        color: _selectedColor,
        acceptOnline: _acceptOnline,
        acceptNew: _acceptNew,
        notificationOn: _notificationOn,
        pointsRequirements: points,
        isActive: _isActive,
        isPublic: _isPublic,
        deliveryType: _deliveryData.deliveryType,
        servedRegions:
            _deliveryData.servedRegions.isNotEmpty
                ? _deliveryData.servedRegions
                : [],
      );

      widget.onUpdateService!(widget.initialService!.id, updateRequest);
    } else {
      final createRequest = CreateServiceRequest(
        title: _titleController.text.trim(),
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        duration: _duration,
        price: price,
        color: _selectedColor,
        acceptOnline: _acceptOnline,
        acceptNew: _acceptNew,
        notificationOn: _notificationOn,
        pointsRequirements: points,
        isPublic: _isPublic,
        deliveryType: _deliveryData.deliveryType,
        servedRegions:
            _deliveryData.servedRegions.isNotEmpty
                ? _deliveryData.servedRegions
                : null,
      );

      widget.onCreateService(createRequest);
    }

    return true;
  }
}
