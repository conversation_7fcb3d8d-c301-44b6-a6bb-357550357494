import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/profile_completion_models.dart';
import '../services/profile_completion_service.dart';

/// Provider for profile completion data
final profileCompletionDataProvider = FutureProvider<ProfileCompletionData>((
  ref,
) async {
  final service = ref.watch(profileCompletionServiceProvider);
  return service.getProfileCompletion();
});

/// Profile completion widget showing overall progress and breakdown
class ProfileCompletionWidget extends ConsumerWidget {
  const ProfileCompletionWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final profileCompletion = ref.watch(profileCompletionDataProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Profile Completion',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () => ref.refresh(profileCompletionDataProvider),
                  iconSize: 20,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Profile completion content
            profileCompletion.when(
              loading:
                  () => const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  ),
              error:
                  (error, stack) => Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: theme.colorScheme.onErrorContainer,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Failed to load profile completion',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onErrorContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
              data:
                  (data) => Column(
                    children: [
                      // Overall progress
                      _buildOverallProgress(context, data),
                      const SizedBox(height: 16),

                      // Breakdown items
                      _buildBreakdownItems(context, data.breakdown),

                      // Next steps if not completed
                      if (!data.overallCompleted &&
                          data.nextSteps.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        _buildNextSteps(context, data.nextSteps),
                      ],
                    ],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallProgress(
    BuildContext context,
    ProfileCompletionData data,
  ) {
    final theme = Theme.of(context);
    final progressColor =
        data.overallCompleted
            ? theme.colorScheme.primary
            : theme.colorScheme.tertiary;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Overall Progress',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${data.overallPercentage}% Complete',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color:
                    data.overallCompleted
                        ? theme.colorScheme.primary.withOpacity(0.1)
                        : theme.colorScheme.tertiary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                data.overallCompleted ? 'Complete' : 'In Progress',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: progressColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: data.overallPercentage / 100,
          backgroundColor: theme.colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(progressColor),
        ),
      ],
    );
  }

  Widget _buildBreakdownItems(
    BuildContext context,
    ProfileCompletionBreakdown breakdown,
  ) {
    final theme = Theme.of(context);

    final items = [
      _BreakdownItemData(
        'Profile Picture',
        Icons.account_circle,
        breakdown.profilePicture,
      ),
      _BreakdownItemData(
        'Provider Info',
        Icons.business,
        breakdown.providerInfo,
      ),
      _BreakdownItemData(
        'Locations',
        Icons.location_on,
        breakdown.providingPlaces,
      ),
      _BreakdownItemData('Services', Icons.room_service, breakdown.services),
      _BreakdownItemData('Queues', Icons.queue, breakdown.queues),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Details',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => _buildBreakdownItem(context, item)),
      ],
    );
  }

  Widget _buildBreakdownItem(BuildContext context, _BreakdownItemData item) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            item.icon,
            size: 16,
            color:
                item.data.completed
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Expanded(child: Text(item.title, style: theme.textTheme.bodySmall)),
          Container(
            width: 60,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: item.data.percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color:
                      item.data.completed
                          ? theme.colorScheme.primary
                          : theme.colorScheme.tertiary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${item.data.percentage}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextSteps(BuildContext context, List<String> nextSteps) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: theme.colorScheme.onPrimaryContainer,
              ),
              const SizedBox(width: 4),
              Text(
                'Next Steps',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...nextSteps.map(
            (step) => Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                '• $step',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _BreakdownItemData {
  final String title;
  final IconData icon;
  final ProfileCompletionItem data;

  const _BreakdownItemData(this.title, this.icon, this.data);
}
