import '../models/appointment_models.dart';
import '../services/appointment_api_service.dart';
import 'appointment_repository.dart';

/// Implementation of AppointmentRepository
class AppointmentRepositoryImpl implements AppointmentRepository {
  final AppointmentApiService _apiService;

  AppointmentRepositoryImpl(this._apiService);

  @override
  Future<List<Appointment>> getAppointments({
    AppointmentSearchFilters? filters,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print('[AppointmentRepository] Getting appointments with filters');

      final response = await _apiService.getAppointments(
        filters: filters,
        page: page,
        pageSize: pageSize,
      );

      if (response.success) {
        return response.appointments;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to fetch appointments',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error getting appointments: $e');
      rethrow;
    }
  }

  @override
  Future<List<Appointment>> getAppointmentsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
    String? queueId,
  }) async {
    try {
      print(
        '[AppointmentRepository] Getting appointments by date range: $startDate - $endDate',
      );

      final response = await _apiService.getAppointmentsByDateRange(
        startDate: startDate,
        endDate: endDate,
        locationId: locationId,
        queueId: queueId,
      );

      if (response.success) {
        return response.appointments;
      } else {
        throw Exception(
          response.error?.message ??
              'Failed to fetch appointments by date range',
        );
      }
    } catch (e) {
      print(
        '[AppointmentRepository] Error getting appointments by date range: $e',
      );
      rethrow;
    }
  }

  @override
  Future<List<Appointment>> getAppointmentsByDate(DateTime date) async {
    final startDate = DateTime(date.year, date.month, date.day);
    final endDate = startDate.add(const Duration(days: 1));

    return getAppointmentsByDateRange(startDate: startDate, endDate: endDate);
  }

  @override
  Future<Appointment> getAppointment(String appointmentId) async {
    try {
      print('[AppointmentRepository] Getting appointment: $appointmentId');

      final response = await _apiService.getAppointment(appointmentId);

      if (response.success && response.appointment != null) {
        return response.appointment!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to fetch appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error getting appointment: $e');
      rethrow;
    }
  }

  @override
  Future<Appointment> createAppointment(AppointmentRequest request) async {
    try {
      print('[AppointmentRepository] Creating appointment');

      final response = await _apiService.createAppointment(request);

      if (response.success && response.appointment != null) {
        return response.appointment!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to create appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error creating appointment: $e');
      rethrow;
    }
  }

  @override
  Future<Appointment> updateAppointment(
    String appointmentId,
    UpdateAppointmentRequest request,
  ) async {
    try {
      print('[AppointmentRepository] Updating appointment: $appointmentId');

      final response = await _apiService.updateAppointment(
        appointmentId,
        request,
      );

      if (response.success && response.appointment != null) {
        return response.appointment!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to update appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error updating appointment: $e');
      rethrow;
    }
  }

  @override
  Future<Appointment> updateAppointmentStatus(
    String appointmentId,
    AppointmentStatus status, {
    String? notes,
  }) async {
    try {
      print(
        '[AppointmentRepository] Updating appointment status: $appointmentId -> ${status.name}',
      );

      final response = await _apiService.updateAppointmentStatus(
        appointmentId,
        status,
        notes: notes,
      );

      if (response.success) {
        // Status update was successful
        if (response.appointment != null) {
          // If we have the updated appointment, return it
          return response.appointment!;
        } else {
          // Status update succeeded but no appointment data returned
          // This is fine - the UI will refresh the appointments list
          // Return a minimal appointment object with updated status
          // We'll find the original appointment in the current state and update its status
          throw Exception('STATUS_UPDATE_SUCCESS_NO_DATA');
        }
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to update appointment status',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error updating appointment status: $e');
      rethrow;
    }
  }

  @override
  Future<void> cancelAppointment(String appointmentId, String reason) async {
    try {
      print('[AppointmentRepository] Cancelling appointment: $appointmentId');

      final response = await _apiService.cancelAppointment(
        appointmentId,
        reason,
      );

      if (!response.success) {
        throw Exception(
          response.error?.message ?? 'Failed to cancel appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error cancelling appointment: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteAppointment(String appointmentId) async {
    try {
      print('[AppointmentRepository] Deleting appointment: $appointmentId');

      final response = await _apiService.deleteAppointment(appointmentId);

      if (!response.success) {
        throw Exception(
          response.error?.message ?? 'Failed to delete appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error deleting appointment: $e');
      rethrow;
    }
  }

  @override
  Future<List<TimeSlot>> getAvailableTimeSlots({
    required DateTime date,
    required String serviceId,
    String? locationId,
    String? queueId,
  }) async {
    try {
      print('[AppointmentRepository] Getting available time slots for: $date');

      return await _apiService.getAvailableTimeSlots(
        date: date,
        serviceId: serviceId,
        locationId: locationId,
        queueId: queueId,
      );
    } catch (e) {
      print('[AppointmentRepository] Error getting available time slots: $e');
      rethrow;
    }
  }

  @override
  Future<AppointmentStats> getAppointmentStats({
    required DateTime startDate,
    required DateTime endDate,
    String? locationId,
  }) async {
    try {
      print(
        '[AppointmentRepository] Getting appointment stats: $startDate - $endDate',
      );

      return await _apiService.getAppointmentStats(
        startDate: startDate,
        endDate: endDate,
        locationId: locationId,
      );
    } catch (e) {
      print('[AppointmentRepository] Error getting appointment stats: $e');
      rethrow;
    }
  }

  @override
  Future<List<Appointment>> searchAppointments(String query) async {
    try {
      print('[AppointmentRepository] Searching appointments: $query');

      final response = await _apiService.searchAppointments(query);

      if (response.success) {
        return response.appointments;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to search appointments',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error searching appointments: $e');
      rethrow;
    }
  }

  @override
  Future<List<Appointment>> getUpcomingAppointments() async {
    final now = DateTime.now();
    final endDate = now.add(const Duration(days: 7));

    return getAppointmentsByDateRange(startDate: now, endDate: endDate);
  }

  @override
  Future<List<Appointment>> getTodaysAppointments() async {
    try {
      print('[AppointmentRepository] Getting today\'s appointments');

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      // Use the main appointments endpoint with date filters
      final filters = AppointmentSearchFilters(
        startDate: startOfDay,
        endDate: endOfDay,
      );

      final appointments = await getAppointments(
        filters: filters,
        page: 1,
        pageSize: 100, // Get all appointments for today
      );

      print(
        '[AppointmentRepository] Found ${appointments.length} appointments for today',
      );
      return appointments;
    } catch (e) {
      print('[AppointmentRepository] Error getting today\'s appointments: $e');
      rethrow;
    }
  }

  @override
  Future<Appointment> rescheduleAppointment({
    required String appointmentId,
    required DateTime newDateTime,
    String? reason,
  }) async {
    // For now, we'll update the appointment with new time
    // In a real implementation, this might be a separate endpoint
    final appointment = await getAppointment(appointmentId);

    final request = UpdateAppointmentRequest.fromDateTimes(
      serviceId: int.parse(appointment.serviceId),
      queueId: int.parse(appointment.queueId!),
      expectedStartTime: newDateTime,
      expectedEndTime: newDateTime.add(Duration(minutes: appointment.duration)),
      notes:
          reason != null
              ? '${appointment.notes ?? ''}\nRescheduled: $reason'
              : appointment.notes,
    );

    return updateAppointment(appointmentId, request);
  }

  @override
  Future<Appointment> completeAppointment(
    String appointmentId, {
    String? notes,
  }) async {
    return updateAppointmentStatus(appointmentId, AppointmentStatus.completed);
  }

  @override
  Future<Appointment> markNoShow(String appointmentId, {String? notes}) async {
    return updateAppointmentStatus(appointmentId, AppointmentStatus.noShow);
  }

  @override
  Future<Appointment> extendAppointment(
    String appointmentId,
    int extensionMinutes,
  ) async {
    try {
      print(
        '[AppointmentRepository] Extending appointment: $appointmentId by $extensionMinutes minutes',
      );

      final response = await _apiService.extendAppointment(
        appointmentId,
        extensionMinutes,
      );

      if (response.success && response.appointment != null) {
        return response.appointment!;
      } else {
        throw Exception(
          response.error?.message ?? 'Failed to extend appointment',
        );
      }
    } catch (e) {
      print('[AppointmentRepository] Error extending appointment: $e');
      rethrow;
    }
  }

  @override
  Future<List<CalendarViewData>> getCalendarViewData({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      print(
        '[AppointmentRepository] Getting calendar view data: $startDate - $endDate',
      );

      final appointments = await getAppointmentsByDateRange(
        startDate: startDate,
        endDate: endDate,
      );

      // Group appointments by date
      final Map<DateTime, List<Appointment>> appointmentsByDate = {};

      for (final appointment in appointments) {
        final date = DateTime(
          appointment.scheduledTime.year,
          appointment.scheduledTime.month,
          appointment.scheduledTime.day,
        );

        appointmentsByDate.putIfAbsent(date, () => []).add(appointment);
      }

      // Create calendar view data for each date
      final calendarData = <CalendarViewData>[];

      for (final entry in appointmentsByDate.entries) {
        final date = entry.key;
        final dayAppointments = entry.value;

        final completedCount =
            dayAppointments
                .where((apt) => apt.status == AppointmentStatus.completed)
                .length;

        final cancelledCount =
            dayAppointments
                .where((apt) => apt.status == AppointmentStatus.canceled)
                .length;

        calendarData.add(
          CalendarViewData(
            date: date,
            appointments: dayAppointments,
            totalAppointments: dayAppointments.length,
            completedAppointments: completedCount,
            cancelledAppointments: cancelledCount,
          ),
        );
      }

      return calendarData;
    } catch (e) {
      print('[AppointmentRepository] Error getting calendar view data: $e');
      rethrow;
    }
  }
}
