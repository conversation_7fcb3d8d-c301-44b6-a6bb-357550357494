import '../storage/storage_service.dart';
import '../auth/jwt_service.dart';

/// Debug utility for checking storage state
class StorageDebug {
  /// Print all authentication-related data in storage
  static void printAllAuthData() {
    print('[] ');
    
    try {
      // Check if storage is initialized
      print('[] ');
      try {
        final authBox = StorageService.auth;
        print('[] ');
        print('[] ');
        print('[] ');
      } catch (e) {
        print('[] ');
      }
      
      // Check JWT token storage
      print('[] ');
      try {
        final jwtToken = StorageService.getAuth<Map<String, dynamic>>('jwt_token');
        if (jwtToken != null) {
          print('[StorageDebug]   - JWT token exists: true');
          print('[StorageDebug]   - JWT token keys: ${jwtToken.keys.toList()}');
          print('[StorageDebug]   - Access token: ${jwtToken['access_token']?.toString().substring(0, 10)}...');
          print('[StorageDebug]   - Token type: ${jwtToken['token_type']}');
          print('[StorageDebug]   - Expires at: ${jwtToken['expires_at']}');
        } else {
          print('[StorageDebug]   - JWT token exists: false');
        }
      } catch (e) {
        print('[] ');
      }
      
      // Check simple token storage
      print('[] ');
      try {
        final accessToken = StorageService.getAuth<String>('access_token');
        final refreshToken = StorageService.getAuth<String>('refresh_token');
        final userId = StorageService.getAuth<String>('user_id');
        
        print('[] ');
        if (accessToken != null) {
          print('[] ');
        }
        print('[] ');
        print('[] ');
        if (userId != null) {
          print('[] ');
        }
      } catch (e) {
        print('[] ');
      }
      
      // Check all auth box contents
      print('[] ');
      try {
        final authBox = StorageService.auth;
        for (final key in authBox.keys) {
          final value = authBox.get(key);
          print('[] ');
        }
      } catch (e) {
        print('[] ');
      }
      
    } catch (e) {
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Check JWT service state vs storage state
  static void compareJwtServiceAndStorage(JwtService jwtService) {
    print('[] ');
    
    // JWT Service state
    print('[] ');
    print('[] ');
    print('[] ');
    
    if (jwtService.currentToken != null) {
      final token = jwtService.currentToken!;
      print('[] ');
      print('[] ');
      print('[] ');
    }
    
    // Storage state
    print('[] ');
    try {
      final storedJwtToken = StorageService.getAuth<Map<String, dynamic>>('jwt_token');
      final storedAccessToken = StorageService.getAuth<String>('access_token');
      
      print('[] ');
      print('[] ');
      
      if (storedJwtToken != null && jwtService.currentToken != null) {
        final storedToken = storedJwtToken['access_token']?.toString();
        final serviceToken = jwtService.currentToken!.accessToken;
        print('[] ');
      }
    } catch (e) {
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Simulate the complete authentication flow
  static Future<void> simulateAuthFlow() async {
    print('[] ');
    
    // Step 1: Check initial state
    print('[] ');
    printAllAuthData();
    
    // Step 2: Simulate login response
    print('[] ');
    final mockLoginResponse = {
      'access_token': 'mock-session-id-12345',
      'refresh_token': 'mock-refresh-token',
      'expires_in': 3600,
      'token_type': 'Bearer',
    };
    
    try {
      // Store using the same method as the real app
      await StorageService.saveAuth('jwt_token', mockLoginResponse);
      await StorageService.saveAuth('access_token', mockLoginResponse['access_token']);
      await StorageService.saveAuth('refresh_token', mockLoginResponse['refresh_token']);
      
      print('[] ');
    } catch (e) {
      print('[] ');
    }
    
    // Step 3: Check state after storage
    print('[] ');
    printAllAuthData();
    
    // Step 4: Clean up
    print('[] ');
    try {
      await StorageService.removeAuth('jwt_token');
      await StorageService.removeAuth('access_token');
      await StorageService.removeAuth('refresh_token');
      print('[] ');
    } catch (e) {
      print('[] ');
    }
    
    print('[] ');
  }
  
  /// Check if storage is properly initialized
  static bool isStorageInitialized() {
    try {
      final authBox = StorageService.auth;
      final isInitialized = authBox.isOpen;
      print('[] ');
      return isInitialized;
    } catch (e) {
      print('[] ');
      return false;
    }
  }
  
  /// Clear all authentication data (for debugging)
  static Future<void> clearAllAuthData() async {
    print('[] ');
    try {
      await StorageService.clearAuth();
      print('[] ');
    } catch (e) {
      print('[] ');
    }
  }
}
