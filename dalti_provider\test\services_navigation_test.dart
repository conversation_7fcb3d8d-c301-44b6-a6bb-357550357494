import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:dalti_provider/features/services/screens/services_screen.dart';
import 'package:dalti_provider/core/routing/app_routes.dart';

void main() {
  group('Services Screen Navigation Tests', () {
    late GoRouter router;
    String? lastNavigatedRoute;

    setUp(() {
      lastNavigatedRoute = null;
      
      // Create a mock router for testing
      router = GoRouter(
        initialLocation: AppRoutes.services,
        routes: [
          GoRoute(
            path: AppRoutes.dashboard,
            builder: (context, state) => const Scaffold(
              body: Text('Dashboard'),
            ),
          ),
          GoRoute(
            path: AppRoutes.services,
            builder: (context, state) => const ServicesScreen(),
          ),
          GoRoute(
            path: '/services/create',
            builder: (context, state) => const Scaffold(
              body: Text('Create Service'),
            ),
          ),
        ],
        redirect: (context, state) {
          lastNavigatedRoute = state.uri.path;
          return null;
        },
      );
    });

    testWidgets('Services screen should have back button in AppBar', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the back button exists
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      
      // Verify the tooltip
      final backButton = find.byIcon(Icons.arrow_back);
      expect(backButton, findsOneWidget);
      
      // Get the IconButton widget and check its tooltip
      final iconButton = tester.widget<IconButton>(
        find.ancestor(
          of: backButton,
          matching: find.byType(IconButton),
        ),
      );
      expect(iconButton.tooltip, equals('Back to Dashboard'));
    });

    testWidgets('Back button should navigate to dashboard', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify we're on the services screen
      expect(find.text('Services'), findsOneWidget);
      expect(lastNavigatedRoute, equals(AppRoutes.services));

      // Tap the back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify navigation to dashboard
      expect(lastNavigatedRoute, equals(AppRoutes.dashboard));
    });

    testWidgets('Services screen should have add and refresh buttons', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify add button exists
      expect(find.byIcon(Icons.add), findsOneWidget);
      
      // Verify refresh button exists
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      
      // Verify tooltips
      final addButton = find.ancestor(
        of: find.byIcon(Icons.add),
        matching: find.byType(IconButton),
      );
      final addIconButton = tester.widget<IconButton>(addButton);
      expect(addIconButton.tooltip, equals('Add Service'));

      final refreshButton = find.ancestor(
        of: find.byIcon(Icons.refresh),
        matching: find.byType(IconButton),
      );
      final refreshIconButton = tester.widget<IconButton>(refreshButton);
      expect(refreshIconButton.tooltip, equals('Refresh'));
    });

    testWidgets('Services screen should have proper AppBar structure', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify AppBar exists
      expect(find.byType(AppBar), findsOneWidget);
      
      // Verify title
      expect(find.text('Services'), findsOneWidget);
      
      // Verify AppBar has leading widget (back button)
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.leading, isNotNull);
      
      // Verify AppBar has actions (add and refresh buttons)
      expect(appBar.actions, isNotNull);
      expect(appBar.actions!.length, equals(2));
    });

    test('Navigation method should use correct route', () {
      // This test verifies that the _onBackPressed method uses the correct route
      // In a real implementation, this would be tested through widget interaction
      expect(AppRoutes.dashboard, equals('/dashboard'));
      expect(AppRoutes.services, equals('/services'));
    });
  });
}
