import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';

import 'package:dalti_provider/core/storage/storage_service.dart';
import 'package:dalti_provider/core/providers/app_providers.dart';
import 'package:dalti_provider/features/auth/models/auth_models.dart';

void main() {
  setUpAll(() async {
    // Initialize Hive for testing
    Hive.init('./test/hive_test_db');
    await StorageService.initForTesting();
  });

  tearDownAll(() async {
    await StorageService.close();
  });

  group('Auth API Integration Tests', () {
    test('Login request structure is correct', () {
      final request = LoginRequest(
        identifier: '<EMAIL>',
        password: 'password123',
      );

      final json = request.toJson();

      expect(json['identifier'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
    });

    test('Email OTP request structure is correct', () {
      final request = EmailOtpRequest(
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: 'password123',
        isProviderRegistration: true,
      );

      final json = request.toJson();

      expect(json['email'], equals('<EMAIL>'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['password'], equals('password123'));
      expect(json['isProviderRegistration'], equals(true));
    });

    test('Provider registration request structure is correct', () {
      final request = ProviderRegistrationRequest(
        otp: '123456',
        identifier: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe',
        providerCategoryId: 1,
        businessName: 'Test Business',
        phone: '+**********',
        email: '<EMAIL>',
      );

      final json = request.toJson();

      expect(json['otp'], equals('123456'));
      expect(json['identifier'], equals('<EMAIL>'));
      expect(json['password'], equals('password123'));
      expect(json['firstName'], equals('John'));
      expect(json['lastName'], equals('Doe'));
      expect(json['providerCategoryId'], equals(1));
      expect(json['businessName'], equals('Test Business'));
      expect(json['phone'], equals('+**********'));
      expect(json['email'], equals('<EMAIL>'));
    });

    test('Auth response parsing works correctly', () {
      final responseData = {
        'sessionId': 'gfpyz37dllbkyc5n6s2fl7oiqm5upfcxgii65lla',
        'user': {
          'id': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
          'email': '<EMAIL>',
          'firstName': 'John',
          'lastName': 'Doe',
          'role': 'CUSTOMER',
        },
        'provider': {
          'id': 1,
          'userId': 'c318cff7-b668-40eb-a8c8-c1ac05fc2593',
          'title': 'Test Business',
          'phone': '**********',
          'providerCategoryId': 1,
          'category': {
            'id': 1,
            'title': 'Healthcare',
            'parentId': null,
          }
        }
      };

      final response = AuthResponse.fromJson(responseData);

      expect(response.success, isTrue);
      expect(response.message, equals('Login successful'));
      expect(response.accessToken, equals('gfpyz37dllbkyc5n6s2fl7oiqm5upfcxgii65lla'));
      expect(response.user?.id, equals('c318cff7-b668-40eb-a8c8-c1ac05fc2593'));
      expect(response.user?.email, equals('<EMAIL>'));
      expect(response.user?.fullName, equals('John Doe'));
      expect(response.user?.role, equals('CUSTOMER'));
      expect(response.provider?.id, equals(1));
      expect(response.provider?.title, equals('Test Business'));
      expect(response.provider?.category?.title, equals('Healthcare'));
    });

    test('API service can be created and configured', () {
      final container = ProviderContainer();
      final authService = container.read(authApiServiceProvider);
      final httpClient = container.read(httpClientProvider);
      
      expect(authService, isNotNull);
      expect(httpClient, isNotNull);
      expect(httpClient.dio.options.baseUrl, contains('dapi'));
      
      container.dispose();
    });

    test('HTTP client has correct base URL for development', () {
      final container = ProviderContainer();
      final httpClient = container.read(httpClientProvider);
      
      // Should be using development URL
      expect(httpClient.dio.options.baseUrl, contains('dapi-test.adscloud.org:8443'));
      
      container.dispose();
    });

    test('Auth interceptor is properly configured', () {
      final container = ProviderContainer();
      final httpClient = container.read(httpClientProvider);
      
      // Check that interceptors are added
      expect(httpClient.dio.interceptors.length, greaterThan(0));
      
      container.dispose();
    });
  });
}
