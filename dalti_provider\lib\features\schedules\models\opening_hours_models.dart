import 'package:equatable/equatable.dart';

/// Represents a time slot within a day
class TimeSlot extends Equatable {
  final String timeFrom; // Format: "HH:mm" (24-hour format)
  final String timeTo;   // Format: "HH:mm" (24-hour format)

  const TimeSlot({
    required this.timeFrom,
    required this.timeTo,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) {
    return TimeSlot(
      timeFrom: json['timeFrom'] as String,
      timeTo: json['timeTo'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timeFrom': timeFrom,
      'timeTo': timeTo,
    };
  }

  /// Validate time format (HH:mm)
  bool get isValid {
    final timeRegex = RegExp(r'^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(timeFrom) && timeRegex.hasMatch(timeTo);
  }

  /// Check if timeFrom is before timeTo
  bool get isValidRange {
    if (!isValid) return false;
    
    final fromParts = timeFrom.split(':');
    final toParts = timeTo.split(':');
    
    final fromMinutes = int.parse(fromParts[0]) * 60 + int.parse(fromParts[1]);
    final toMinutes = int.parse(toParts[0]) * 60 + int.parse(toParts[1]);
    
    return fromMinutes < toMinutes;
  }

  @override
  List<Object?> get props => [timeFrom, timeTo];

  @override
  String toString() => '$timeFrom - $timeTo';
}

/// Represents opening hours for a specific day of the week
class DayOpeningHours extends Equatable {
  final String dayOfWeek; // "Sunday", "Monday", etc.
  final bool isActive;    // Whether the location/queue is open on this day
  final List<TimeSlot> hours; // Multiple time slots per day

  const DayOpeningHours({
    required this.dayOfWeek,
    required this.isActive,
    required this.hours,
  });

  factory DayOpeningHours.fromJson(Map<String, dynamic> json) {
    return DayOpeningHours(
      dayOfWeek: json['dayOfWeek'] as String,
      isActive: json['isActive'] as bool? ?? false,
      hours: (json['hours'] as List<dynamic>?)
              ?.map((slot) => TimeSlot.fromJson(slot as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dayOfWeek': dayOfWeek,
      'isActive': isActive,
      'hours': hours.map((slot) => slot.toJson()).toList(),
    };
  }

  /// Create a closed day
  factory DayOpeningHours.closed(String dayOfWeek) {
    return DayOpeningHours(
      dayOfWeek: dayOfWeek,
      isActive: false,
      hours: [],
    );
  }

  /// Create a day with single time slot
  factory DayOpeningHours.singleSlot({
    required String dayOfWeek,
    required String openTime,
    required String closeTime,
  }) {
    return DayOpeningHours(
      dayOfWeek: dayOfWeek,
      isActive: true,
      hours: [
        TimeSlot(timeFrom: openTime, timeTo: closeTime),
      ],
    );
  }

  /// Check if all time slots are valid
  bool get isValid {
    if (!isActive) return true; // Closed days are always valid
    if (hours.isEmpty) return false; // Active days must have hours
    return hours.every((slot) => slot.isValid && slot.isValidRange);
  }

  @override
  List<Object?> get props => [dayOfWeek, isActive, hours];

  @override
  String toString() {
    if (!isActive) return '$dayOfWeek: Closed';
    if (hours.isEmpty) return '$dayOfWeek: No hours set';
    return '$dayOfWeek: ${hours.map((h) => h.toString()).join(', ')}';
  }
}

/// Complete opening hours for a location or queue
class OpeningHours extends Equatable {
  final List<DayOpeningHours> days;

  const OpeningHours({
    required this.days,
  });

  factory OpeningHours.fromJson(List<dynamic> json) {
    return OpeningHours(
      days: json
          .map((day) => DayOpeningHours.fromJson(day as Map<String, dynamic>))
          .toList(),
    );
  }

  List<Map<String, dynamic>> toJson() {
    return days.map((day) => day.toJson()).toList();
  }

  /// Create default opening hours (all days closed)
  factory OpeningHours.allClosed() {
    const dayNames = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    
    return OpeningHours(
      days: dayNames
          .map((day) => DayOpeningHours.closed(day))
          .toList(),
    );
  }

  /// Create standard business hours (Monday-Friday 9-17, weekends closed)
  factory OpeningHours.standardBusiness() {
    const dayNames = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    
    return OpeningHours(
      days: dayNames.map((day) {
        if (day == 'Sunday' || day == 'Saturday') {
          return DayOpeningHours.closed(day);
        } else {
          return DayOpeningHours.singleSlot(
            dayOfWeek: day,
            openTime: '09:00',
            closeTime: '17:00',
          );
        }
      }).toList(),
    );
  }

  /// Get opening hours for a specific day
  DayOpeningHours? getDayHours(String dayOfWeek) {
    try {
      return days.firstWhere((day) => day.dayOfWeek == dayOfWeek);
    } catch (e) {
      return null;
    }
  }

  /// Check if open on a specific day
  bool isOpenOn(String dayOfWeek) {
    final dayHours = getDayHours(dayOfWeek);
    return dayHours?.isActive ?? false;
  }

  /// Get all active days
  List<String> get activeDays {
    return days
        .where((day) => day.isActive)
        .map((day) => day.dayOfWeek)
        .toList();
  }

  /// Check if all days are valid
  bool get isValid {
    return days.every((day) => day.isValid);
  }

  /// Convert to legacy format for backward compatibility
  Map<String, String> toLegacyFormat() {
    final Map<String, String> legacy = {};
    
    for (final day in days) {
      final dayKey = day.dayOfWeek.toLowerCase();
      if (day.isActive && day.hours.isNotEmpty) {
        final firstSlot = day.hours.first;
        legacy['${dayKey}_open'] = firstSlot.timeFrom;
        legacy['${dayKey}_close'] = firstSlot.timeTo;
      }
    }
    
    return legacy;
  }

  /// Create from legacy format
  factory OpeningHours.fromLegacyFormat(Map<String, String> legacy) {
    const dayNames = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    
    final days = dayNames.map((dayName) {
      final dayKey = dayName.toLowerCase();
      final openKey = '${dayKey}_open';
      final closeKey = '${dayKey}_close';
      
      final openTime = legacy[openKey];
      final closeTime = legacy[closeKey];
      
      if (openTime != null && closeTime != null) {
        return DayOpeningHours.singleSlot(
          dayOfWeek: dayName,
          openTime: openTime,
          closeTime: closeTime,
        );
      } else {
        return DayOpeningHours.closed(dayName);
      }
    }).toList();
    
    return OpeningHours(days: days);
  }

  @override
  List<Object?> get props => [days];

  @override
  String toString() {
    final activeDaysStr = activeDays.join(', ');
    return 'OpeningHours(active days: $activeDaysStr)';
  }
}
