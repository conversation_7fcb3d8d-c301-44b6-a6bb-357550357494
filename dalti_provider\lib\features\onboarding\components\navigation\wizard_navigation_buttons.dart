import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/wizard_navigation_provider.dart';
import '../../models/onboarding_models.dart';

/// Navigation buttons for the wizard
/// Handles next, previous, and completion actions
class WizardNavigationButtons extends ConsumerWidget {
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final VoidCallback? onComplete;
  final String? nextButtonText;
  final String? previousButtonText;
  final String? completeButtonText;
  final bool showSkip;
  final VoidCallback? onSkip;
  final bool isValid;
  final String? validationMessage;

  const WizardNavigationButtons({
    super.key,
    this.onNext,
    this.onPrevious,
    this.onComplete,
    this.nextButtonText,
    this.previousButtonText,
    this.completeButtonText,
    this.showSkip = false,
    this.onSkip,
    this.isValid = true,
    this.validationMessage,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final navigationState = ref.watch(wizardNavigationProvider);
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Validation message
          if (!isValid && validationMessage != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.error.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: theme.colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      validationMessage!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Navigation buttons
          Row(
            children: [
              // Previous button
              if (navigationState.canGoPrevious) ...[
                OutlinedButton(
                  onPressed: navigationState.isLoading 
                      ? null 
                      : () => _handlePrevious(wizardNotifier),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.arrow_back,
                        size: 18,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(previousButtonText ?? 'Previous'),
                    ],
                  ),
                ),
              ],
              
              // Skip button (if enabled)
              if (showSkip && onSkip != null) ...[
                if (navigationState.canGoPrevious) const SizedBox(width: 12),
                TextButton(
                  onPressed: navigationState.isLoading ? null : onSkip,
                  child: Text(
                    'Skip',
                    style: TextStyle(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ),
              ],
              
              const Spacer(),
              
              // Next/Complete button
              _buildPrimaryButton(theme, navigationState, wizardNotifier),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryButton(
    ThemeData theme,
    WizardNavigationState navigationState,
    WizardNavigationNotifier wizardNotifier,
  ) {
    // Determine button text and action
    String buttonText;
    VoidCallback? onPressed;
    
    if (navigationState.currentStep == OnboardingStep.summary) {
      buttonText = completeButtonText ?? 'Complete Setup';
      onPressed = isValid && !navigationState.isLoading 
          ? () => _handleComplete(wizardNotifier)
          : null;
    } else {
      buttonText = nextButtonText ?? 'Next';
      onPressed = isValid && navigationState.canGoNext && !navigationState.isLoading
          ? () => _handleNext(wizardNotifier)
          : null;
    }
    
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(
          horizontal: 32,
          vertical: 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: navigationState.isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.onPrimary,
                ),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  buttonText,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  navigationState.currentStep == OnboardingStep.summary
                      ? Icons.check
                      : Icons.arrow_forward,
                  size: 18,
                  color: theme.colorScheme.onPrimary,
                ),
              ],
            ),
    );
  }

  void _handlePrevious(WizardNavigationNotifier wizardNotifier) {
    if (onPrevious != null) {
      onPrevious!();
    } else {
      wizardNotifier.goToPreviousStep();
    }
  }

  void _handleNext(WizardNavigationNotifier wizardNotifier) {
    if (onNext != null) {
      onNext!();
    } else {
      wizardNotifier.goToNextStep();
    }
  }

  void _handleComplete(WizardNavigationNotifier wizardNotifier) {
    if (onComplete != null) {
      onComplete!();
    } else {
      wizardNotifier.completeWizard();
    }
  }
}

/// Simplified navigation buttons for specific use cases
class SimpleWizardButtons extends ConsumerWidget {
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;
  final bool isValid;
  final String? nextText;

  const SimpleWizardButtons({
    super.key,
    this.onNext,
    this.onPrevious,
    this.isValid = true,
    this.nextText,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WizardNavigationButtons(
      onNext: onNext,
      onPrevious: onPrevious,
      isValid: isValid,
      nextButtonText: nextText,
    );
  }
}

/// Navigation buttons with custom validation
class ValidatedWizardButtons extends ConsumerWidget {
  final bool Function() validator;
  final String validationMessage;
  final VoidCallback? onNext;
  final VoidCallback? onPrevious;

  const ValidatedWizardButtons({
    super.key,
    required this.validator,
    required this.validationMessage,
    this.onNext,
    this.onPrevious,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isValid = validator();
    
    return WizardNavigationButtons(
      onNext: onNext,
      onPrevious: onPrevious,
      isValid: isValid,
      validationMessage: isValid ? null : validationMessage,
    );
  }
}
