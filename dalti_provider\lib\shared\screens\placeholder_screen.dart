import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/routing/app_routes.dart';

class PlaceholderScreen extends StatelessWidget {
  final String title;
  final IconData icon;
  final String description;
  final Color? color;

  const PlaceholderScreen({
    super.key,
    required this.title,
    required this.icon,
    required this.description,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go(AppRoutes.dashboard),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 80,
                color: color ?? Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 24),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () => context.go(AppRoutes.dashboard),
                child: const Text('Back to Dashboard'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Specific placeholder screens
// class ServicesScreen extends StatelessWidget {
//   const ServicesScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return const PlaceholderScreen(
//       title: 'Services',
//       icon: Icons.build,
//       description: 'Manage your services and pricing here.\nComing soon...',
//       color: Colors.green,
//     );
//   }
// }

// QueuesScreen moved to features/queues/screens/queues_screen.dart

// SchedulesScreen moved to features/schedules/screens/schedules_screen.dart

class CustomersScreen extends StatelessWidget {
  const CustomersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const PlaceholderScreen(
      title: 'Customers',
      icon: Icons.people,
      description: 'Manage your customer relationships here.\nComing soon...',
      color: Colors.teal,
    );
  }
}

class AppointmentsScreen extends StatelessWidget {
  const AppointmentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const PlaceholderScreen(
      title: 'Appointments',
      icon: Icons.event,
      description: 'Manage your appointments here.\nComing soon...',
      color: Colors.red,
    );
  }
}

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const PlaceholderScreen(
      title: 'Profile',
      icon: Icons.person,
      description: 'Manage your profile settings here.\nComing soon...',
      color: Colors.indigo,
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const PlaceholderScreen(
      title: 'Settings',
      icon: Icons.settings,
      description: 'App settings and preferences.\nComing soon...',
      color: Colors.grey,
    );
  }
}
