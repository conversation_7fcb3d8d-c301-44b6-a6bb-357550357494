import '../../../core/network/api_service.dart';
import '../models/provider_category.dart';

/// Service for managing provider categories
class CategoryService {
  final AuthApiService _apiService;
  List<ProviderCategory>? _cachedCategories;
  DateTime? _lastFetchTime;
  
  // Cache duration: 1 hour
  static const Duration _cacheDuration = Duration(hours: 1);

  CategoryService(this._apiService);

  /// Get provider categories with caching
  Future<List<ProviderCategory>> getCategories({bool forceRefresh = false}) async {
    // Check if we have cached data and it's still valid
    if (!forceRefresh && 
        _cachedCategories != null && 
        _lastFetchTime != null &&
        DateTime.now().difference(_lastFetchTime!) < _cacheDuration) {
      print('[] ');
      return _cachedCategories!;
    }

    try {
      print('[] ');
      
      // Fetch from API
      final categories = await _apiService.getProviderCategories();

      if (categories.isNotEmpty && _isValidCategoryData(categories)) {
        // Build hierarchical structure from flat API response
        final hierarchicalCategories = _buildHierarchy(categories);

        // Debug logging
        print('[] ');
        for (final parent in hierarchicalCategories) {
          print('[] ');
          for (final child in parent.children) {
            print('[] ');
          }
        }

        // Cache the result
        _cachedCategories = hierarchicalCategories;
        _lastFetchTime = DateTime.now();

        print('[] ');
        return hierarchicalCategories;
      } else {
        print('[] ');
        return _getFallbackCategories();
      }
    } catch (e) {
      print('[] ');
      print('[] ');
      return _getFallbackCategories();
    }
  }

  /// Build hierarchical structure from flat category list
  List<ProviderCategory> _buildHierarchy(List<ProviderCategory> flatCategories) {
    final Map<int, ProviderCategory> categoryMap = {};
    final List<ProviderCategory> parentCategories = [];
    final Map<int, List<ProviderCategory>> childrenMap = {};

    // First pass: create map and identify parents/children
    for (final category in flatCategories) {
      categoryMap[category.id] = category;
      
      if (category.parentId == null) {
        // This is a parent category
        parentCategories.add(category);
        childrenMap[category.id] = [];
      } else {
        // This is a child category
        final parentId = category.parentId!;
        if (!childrenMap.containsKey(parentId)) {
          childrenMap[parentId] = [];
        }
        childrenMap[parentId]!.add(category);
      }
    }

    // Second pass: build hierarchy
    final List<ProviderCategory> result = [];
    for (final parent in parentCategories) {
      final children = childrenMap[parent.id] ?? [];
      
      // Create new parent with children
      // Parent categories should not be selectable
      final parentWithChildren = ProviderCategory(
        id: parent.id,
        name: parent.name,
        description: parent.description,
        icon: parent.icon,
        parentId: parent.parentId,
        isSelectable: false, // Parent categories are never selectable
        children: children,
      );
      
      result.add(parentWithChildren);
    }

    return result;
  }

  /// Validate if API category data is usable
  bool _isValidCategoryData(List<ProviderCategory> categories) {
    // Check if categories have valid names (not empty)
    final hasValidNames = categories.any((category) =>
        category.name.isNotEmpty && category.name.trim().isNotEmpty);

    if (!hasValidNames) {
      print('[] ');
      return false;
    }

    return true;
  }

  /// Get fallback hardcoded categories when API fails
  List<ProviderCategory> _getFallbackCategories() {
    return ProviderCategories.getParentCategories();
  }

  /// Clear cached categories
  void clearCache() {
    _cachedCategories = null;
    _lastFetchTime = null;
    print('[] ');
  }

  /// Get all selectable categories (flattened)
  Future<List<ProviderCategory>> getSelectableCategories({bool forceRefresh = false}) async {
    final categories = await getCategories(forceRefresh: forceRefresh);
    final List<ProviderCategory> selectableCategories = [];
    
    for (final parent in categories) {
      selectableCategories.addAll(parent.selectableChildren);
    }
    
    return selectableCategories;
  }

  /// Search categories by name
  Future<List<ProviderCategory>> searchCategories(String query, {bool forceRefresh = false}) async {
    if (query.isEmpty) {
      return getSelectableCategories(forceRefresh: forceRefresh);
    }

    final categories = await getCategories(forceRefresh: forceRefresh);
    final lowerQuery = query.toLowerCase();
    final List<ProviderCategory> results = [];

    for (final parent in categories) {
      // Search in parent
      if (parent.name.toLowerCase().contains(lowerQuery) ||
          parent.description.toLowerCase().contains(lowerQuery)) {
        if (parent.isSelectable) results.add(parent);
      }

      // Search in children
      for (final child in parent.children) {
        if (child.name.toLowerCase().contains(lowerQuery) ||
            child.description.toLowerCase().contains(lowerQuery)) {
          if (child.isSelectable) results.add(child);
        }
      }
    }

    return results;
  }

  /// Get category by ID
  Future<ProviderCategory?> getCategoryById(int id, {bool forceRefresh = false}) async {
    final categories = await getCategories(forceRefresh: forceRefresh);
    
    // Search in parent categories
    for (final category in categories) {
      if (category.id == id) return category;
      
      // Search in child categories
      for (final child in category.children) {
        if (child.id == id) return child;
      }
    }
    
    return null;
  }

  /// Check if category is selectable
  Future<bool> isCategorySelectable(int id, {bool forceRefresh = false}) async {
    final category = await getCategoryById(id, forceRefresh: forceRefresh);
    return category?.isSelectable ?? false;
  }

  /// Get children of a specific parent category
  Future<List<ProviderCategory>> getChildrenOf(int parentId, {bool forceRefresh = false}) async {
    final parent = await getCategoryById(parentId, forceRefresh: forceRefresh);
    return parent?.children ?? [];
  }
}
