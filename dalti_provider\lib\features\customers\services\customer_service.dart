import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/customer_models.dart';
import '../repository/customer_repository.dart';

/// Service class for customer business logic operations
class CustomerService {
  final CustomerRepository _repository;

  CustomerService(this._repository);

  /// Validate customer data before creation/update
  CustomerValidationResult validateCustomer(CustomerRequest request) {
    final errors = <String>[];

    // Validate required fields
    if (request.firstName.trim().isEmpty) {
      errors.add('First name is required');
    }

    if (request.lastName.trim().isEmpty) {
      errors.add('Last name is required');
    }

    // Email validation (optional)
    if (request.email?.isNotEmpty == true && !_isValidEmail(request.email!)) {
      errors.add('Please enter a valid email address');
    }

    // Mobile number validation (now optional)
    if (request.mobileNumber != null) {
      if (request.mobileNumber!.trim().isEmpty) {
        errors.add('Mobile number cannot be empty if provided');
      } else if (!_isValidPhoneNumber(request.mobileNumber!)) {
        errors.add('Please enter a valid 9-digit mobile number');
      }
    }

    // Validate optional fields
    if (request.dateOfBirth != null &&
        request.dateOfBirth!.isAfter(DateTime.now())) {
      errors.add('Date of birth cannot be in the future');
    }

    return CustomerValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  /// Check if email already exists for another customer
  Future<bool> isEmailAvailable(
    String email, {
    String? excludeCustomerId,
  }) async {
    try {
      final exists = await _repository.customerExistsByEmail(email);
      if (!exists) return true;

      // If we're updating a customer, check if the email belongs to the same customer
      if (excludeCustomerId != null) {
        // This would require additional API support to check email ownership
        // For now, we'll assume it's available if we're updating
        return true;
      }

      return false;
    } catch (e) {
      // If we can't check, assume it's available to avoid blocking the user
      return true;
    }
  }

  /// Check if phone number already exists for another customer
  Future<bool> isPhoneAvailable(
    String phoneNumber, {
    String? excludeCustomerId,
  }) async {
    try {
      final exists = await _repository.customerExistsByPhone(phoneNumber);
      if (!exists) return true;

      // If we're updating a customer, check if the phone belongs to the same customer
      if (excludeCustomerId != null) {
        // This would require additional API support to check phone ownership
        // For now, we'll assume it's available if we're updating
        return true;
      }

      return false;
    } catch (e) {
      // If we can't check, assume it's available to avoid blocking the user
      return true;
    }
  }

  /// Launch phone call to customer
  Future<bool> callCustomer(Customer customer) async {
    try {
      if (customer.phoneNumber == null || customer.phoneNumber!.isEmpty) {
        return false;
      }
      final phoneUrl = Uri.parse('tel:+213${customer.phoneNumber}');
      if (await canLaunchUrl(phoneUrl)) {
        await launchUrl(phoneUrl);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Launch email to customer
  Future<bool> emailCustomer(
    Customer customer, {
    String? subject,
    String? body,
  }) async {
    try {
      final emailUrl = Uri.parse(
        'mailto:${customer.email}${subject != null ? '?subject=${Uri.encodeComponent(subject)}' : ''}${body != null ? '&body=${Uri.encodeComponent(body)}' : ''}',
      );
      if (await canLaunchUrl(emailUrl)) {
        await launchUrl(emailUrl);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Send SMS to customer
  Future<bool> smsCustomer(Customer customer, {String? message}) async {
    try {
      if (customer.phoneNumber == null || customer.phoneNumber!.isEmpty) {
        return false;
      }
      final smsUrl = Uri.parse(
        'sms:+213${customer.phoneNumber}${message != null ? '?body=${Uri.encodeComponent(message)}' : ''}',
      );
      if (await canLaunchUrl(smsUrl)) {
        await launchUrl(smsUrl);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Calculate customer loyalty score
  int calculateLoyaltyScore(Customer customer) {
    final now = DateTime.now();
    final daysSinceCreated = now.difference(customer.createdAt).inDays;

    // Base score from appointments (max 50 points)
    final appointmentScore = (customer.totalAppointments * 2).clamp(0, 50);

    // Score from spending (max 30 points)
    final spendingScore = (customer.totalSpent / 200).clamp(0, 30);

    // Longevity score (max 20 points)
    final longevityScore = (daysSinceCreated / 365 * 20).clamp(0, 20);

    return (appointmentScore + spendingScore + longevityScore).round();
  }

  /// Get customer loyalty level
  CustomerLoyaltyLevel getLoyaltyLevel(int score) {
    if (score >= 80) {
      return CustomerLoyaltyLevel.vip;
    } else if (score >= 60) {
      return CustomerLoyaltyLevel.loyal;
    } else if (score >= 40) {
      return CustomerLoyaltyLevel.regular;
    } else {
      return CustomerLoyaltyLevel.newCustomer;
    }
  }

  /// Get customer status color
  Color getStatusColor(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return Colors.green;
      case CustomerStatus.inactive:
        return Colors.orange;
      case CustomerStatus.blocked:
        return Colors.red;
    }
  }

  /// Get customer status icon
  IconData getStatusIcon(CustomerStatus status) {
    switch (status) {
      case CustomerStatus.active:
        return Icons.check_circle;
      case CustomerStatus.inactive:
        return Icons.pause_circle;
      case CustomerStatus.blocked:
        return Icons.block;
    }
  }

  /// Format customer display name
  String getDisplayName(Customer customer) {
    return '${customer.firstName} ${customer.lastName}';
  }

  /// Format customer initials
  String getInitials(Customer customer) {
    final first =
        customer.firstName.isNotEmpty
            ? customer.firstName[0].toUpperCase()
            : '';
    final last =
        customer.lastName.isNotEmpty ? customer.lastName[0].toUpperCase() : '';
    return '$first$last';
  }

  /// Get customer age if date of birth is available
  int? getAge(Customer customer) {
    if (customer.dateOfBirth == null) return null;

    final now = DateTime.now();
    final age = now.year - customer.dateOfBirth!.year;

    // Adjust if birthday hasn't occurred this year
    if (now.month < customer.dateOfBirth!.month ||
        (now.month == customer.dateOfBirth!.month &&
            now.day < customer.dateOfBirth!.day)) {
      return age - 1;
    }

    return age;
  }

  /// Private helper methods
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPhoneNumber(String phoneNumber) {
    return RegExp(r'^\d{9}$').hasMatch(phoneNumber.trim());
  }
}

/// Customer validation result
class CustomerValidationResult {
  final bool isValid;
  final List<String> errors;

  const CustomerValidationResult({required this.isValid, required this.errors});
}

/// Customer loyalty levels
enum CustomerLoyaltyLevel { newCustomer, regular, loyal, vip }

/// Extension for loyalty level properties
extension CustomerLoyaltyLevelExtension on CustomerLoyaltyLevel {
  String get name {
    switch (this) {
      case CustomerLoyaltyLevel.newCustomer:
        return 'New Customer';
      case CustomerLoyaltyLevel.regular:
        return 'Regular Customer';
      case CustomerLoyaltyLevel.loyal:
        return 'Loyal Customer';
      case CustomerLoyaltyLevel.vip:
        return 'VIP Customer';
    }
  }

  Color get color {
    switch (this) {
      case CustomerLoyaltyLevel.newCustomer:
        return Colors.orange;
      case CustomerLoyaltyLevel.regular:
        return Colors.blue;
      case CustomerLoyaltyLevel.loyal:
        return Colors.green;
      case CustomerLoyaltyLevel.vip:
        return Colors.purple;
    }
  }

  IconData get icon {
    switch (this) {
      case CustomerLoyaltyLevel.newCustomer:
        return Icons.person_outline;
      case CustomerLoyaltyLevel.regular:
        return Icons.person;
      case CustomerLoyaltyLevel.loyal:
        return Icons.star;
      case CustomerLoyaltyLevel.vip:
        return Icons.diamond;
    }
  }
}
