import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:async';
import '../../appointments/providers/appointment_provider.dart';
import '../../appointments/models/appointment_models.dart';

/// Service Session Screen
/// Displays customer information, service details, and countdown timer
/// for the active service session
class ServiceScreen extends ConsumerStatefulWidget {
  final String appointmentId;

  const ServiceScreen({super.key, required this.appointmentId});

  @override
  ConsumerState<ServiceScreen> createState() => _ServiceScreenState();
}

class _ServiceScreenState extends ConsumerState<ServiceScreen> {
  Timer? _timer;
  Duration _remainingTime = Duration.zero;
  bool _isLoading = true;
  bool _isCompleting = false;
  Appointment? _appointment;

  @override
  void initState() {
    super.initState();
    _loadAppointment();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _loadAppointment() async {
    try {
      print(
        '[ServiceScreen] Loading appointment with ID: ${widget.appointmentId}',
      );

      // Try to get appointment directly from repository first
      final repository = ref.read(appointmentRepositoryProvider);
      Appointment? appointment;

      try {
        appointment = await repository.getAppointment(widget.appointmentId);
        print('[ServiceScreen] Found appointment: ${appointment.customerName}');
      } catch (e) {
        print('[ServiceScreen] Direct appointment fetch failed: $e');

        // Fallback: Load all appointments and search
        await ref.read(appointmentNotifierProvider.notifier).loadAppointments();
        final appointmentsAsync = ref.read(appointmentNotifierProvider);

        if (appointmentsAsync.hasValue) {
          final appointments = appointmentsAsync.value!;
          print('[ServiceScreen] Loaded ${appointments.length} appointments');

          appointment = appointments.firstWhere(
            (apt) => apt.id == widget.appointmentId,
            orElse: () => throw Exception('Appointment not found in list'),
          );
        } else {
          throw Exception('Failed to load appointments');
        }
      }

      setState(() {
        _appointment = appointment;
        _remainingTime = _calculateRemainingTime(appointment);
        _isLoading = false;
      });

      _startTimer();
    } catch (e, stackTrace) {
      print('[ServiceScreen] Error loading appointment: $e');
      print('[ServiceScreen] Stack trace: $stackTrace');
      if (mounted) {
        _showErrorAndExit('Failed to load appointment details.\n\nError: $e');
      }
    }
  }

  /// Calculate remaining time based on expectedAppointmentEndTime
  Duration _calculateRemainingTime(Appointment? appointment) {
    if (appointment == null) {
      return Duration(minutes: 60); // Default fallback
    }

    final now = DateTime.now();

    print('[ServiceScreen] Calculating remaining time:');
    print('[ServiceScreen] Current time: $now');
    print('[ServiceScreen] Expected end time: ${appointment.expectedEndTime}');
    print('[ServiceScreen] Service duration: ${appointment.duration} minutes');
    print(
      '[ServiceScreen] Real start time: ${appointment.realAppointmentStartTime}',
    );
    print('[ServiceScreen] Scheduled time: ${appointment.scheduledTime}');

    // Use expectedEndTime if available, otherwise calculate from start time + duration
    DateTime endTime;
    if (appointment.expectedEndTime != null) {
      endTime = appointment.expectedEndTime!;
      print('[ServiceScreen] Using expectedEndTime from API: $endTime');
    } else {
      // Fallback: calculate end time from start time + duration
      final startTime =
          appointment.realAppointmentStartTime ?? appointment.scheduledTime;
      endTime = startTime.add(Duration(minutes: appointment.duration));
      print(
        '[ServiceScreen] Calculated end time from start + duration: $endTime',
      );
    }

    // Calculate remaining time until expected end
    final remainingTime = endTime.difference(now);
    print(
      '[ServiceScreen] Remaining time: ${remainingTime.inMinutes}m ${remainingTime.inSeconds % 60}s',
    );

    // If service has already exceeded its expected end time, return zero
    if (remainingTime.isNegative) {
      print(
        '[ServiceScreen] Service has exceeded expected end time, returning zero',
      );
      return Duration.zero;
    }

    return remainingTime;
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Recalculate remaining time based on actual start time
      final newRemainingTime = _calculateRemainingTime(_appointment);

      setState(() {
        _remainingTime = newRemainingTime;
      });

      if (_remainingTime.inSeconds <= 0) {
        timer.cancel();
        _showTimeUpDialog();
      }
    });
  }

  void _showTimeUpDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Service Time Complete'),
            content: const Text(
              'The allocated service time has ended. Would you like to complete the appointment?',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _extendTime();
                },
                child: const Text('Extend Time'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _completeAppointment();
                },
                child: const Text('Complete'),
              ),
            ],
          ),
    );
  }

  void _extendTime() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Extend Service Time'),
            content: const Text(
              'How much additional time do you need?\n(Maximum 60 minutes per extension)',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _addTime(15);
                },
                child: const Text('15 min'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _addTime(30);
                },
                child: const Text('30 min'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _addTime(45);
                },
                child: const Text('45 min'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _addTime(60);
                },
                child: const Text('60 min'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  Future<void> _addTime(int minutes) async {
    try {
      print('[ServiceScreen] Extending appointment by $minutes minutes');

      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Call the extend appointment API
      final extendedAppointment = await ref
          .read(appointmentNotifierProvider.notifier)
          .extendAppointment(widget.appointmentId, minutes);

      // Update local appointment data
      setState(() {
        _appointment = extendedAppointment;
        _remainingTime = _calculateRemainingTime(extendedAppointment);
      });

      // Restart timer if needed
      if (_timer?.isActive != true) {
        _startTimer();
      }

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Extended appointment by $minutes minutes'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('[ServiceScreen] Error extending appointment: $e');

      // Close loading dialog if still open
      if (mounted) {
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to extend appointment: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeAppointment() async {
    if (_isCompleting) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      await ref
          .read(appointmentNotifierProvider.notifier)
          .updateAppointmentStatus(
            widget.appointmentId,
            AppointmentStatus.completed,
          );

      if (mounted) {
        // Show success message and navigate back
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appointment completed successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to appointments screen and refresh
        context.go('/appointments');
      }
    } catch (e) {
      print('[ServiceScreen] Error completing appointment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to complete appointment. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCompleting = false;
        });
      }
    }
  }

  void _showErrorAndExit(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Error'),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.go('/appointments');
                },
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  Color _getTimerColor() {
    if (_appointment == null) {
      return Colors.green;
    }

    // Calculate total duration based on expectedEndTime if available
    Duration totalDuration;
    if (_appointment!.expectedEndTime != null) {
      final startTime =
          _appointment!.realAppointmentStartTime ?? _appointment!.scheduledTime;
      totalDuration = _appointment!.expectedEndTime!.difference(startTime);
    } else {
      totalDuration = Duration(minutes: _appointment!.duration);
    }

    final totalMinutes = totalDuration.inMinutes;
    final remainingMinutes = _remainingTime.inMinutes;

    // Avoid division by zero
    if (totalMinutes <= 0) {
      return Colors.red;
    }

    final percentage = remainingMinutes / totalMinutes;

    if (percentage > 0.5) {
      return Colors.green;
    } else if (percentage > 0.25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Service Session'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_appointment == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Service Session'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: Text('Appointment not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Service Session'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.go('/appointments'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timer Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: _getTimerColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getTimerColor(), width: 2),
              ),
              child: Column(
                children: [
                  Text(
                    'Time Remaining',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: _getTimerColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatDuration(_remainingTime),
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: _getTimerColor(),
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Customer Information
            _buildInfoCard('Customer Information', [
              _buildInfoRow('Name', _appointment!.customerName),
              if (_appointment!.customerPhone != null)
                _buildInfoRow('Phone', _appointment!.customerPhone!),
              if (_appointment!.customerEmail != null)
                _buildInfoRow('Email', _appointment!.customerEmail!),
            ]),

            const SizedBox(height: 16),

            // Service Information
            _buildInfoCard('Service Details', [
              _buildInfoRow('Service', _appointment!.serviceName),
              _buildInfoRow('Duration', '${_appointment!.duration} minutes'),
              _buildInfoRow('Status', _getStatusText(_appointment!.status)),
              if (_appointment!.notes != null &&
                  _appointment!.notes!.isNotEmpty)
                _buildInfoRow('Notes', _appointment!.notes!),
            ]),

            const Spacer(),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  flex: 3,
                  child: OutlinedButton(
                    onPressed: _extendTime,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Extend Time',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 4,
                  child: ElevatedButton(
                    onPressed: _isCompleting ? null : _completeAppointment,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child:
                        _isCompleting
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : const Text(
                              'Complete Service',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  String _getStatusText(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return 'Pending';
      case AppointmentStatus.scheduled:
        return 'Scheduled';
      case AppointmentStatus.confirmed:
        return 'Confirmed';
      case AppointmentStatus.inProgress:
        return 'In Progress';
      case AppointmentStatus.completed:
        return 'Completed';
      case AppointmentStatus.canceled:
        return 'Canceled';
      case AppointmentStatus.noShow:
        return 'No Show';
      case AppointmentStatus.rescheduled:
        return 'Rescheduled';
    }
  }
}
