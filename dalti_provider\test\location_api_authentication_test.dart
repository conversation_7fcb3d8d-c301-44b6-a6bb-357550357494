import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/locations/services/location_api_service.dart';
import 'package:dalti_provider/features/locations/services/location_integration_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/network/interceptors/auth_interceptor.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/auth/models/jwt_token.dart';

void main() {
  group('Location API Authentication Tests', () {
    test('LocationApiService inherits from ApiService correctly', () {
      final httpClient = HttpClient();
      final locationApiService = LocationApiService(httpClient);

      // Verify that LocationApiService has access to httpClient
      expect(locationApiService.httpClient, equals(httpClient));
    });

    test('LocationIntegrationService inherits from ApiService correctly', () {
      final httpClient = HttpClient();
      final integrationService = LocationIntegrationService(httpClient);

      // Verify that LocationIntegrationService has access to httpClient
      expect(integrationService.httpClient, equals(httpClient));
    });

    test('HttpClient with AuthInterceptor setup works correctly', () {
      // Create a mock JWT service with a valid token
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);
      
      // Create auth interceptor
      final authInterceptor = AuthInterceptor(jwtService);
      
      // Create HTTP client with auth interceptor
      final authenticatedHttpClient = HttpClient(authInterceptor: authInterceptor);
      
      expect(authenticatedHttpClient, isNotNull);
    });

    test('Authentication flow for location API endpoints', () async {
      // 1. Simulate successful login
      final loginResponse = {
        'sessionId': 'test-session-12345',
        'refreshToken': 'test-refresh-token',
        'user': {
          'id': 'user-123',
          'email': '<EMAIL>',
        },
        'provider': {
          'id': 1,
          'businessName': 'Test Clinic',
        },
      };

      // 2. Create JWT token from login response
      final tokenData = {
        'access_token': loginResponse['sessionId'],
        'refresh_token': loginResponse['refreshToken'],
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      final jwtToken = JwtToken.fromJson(tokenData);
      expect(jwtToken.isValid, isTrue);

      // 3. Verify authorization header format
      final authHeader = '${jwtToken.tokenType} ${jwtToken.accessToken}';
      expect(authHeader, equals('Bearer test-session-12345'));

      // 4. This header should be automatically added to all location API calls
      // by the AuthInterceptor when making requests to protected endpoints
      final protectedEndpoints = [
        '/api/auth/providers/locations',
        '/api/auth/providers/locations/1',
        '/api/auth/providers/locations/1/queues',
        '/api/auth/providers/schedules',
      ];

      for (final endpoint in protectedEndpoints) {
        // Verify these endpoints are not in the skip list
        const skipAuthPaths = [
          '/api/auth/provider/login',
          '/api/auth/request-email-otp',
          '/api/auth/provider/verify-otp-register',
          '/api/auth/refresh-token',
          '/health',
        ];

        final shouldSkip = skipAuthPaths.any((skipPath) => endpoint.contains(skipPath));
        expect(shouldSkip, isFalse, 
            reason: 'Endpoint $endpoint should require authentication');
      }
    });

    test('Location API service methods use correct endpoints', () {
      final httpClient = HttpClient();
      final locationApiService = LocationApiService(httpClient);

      // Verify the service is properly configured
      expect(locationApiService.httpClient, isNotNull);
      
      // The following endpoints should be called by LocationApiService:
      final expectedEndpoints = [
        '/api/auth/providers/locations',           // getLocations()
        '/api/auth/providers/locations/{id}',      // getLocationById()
        '/api/auth/providers/locations',           // createLocation() - POST
        '/api/auth/providers/locations/{id}',      // updateLocation() - PUT
        '/api/auth/providers/locations/{id}',      // deleteLocation() - DELETE
        '/api/auth/providers/locations/{id}/can-delete', // canDeleteLocation()
        '/api/auth/providers/locations/{id}/stats',      // getLocationStats()
      ];

      // All these endpoints start with '/api/auth/providers/' which means
      // they require authentication and will get the Authorization header
      for (final endpoint in expectedEndpoints) {
        expect(endpoint.startsWith('/api/auth/providers/'), isTrue,
            reason: 'Endpoint $endpoint should be a protected provider endpoint');
      }
    });

    test('Location integration service methods use correct endpoints', () {
      final httpClient = HttpClient();
      final integrationService = LocationIntegrationService(httpClient);

      // Verify the service is properly configured
      expect(integrationService.httpClient, isNotNull);
      
      // The following endpoints should be called by LocationIntegrationService:
      final expectedEndpoints = [
        '/api/auth/providers/locations/{id}/queues',  // getLocationQueues()
        '/api/auth/providers/schedules',              // getLocationSchedules()
      ];

      // All these endpoints start with '/api/auth/providers/' which means
      // they require authentication and will get the Authorization header
      for (final endpoint in expectedEndpoints) {
        expect(endpoint.startsWith('/api/auth/providers/'), isTrue,
            reason: 'Endpoint $endpoint should be a protected provider endpoint');
      }
    });

    test('Complete authentication verification for location management', () {
      // This test verifies the complete authentication flow for location management

      // 1. User logs in and gets sessionId
      const sessionId = 'session-abc123';
      
      // 2. SessionId is stored as JWT token
      final tokenData = {
        'access_token': sessionId,
        'refresh_token': 'refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };
      
      final jwtToken = JwtToken.fromJson(tokenData);
      expect(jwtToken.accessToken, equals(sessionId));
      expect(jwtToken.isValid, isTrue);

      // 3. Authorization header is generated
      final authHeader = '${jwtToken.tokenType} ${jwtToken.accessToken}';
      expect(authHeader, equals('Bearer session-abc123'));

      // 4. When LocationApiService makes API calls, the AuthInterceptor
      //    automatically adds this header to all requests to protected endpoints

      // 5. The Dalti Provider API receives requests like:
      //    GET /api/auth/providers/locations
      //    Authorization: Bearer session-abc123
      //
      //    POST /api/auth/providers/locations
      //    Authorization: Bearer session-abc123
      //    Content-Type: application/json
      //    { "name": "New Location", ... }

      // 6. API validates the sessionId and allows the request
      
      // This ensures that all location management operations are properly authenticated
      expect(authHeader.contains(sessionId), isTrue);
      expect(authHeader.startsWith('Bearer '), isTrue);
    });

    test('Token expiration and refresh for location API calls', () {
      // Test token expiration scenario
      final expiredToken = JwtToken(
        accessToken: 'expired-session-id',
        refreshToken: 'refresh-token',
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
      );

      expect(expiredToken.isExpired, isTrue);
      expect(expiredToken.isValid, isFalse);

      // When an expired token is used, the AuthInterceptor should:
      // 1. Detect the 401 response
      // 2. Attempt to refresh the token
      // 3. Retry the original request with the new token
      // 4. If refresh fails, clear the token and redirect to login

      // Test valid token
      final validToken = JwtToken(
        accessToken: 'valid-session-id',
        refreshToken: 'refresh-token',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      expect(validToken.isExpired, isFalse);
      expect(validToken.isValid, isTrue);

      final authHeader = '${validToken.tokenType} ${validToken.accessToken}';
      expect(authHeader, equals('Bearer valid-session-id'));
    });
  });
}
