import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/routing/app_routes.dart';
import '../models/onboarding_models.dart';

/// Onboarding completion screen showing success and next steps
class OnboardingCompletionScreen extends ConsumerWidget {
  final OnboardingCompletion completion;

  const OnboardingCompletionScreen({
    super.key,
    required this.completion,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              const Spacer(),
              
              // Success animation/icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: context.colors.primary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  size: 80,
                  color: context.colors.onPrimary,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Success title
              Text(
                'Setup Complete!',
                style: context.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: context.colors.primary,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Success message
              Text(
                'Congratulations! Your business "${completion.businessProfile.businessName}" is now ready to serve customers.',
                style: context.textTheme.bodyLarge?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Setup summary
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: context.colors.surfaceVariant.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: context.colors.outline.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'What You\'ve Set Up:',
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildSummaryItem(
                      context,
                      Icons.business,
                      'Business Profile',
                      completion.businessProfile.businessName,
                    ),
                    
                    _buildSummaryItem(
                      context,
                      Icons.location_on,
                      'Primary Location',
                      completion.primaryLocation.name,
                    ),
                    
                    _buildSummaryItem(
                      context,
                      Icons.room_service,
                      'Services',
                      '${completion.services.length} service(s) created',
                    ),
                    
                    _buildSummaryItem(
                      context,
                      Icons.queue,
                      'Queues',
                      '${completion.queues.length} queue(s) configured',
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Next steps
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: context.colors.primaryContainer.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: context.colors.primary.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: context.colors.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Next Steps:',
                          style: context.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: context.colors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    _buildNextStepItem(
                      context,
                      '1. Review your dashboard to see your business overview',
                    ),
                    _buildNextStepItem(
                      context,
                      '2. Start accepting customer bookings',
                    ),
                    _buildNextStepItem(
                      context,
                      '3. Manage your queues and monitor customer flow',
                    ),
                    _buildNextStepItem(
                      context,
                      '4. Add more locations or services as needed',
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Action buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => context.go(AppRoutes.dashboard),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Go to Dashboard'),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  TextButton(
                    onPressed: () => context.go(AppRoutes.profile),
                    child: const Text('View Profile'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: context.colors.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: context.textTheme.bodySmall?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.check_circle,
            size: 16,
            color: context.colors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.arrow_forward,
            size: 16,
            color: context.colors.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
