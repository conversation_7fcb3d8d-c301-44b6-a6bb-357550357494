import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import 'appointment_card.dart';
import 'calendar_header.dart';

/// Weekly calendar view showing appointments for a week
class WeeklyCalendarView extends ConsumerStatefulWidget {
  final DateTime selectedDate;
  final Function(DateTime) onDateChanged;
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;

  const WeeklyCalendarView({
    super.key,
    required this.selectedDate,
    required this.onDateChanged,
    this.onAppointmentTap,
    this.onAddAppointment,
  });

  @override
  ConsumerState<WeeklyCalendarView> createState() => _WeeklyCalendarViewState();
}

class _WeeklyCalendarViewState extends ConsumerState<WeeklyCalendarView> {
  final ScrollController _scrollController = ScrollController();
  late DateTime _startOfWeek;
  late DateTime _endOfWeek;

  @override
  void initState() {
    super.initState();
    _calculateWeekRange();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentTime();
    });
  }

  @override
  void didUpdateWidget(WeeklyCalendarView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedDate != widget.selectedDate) {
      _calculateWeekRange();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _calculateWeekRange() {
    // Calculate start of week (Monday)
    final weekday = widget.selectedDate.weekday;
    _startOfWeek = widget.selectedDate.subtract(Duration(days: weekday - 1));
    _endOfWeek = _startOfWeek.add(const Duration(days: 6));
  }

  void _scrollToCurrentTime() {
    if (!mounted) return;
    
    final now = DateTime.now();
    if (_isDateInWeek(now)) {
      // Scroll to current hour
      final hourHeight = 60.0; // Height per hour slot
      final currentHour = now.hour;
      final scrollOffset = (currentHour - 2) * hourHeight; // Show 2 hours before current
      
      if (scrollOffset > 0) {
        _scrollController.animateTo(
          scrollOffset.clamp(0, _scrollController.position.maxScrollExtent),
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Calendar header
        CalendarHeader(
          currentDate: widget.selectedDate,
          viewType: 'week',
          onPreviousPressed: () => _changeWeek(-1),
          onNextPressed: () => _changeWeek(1),
          onTodayPressed: () => widget.onDateChanged(DateTime.now()),
          onViewChanged: (viewType) {
            // Handle view change in parent widget
          },
          onAddAppointment: widget.onAddAppointment,
        ),
        
        // Week view content
        Expanded(
          child: _buildWeekView(),
        ),
      ],
    );
  }

  Widget _buildWeekView() {
    final appointmentsAsync = ref.watch(appointmentNotifierProvider);
    
    return appointmentsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load appointments',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.read(appointmentNotifierProvider.notifier).refreshAppointments(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (appointments) {
        // Filter appointments for the week
        final weekAppointments = appointments.where((apt) => 
          _isDateInWeek(apt.scheduledTime)
        ).toList();
        
        return _buildWeekGrid(weekAppointments);
      },
    );
  }

  Widget _buildWeekGrid(List<Appointment> appointments) {
    return Column(
      children: [
        // Days header
        _buildDaysHeader(),
        
        // Week grid
        Expanded(
          child: Row(
            children: [
              // Time labels column
              SizedBox(
                width: 50,
                child: Column(
                  children: [
                    const SizedBox(height: 40), // Space for day headers
                    Expanded(
                      child: ListView.builder(
                        controller: _scrollController,
                        itemCount: 24, // 24 hours
                        itemBuilder: (context, index) {
                          final hour = index;
                          return SizedBox(
                            height: 60,
                            child: Center(
                              child: Text(
                                DateFormat('HH:mm').format(DateTime(2023, 1, 1, hour)),
                                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              
              // Days columns
              Expanded(
                child: Row(
                  children: List.generate(7, (dayIndex) {
                    final date = _startOfWeek.add(Duration(days: dayIndex));
                    final dayAppointments = appointments.where((apt) => 
                      _isSameDay(apt.scheduledTime, date)
                    ).toList();
                    
                    return Expanded(
                      child: _buildDayColumn(date, dayAppointments),
                    );
                  }),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDaysHeader() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 50), // Space for time column
          Expanded(
            child: Row(
              children: List.generate(7, (index) {
                final date = _startOfWeek.add(Duration(days: index));
                final isToday = _isSameDay(date, DateTime.now());
                
                return Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                        ),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          DateFormat('E').format(date),
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: isToday 
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                            fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        Text(
                          DateFormat('d').format(date),
                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: isToday 
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurface,
                            fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayColumn(DateTime date, List<Appointment> dayAppointments) {
    final isToday = _isSameDay(date, DateTime.now());
    
    return Container(
      decoration: BoxDecoration(
        color: isToday 
            ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.1)
            : null,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Stack(
        children: [
          // Hour grid lines
          ListView.builder(
            controller: ScrollController(), // Separate controller for grid
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 24,
            itemBuilder: (context, index) {
              return Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
                      width: 0.5,
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Appointments
          ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(2),
            itemCount: dayAppointments.length,
            itemBuilder: (context, index) {
              final appointment = dayAppointments[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: AppointmentCard(
                  appointment: appointment,
                  isCompact: true,
                  showActions: false,
                  onTap: () => widget.onAppointmentTap?.call(appointment),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _changeWeek(int weeks) {
    final newDate = widget.selectedDate.add(Duration(days: weeks * 7));
    widget.onDateChanged(newDate);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  bool _isDateInWeek(DateTime date) {
    return date.isAfter(_startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(_endOfWeek.add(const Duration(days: 1)));
  }
}
