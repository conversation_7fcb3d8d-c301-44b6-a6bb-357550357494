import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/profile_completion_models.dart';
import '../services/profile_completion_service.dart';

part 'profile_completion_provider.g.dart';

/// Profile completion state
class ProfileCompletionState {
  final ProfileCompletionData? data;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const ProfileCompletionState({
    this.data,
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  ProfileCompletionState copyWith({
    ProfileCompletionData? data,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return ProfileCompletionState(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasData => data != null;
  bool get hasError => error != null;
}

/// Provider for ProfileCompletionService
@riverpod
ProfileCompletionService profileCompletionService(
  ProfileCompletionServiceRef ref,
) {
  final httpClient = ref.watch(httpClientProvider);
  return ProfileCompletionService(httpClient);
}

/// Profile completion provider
@riverpod
class ProfileCompletionNotifier extends _$ProfileCompletionNotifier {
  @override
  ProfileCompletionState build() {
    // Load initial data
    _loadProfileCompletion();

    return const ProfileCompletionState(isLoading: true);
  }

  /// Load profile completion data
  Future<void> _loadProfileCompletion() async {
    try {
      print('[ProfileCompletionNotifier] Loading profile completion data...');

      final service = ref.read(profileCompletionServiceProvider);
      final data = await service.getProfileCompletion();

      state = state.copyWith(
        data: data,
        isLoading: false,
        error: null,
        lastUpdated: DateTime.now(),
      );

      print(
        '[ProfileCompletionNotifier] Profile completion data loaded successfully',
      );
    } catch (e) {
      print(
        '[ProfileCompletionNotifier] Error loading profile completion data: $e',
      );

      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// Refresh profile completion data
  Future<void> refresh() async {
    state = state.copyWith(isLoading: true, error: null);
    await _loadProfileCompletion();
  }
}
