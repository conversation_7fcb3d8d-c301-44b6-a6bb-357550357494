import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/core/storage/storage_service.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/debug/storage_debug.dart';

void main() {
  group('Storage Verification Tests', () {
    setUpAll(() async {
      // Initialize storage for testing
      await StorageService.init();
    });

    tearDown(() async {
      // Clear all data after each test
      await StorageService.clearAuth();
    });

    test('Storage service can store and retrieve auth data', () async {
      // Test basic storage operations
      const testKey = 'test_token';
      const testValue = 'test-session-id-12345';

      // Store data
      await StorageService.saveAuth(testKey, testValue);

      // Retrieve data
      final retrievedValue = StorageService.getAuth<String>(testKey);

      expect(retrievedValue, equals(testValue));
      print('✅ Basic storage test passed');
    });

    test('JWT service can store and retrieve tokens', () async {
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);

      // Initialize JWT service
      await jwtService.initialize();

      // Simulate login response
      final loginResponse = {
        'access_token': 'test-session-id-67890',
        'refresh_token': 'test-refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // Store token
      await jwtService.storeTokenFromResponse(loginResponse);

      // Verify token is stored
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentToken, isNotNull);
      expect(jwtService.currentToken!.accessToken, equals('test-session-id-67890'));

      // Verify authorization header
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, equals('Bearer test-session-id-67890'));

      print('✅ JWT service storage test passed');
      print('   - Token stored: ${jwtService.currentToken!.accessToken}');
      print('   - Auth header: $authHeader');
    });

    test('Storage debug tools work correctly', () async {
      // Test storage debug functionality
      print('🔍 Testing storage debug tools...');

      // Check initial state
      expect(StorageDebug.isStorageInitialized(), isTrue);

      // Store some test data
      await StorageService.saveAuth('test_access_token', 'test-token-123');
      await StorageService.saveAuth('test_user_id', 'user-456');

      // Print debug info (this will show in test output)
      StorageDebug.printAllAuthData();

      // Verify data exists
      final accessToken = StorageService.getAuth<String>('test_access_token');
      final userId = StorageService.getAuth<String>('test_user_id');

      expect(accessToken, equals('test-token-123'));
      expect(userId, equals('user-456'));

      print('✅ Storage debug tools test passed');
    });

    test('Complete authentication flow simulation', () async {
      print('🔄 Simulating complete authentication flow...');

      // Create JWT service
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);
      await jwtService.initialize();

      // Step 1: Check initial state (should be unauthenticated)
      expect(jwtService.isAuthenticated, isFalse);
      print('   Step 1: Initial state - not authenticated ✅');

      // Step 2: Simulate login
      final loginResponse = {
        'access_token': 'session-abc123',
        'refresh_token': 'refresh-xyz789',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      await jwtService.storeTokenFromResponse(loginResponse);
      print('   Step 2: Login response stored ✅');

      // Step 3: Verify authentication state
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.getAuthorizationHeader(), equals('Bearer session-abc123'));
      print('   Step 3: Authentication verified ✅');

      // Step 4: Simulate app restart (create new JWT service)
      final newJwtService = JwtService(HttpClient());
      await newJwtService.initialize();

      // Step 5: Verify token persists after restart
      expect(newJwtService.isAuthenticated, isTrue);
      expect(newJwtService.getAuthorizationHeader(), equals('Bearer session-abc123'));
      print('   Step 4: Token persists after restart ✅');

      // Step 6: Simulate logout
      await newJwtService.clearToken();
      expect(newJwtService.isAuthenticated, isFalse);
      print('   Step 5: Logout successful ✅');

      print('✅ Complete authentication flow test passed');
    });

    test('Debug what happens during real login flow', () async {
      print('🔍 Debugging real login flow simulation...');

      // Simulate the exact flow that happens in the app
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);
      await jwtService.initialize();

      // Step 1: Simulate API response (what comes from Dalti Provider API)
      final apiResponse = {
        'sessionId': 'dalti-session-12345',  // This is what API returns
        'refreshToken': 'dalti-refresh-token',
        'user': {'id': 'user-123', 'email': '<EMAIL>'},
        'provider': {'id': 1, 'businessName': 'Test Clinic'},
      };

      // Step 2: Convert to AuthResponse format (what AuthResponse.fromJson does)
      final authResponseData = {
        'success': true,
        'message': 'Login successful',
        'accessToken': apiResponse['sessionId'],  // sessionId becomes accessToken
        'refreshToken': apiResponse['refreshToken'],
        'user': apiResponse['user'],
        'provider': apiResponse['provider'],
      };

      // Step 3: Convert to JWT service format (what happens in AuthRepository)
      final jwtTokenData = {
        'access_token': authResponseData['accessToken'],  // sessionId
        'refresh_token': authResponseData['refreshToken'],
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // Step 4: Store in JWT service
      await jwtService.storeTokenFromResponse(jwtTokenData);

      // Step 5: Verify everything works
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentToken!.accessToken, equals('dalti-session-12345'));
      expect(jwtService.getAuthorizationHeader(), equals('Bearer dalti-session-12345'));

      print('✅ Real login flow simulation passed');
      print('   - API sessionId: ${apiResponse['sessionId']}');
      print('   - Stored as accessToken: ${jwtService.currentToken!.accessToken}');
      print('   - Auth header: ${jwtService.getAuthorizationHeader()}');
      print('   - This header should be sent to /api/auth/providers/locations');
    });
  });
}
