import 'dart:typed_data';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/profile_models.dart';
import '../services/profile_api_service.dart';

part 'profile_provider.g.dart';

/// Profile API service provider
@riverpod
ProfileApiService profileApiService(ProfileApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return ProfileApiService(httpClient);
}

/// Profile state notifier
class ProfileNotifier extends StateNotifier<AsyncValue<ProfileData>> {
  ProfileNotifier(this._apiService) : super(const AsyncValue.loading());

  final ProfileApiService _apiService;

  /// Load profile data
  Future<void> loadProfile() async {
    state = const AsyncValue.loading();

    try {
      final result = await _apiService.getProfile();

      result.when(
        success: (data) {
          state = AsyncValue.data(data);
        },
        error: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update profile data
  Future<bool> updateProfile(ProfileUpdateRequest request) async {
    // Keep current data while updating
    final currentData = state.value;
    if (currentData == null) return false;

    // Set loading state
    state = const AsyncValue.loading();

    try {
      final result = await _apiService.updateProfile(request);

      return result.when(
        success: (data) {
          state = AsyncValue.data(data);
          return true;
        },
        error: (error) {
          // Restore previous data on error
          state = AsyncValue.data(currentData);
          // Re-throw error so UI can handle it
          throw error;
        },
      );
    } catch (e, stackTrace) {
      // Restore previous data on error
      state = AsyncValue.data(currentData);
      state = AsyncValue.error(e, stackTrace);
      return false;
    }
  }

  /// Refresh profile data
  Future<void> refresh() async {
    await loadProfile();
  }
}

/// Profile provider
@riverpod
class Profile extends _$Profile {
  @override
  AsyncValue<ProfileData> build() {
    // Auto-load profile when provider is first accessed
    _loadProfile();
    return const AsyncValue.loading();
  }

  Future<void> _loadProfile() async {
    final apiService = ref.read(profileApiServiceProvider);

    try {
      final profileResult = await apiService.getProfile();

      await profileResult.when(
        success: (profileData) async {
          try {
            // After getting the main profile, fetch the picture info
            final pictureInfo = await apiService.getProfilePicture();
            if (pictureInfo.hasProfilePicture &&
                pictureInfo.profilePicture != null) {
              // If picture exists, update the profile data with the download URL
              final updatedProfileData = profileData.copyWith(
                profilePictureUrl: pictureInfo.profilePicture!.downloadUrl,
              );
              state = AsyncValue.data(updatedProfileData);
            } else {
              // No picture, just use the original profile data
              state = AsyncValue.data(profileData);
            }
          } catch (e) {
            // If fetching the picture fails, we still have the main profile data
            print(
              '[ProfileProvider] Failed to fetch profile picture, but continuing with main profile data. Error: $e',
            );
            state = AsyncValue.data(profileData);
          }
        },
        error: (error) {
          state = AsyncValue.error(error, StackTrace.current);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update profile
  Future<bool> updateProfile(ProfileUpdateRequest request) async {
    final apiService = ref.read(profileApiServiceProvider);
    final currentData = state.value;

    if (currentData == null) return false;

    // Set loading state
    state = const AsyncValue.loading();

    try {
      final result = await apiService.updateProfile(request);

      return result.when(
        success: (data) {
          state = AsyncValue.data(data);
          return true;
        },
        error: (error) {
          // Restore previous data on error
          state = AsyncValue.data(currentData);
          // Re-throw error so UI can handle it
          throw error;
        },
      );
    } catch (e, stackTrace) {
      // Restore previous data on error
      state = AsyncValue.data(currentData);
      state = AsyncValue.error(e, stackTrace);
      return false;
    }
  }

  /// Upload profile picture
  Future<void> uploadProfilePicture(
    String fileName,
    String fileType,
    Uint8List bytes,
  ) async {
    final apiService = ref.read(profileApiServiceProvider);

    try {
      // Step 1: Request the upload URL
      final uploadInfo = await apiService.requestProfilePictureUploadUrl(
        fileName,
        fileType,
      );

      // Step 2: Upload the file to the returned URL
      await apiService.uploadProfilePicture(
        uploadInfo,
        bytes,
        fileName,
        fileType,
      );

      // Step 3: Refresh profile data to get the new picture URL
      await refresh();
    } catch (e) {
      // Re-throw to be handled by the UI
      throw Exception('Failed to upload profile picture: $e');
    }
  }

  /// Refresh profile data
  Future<void> refresh() async {
    await _loadProfile();
  }
}

/// Legacy StateNotifier provider for backward compatibility
@riverpod
ProfileNotifier profileNotifier(ProfileNotifierRef ref) {
  final apiService = ref.watch(profileApiServiceProvider);
  return ProfileNotifier(apiService);
}

/// Profile state provider using StateNotifier
@riverpod
AsyncValue<ProfileData> profileState(ProfileStateRef ref) {
  final notifier = ref.watch(profileNotifierProvider);
  return ref.watch(
    profileNotifierProvider.select((notifier) => notifier.state),
  );
}

/// Provider categories provider
@riverpod
Future<List<ProfileCategory>> profileCategories(
  ProfileCategoriesRef ref,
) async {
  final apiService = ref.watch(profileApiServiceProvider);
  return apiService.getCategories();
}
