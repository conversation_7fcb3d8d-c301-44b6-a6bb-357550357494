import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_provider.dart';
import '../../../../core/utils/phone_validator.dart';

/// Model for phone number data
class PhoneNumberData {
  final String? mobile;
  final String? landline;
  final String? fax;

  const PhoneNumberData({this.mobile, this.landline, this.fax});

  PhoneNumberData copyWith({String? mobile, String? landline, String? fax}) {
    return PhoneNumberData(
      mobile: mobile ?? this.mobile,
      landline: landline ?? this.landline,
      fax: fax ?? this.fax,
    );
  }

  /// Check if at least one phone number is provided
  bool get hasPhoneNumber {
    return (mobile?.isNotEmpty ?? false) ||
        (landline?.isNotEmpty ?? false) ||
        (fax?.isNotEmpty ?? false);
  }

  /// Check if at least one valid phone number is provided
  bool get hasValidPhoneNumber {
    return PhoneValidator.hasValidPhone(
      mobile: mobile,
      landline: landline,
      fax: fax,
    );
  }

  /// Get phone validation error message
  String? get phoneValidationError {
    return PhoneValidator.validatePhoneRequirement(
      mobile: mobile,
      landline: landline,
      fax: fax,
      isRequired: true,
    );
  }

  /// Get the primary phone number (mobile first, then landline, then fax)
  String? get primaryPhone {
    if (mobile?.isNotEmpty ?? false) return mobile;
    if (landline?.isNotEmpty ?? false) return landline;
    if (fax?.isNotEmpty ?? false) return fax;
    return null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhoneNumberData &&
        other.mobile == mobile &&
        other.landline == landline &&
        other.fax == fax;
  }

  @override
  int get hashCode => mobile.hashCode ^ landline.hashCode ^ fax.hashCode;
}

/// Reusable widget for managing multiple phone numbers
/// Used in BusinessProfile step
class PhoneNumbersWidget extends ConsumerStatefulWidget {
  final PhoneNumberData? initialData;
  final ValueChanged<PhoneNumberData> onChanged;
  final String? title;
  final String? subtitle;
  final bool enabled;
  final bool isRequired;

  const PhoneNumbersWidget({
    super.key,
    required this.onChanged,
    this.initialData,
    this.title,
    this.subtitle,
    this.enabled = true,
    this.isRequired = true,
  });

  @override
  ConsumerState<PhoneNumbersWidget> createState() => _PhoneNumbersWidgetState();
}

class _PhoneNumbersWidgetState extends ConsumerState<PhoneNumbersWidget> {
  late TextEditingController _mobileController;
  late TextEditingController _landlineController;
  late TextEditingController _faxController;

  late PhoneNumberData _phoneData;

  @override
  void initState() {
    super.initState();

    _phoneData = widget.initialData ?? const PhoneNumberData();

    _mobileController = TextEditingController(text: _phoneData.mobile);
    _landlineController = TextEditingController(text: _phoneData.landline);
    _faxController = TextEditingController(text: _phoneData.fax);

    // Add listeners
    _mobileController.addListener(_onMobileChanged);
    _landlineController.addListener(_onLandlineChanged);
    _faxController.addListener(_onFaxChanged);
  }

  @override
  void didUpdateWidget(PhoneNumbersWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.initialData != oldWidget.initialData) {
      _phoneData = widget.initialData ?? const PhoneNumberData();

      _mobileController.text = _phoneData.mobile ?? '';
      _landlineController.text = _phoneData.landline ?? '';
      _faxController.text = _phoneData.fax ?? '';
    }
  }

  @override
  void dispose() {
    _mobileController.dispose();
    _landlineController.dispose();
    _faxController.dispose();
    super.dispose();
  }

  void _onMobileChanged() {
    _updatePhoneData(mobile: _mobileController.text.trim());
  }

  void _onLandlineChanged() {
    _updatePhoneData(landline: _landlineController.text.trim());
  }

  void _onFaxChanged() {
    _updatePhoneData(fax: _faxController.text.trim());
  }

  void _updatePhoneData({String? mobile, String? landline, String? fax}) {
    _phoneData = _phoneData.copyWith(
      mobile: mobile ?? _phoneData.mobile,
      landline: landline ?? _phoneData.landline,
      fax: fax ?? _phoneData.fax,
    );

    widget.onChanged(_phoneData);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          if (widget.title != null) ...[
            Row(
              children: [
                Icon(Icons.phone, color: theme.colorScheme.primary, size: 24),
                const SizedBox(width: 8),
                Text(
                  widget.title!,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.isRequired) ...[
                  const SizedBox(width: 4),
                  Text(
                    '*',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: theme.colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
            if (widget.subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                widget.subtitle!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
            const SizedBox(height: 16),
          ],

          // Required notice
          if (widget.isRequired) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'At least one phone number is required for customer contact.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Phone number fields
          _buildPhoneField(
            controller: _mobileController,
            label: 'Mobile Phone',
            icon: Icons.smartphone,
            hint: 'e.g., +213 555 123 456',
            isPrimary: true,
          ),

          const SizedBox(height: 16),

          _buildPhoneField(
            controller: _landlineController,
            label: 'Landline Phone',
            icon: Icons.phone,
            hint: 'e.g., +213 21 123 456',
          ),

          const SizedBox(height: 16),

          _buildPhoneField(
            controller: _faxController,
            label: 'Fax Number',
            icon: Icons.fax,
            hint: 'e.g., +213 21 123 457',
          ),

          // Validation message
          if (widget.isRequired && _phoneData.phoneValidationError != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.error.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: theme.colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _phoneData.phoneValidationError!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPhoneField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hint,
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      enabled: widget.enabled,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9+\-\s\(\)]')),
      ],
      decoration: InputDecoration(
        labelText: isPrimary ? '$label (Recommended)' : label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        suffixIcon:
            isPrimary
                ? Icon(Icons.star, color: theme.colorScheme.primary, size: 20)
                : null,
      ),
      validator: (value) {
        // Validate individual phone number format
        final phoneError = PhoneValidator.validateAlgerianPhone(value);
        if (phoneError != null) {
          return phoneError;
        }

        // If this is required and no valid phone numbers are provided
        if (widget.isRequired && !_phoneData.hasValidPhoneNumber) {
          // Only show this error on the first field (mobile) to avoid duplicate messages
          if (controller == _mobileController) {
            return 'At least one valid phone number is required';
          }
        }

        return null;
      },
    );
  }
}
