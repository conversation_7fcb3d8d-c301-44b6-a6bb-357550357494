import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../models/location_models.dart';

/// API service for location management operations
class LocationApiService extends ApiService {
  LocationApiService(super.httpClient);

  /// Get all locations for the current provider
  Future<List<Location>> getLocations({
    bool? isActive,
    String? search,
  }) async {
    try {
      print('[] ');

      final queryParams = <String, dynamic>{};
      if (isActive != null) queryParams['isActive'] = isActive;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;

      final response = await httpClient.get(
        '/api/auth/providers/locations',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic> && responseData['data'] is List) {
          // Handle paginated response
          final locations = (responseData['data'] as List)
              .map((locationJson) => Location.fromJson(locationJson as Map<String, dynamic>))
              .toList();

          print('[] ');
          return locations;
        } else if (responseData is List) {
          // Handle direct list response
          final locations = responseData
              .map((locationJson) => Location.fromJson(locationJson as Map<String, dynamic>))
              .toList();

          print('[] ');
          return locations;
        } else {
          print('[] ');
          return [];
        }
      } else {
        print('[] ');
        return [];
      }
    } catch (e) {
      print('[] ');
      rethrow;
    }
  }

  /// Get a specific location by ID
  Future<Location> getLocationById(int id) async {
    try {
      print('[] ');

      final response = await httpClient.get('/api/auth/providers/locations/$id');

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Handle direct object response or extract from data field
          final locationData = responseData['data'] ?? responseData;
          final location = Location.fromJson(locationData as Map<String, dynamic>);

          print('[] ');
          return location;
        } else {
          throw Exception('Invalid location response format');
        }
      } else {
        throw Exception('Failed to fetch location with status: ${response.statusCode}');
      }
    } catch (e) {
      print('[] ');
      rethrow;
    }
  }

  /// Create a new location
  Future<Location> createLocation(CreateLocationRequest request) async {
    try {
      print('[] ');
      print('[] ');

      final response = await httpClient.post(
        '/api/auth/providers/locations',
        data: request.toJson(),
      );

      if (response.statusCode == 201 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Handle response with data field or direct object
        final locationData = responseData['data'] ?? responseData;
        final location = Location.fromJson(locationData as Map<String, dynamic>);

        print('[] ');
        return location;
      } else {
        throw Exception('Failed to create location with status: ${response.statusCode}');
      }
    } catch (e) {
      print('[] ');
      
      // Handle specific error types
      if (e is DioException) {
        if (e.response?.statusCode == 400) {
          throw Exception('Invalid location data. Please check all required fields.');
        } else if (e.response?.statusCode == 409) {
          throw Exception('A location with this name already exists.');
        } else if (e.response?.statusCode == 422) {
          throw Exception('Invalid coordinates or address format.');
        }
      }
      
      rethrow;
    }
  }

  /// Update an existing location
  Future<Location> updateLocation(int id, UpdateLocationRequest request) async {
    try {
      print('[] ');

      final response = await httpClient.put(
        '/api/auth/providers/locations/$id',
        data: request.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        // Handle response with data field or direct object
        final locationData = responseData['data'] ?? responseData;
        final location = Location.fromJson(locationData as Map<String, dynamic>);

        print('[] ');
        return location;
      } else {
        throw Exception('Failed to update location with status: ${response.statusCode}');
      }
    } catch (e) {
      print('[] ');
      
      // Handle specific error types
      if (e is DioException) {
        if (e.response?.statusCode == 404) {
          throw Exception('Location not found.');
        } else if (e.response?.statusCode == 400) {
          throw Exception('Invalid location data. Please check all fields.');
        } else if (e.response?.statusCode == 409) {
          throw Exception('A location with this name already exists.');
        }
      }
      
      rethrow;
    }
  }

  /// Delete a location
  Future<bool> deleteLocation(int id) async {
    try {
      print('[] ');

      final response = await httpClient.delete('/api/auth/providers/locations/$id');

      if (response.statusCode == 200 || response.statusCode == 204) {
        print('[] ');
        return true;
      } else {
        throw Exception('Failed to delete location with status: ${response.statusCode}');
      }
    } catch (e) {
      print('[] ');
      
      // Handle specific error types
      if (e is DioException) {
        if (e.response?.statusCode == 404) {
          throw Exception('Location not found.');
        } else if (e.response?.statusCode == 409) {
          throw Exception('Cannot delete location. It has associated queues or schedules.');
        } else if (e.response?.statusCode == 403) {
          throw Exception('You do not have permission to delete this location.');
        }
      }
      
      rethrow;
    }
  }

  /// Check if a location can be deleted (dependency check)
  Future<bool> canDeleteLocation(int id) async {
    try {
      print('[] ');

      final response = await httpClient.get('/api/auth/providers/locations/$id/can-delete');

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        final canDelete = responseData['canDelete'] as bool? ?? false;

        print('[] ');
        return canDelete;
      } else {
        print('[] ');
        return false;
      }
    } catch (e) {
      print('[] ');
      // If endpoint doesn't exist, assume we can try to delete
      return true;
    }
  }

  /// Get location statistics
  Future<Map<String, dynamic>> getLocationStats(int id) async {
    try {
      print('[] ');

      final response = await httpClient.get('/api/auth/providers/locations/$id/stats');

      if (response.statusCode == 200 && response.data != null) {
        final stats = response.data as Map<String, dynamic>;
        print('[] ');
        return stats;
      } else {
        print('[] ');
        return {};
      }
    } catch (e) {
      print('[] ');
      // Return empty stats if endpoint doesn't exist
      return {};
    }
  }
}
