import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../schedules/models/opening_hours_models.dart';
import '../../../../core/theme/theme_provider.dart';
import 'time_slot_dialog.dart';

/// Reusable widget for managing opening hours
/// Used in both Location and Queue setup steps
class OpeningHoursWidget extends ConsumerStatefulWidget {
  final OpeningHours? initialOpeningHours;
  final ValueChanged<OpeningHours> onChanged;
  final String? title;
  final String? subtitle;
  final bool enabled;
  final bool showInheritOption;
  final bool isInheriting;
  final VoidCallback? onInheritToggle;

  const OpeningHoursWidget({
    super.key,
    required this.onChanged,
    this.initialOpeningHours,
    this.title,
    this.subtitle,
    this.enabled = true,
    this.showInheritOption = false,
    this.isInheriting = false,
    this.onInheritToggle,
  });

  @override
  ConsumerState<OpeningHoursWidget> createState() => _OpeningHoursWidgetState();
}

class _OpeningHoursWidgetState extends ConsumerState<OpeningHoursWidget> {
  late OpeningHours _openingHours;
  
  static const List<String> _daysOfWeek = [
    'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
    'Thursday', 'Friday', 'Saturday'
  ];

  @override
  void initState() {
    super.initState();
    _openingHours = widget.initialOpeningHours ?? OpeningHours.allClosed();
  }

  @override
  void didUpdateWidget(OpeningHoursWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialOpeningHours != oldWidget.initialOpeningHours) {
      _openingHours = widget.initialOpeningHours ?? OpeningHours.allClosed();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ref.watch(themeProvider);
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            if (widget.title != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    color: theme.colors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.title!,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  widget.subtitle!,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colors.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
              const SizedBox(height: 16),
            ],

            // Inherit option (for queues)
            if (widget.showInheritOption) ...[
              SwitchListTile(
                title: const Text('Inherit opening hours from location'),
                subtitle: const Text('Use the same hours as the location'),
                value: widget.isInheriting,
                onChanged: widget.enabled ? (_) => widget.onInheritToggle?.call() : null,
                activeColor: theme.colors.primary,
              ),
              const SizedBox(height: 16),
            ],

            // Opening hours editor
            if (!widget.isInheriting) ...[
              // Quick actions
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: widget.enabled ? _setStandardBusinessHours : null,
                      icon: const Icon(Icons.business_center, size: 18),
                      label: const Text('Standard Hours'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: widget.enabled ? _setAllClosed : null,
                      icon: const Icon(Icons.close, size: 18),
                      label: const Text('All Closed'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Days of week
              ...List.generate(_daysOfWeek.length, (index) {
                final dayName = _daysOfWeek[index];
                final dayHours = _openingHours.getDayHours(dayName);
                
                return _buildDayRow(
                  context,
                  theme,
                  dayName,
                  dayHours ?? DayOpeningHours.closed(dayName),
                );
              }),
            ] else ...[
              // Inherit message
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colors.primary.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This queue will use the same opening hours as its location.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDayRow(
    BuildContext context,
    ThemeData theme,
    String dayName,
    DayOpeningHours dayHours,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // Day name
          SizedBox(
            width: 80,
            child: Text(
              dayName,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          // Active toggle
          Switch(
            value: dayHours.isActive,
            onChanged: widget.enabled ? (value) => _toggleDay(dayName, value) : null,
            activeColor: theme.colors.primary,
          ),
          
          const SizedBox(width: 16),
          
          // Time slots
          if (dayHours.isActive) ...[
            Expanded(
              child: dayHours.hours.isEmpty
                  ? _buildAddTimeSlotButton(dayName)
                  : _buildTimeSlots(dayName, dayHours.hours),
            ),
          ] else ...[
            Expanded(
              child: Text(
                'Closed',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colors.onSurface.withOpacity(0.5),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAddTimeSlotButton(String dayName) {
    return TextButton.icon(
      onPressed: widget.enabled ? () => _addTimeSlot(dayName) : null,
      icon: const Icon(Icons.add, size: 16),
      label: const Text('Add hours'),
      style: TextButton.styleFrom(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.zero,
      ),
    );
  }

  Widget _buildTimeSlots(String dayName, List<TimeSlot> timeSlots) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: timeSlots.asMap().entries.map((entry) {
        final index = entry.key;
        final timeSlot = entry.value;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              // Time range
              Expanded(
                child: Text(
                  '${timeSlot.timeFrom} - ${timeSlot.timeTo}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              
              // Edit button
              IconButton(
                onPressed: widget.enabled ? () => _editTimeSlot(dayName, index, timeSlot) : null,
                icon: const Icon(Icons.edit, size: 16),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),
              
              // Delete button
              if (timeSlots.length > 1 || !widget.enabled)
                IconButton(
                  onPressed: widget.enabled ? () => _removeTimeSlot(dayName, index) : null,
                  icon: const Icon(Icons.delete, size: 16),
                  constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                  padding: EdgeInsets.zero,
                ),
            ],
          ),
        );
      }).toList(),
    );
  }

  void _toggleDay(String dayName, bool isActive) {
    final dayHours = _openingHours.getDayHours(dayName);
    if (dayHours == null) return;

    final updatedDayHours = isActive
        ? DayOpeningHours.singleSlot(
            dayOfWeek: dayName,
            openTime: '09:00',
            closeTime: '17:00',
          )
        : DayOpeningHours.closed(dayName);

    _updateDayHours(dayName, updatedDayHours);
  }

  void _addTimeSlot(String dayName) {
    _editTimeSlot(dayName, -1, null);
  }

  void _editTimeSlot(String dayName, int index, TimeSlot? timeSlot) async {
    final newTimeSlot = await TimeSlotDialog.show(
      context,
      initialTimeSlot: timeSlot,
      dayName: dayName,
    );

    if (newTimeSlot == null) return;

    final dayHours = _openingHours.getDayHours(dayName);
    if (dayHours == null) return;

    final updatedHours = List<TimeSlot>.from(dayHours.hours);
    if (index >= 0 && index < updatedHours.length) {
      updatedHours[index] = newTimeSlot;
    } else {
      updatedHours.add(newTimeSlot);
    }

    final updatedDayHours = DayOpeningHours(
      dayOfWeek: dayName,
      isActive: true,
      hours: updatedHours,
    );

    _updateDayHours(dayName, updatedDayHours);
  }

  void _removeTimeSlot(String dayName, int index) {
    final dayHours = _openingHours.getDayHours(dayName);
    if (dayHours == null) return;

    final updatedHours = List<TimeSlot>.from(dayHours.hours);
    if (index >= 0 && index < updatedHours.length) {
      updatedHours.removeAt(index);
    }

    final updatedDayHours = DayOpeningHours(
      dayOfWeek: dayName,
      isActive: updatedHours.isNotEmpty,
      hours: updatedHours,
    );

    _updateDayHours(dayName, updatedDayHours);
  }

  void _updateDayHours(String dayName, DayOpeningHours newDayHours) {
    final updatedDays = _openingHours.days.map((day) {
      return day.dayOfWeek == dayName ? newDayHours : day;
    }).toList();

    setState(() {
      _openingHours = OpeningHours(days: updatedDays);
    });

    widget.onChanged(_openingHours);
  }

  void _setStandardBusinessHours() {
    setState(() {
      _openingHours = OpeningHours.standardBusiness();
    });
    widget.onChanged(_openingHours);
  }

  void _setAllClosed() {
    setState(() {
      _openingHours = OpeningHours.allClosed();
    });
    widget.onChanged(_openingHours);
  }
}
