// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'app_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storageServiceHash() => r'a6d23bc030486b6d1106efa40d3a7733b6bf906f';

/// Storage service provider
///
/// Copied from [storageService].
@ProviderFor(storageService)
final storageServiceProvider = AutoDisposeProvider<StorageService>.internal(
  storageService,
  name: r'storageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$storageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef StorageServiceRef = AutoDisposeProviderRef<StorageService>;
String _$currentUserIdHash() => r'f0108f50568f97a568e6796c88e004048447b3ab';

/// Current user ID provider
///
/// Copied from [currentUserId].
@ProviderFor(currentUserId)
final currentUserIdProvider = AutoDisposeProvider<String?>.internal(
  currentUserId,
  name: r'currentUserIdProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentUserIdHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CurrentUserIdRef = AutoDisposeProviderRef<String?>;
String _$isAuthenticatedHash() => r'a9ca111c7436dd44a69143bfd7f3f8dda74c4488';

/// Is authenticated provider
///
/// Copied from [isAuthenticated].
@ProviderFor(isAuthenticated)
final isAuthenticatedProvider = AutoDisposeProvider<bool>.internal(
  isAuthenticated,
  name: r'isAuthenticatedProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isAuthenticatedHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsAuthenticatedRef = AutoDisposeProviderRef<bool>;
String _$isDarkModeHash() => r'd7c2de1a1c2eea11f4cd4974f2bf57ff07696c39';

/// Is dark mode provider
///
/// Copied from [isDarkMode].
@ProviderFor(isDarkMode)
final isDarkModeProvider = AutoDisposeProvider<bool>.internal(
  isDarkMode,
  name: r'isDarkModeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isDarkModeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef IsDarkModeRef = AutoDisposeProviderRef<bool>;
String _$httpClientRawHash() => r'91a99c80ad30b3931ac43e9dfe83e7df5533391c';

/// HTTP client provider (without auth interceptor to avoid circular dependency)
///
/// Copied from [httpClientRaw].
@ProviderFor(httpClientRaw)
final httpClientRawProvider = AutoDisposeProvider<HttpClient>.internal(
  httpClientRaw,
  name: r'httpClientRawProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$httpClientRawHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HttpClientRawRef = AutoDisposeProviderRef<HttpClient>;
String _$jwtServiceHash() => r'beb69aeedfa4e4ea893c2174fafcbf618df9a55f';

/// JWT service provider (singleton)
///
/// Copied from [jwtService].
@ProviderFor(jwtService)
final jwtServiceProvider = Provider<JwtService>.internal(
  jwtService,
  name: r'jwtServiceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$jwtServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JwtServiceRef = ProviderRef<JwtService>;
String _$initializedJwtServiceHash() =>
    r'33eede066137f42d075b1bee020f0beb42dbc9ef';

/// Initialized JWT service provider
///
/// Copied from [initializedJwtService].
@ProviderFor(initializedJwtService)
final initializedJwtServiceProvider = FutureProvider<JwtService>.internal(
  initializedJwtService,
  name: r'initializedJwtServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$initializedJwtServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef InitializedJwtServiceRef = FutureProviderRef<JwtService>;
String _$httpClientHash() => r'252a23a881ba63865d3d4cb1b96e588b7f5a092e';

/// HTTP client provider with proper auth interceptor
///
/// Copied from [httpClient].
@ProviderFor(httpClient)
final httpClientProvider = AutoDisposeProvider<HttpClient>.internal(
  httpClient,
  name: r'httpClientProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$httpClientHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HttpClientRef = AutoDisposeProviderRef<HttpClient>;
String _$authApiServiceHash() => r'ca196d41d080b4a86efb052d7dae0f5638d62880';

/// Auth API service provider
///
/// Copied from [authApiService].
@ProviderFor(authApiService)
final authApiServiceProvider = AutoDisposeProvider<AuthApiService>.internal(
  authApiService,
  name: r'authApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$authApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthApiServiceRef = AutoDisposeProviderRef<AuthApiService>;
String _$testApiServiceHash() => r'd6d87954b0ed99f8c3391be1aa7ab35c26169150';

/// Test API service provider
///
/// Copied from [testApiService].
@ProviderFor(testApiService)
final testApiServiceProvider = AutoDisposeProvider<TestApiService>.internal(
  testApiService,
  name: r'testApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$testApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TestApiServiceRef = AutoDisposeProviderRef<TestApiService>;
String _$notificationApiServiceHash() =>
    r'e941bbff26b8818e9c390c0b4d03daf6cac89852';

/// Notification API service provider
///
/// Copied from [notificationApiService].
@ProviderFor(notificationApiService)
final notificationApiServiceProvider =
    AutoDisposeProvider<NotificationApiService>.internal(
  notificationApiService,
  name: r'notificationApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$notificationApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef NotificationApiServiceRef
    = AutoDisposeProviderRef<NotificationApiService>;
String _$categoryServiceHash() => r'8e3da79ff25c23eca6f8d47ce259b5a9e3f6cceb';

/// Category service provider
///
/// Copied from [categoryService].
@ProviderFor(categoryService)
final categoryServiceProvider = AutoDisposeProvider<CategoryService>.internal(
  categoryService,
  name: r'categoryServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoryServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoryServiceRef = AutoDisposeProviderRef<CategoryService>;
String _$serviceApiServiceHash() => r'b267bd4083bb88bd6b114ca02c85f5dd9f4f32d1';

/// Service API service provider
///
/// Copied from [serviceApiService].
@ProviderFor(serviceApiService)
final serviceApiServiceProvider =
    AutoDisposeProvider<ServiceApiService>.internal(
  serviceApiService,
  name: r'serviceApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serviceApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ServiceApiServiceRef = AutoDisposeProviderRef<ServiceApiService>;
String _$serviceRepositoryHash() => r'a65250ddf27d259f4cdcdb1deee2a4be23898b2a';

/// Service repository provider
///
/// Copied from [serviceRepository].
@ProviderFor(serviceRepository)
final serviceRepositoryProvider =
    AutoDisposeProvider<ServiceRepository>.internal(
  serviceRepository,
  name: r'serviceRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$serviceRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ServiceRepositoryRef = AutoDisposeProviderRef<ServiceRepository>;
String _$profileApiServiceHash() => r'4d23744e8d85e77fadce2b263db7bde9b6c1a194';

/// Profile API service provider
///
/// Copied from [profileApiService].
@ProviderFor(profileApiService)
final profileApiServiceProvider =
    AutoDisposeProvider<ProfileApiService>.internal(
  profileApiService,
  name: r'profileApiServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$profileApiServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ProfileApiServiceRef = AutoDisposeProviderRef<ProfileApiService>;
String _$routerHash() => r'8266cdae325c0ea6058c74a601267267aac605cf';

/// Router provider with state-based routing
///
/// Copied from [router].
@ProviderFor(router)
final routerProvider = AutoDisposeProvider<GoRouter>.internal(
  router,
  name: r'routerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$routerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RouterRef = AutoDisposeProviderRef<GoRouter>;
String _$appStateNotifierHash() => r'b147ed5d2ab00c4090beebbc7a953ab7c8868c32';

/// App state notifier
///
/// Copied from [AppStateNotifier].
@ProviderFor(AppStateNotifier)
final appStateNotifierProvider =
    AutoDisposeNotifierProvider<AppStateNotifier, AppState>.internal(
  AppStateNotifier.new,
  name: r'appStateNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$appStateNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AppStateNotifier = AutoDisposeNotifier<AppState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
