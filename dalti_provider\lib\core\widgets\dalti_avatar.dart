import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../features/profile/providers/profile_provider.dart';
import '../theme/app_colors.dart';

class DaltiAvatar extends ConsumerWidget {
  final double radius;
  final double iconSize;

  const DaltiAvatar({super.key, this.radius = 30.0, this.iconSize = 36.0});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final profileAsync = ref.watch(profileProvider);

    return profileAsync.when(
      data: (profile) {
        final imageUrl = profile.profilePictureUrl;
        return CircleAvatar(
          radius: radius,
          backgroundColor: AppColors.primary.withOpacity(0.1),
          backgroundImage: imageUrl != null ? NetworkImage(imageUrl) : null,
          child:
              imageUrl == null
                  ? Icon(Icons.person, size: iconSize, color: AppColors.primary)
                  : null,
        );
      },
      loading:
          () => CircleAvatar(
            radius: radius,
            backgroundColor: AppColors.primary.withOpacity(0.1),
            child: Padding(
              padding: EdgeInsets.all(radius / 2),
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
          ),
      error:
          (error, stackTrace) => CircleAvatar(
            radius: radius,
            backgroundColor: AppColors.primary.withOpacity(0.1),
            child: Icon(Icons.error_outline, size: iconSize, color: Colors.red),
          ),
    );
  }
}
