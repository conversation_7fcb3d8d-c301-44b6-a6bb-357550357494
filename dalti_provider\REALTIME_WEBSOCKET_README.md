# Real-time WebSocket Messaging and Notifications

This document explains how to use the real-time WebSocket messaging and notifications system implemented in the Dalti Provider Flutter app.

## Overview

The real-time system provides:
- **Real-time messaging** with Socket.IO WebSocket connections
- **Live conversation updates** with automatic message synchronization
- **Real-time notifications** for appointments, messages, and system events
- **Queue status updates** for live queue management
- **Connection management** with automatic reconnection and error handling

## Architecture

### Core Components

1. **WebSocketService** (`lib/core/services/websocket_service.dart`)
   - Manages Socket.IO connection to the backend
   - <PERSON><PERSON> authentication and room management
   - Provides streams for different event types

2. **Realtime Providers** (`lib/core/providers/realtime_providers.dart`)
   - Riverpod providers for real-time data streams
   - Combines WebSocket events with API data
   - Manages state synchronization

3. **Message API Service** (`lib/features/messages/services/message_api_service.dart`)
   - REST API integration for messages and conversations
   - Handles initial data loading and message sending

## Setup and Initialization

### 1. Add Dependencies

The following dependency is already added to `pubspec.yaml`:

```yaml
dependencies:
  socket_io_client: ^2.0.3+1
```

### 2. Initialize Real-time Services

In your app startup (e.g., `main.dart` or app initialization):

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'lib/core/providers/realtime_providers.dart';

// Initialize real-time services
await ref.read(initializeRealtimeServicesProvider.future);
```

### 3. WebSocket Configuration

The WebSocket service connects to:
- **Development**: `wss://dapi-test.adscloud.org:8443/socket.io/?EIO=4&transport=websocket`
- **Production**: `wss://dapi-test.adscloud.org:8443/socket.io/?EIO=4&transport=websocket`

Authentication is handled automatically using JWT tokens from the auth service.

## Usage Examples

### Real-time Conversations

```dart
class ConversationsScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conversationsStream = ref.watch(realtimeConversationsProvider);
    
    return conversationsStream.when(
      data: (conversations) => ListView.builder(
        itemCount: conversations.length,
        itemBuilder: (context, index) {
          final conversation = conversations[index];
          return ListTile(
            title: Text(conversation.customerName),
            subtitle: Text(conversation.lastMessage?.content ?? ''),
            trailing: conversation.unreadCount > 0 
              ? CircleAvatar(child: Text('${conversation.unreadCount}'))
              : null,
          );
        },
      ),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}
```

### Real-time Messages for a Conversation

```dart
class ChatScreen extends ConsumerWidget {
  final String conversationId;
  
  const ChatScreen({required this.conversationId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Join the conversation room for real-time updates
    ref.read(conversationRoomManagerProvider.notifier).joinRoom(conversationId);
    
    final messagesStream = ref.watch(realtimeMessagesProvider(conversationId));
    
    return messagesStream.when(
      data: (messages) => ListView.builder(
        reverse: true,
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final message = messages[index];
          return MessageBubble(message: message);
        },
      ),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}
```

### Real-time Notifications

```dart
class NotificationsScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsStream = ref.watch(realtimeNotificationsProvider);
    final unreadCountStream = ref.watch(realtimeUnreadNotificationsCountProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'),
        actions: [
          unreadCountStream.when(
            data: (count) => count > 0 
              ? Badge(label: Text('$count'), child: Icon(Icons.notifications))
              : Icon(Icons.notifications),
            loading: () => Icon(Icons.notifications),
            error: (_, __) => Icon(Icons.notifications),
          ),
        ],
      ),
      body: notificationsStream.when(
        data: (notifications) => ListView.builder(
          itemCount: notifications.length,
          itemBuilder: (context, index) {
            final notification = notifications[index];
            return NotificationTile(notification: notification);
          },
        ),
        loading: () => CircularProgressIndicator(),
        error: (error, stack) => Text('Error: $error'),
      ),
    );
  }
}
```

### WebSocket Connection Status

```dart
class ConnectionStatusWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionState = ref.watch(webSocketConnectionStateProvider);
    
    return connectionState.when(
      data: (isConnected) => Row(
        children: [
          Icon(
            isConnected ? Icons.wifi : Icons.wifi_off,
            color: isConnected ? Colors.green : Colors.red,
          ),
          Text(isConnected ? 'Connected' : 'Disconnected'),
        ],
      ),
      loading: () => Text('Connecting...'),
      error: (error, stack) => Text('Connection Error'),
    );
  }
}
```

### Real-time Queue Updates

```dart
class QueueStatusScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allQueuesStream = ref.watch(realtimeAllQueuesProvider);
    
    return allQueuesStream.when(
      data: (queues) => ListView(
        children: queues.entries.map((entry) {
          final queueStatus = entry.value;
          return ListTile(
            title: Text(queueStatus.queueName),
            subtitle: Text('${queueStatus.waitingCount} waiting'),
            trailing: Text('${queueStatus.averageWaitTime.toInt()}min'),
          );
        }).toList(),
      ),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }
}
```

## Available Providers

### Core Providers

- `webSocketServiceProvider` - WebSocket service instance
- `webSocketConnectionStateProvider` - Connection status stream
- `initializeRealtimeServicesProvider` - Service initialization

### Messaging Providers

- `realtimeConversationsProvider` - Live conversations list
- `realtimeMessagesProvider(conversationId)` - Messages for specific conversation
- `realtimeUnreadMessagesCountProvider` - Total unread messages count

### Notification Providers

- `realtimeNotificationsProvider` - Live notifications list
- `realtimeNewNotificationProvider` - Stream of new notifications
- `realtimeUnreadNotificationsCountProvider` - Unread notifications count

### Queue Providers

- `realtimeAllQueuesProvider` - All queue statuses
- `realtimeQueueStatusProvider(queueId)` - Specific queue status

### Management Providers

- `conversationRoomManagerProvider` - Manage conversation room subscriptions
- `queueStatusManagerProvider` - Manage queue status requests

## Backend Events

The WebSocket service listens for these Socket.IO events:

- `newMessage` - New message received
- `conversationStarted` - New conversation created
- `messageRead` - Message marked as read
- `queueUpdate` - Queue status changed
- `notification` - New notification received

## Error Handling

The system includes comprehensive error handling:

- **Connection failures** - Automatic reconnection with exponential backoff
- **Authentication errors** - Token refresh and re-authentication
- **Network issues** - Graceful degradation to API-only mode
- **Data parsing errors** - Fallback to cached data

## Performance Considerations

- **Efficient updates** - Only affected data is refreshed
- **Memory management** - Automatic cleanup of unused streams
- **Connection pooling** - Single WebSocket connection for all features
- **Offline support** - Graceful handling of network disconnections

## Testing

See `lib/examples/realtime_integration_example.dart` for a complete working example of all real-time features.

## Troubleshooting

### Common Issues

1. **Connection not establishing**
   - Check JWT token validity
   - Verify network connectivity
   - Check server URL configuration

2. **Messages not updating in real-time**
   - Ensure conversation room is joined
   - Check WebSocket connection status
   - Verify user permissions

3. **High memory usage**
   - Dispose unused providers
   - Limit conversation history
   - Check for memory leaks in listeners

### Debug Logging

Enable debug logging in WebSocketService:

```dart
developer.log('[WebSocketService] Debug message');
```

All WebSocket events are logged with timestamps and detailed information.
