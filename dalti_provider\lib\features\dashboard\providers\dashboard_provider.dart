import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../models/dashboard_models.dart';
import '../repository/dashboard_repository.dart';
import '../repository/dashboard_repository_impl.dart';
import '../services/dashboard_api_service.dart';

part 'dashboard_provider.g.dart';

/// Provider for DashboardApiService
@riverpod
DashboardApiService dashboardApiService(DashboardApiServiceRef ref) {
  final httpClient = ref.watch(httpClientProvider);
  return DashboardApiService(httpClient);
}

/// Provider for DashboardRepository
@riverpod
DashboardRepository dashboardRepository(DashboardRepositoryRef ref) {
  final apiService = ref.watch(dashboardApiServiceProvider);
  return DashboardRepositoryImpl(apiService: apiService);
}

/// Dashboard state for managing dashboard data and loading states
class DashboardState {
  final DashboardData? data;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final DateTime? lastUpdated;

  const DashboardState({
    this.data,
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastUpdated,
  });

  DashboardState copyWith({
    DashboardData? data,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    DateTime? lastUpdated,
  }) {
    return DashboardState(
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get hasData => data != null;
  bool get hasError => error != null;
}

/// Dashboard provider for managing dashboard state and auto-refresh
@riverpod
class DashboardNotifier extends _$DashboardNotifier {
  Timer? _autoRefreshTimer;
  Timer? _quickStatsTimer;
  Timer? _scheduleTimer;

  @override
  DashboardState build() {
    // Initialize auto-refresh timers
    _setupAutoRefresh();
    
    // Load initial data
    _loadDashboardData();
    
    // Clean up timers when provider is disposed
    ref.onDispose(() {
      _autoRefreshTimer?.cancel();
      _quickStatsTimer?.cancel();
      _scheduleTimer?.cancel();
    });

    return const DashboardState(isLoading: true);
  }

  /// Load dashboard data
  Future<void> _loadDashboardData() async {
    try {
      print('[DashboardNotifier] Loading dashboard data...');
      
      final repository = ref.read(dashboardRepositoryProvider);
      
      // Check if we have cached data and don't need refresh
      final cachedData = repository.getCachedData();
      if (cachedData != null && !repository.needsRefresh()) {
        print('[DashboardNotifier] Using cached dashboard data');
        state = state.copyWith(
          data: cachedData,
          isLoading: false,
          error: null,
          lastUpdated: cachedData.lastUpdated,
        );
        return;
      }

      // Fetch fresh data
      final dashboardData = await repository.getDashboardData();
      
      state = state.copyWith(
        data: dashboardData,
        isLoading: false,
        error: null,
        lastUpdated: DateTime.now(),
      );
      
      print('[DashboardNotifier] Dashboard data loaded successfully');
    } catch (e) {
      print('[DashboardNotifier] Error loading dashboard data: $e');
      
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh dashboard data (pull-to-refresh)
  Future<void> refreshDashboardData() async {
    try {
      print('[DashboardNotifier] Refreshing dashboard data...');
      
      state = state.copyWith(isRefreshing: true, error: null);
      
      final repository = ref.read(dashboardRepositoryProvider);
      final dashboardData = await repository.refreshDashboardData();
      
      state = state.copyWith(
        data: dashboardData,
        isRefreshing: false,
        error: null,
        lastUpdated: DateTime.now(),
      );
      
      print('[DashboardNotifier] Dashboard data refreshed successfully');
    } catch (e) {
      print('[DashboardNotifier] Error refreshing dashboard data: $e');
      
      state = state.copyWith(
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// Update business metrics only
  Future<void> updateBusinessMetrics() async {
    try {
      print('[DashboardNotifier] Updating business metrics...');
      
      final repository = ref.read(dashboardRepositoryProvider);
      final businessMetrics = await repository.getBusinessOverview();
      
      if (state.data != null) {
        final updatedData = state.data!.copyWith(
          businessMetrics: businessMetrics,
          lastUpdated: DateTime.now(),
        );
        
        state = state.copyWith(
          data: updatedData,
          lastUpdated: DateTime.now(),
        );
        
        print('[DashboardNotifier] Business metrics updated successfully');
      }
    } catch (e) {
      print('[DashboardNotifier] Error updating business metrics: $e');
    }
  }

  /// Update schedule data only
  Future<void> updateScheduleData() async {
    try {
      print('[DashboardNotifier] Updating schedule data...');
      
      final repository = ref.read(dashboardRepositoryProvider);
      final scheduleData = await repository.getTodaySchedule();
      
      if (state.data != null) {
        final updatedData = state.data!.copyWith(
          scheduleData: scheduleData,
          lastUpdated: DateTime.now(),
        );
        
        state = state.copyWith(
          data: updatedData,
          lastUpdated: DateTime.now(),
        );
        
        print('[DashboardNotifier] Schedule data updated successfully');
      }
    } catch (e) {
      print('[DashboardNotifier] Error updating schedule data: $e');
    }
  }

  /// Update quick stats only
  Future<void> updateQuickStats() async {
    try {
      print('[DashboardNotifier] Updating quick stats...');
      
      final repository = ref.read(dashboardRepositoryProvider);
      final quickStats = await repository.getQuickStats();
      
      if (state.data != null) {
        final updatedData = state.data!.copyWith(
          quickStats: quickStats,
          lastUpdated: DateTime.now(),
        );
        
        state = state.copyWith(
          data: updatedData,
          lastUpdated: DateTime.now(),
        );
        
        print('[DashboardNotifier] Quick stats updated successfully');
      }
    } catch (e) {
      print('[DashboardNotifier] Error updating quick stats: $e');
    }
  }

  /// Update notifications
  Future<void> updateNotifications() async {
    try {
      print('[DashboardNotifier] Updating notifications...');
      
      final repository = ref.read(dashboardRepositoryProvider);
      final notifications = await repository.getNotifications(limit: 5);
      
      if (state.data != null) {
        final updatedData = state.data!.copyWith(
          notifications: notifications,
          unreadNotificationCount: notifications.where((n) => !n.isRead).length,
          lastUpdated: DateTime.now(),
        );
        
        state = state.copyWith(
          data: updatedData,
          lastUpdated: DateTime.now(),
        );
        
        print('[DashboardNotifier] Notifications updated successfully');
      }
    } catch (e) {
      print('[DashboardNotifier] Error updating notifications: $e');
    }
  }

  /// Mark notification as read
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      print('[DashboardNotifier] Marking notification as read: $notificationId');
      
      final repository = ref.read(dashboardRepositoryProvider);
      await repository.markNotificationAsRead(notificationId);
      
      // Update local state immediately for better UX
      if (state.data != null) {
        final updatedNotifications = state.data!.notifications.map((notification) {
          if (notification.id == notificationId) {
            return notification.copyWith(isRead: true);
          }
          return notification;
        }).toList();
        
        final updatedData = state.data!.copyWith(
          notifications: updatedNotifications,
          unreadNotificationCount: updatedNotifications.where((n) => !n.isRead).length,
        );
        
        state = state.copyWith(data: updatedData);
      }
      
      print('[DashboardNotifier] Notification marked as read successfully');
    } catch (e) {
      print('[DashboardNotifier] Error marking notification as read: $e');
    }
  }

  /// Emergency queue control
  Future<EmergencyQueueControlResponse?> emergencyQueueControl(
    EmergencyQueueControl request,
  ) async {
    try {
      print('[DashboardNotifier] Executing emergency queue control: ${request.action}');
      
      final repository = ref.read(dashboardRepositoryProvider);
      final response = await repository.emergencyQueueControl(request);
      
      // Refresh schedule data after emergency control
      await updateScheduleData();
      
      print('[DashboardNotifier] Emergency queue control executed successfully');
      return response;
    } catch (e) {
      print('[DashboardNotifier] Error executing emergency queue control: $e');
      return null;
    }
  }

  /// Setup auto-refresh timers with different intervals
  void _setupAutoRefresh() {
    print('[DashboardNotifier] Setting up auto-refresh timers...');
    
    // Business metrics: Every 5 minutes
    _autoRefreshTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => updateBusinessMetrics(),
    );
    
    // Schedule data: Every 30 seconds
    _scheduleTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => updateScheduleData(),
    );
    
    // Quick stats and notifications: Every 10 seconds
    _quickStatsTimer = Timer.periodic(
      const Duration(seconds: 10),
      (_) {
        updateQuickStats();
        updateNotifications();
      },
    );
    
    print('[DashboardNotifier] Auto-refresh timers set up successfully');
  }

  /// Pause auto-refresh (when app goes to background)
  void pauseAutoRefresh() {
    print('[DashboardNotifier] Pausing auto-refresh...');
    _autoRefreshTimer?.cancel();
    _quickStatsTimer?.cancel();
    _scheduleTimer?.cancel();
  }

  /// Resume auto-refresh (when app comes to foreground)
  void resumeAutoRefresh() {
    print('[DashboardNotifier] Resuming auto-refresh...');
    _setupAutoRefresh();

    // Refresh data immediately when resuming
    _loadDashboardData();
  }

  /// Start auto-refresh manually
  void startAutoRefresh() {
    print('[DashboardNotifier] Starting auto-refresh manually...');
    _setupAutoRefresh();
  }

  /// Stop auto-refresh manually
  void stopAutoRefresh() {
    print('[DashboardNotifier] Stopping auto-refresh manually...');
    pauseAutoRefresh();
  }
}
