import 'package:flutter/material.dart';
import '../../../../core/theme/theme_provider.dart';

/// Dialog to confirm skipping the onboarding process
class SkipOnboardingDialog extends StatelessWidget {
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const SkipOnboardingDialog({
    super.key,
    this.onConfirm,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.skip_next,
            color: context.colors.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            'Skip Setup?',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You can skip the setup process and complete it later. However, some features may be limited until you finish setting up your business.',
            style: context.textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'You can complete setup later by:',
                  style: context.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 8),
                _buildSetupOption(
                  context,
                  '• Going to Settings > Business Setup',
                ),
                _buildSetupOption(
                  context,
                  '• Using the setup reminder in your dashboard',
                ),
                _buildSetupOption(
                  context,
                  '• Clicking "Complete Setup" when prompted',
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: onCancel ?? () => Navigator.of(context).pop(false),
          child: Text(
            'Continue Setup',
            style: TextStyle(
              color: context.colors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: onConfirm ?? () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: context.colors.surfaceVariant,
            foregroundColor: context.colors.onSurfaceVariant,
          ),
          child: const Text('Skip for Now'),
        ),
      ],
    );
  }

  Widget _buildSetupOption(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        text,
        style: context.textTheme.bodySmall?.copyWith(
          color: context.colors.onSurfaceVariant,
        ),
      ),
    );
  }

  /// Show the skip onboarding dialog
  static Future<bool?> show(BuildContext context) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const SkipOnboardingDialog(),
    );
  }
}

/// Simple skip confirmation dialog for quick use
class SimpleSkipDialog extends StatelessWidget {
  const SimpleSkipDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Skip Setup?'),
      content: const Text(
        'Are you sure you want to skip the business setup? You can complete it later from your dashboard.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('Continue Setup'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: const Text('Skip'),
        ),
      ],
    );
  }

  /// Show the simple skip dialog
  static Future<bool?> show(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => const SimpleSkipDialog(),
    );
  }
}
