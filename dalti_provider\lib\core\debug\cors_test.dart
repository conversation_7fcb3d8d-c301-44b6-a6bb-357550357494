import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import '../config/app_config.dart';

/// CORS testing utility for web debugging
class CorsTest {
  /// Test basic connectivity to the API server
  static Future<void> testBasicConnectivity() async {
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    print('[] ');
    
    final dio = Dio();
    final baseUrl = AppConfig.apiBaseUrl;
    
    try {
      print('[] ');
      
      final response = await dio.get('$baseUrl/health');
      
      print('[] ');
      print('[] ');
      print('[] ');
      
    } catch (e) {
      print('[] ');
      print('[] ');
      
      if (e.toString().contains('CORS')) {
        print('[] ');
        _printCorsHelp();
      } else if (e.toString().contains('CONNECTION_REFUSED')) {
        print('[] ');
      } else {
        print('[] ');
      }
    }
    
    print('[] ');
  }
  
  /// Test login endpoint specifically
  static Future<void> testLoginEndpoint() async {
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    print('[] ');
    
    final dio = Dio();
    final baseUrl = AppConfig.apiBaseUrl;
    
    try {
      print('[] ');
      
      // Test with dummy credentials to see if we get a proper response
      final response = await dio.post(
        '$baseUrl/api/auth/provider/login',
        data: {
          'identifier': '<EMAIL>',
          'password': 'dummy-password',
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      
      print('[] ');
      print('[] ');
      print('[] ');
      
    } catch (e) {
      print('[] ');
      print('[] ');
      
      if (e.toString().contains('CORS')) {
        print('[] ');
        _printCorsHelp();
      } else if (e.toString().contains('401') || e.toString().contains('400')) {
        print('[] ');
        print('[] ');
      } else {
        print('[] ');
      }
    }
    
    print('[] ');
  }
  
  /// Test protected endpoint (locations)
  static Future<void> testProtectedEndpoint() async {
    if (!kIsWeb) {
      print('[] ');
      return;
    }
    
    print('[] ');
    
    final dio = Dio();
    final baseUrl = AppConfig.apiBaseUrl;
    
    try {
      print('[] ');
      
      // Test without auth header first
      final response = await dio.post(
        '$baseUrl/api/auth/providers/locations',
        data: {
          'name': 'Test Location',
          'address': 'Test Address',
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );
      
      print('[] ');
      
    } catch (e) {
      print('[] ');
      print('[] ');
      
      if (e.toString().contains('CORS')) {
        print('[] ');
        _printCorsHelp();
      } else if (e.toString().contains('401')) {
        print('[] ');
        print('[] ');
      } else {
        print('[] ');
      }
    }
    
    print('[] ');
  }
  
  /// Run all CORS tests
  static Future<void> runAllTests() async {
    print('[] ');
    
    await testBasicConnectivity();
    await Future.delayed(const Duration(seconds: 1));
    
    await testLoginEndpoint();
    await Future.delayed(const Duration(seconds: 1));
    
    await testProtectedEndpoint();
    
    print('[] ');
    print('[] ');
  }
  
  /// Print CORS help information
  static void _printCorsHelp() {
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
  }
  
  /// Print browser debugging instructions
  static void printBrowserDebuggingInstructions() {
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
    print('[] ');
  }
}
