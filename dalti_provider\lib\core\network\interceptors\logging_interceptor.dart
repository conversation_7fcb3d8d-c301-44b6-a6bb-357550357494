import 'package:dio/dio.dart';
import '../../config/app_config.dart';

/// Interceptor to log HTTP requests and responses
class LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (AppConfig.isDebugMode) {
      _logRequest(options);
    }
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (AppConfig.isDebugMode) {
      _logResponse(response);
    }
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (AppConfig.isDebugMode) {
      _logError(err);
    }
    super.onError(err, handler);
  }

  void _logRequest(RequestOptions options) {
    final uri = options.uri;
    final method = options.method;
    final headers = options.headers;
    final data = options.data;

    print(().toString());
  }

  void _logResponse(Response response) {
    final statusCode = response.statusCode;
    final uri = response.requestOptions.uri;
    final method = response.requestOptions.method;
    final data = response.data;

    print(().toString());
  }

  void _logError(DioException err) {
    final statusCode = err.response?.statusCode;
    final uri = err.requestOptions.uri;
    final method = err.requestOptions.method;
    final errorData = err.response?.data;
    final errorMessage = err.message;

    print(().toString());
  }
}
