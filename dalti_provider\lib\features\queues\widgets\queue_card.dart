import 'package:flutter/material.dart';
import '../models/queue_models.dart';
import '../../locations/providers/location_provider.dart';
import '../../locations/models/location_models.dart';
import '../../services/models/service_models.dart';
import '../../services/providers/service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class QueueCard extends ConsumerWidget {
  final Queue queue;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onToggleStatus;
  final bool showLocation;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? margin;

  const QueueCard({
    Key? key,
    required this.queue,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
    required this.onToggleStatus,
    this.showLocation = true,
    this.backgroundColor,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get location
    final locationState = ref.watch(locationNotifierProvider);
    final location = locationState.locations.firstWhere(
      (loc) => loc.id == queue.locationId,
      orElse:
          () => const Location(
            id: 0,
            name: 'Unknown',
            address: '',
            city: '',
            isMobileHidden: false,
            parking: false,
            elevator: false,
            handicapAccess: false,
          ),
    );

    return Card(
      margin: margin,
      elevation: 1,
      color:
          backgroundColor ??
          (theme.brightness == Brightness.light
              ? Colors
                  .white // Pure white in light mode
              : theme.cardColor),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color:
              theme.brightness == Brightness.light
                  ? Colors.grey.shade200
                  : Colors.transparent,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and menu
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status indicator and title
                  Container(
                    width: 4,
                    height:
                        showLocation
                            ? 32
                            : 24, // Adjust height based on whether location is shown
                    decoration: BoxDecoration(
                      color:
                          queue.isActive
                              ? colorScheme.primary
                              : Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          queue.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (showLocation && location.id != 0) ...[
                          const SizedBox(height: 2),
                          Text(
                            location.name,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurface.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Menu
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      color: colorScheme.onSurface.withOpacity(0.7),
                      size: 20,
                    ),
                    itemBuilder:
                        (context) => [
                          PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.edit,
                                  size: 18,
                                  color: colorScheme.onSurface,
                                ),
                                const SizedBox(width: 8),
                                const Text('Edit'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'toggle',
                            child: Row(
                              children: [
                                Icon(
                                  queue.isActive
                                      ? Icons.pause_circle
                                      : Icons.play_circle,
                                  size: 18,
                                  color: colorScheme.onSurface,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  queue.isActive ? 'Deactivate' : 'Activate',
                                ),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.delete,
                                  size: 18,
                                  color: colorScheme.error,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Delete',
                                  style: TextStyle(color: colorScheme.error),
                                ),
                              ],
                            ),
                          ),
                        ],
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit();
                          break;
                        case 'toggle':
                          onToggleStatus();
                          break;
                        case 'delete':
                          onDelete();
                          break;
                      }
                    },
                  ),
                ],
              ),

              // Service names as chips
              if (queue.services.isNotEmpty) ...[
                SizedBox(
                  height: showLocation ? 8 : 4,
                ), // Reduce spacing when location is not shown
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    ...queue.services
                        .where((qs) => qs.service != null)
                        .map(
                          (service) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              service.service!.title,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onPrimaryContainer,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class CompactQueueCard extends StatelessWidget {
  final Queue queue;
  final VoidCallback? onTap;

  const CompactQueueCard({super.key, required this.queue, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Material(
        color:
            queue.isActive
                ? Theme.of(context).primaryColor.withOpacity(0.1)
                : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    queue.isActive
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade300,
                width: queue.isActive ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  queue.title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color:
                        queue.isActive ? Theme.of(context).primaryColor : null,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${queue.serviceCount} service${queue.serviceCount != 1 ? 's' : ''}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                    fontSize: 10,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: queue.isActive ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      queue.statusText,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            queue.isActive
                                ? Colors.green.shade700
                                : Colors.grey.shade700,
                        fontSize: 9,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
