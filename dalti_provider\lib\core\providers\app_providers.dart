import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../storage/storage_service.dart';
import '../network/http_client.dart';
import '../network/api_service.dart';
import '../network/interceptors/auth_interceptor.dart';
import '../auth/jwt_service.dart';
import '../services/notification_api_service.dart';
import '../debug/storage_debug.dart';
import '../debug/web_debug.dart';
import '../debug/session_debug.dart';
import '../routing/app_routes.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/otp_verification_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/verify_reset_otp_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart';
import '../../features/onboarding/screens/onboarding_wizard_screen.dart';
import '../../features/onboarding/providers/onboarding_provider.dart';
import '../../features/onboarding/services/onboarding_api_service.dart';
import '../../features/onboarding/widgets/onboarding_guard.dart';
import '../../features/auth/services/category_service.dart';

import '../../features/profile/screens/profile_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/qr_scanner/screens/qr_scanner_screen.dart';
import '../../features/queues/screens/queue_form_screen.dart';
import '../../features/profile/services/profile_api_service.dart';

// Export onboarding providers for use in other parts of the app
export '../../features/onboarding/providers/onboarding_provider.dart';
import '../../features/services/services/service_api_service.dart';
import '../../features/services/repository/service_repository.dart';
import '../../features/services/repository/service_repository_impl.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../core/widgets/main_layout.dart';
import '../../features/locations/screens/locations_screen.dart';
import '../../features/locations/screens/add_location_screen.dart';
import '../../features/locations/screens/edit_location_screen.dart';
import '../../features/locations/screens/location_details_screen.dart';
import '../../features/services/screens/services_screen.dart';
import '../../features/services/screens/create_service_screen.dart';
import '../../features/services/screens/edit_service_screen.dart';
import '../../features/services/screens/service_details_screen.dart';
import '../../features/schedules/screens/schedules_screen.dart';
import '../../features/schedules/screens/schedule_form_screen.dart';
import '../../features/queues/screens/queues_screen.dart';
import '../../features/customers/screens/customer_directory_screen.dart';
import '../../features/customers/screens/new_customer_screen.dart';
import '../../features/customers/screens/customer_profile_screen.dart';
import '../../features/customers/screens/edit_customer_screen.dart';
import '../../features/messages/screens/messages_screen.dart';
import '../../features/messages/screens/conversation_screen.dart';
import '../../features/messages/screens/new_conversation_screen.dart';
import '../../features/notifications/screens/notifications_screen.dart';
import '../../features/appointments/screens/appointments_screen.dart';
import '../../features/appointments/screens/add_appointment_screen.dart';
import '../../features/appointments/screens/edit_appointment_screen.dart';
import '../../features/appointments/screens/appointment_details_screen.dart';
import '../../features/appointments/models/appointment_models.dart'
    as appointment_models;
import '../../features/service_session/screens/service_screen.dart';

part 'app_providers.g.dart';

// Import the SplashScreen from main.dart
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.business, size: 80, color: Colors.white),
            SizedBox(height: 24),
            Text(
              'Dalti Provider',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Business Management Made Simple',
              style: TextStyle(fontSize: 16, color: Colors.white70),
            ),
            SizedBox(height: 48),
            CircularProgressIndicator(color: Colors.white),
          ],
        ),
      ),
    );
  }
}

/// App initialization state
enum AppInitState { loading, authenticated, unauthenticated, error }

/// App state model
class AppState {
  final AppInitState initState;
  final String? error;
  final bool isDarkMode;
  final String? currentUserId;

  const AppState({
    required this.initState,
    this.error,
    this.isDarkMode = false,
    this.currentUserId,
  });

  AppState copyWith({
    AppInitState? initState,
    String? error,
    bool? isDarkMode,
    String? currentUserId,
  }) {
    return AppState(
      initState: initState ?? this.initState,
      error: error ?? this.error,
      isDarkMode: isDarkMode ?? this.isDarkMode,
      currentUserId: currentUserId ?? this.currentUserId,
    );
  }
}

/// App state notifier
@riverpod
class AppStateNotifier extends _$AppStateNotifier {
  @override
  AppState build() {
    // Start with loading state and initialize asynchronously
    _initializeAuth();

    final isDarkMode =
        StorageService.getSetting<bool>('dark_mode', defaultValue: false) ??
        false;

    return AppState(initState: AppInitState.loading, isDarkMode: isDarkMode);
  }

  /// Initialize authentication state asynchronously
  Future<void> _initializeAuth() async {
    try {
      print('[] ');

      // Print web-specific debug info
      WebDebug.printWebInfo();
      WebDebug.checkWebIssues();

      // Check storage state first
      StorageDebug.printAllAuthData();
      SessionDebug.printSessionState();
      SessionDebug.printBrowserStorageInfo();

      // Wait for JWT service to be fully initialized
      final jwtService = await ref.read(initializedJwtServiceProvider.future);

      print('[AppStateNotifier] JWT service initialized');
      StorageDebug.compareJwtServiceAndStorage(jwtService);
      SessionDebug.compareJwtServiceWithStorage(jwtService);

      if (jwtService.isAuthenticated) {
        // User is authenticated
        print('[AppStateNotifier] User is authenticated');
        state = state.copyWith(
          initState: AppInitState.authenticated,
          currentUserId: jwtService.currentUserId,
        );
      } else {
        // User is not authenticated
        print('[AppStateNotifier] User is not authenticated');
        state = state.copyWith(initState: AppInitState.unauthenticated);
      }
    } catch (e) {
      print('[AppStateNotifier] Error during initialization: $e');
      state = state.copyWith(
        initState: AppInitState.error,
        error: e.toString(),
      );
    }
  }

  /// Set authentication state with JWT token
  Future<void> setAuthenticated(Map<String, dynamic> tokenResponse) async {
    try {
      final jwtService = ref.read(jwtServiceProvider);
      await jwtService.storeTokenFromResponse(tokenResponse);

      final userId = jwtService.currentUserId;

      state = state.copyWith(
        initState: AppInitState.authenticated,
        currentUserId: userId,
      );
    } catch (e) {
      state = state.copyWith(
        initState: AppInitState.error,
        error: 'Failed to set authentication: $e',
      );
    }
  }

  /// Set authentication state with simple token (for demo/testing)
  void setAuthenticatedSimple(String userId, String token) {
    StorageService.saveAuth('user_id', userId);
    StorageService.saveAuth('access_token', token);

    state = state.copyWith(
      initState: AppInitState.authenticated,
      currentUserId: userId,
    );
  }

  /// Set unauthenticated state
  Future<void> setUnauthenticated() async {
    try {
      final jwtService = ref.read(jwtServiceProvider);
      await jwtService.clearToken();
    } catch (e) {
      // Fallback to clearing storage directly
      StorageService.clearAuth();
    }

    state = state.copyWith(
      initState: AppInitState.unauthenticated,
      currentUserId: null,
    );
  }

  /// Toggle dark mode
  void toggleDarkMode() {
    final newDarkMode = !state.isDarkMode;
    StorageService.saveSetting('dark_mode', newDarkMode);

    state = state.copyWith(isDarkMode: newDarkMode);
  }

  /// Set error state
  void setError(String error) {
    state = state.copyWith(initState: AppInitState.error, error: error);
  }
}

/// Storage service provider
@riverpod
StorageService storageService(Ref ref) {
  return StorageService();
}

/// Current user ID provider
@riverpod
String? currentUserId(Ref ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.currentUserId;
}

/// Is authenticated provider
@riverpod
bool isAuthenticated(Ref ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.initState == AppInitState.authenticated;
}

/// Is dark mode provider
@riverpod
bool isDarkMode(Ref ref) {
  final appState = ref.watch(appStateNotifierProvider);
  return appState.isDarkMode;
}

/// HTTP client provider (without auth interceptor to avoid circular dependency)
@riverpod
HttpClient httpClientRaw(Ref ref) {
  return HttpClient();
}

/// JWT service provider (singleton)
@Riverpod(keepAlive: true)
JwtService jwtService(Ref ref) {
  final httpClient = ref.watch(httpClientRawProvider);
  return JwtService(httpClient);
}

/// Initialized JWT service provider
@Riverpod(keepAlive: true)
Future<JwtService> initializedJwtService(Ref ref) async {
  final jwtService = ref.watch(jwtServiceProvider);
  await jwtService.initialize();
  return jwtService;
}

/// HTTP client provider with proper auth interceptor
@riverpod
HttpClient httpClient(Ref ref) {
  final jwtService = ref.watch(jwtServiceProvider);
  final authInterceptor = AuthInterceptor(jwtService);
  return HttpClient(authInterceptor: authInterceptor);
}

/// Auth API service provider
@riverpod
AuthApiService authApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  final jwtService = ref.watch(jwtServiceProvider);
  return AuthApiService(httpClient, jwtService);
}

/// Test API service provider
@riverpod
TestApiService testApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return TestApiService(httpClient);
}

/// Notification API service provider
@riverpod
NotificationApiService notificationApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return NotificationApiService(httpClient);
}

/// Category service provider
@riverpod
CategoryService categoryService(Ref ref) {
  final authApiService = ref.watch(authApiServiceProvider);
  return CategoryService(authApiService);
}

/// Service API service provider
@riverpod
ServiceApiService serviceApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return ServiceApiService(httpClient);
}

/// Service repository provider
@riverpod
ServiceRepository serviceRepository(Ref ref) {
  final apiService = ref.watch(serviceApiServiceProvider);
  return ServiceRepositoryImpl(apiService: apiService);
}

/// Profile API service provider
@riverpod
ProfileApiService profileApiService(Ref ref) {
  final httpClient = ref.watch(httpClientProvider);
  return ProfileApiService(httpClient);
}

/// Router provider with state-based routing
@riverpod
GoRouter router(Ref ref) {
  final appState = ref.watch(appStateNotifierProvider);

  return GoRouter(
    initialLocation: AppRoutes.splash,
    redirect: (context, state) async {
      final isAuthenticated = appState.initState == AppInitState.authenticated;
      final isLoading = appState.initState == AppInitState.loading;
      final currentPath = state.uri.path;

      // If still loading, stay on splash
      if (isLoading && currentPath != AppRoutes.splash) {
        return AppRoutes.splash;
      }

      // If not loading anymore and on splash, redirect based on auth state
      if (!isLoading && currentPath == AppRoutes.splash) {
        if (isAuthenticated) {
          // Check if user needs onboarding
          try {
            final container = ProviderScope.containerOf(context);
            final needsOnboarding = await container.read(
              needsOnboardingProvider.future,
            );
            return needsOnboarding ? AppRoutes.onboarding : AppRoutes.dashboard;
          } catch (e) {
            print('[Router] Error checking onboarding status: $e');
            return AppRoutes.dashboard; // Fallback to dashboard
          }
        } else {
          return AppRoutes.login;
        }
      }

      // If authenticated and trying to access auth pages, redirect appropriately
      if (isAuthenticated && _isAuthRoute(currentPath)) {
        try {
          final container = ProviderScope.containerOf(context);
          final needsOnboarding = await container.read(
            needsOnboardingProvider.future,
          );
          return needsOnboarding ? AppRoutes.onboarding : AppRoutes.dashboard;
        } catch (e) {
          print('[Router] Error checking onboarding status: $e');
          return AppRoutes.dashboard; // Fallback to dashboard
        }
      }

      // If authenticated and trying to access main app but needs onboarding
      if (isAuthenticated &&
          AppRoutes.requiresOnboardingCompleted(currentPath) &&
          currentPath != AppRoutes.onboarding) {
        try {
          final container = ProviderScope.containerOf(context);
          final needsOnboarding = await container.read(
            needsOnboardingProvider.future,
          );
          if (needsOnboarding) {
            return AppRoutes.onboarding;
          }
        } catch (e) {
          print(
            '[Router] Error checking onboarding status for protected route: $e',
          );
          // Continue to allow access if check fails
        }
      }

      // If not authenticated and trying to access protected pages, redirect to login
      if (!isAuthenticated && AppRoutes.requiresAuth(currentPath)) {
        return AppRoutes.login;
      }

      // No redirect needed
      return null;
    },
    routes: [
      // Splash route
      GoRoute(
        path: AppRoutes.splash,
        builder: (context, state) => const SplashScreen(),
      ),

      // Auth routes
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.register,
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: AppRoutes.otpVerification,
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          final registrationData =
              state.uri.queryParameters['registrationData'];
          return OtpVerificationScreen(
            email: email,
            registrationData: registrationData,
          );
        },
      ),

      // Password reset routes
      GoRoute(
        path: AppRoutes.forgotPassword,
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: AppRoutes.verifyResetOtp,
        builder: (context, state) {
          final email = state.uri.queryParameters['email'] ?? '';
          return VerifyResetOtpScreen(email: email);
        },
      ),
      GoRoute(
        path: AppRoutes.resetPassword,
        builder: (context, state) {
          final resetToken = state.uri.queryParameters['resetToken'] ?? '';
          return ResetPasswordScreen(resetToken: resetToken);
        },
      ),

      // Onboarding route
      GoRoute(
        path: AppRoutes.onboarding,
        builder: (context, state) => const OnboardingWizardScreen(),
      ),

      // Main app routes
      GoRoute(
        path: AppRoutes.dashboard,
        builder:
            (context, state) => const OnboardingGuard(
              child: MainLayout(
                currentPath: AppRoutes.dashboard,
                child: DashboardScreen(),
              ),
            ),
      ),
      GoRoute(
        path: AppRoutes.profile,
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: AppRoutes.settings,
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: AppRoutes.qrScanner,
        builder: (context, state) => const QRScannerScreen(),
      ),
      GoRoute(
        path: '/service/:appointmentId',
        builder: (context, state) {
          final appointmentId = state.pathParameters['appointmentId'] ?? '';
          return ServiceScreen(appointmentId: appointmentId);
        },
      ),

      // Business management routes
      GoRoute(
        path: AppRoutes.locations,
        builder: (context, state) => const LocationsScreen(),
      ),
      GoRoute(
        path: AppRoutes.addLocation,
        builder: (context, state) => const AddLocationScreen(),
      ),
      GoRoute(
        path: AppRoutes.editLocation,
        builder: (context, state) {
          final locationId = state.pathParameters['id'] ?? '';
          return EditLocationScreen(locationId: locationId);
        },
      ),
      GoRoute(
        path: AppRoutes.locationDetails,
        builder: (context, state) {
          final locationId = state.pathParameters['id'] ?? '';
          return LocationDetailsScreen(locationId: locationId);
        },
      ),
      GoRoute(
        path: AppRoutes.services,
        builder: (context, state) => const ServicesScreen(),
      ),
      GoRoute(
        path: '/services/create',
        builder: (context, state) => const CreateServiceScreen(),
      ),
      GoRoute(
        path: '/services/:id',
        builder: (context, state) {
          final serviceId = state.pathParameters['id'] ?? '';
          return ServiceDetailsScreen(serviceId: serviceId);
        },
      ),
      GoRoute(
        path: '/services/:id/edit',
        builder: (context, state) {
          final serviceId = state.pathParameters['id'] ?? '';
          return EditServiceScreen(serviceId: serviceId);
        },
      ),
      GoRoute(
        path: AppRoutes.queues,
        builder: (context, state) => const QueuesScreen(),
      ),
      GoRoute(
        path: AppRoutes.addQueue,
        builder: (context, state) {
          final locationId = int.tryParse(
            state.uri.queryParameters['locationId'] ?? '',
          );
          return QueueFormScreen(initialLocationId: locationId);
        },
      ),
      GoRoute(
        path: AppRoutes.editQueue,
        builder: (context, state) {
          final queueId = int.tryParse(state.pathParameters['id'] ?? '');
          return QueueFormScreen(queueId: queueId);
        },
      ),
      GoRoute(
        path: AppRoutes.schedules,
        builder: (context, state) => const SchedulesScreen(),
      ),
      GoRoute(
        path: AppRoutes.addSchedule,
        builder: (context, state) => const ScheduleFormScreen(),
      ),
      GoRoute(
        path: AppRoutes.editSchedule,
        builder: (context, state) {
          final scheduleId = int.tryParse(state.pathParameters['id'] ?? '');
          return ScheduleFormScreen(scheduleId: scheduleId);
        },
      ),
      GoRoute(
        path: AppRoutes.customers,
        builder:
            (context, state) => const OnboardingGuard(
              child: MainLayout(
                currentPath: AppRoutes.customers,
                child: CustomerDirectoryScreen(),
              ),
            ),
      ),
      GoRoute(
        path: '/customers/new',
        builder:
            (context, state) =>
                const OnboardingGuard(child: NewCustomerScreen()),
      ),
      GoRoute(
        path: '/customers/:id',
        builder: (context, state) {
          final customerId = state.pathParameters['id'] ?? '';
          return OnboardingGuard(
            child: CustomerProfileScreen(customerId: customerId),
          );
        },
      ),
      GoRoute(
        path: '/customers/:id/edit',
        builder: (context, state) {
          final customerId = state.pathParameters['id'] ?? '';
          return OnboardingGuard(
            child: EditCustomerScreen(customerId: customerId),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.appointments,
        builder:
            (context, state) => const MainLayout(
              currentPath: AppRoutes.appointments,
              child: AppointmentsScreen(),
            ),
      ),
      GoRoute(
        path: AppRoutes.addAppointment,
        builder: (context, state) => const AddAppointmentScreen(),
      ),
      GoRoute(
        path: AppRoutes.editAppointment,
        builder: (context, state) {
          final appointmentId = state.pathParameters['id'] ?? '';
          final appointment = state.extra as appointment_models.Appointment?;
          if (appointment != null) {
            return EditAppointmentScreen(appointment: appointment);
          } else {
            // Handle error case - redirect or show error
            return const AppointmentsScreen();
          }
        },
      ),
      GoRoute(
        path: '/appointments/:id/details',
        builder: (context, state) {
          final appointmentId = state.pathParameters['id'] ?? '';
          final appointment = state.extra as appointment_models.Appointment?;
          if (appointment != null) {
            return AppointmentDetailsScreen(appointment: appointment);
          } else {
            // Handle error case - redirect or show error
            return const AppointmentsScreen();
          }
        },
      ),
      GoRoute(
        path: AppRoutes.messages,
        builder:
            (context, state) => const OnboardingGuard(
              child: MainLayout(
                currentPath: AppRoutes.messages,
                child: MessagesScreen(),
              ),
            ),
      ),
      GoRoute(
        path: AppRoutes.newConversation,
        builder:
            (context, state) =>
                const OnboardingGuard(child: NewConversationScreen()),
      ),
      GoRoute(
        path: AppRoutes.conversation,
        builder: (context, state) {
          final conversationId = state.pathParameters['conversationId'] ?? '';
          return OnboardingGuard(
            child: ConversationScreen(conversationId: conversationId),
          );
        },
      ),
      GoRoute(
        path: AppRoutes.notifications,
        builder:
            (context, state) => const OnboardingGuard(
              child: MainLayout(
                currentPath: AppRoutes.notifications,
                child: NotificationsScreen(),
              ),
            ),
      ),
    ],
  );
}

/// Helper function to check if route is an auth route
bool _isAuthRoute(String path) {
  const authRoutes = [
    AppRoutes.login,
    AppRoutes.register,
    AppRoutes.otpVerification,
  ];
  return authRoutes.contains(path);
}
