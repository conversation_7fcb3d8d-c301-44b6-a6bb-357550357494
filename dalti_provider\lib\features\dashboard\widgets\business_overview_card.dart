import 'package:flutter/material.dart';
import '../models/dashboard_models.dart';

/// Business overview card showing key metrics and performance indicators
class BusinessOverviewCard extends StatelessWidget {
  final BusinessMetrics? businessMetrics;
  final bool isLoading;

  const BusinessOverviewCard({
    super.key,
    this.businessMetrics,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return RepaintBoundary(
      child: Seman<PERSON>(
        label: 'Business Overview Card',
        child: Card(
          child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.business_center,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Business Overview',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Metrics Grid
            if (businessMetrics != null)
              _buildMetricsGrid(context, businessMetrics!)
            else
              _buildLoadingState(context),
          ],
        ),
      ),
        ),
      ),
    );
  }

  /// Build metrics grid with 2x2 layout
  Widget _buildMetricsGrid(BuildContext context, BusinessMetrics metrics) {
    return Column(
      children: [
        // Top row
        Row(
          children: [
            Expanded(
              child: _MetricItem(
                icon: Icons.attach_money,
                label: 'Today\'s Revenue',
                value: '\$${metrics.todayRevenue.toStringAsFixed(0)}',
                trend: metrics.revenueChange,
                color: Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _MetricItem(
                icon: Icons.event,
                label: 'Appointments',
                value: '${metrics.todayAppointments}',
                subtitle: '${metrics.completedAppointments} completed',
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Bottom row
        Row(
          children: [
            Expanded(
              child: _MetricItem(
                icon: Icons.queue,
                label: 'Active Queues',
                value: '${metrics.activeQueues}',
                subtitle: '${metrics.totalCustomersToday} customers',
                color: Colors.orange,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _MetricItem(
                icon: Icons.star,
                label: 'Satisfaction',
                value: '${metrics.customerSatisfaction.toStringAsFixed(1)}',
                subtitle: 'Average rating',
                color: Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build loading state with skeleton items
  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildSkeletonItem(theme)),
            const SizedBox(width: 12),
            Expanded(child: _buildSkeletonItem(theme)),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildSkeletonItem(theme)),
            const SizedBox(width: 12),
            Expanded(child: _buildSkeletonItem(theme)),
          ],
        ),
      ],
    );
  }

  /// Build skeleton loading item
  Widget _buildSkeletonItem(ThemeData theme) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

/// Individual metric item widget
class _MetricItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final String? subtitle;
  final double? trend;
  final Color color;

  const _MetricItem({
    required this.icon,
    required this.label,
    required this.value,
    this.subtitle,
    this.trend,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and trend
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const Spacer(),
              if (trend != null)
                _TrendIndicator(
                  trend: trend!,
                  color: color,
                ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Value
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          
          // Label
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
                fontSize: 11,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Trend indicator widget
class _TrendIndicator extends StatelessWidget {
  final double trend;
  final Color color;

  const _TrendIndicator({
    required this.trend,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isPositive = trend >= 0;
    final trendColor = isPositive ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: trendColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            color: trendColor,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            '${trend.abs().toStringAsFixed(1)}%',
            style: theme.textTheme.bodySmall?.copyWith(
              color: trendColor,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }
}
