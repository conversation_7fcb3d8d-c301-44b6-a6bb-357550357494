import 'package:dio/dio.dart';
import '../../../core/network/api_service.dart';
import '../../../core/network/http_client.dart';
import '../models/profile_models.dart';

/// API service for provider profile operations
class ProfileApiService extends ApiService {
  ProfileApiService(HttpClient httpClient) : super(httpClient);

  /// Get provider profile
  Future<ProfileResult> getProfile() async {
    try {
      print('[ProfileApiService] Fetching provider profile');

      final response = await httpClient.get('/api/auth/providers/profile');

      print('[ProfileApiService] Profile response status: ${response.statusCode}');
      print('[ProfileApiService] Profile response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['data'] != null) {
          final profileData = ProfileData.fromJson(responseData['data']);
          return ProfileResult.success(profileData);
        } else {
          return ProfileResult.error(
            ProfileError(
              code: 'INVALID_RESPONSE',
              message: responseData['message'] ?? 'Invalid response format',
            ),
          );
        }
      } else {
        return ProfileResult.error(
          ProfileError(
            code: 'HTTP_ERROR',
            message: 'Failed to fetch profile: ${response.statusCode}',
          ),
        );
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Profile fetch error: $e');
      
      String errorMessage = 'Failed to fetch profile';
      String errorCode = 'NETWORK_ERROR';
      
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;
        
        switch (statusCode) {
          case 401:
            errorCode = 'UNAUTHORIZED';
            errorMessage = 'Authentication required';
            break;
          case 404:
            errorCode = 'NOT_FOUND';
            errorMessage = 'Provider profile not found';
            break;
          case 500:
            errorCode = 'SERVER_ERROR';
            errorMessage = 'Server error occurred';
            break;
          default:
            if (responseData is Map<String, dynamic> && responseData['message'] != null) {
              errorMessage = responseData['message'];
            }
        }
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        errorCode = 'TIMEOUT';
        errorMessage = 'Request timeout. Please try again.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorCode = 'CONNECTION_ERROR';
        errorMessage = 'Network connection error. Please check your internet connection.';
      }

      return ProfileResult.error(
        ProfileError(
          code: errorCode,
          message: errorMessage,
          details: e.toString(),
        ),
      );
    } catch (e) {
      print('[ProfileApiService] Unexpected profile fetch error: $e');
      
      return ProfileResult.error(
        ProfileError(
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Update provider profile
  Future<ProfileResult> updateProfile(ProfileUpdateRequest request) async {
    try {
      print('[ProfileApiService] Updating provider profile');
      print('[ProfileApiService] Update request: ${request.toJson()}');

      final response = await httpClient.put(
        '/api/auth/providers/profile',
        data: request.toJson(),
      );

      print('[ProfileApiService] Update response status: ${response.statusCode}');
      print('[ProfileApiService] Update response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true && responseData['data'] != null) {
          final profileData = ProfileData.fromJson(responseData['data']);
          return ProfileResult.success(profileData);
        } else {
          return ProfileResult.error(
            ProfileError(
              code: 'INVALID_RESPONSE',
              message: responseData['message'] ?? 'Invalid response format',
            ),
          );
        }
      } else {
        return ProfileResult.error(
          ProfileError(
            code: 'HTTP_ERROR',
            message: 'Failed to update profile: ${response.statusCode}',
          ),
        );
      }
    } on DioException catch (e) {
      print('[ProfileApiService] Profile update error: $e');
      
      String errorMessage = 'Failed to update profile';
      String errorCode = 'NETWORK_ERROR';
      List<String>? validationErrors;
      
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;
        
        switch (statusCode) {
          case 400:
            errorCode = 'VALIDATION_ERROR';
            errorMessage = 'Invalid profile data';
            if (responseData is Map<String, dynamic>) {
              errorMessage = responseData['message'] ?? errorMessage;
              if (responseData['errors'] is List) {
                validationErrors = List<String>.from(responseData['errors']);
              }
            }
            break;
          case 401:
            errorCode = 'UNAUTHORIZED';
            errorMessage = 'Authentication required';
            break;
          case 404:
            errorCode = 'NOT_FOUND';
            errorMessage = 'Provider profile not found';
            break;
          case 500:
            errorCode = 'SERVER_ERROR';
            errorMessage = 'Server error occurred';
            break;
          default:
            if (responseData is Map<String, dynamic> && responseData['message'] != null) {
              errorMessage = responseData['message'];
            }
        }
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        errorCode = 'TIMEOUT';
        errorMessage = 'Request timeout. Please try again.';
      } else if (e.type == DioExceptionType.connectionError) {
        errorCode = 'CONNECTION_ERROR';
        errorMessage = 'Network connection error. Please check your internet connection.';
      }

      return ProfileResult.error(
        ProfileError(
          code: errorCode,
          message: errorMessage,
          details: e.toString(),
          validationErrors: validationErrors,
        ),
      );
    } catch (e) {
      print('[ProfileApiService] Unexpected profile update error: $e');
      
      return ProfileResult.error(
        ProfileError(
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          details: e.toString(),
        ),
      );
    }
  }
}
