import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/message_models.dart';
import '../providers/message_provider.dart';
import '../services/message_api_service.dart';

class NewConversationDialog extends ConsumerStatefulWidget {
  final Function(Conversation) onConversationCreated;

  const NewConversationDialog({super.key, required this.onConversationCreated});

  @override
  ConsumerState<NewConversationDialog> createState() =>
      _NewConversationDialogState();
}

class _NewConversationDialogState extends ConsumerState<NewConversationDialog> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  CustomerForConversation? _selectedCustomer;
  List<CustomerForConversation> _customers = [];
  List<CustomerForConversation> _filteredCustomers = [];
  bool _isCreating = false;
  bool _isLoadingCustomers = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() {
        _isLoadingCustomers = true;
        _error = null;
      });

      final apiService = ref.read(messageApiServiceProvider);
      final customers = await apiService.getCustomersForConversation();

      setState(() {
        _customers = customers;
        _filteredCustomers = customers;
        _isLoadingCustomers = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoadingCustomers = false;
      });
    }
  }

  void _filterCustomers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCustomers = _customers;
      } else {
        _filteredCustomers =
            _customers
                .where(
                  (customer) =>
                      customer.displayName.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ||
                      (customer.email?.toLowerCase().contains(
                            query.toLowerCase(),
                          ) ??
                          false) ||
                      (customer.mobileNumber?.contains(query) ?? false),
                )
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Text(
                  'Start New Conversation',
                  style: context.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Customer search
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search customers...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onChanged: _filterCustomers,
            ),

            const SizedBox(height: 16),

            // Selected customer
            if (_selectedCustomer != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.colors.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: context.colors.primary,
                      child: Text(
                        _selectedCustomer!.initials,
                        style: context.textTheme.titleSmall?.copyWith(
                          color: context.colors.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedCustomer!.displayName,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (_selectedCustomer!.mobileNumber != null)
                            Text(
                              _selectedCustomer!.mobileNumber!,
                              style: context.textTheme.bodySmall?.copyWith(
                                color: context.colors.onSurfaceVariant,
                              ),
                            ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _selectedCustomer = null;
                        });
                      },
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Customer list or initial message
            if (_selectedCustomer == null) ...[
              Text(
                'Select a customer:',
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(child: _buildCustomerList()),
            ] else ...[
              // Initial message
              Text(
                'Initial message (optional):',
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  decoration: InputDecoration(
                    hintText: 'Type your message here...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed:
                      _isCreating ? null : () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed:
                      _selectedCustomer != null && !_isCreating
                          ? _createConversation
                          : null,
                  child:
                      _isCreating
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Text('Start Conversation'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerList() {
    if (_isLoadingCustomers) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: context.colors.error),
            const SizedBox(height: 8),
            Text(
              'Error loading customers',
              style: context.textTheme.titleMedium,
            ),
            const SizedBox(height: 4),
            Text(
              _error!,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCustomers,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredCustomers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 48,
              color: context.colors.onSurfaceVariant,
            ),
            const SizedBox(height: 8),
            Text('No customers found', style: context.textTheme.titleMedium),
            const SizedBox(height: 4),
            Text(
              'Try adjusting your search or add customers first',
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredCustomers.length,
      itemBuilder: (context, index) {
        final customer = _filteredCustomers[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: context.colors.primary,
            child: Text(
              customer.initials,
              style: context.textTheme.titleSmall?.copyWith(
                color: context.colors.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          title: Text(customer.displayName),
          subtitle:
              customer.mobileNumber != null
                  ? Text(customer.mobileNumber!)
                  : customer.email != null
                  ? Text(customer.email!)
                  : null,
          onTap: () {
            setState(() {
              _selectedCustomer = customer;
            });
          },
        );
      },
    );
  }

  Future<void> _createConversation() async {
    if (_selectedCustomer == null) return;

    setState(() {
      _isCreating = true;
    });

    try {
      final request = CreateConversationRequest(
        customerId: _selectedCustomer!.id,
        initialMessage:
            _messageController.text.trim().isNotEmpty
                ? _messageController.text.trim()
                : null,
      );

      final conversation = await ref
          .read(conversationsNotifierProvider.notifier)
          .createConversation(request);

      if (conversation != null) {
        widget.onConversationCreated(conversation);
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to create conversation'),
              backgroundColor: context.colors.error,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: context.colors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }
}
