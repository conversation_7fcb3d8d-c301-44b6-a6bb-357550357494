import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../components/schedule/opening_hours_widget.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../locations/models/location_models.dart';
import '../../locations/constants/algerian_cities.dart';
import '../../locations/services/location_service.dart';
import '../../schedules/models/opening_hours_models.dart';

/// Enhanced Location Setup step with opening hours integration
class EnhancedLocationSetupStep extends ConsumerStatefulWidget {
  const EnhancedLocationSetupStep({super.key});

  @override
  ConsumerState<EnhancedLocationSetupStep> createState() => _EnhancedLocationSetupStepState();
}

class _EnhancedLocationSetupStepState extends ConsumerState<EnhancedLocationSetupStep> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _shortNameController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();

  bool _parking = false;
  bool _elevator = false;
  bool _handicapAccess = false;
  bool _isGettingLocation = false;
  OpeningHours _openingHours = OpeningHours.standardBusiness();

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _shortNameController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _postalCodeController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  /// Load existing location data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final location = onboardingState.data?.primaryLocation;

    if (location != null) {
      _nameController.text = location.name;
      _addressController.text = location.address;
      _cityController.text = location.city;
      _parking = location.parking;
      _elevator = location.elevator;
      _handicapAccess = location.handicapAccess;

      // Mark step as completed if data exists
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.markStepCompleted(OnboardingStep.locationSetup, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = ref.watch(themeProvider);
    
    return Scaffold(
      backgroundColor: theme.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Step header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colors.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: theme.colors.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Location Setup',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Location name field
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Location Name *',
                          hintText: 'e.g., Main Clinic, Downtown Office',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.business),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Location name is required';
                          }
                          if (value.trim().length < 2) {
                            return 'Location name must be at least 2 characters';
                          }
                          return null;
                        },
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      // Short name field (optional)
                      TextFormField(
                        controller: _shortNameController,
                        decoration: const InputDecoration(
                          labelText: 'Short Name (Optional)',
                          hintText: 'e.g., Main Branch, Downtown',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.short_text),
                        ),
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.words,
                      ),

                      const SizedBox(height: 16),

                      // Street address field
                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'Street Address *',
                          hintText: 'e.g., 123 Main Street',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_on),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Street address is required';
                          }
                          if (value.trim().length < 5) {
                            return 'Please enter a complete address';
                          }
                          return null;
                        },
                        onChanged: _onFieldChanged,
                        textCapitalization: TextCapitalization.words,
                        maxLines: 2,
                      ),

                      const SizedBox(height: 16),

                      // City dropdown
                      DropdownButtonFormField<String>(
                        value: _cityController.text.isNotEmpty && AlgerianCities.isValidCity(_cityController.text)
                            ? _cityController.text
                            : null,
                        decoration: const InputDecoration(
                          labelText: 'City *',
                          hintText: 'Select an Algerian city',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.location_city),
                        ),
                        items: AlgerianCities.sortedCities.map((String city) {
                          return DropdownMenuItem<String>(
                            value: city,
                            child: Text(city),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              _cityController.text = newValue;
                            });
                            _onFieldChanged('');
                          }
                        },
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'City is required';
                          }
                          return null;
                        },
                        isExpanded: true,
                      ),

                      const SizedBox(height: 16),

                      // Coordinates section
                      _buildCoordinatesSection(theme),

                      const SizedBox(height: 24),

                      // Opening hours section using reusable component
                      OpeningHoursWidget(
                        initialOpeningHours: _openingHours,
                        onChanged: _onOpeningHoursChanged,
                        title: 'Opening Hours',
                        subtitle: 'Set your business operating hours for this location',
                      ),

                      const SizedBox(height: 24),

                      // Accessibility features section
                      _buildAccessibilitySection(theme),

                      const SizedBox(height: 24),

                      // Info card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colors.primary.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: theme.colors.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'This will be your primary business location. You can add more locations later.',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colors.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Navigation buttons
            WizardNavigationButtons(
              isValid: _validateForm(),
              validationMessage: _validateForm() ? null : 'Please fill in all required fields and get your location',
              onNext: () => _saveAndProceed(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoordinatesSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.outline.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.my_location,
                color: theme.colors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Location Coordinates',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Get your current location to help customers find you easily.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colors.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),

          // Latitude and Longitude fields
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _latitudeController,
                  enabled: false,
                  decoration: const InputDecoration(
                    labelText: 'Latitude *',
                    hintText: 'Will be filled automatically',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.place),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please get current location';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _longitudeController,
                  enabled: false,
                  decoration: const InputDecoration(
                    labelText: 'Longitude *',
                    hintText: 'Will be filled automatically',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.place),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please get current location';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Get location button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isGettingLocation ? null : _getCurrentLocation,
              icon: _isGettingLocation
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.my_location),
              label: Text(_isGettingLocation ? 'Getting Location...' : 'Get Current Location'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessibilitySection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colors.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colors.outline.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.accessible,
                color: theme.colors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Accessibility Features',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Select the accessibility features available at your location.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colors.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),

          // Accessibility checkboxes
          CheckboxListTile(
            title: const Text('Parking Available'),
            subtitle: const Text('On-site parking for customers'),
            value: _parking,
            onChanged: (bool? value) {
              setState(() {
                _parking = value ?? false;
              });
              _onFieldChanged('');
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          CheckboxListTile(
            title: const Text('Elevator Access'),
            subtitle: const Text('Elevator available for upper floors'),
            value: _elevator,
            onChanged: (bool? value) {
              setState(() {
                _elevator = value ?? false;
              });
              _onFieldChanged('');
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),

          CheckboxListTile(
            title: const Text('Wheelchair Accessible'),
            subtitle: const Text('Wheelchair accessible entrance and facilities'),
            value: _handicapAccess,
            onChanged: (bool? value) {
              setState(() {
                _handicapAccess = value ?? false;
              });
              _onFieldChanged('');
            },
            controlAffinity: ListTileControlAffinity.leading,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  /// Handle field changes
  void _onFieldChanged(String value) {
    setState(() {}); // Trigger rebuild for validation
    
    final isValid = _validateForm();
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.markStepCompleted(OnboardingStep.locationSetup, isValid);
    
    if (isValid) {
      _saveLocationData();
    }
  }

  /// Handle opening hours changes
  void _onOpeningHoursChanged(OpeningHours openingHours) {
    setState(() {
      _openingHours = openingHours;
    });
    _onFieldChanged(''); // Trigger validation
  }

  /// Validate the form
  bool _validateForm() {
    return _nameController.text.trim().isNotEmpty &&
           _nameController.text.trim().length >= 2 &&
           _addressController.text.trim().isNotEmpty &&
           _addressController.text.trim().length >= 5 &&
           _cityController.text.trim().isNotEmpty &&
           _latitudeController.text.trim().isNotEmpty &&
           _longitudeController.text.trim().isNotEmpty;
  }

  /// Get current device location
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      final result = await LocationService.getCurrentLocation();

      if (result.isSuccess) {
        setState(() {
          _latitudeController.text = result.latitude!.toString();
          _longitudeController.text = result.longitude!.toString();
        });

        _onFieldChanged('');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Location acquired successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to get location: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  /// Save location data
  void _saveLocationData() {
    if (!_validateForm()) return;

    final enhancedLocation = EnhancedLocation(
      id: 1, // Temporary ID for primary location
      name: _nameController.text.trim(),
      shortName: _shortNameController.text.trim().isNotEmpty
          ? _shortNameController.text.trim()
          : null,
      address: _addressController.text.trim(),
      city: _cityController.text.trim(),
      country: 'Algeria',
      timezone: 'Africa/Algiers',
      latitude: double.tryParse(_latitudeController.text),
      longitude: double.tryParse(_longitudeController.text),
      parking: _parking,
      elevator: _elevator,
      handicapAccess: _handicapAccess,
      openingHours: _openingHours,
    );

    // Convert to legacy location for backward compatibility
    final legacyLocation = enhancedLocation.toLegacyLocation();

    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).savePrimaryLocation(legacyLocation);
  }

  /// Save and proceed to next step
  void _saveAndProceed() {
    if (_formKey.currentState?.validate() ?? false) {
      _saveLocationData();
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.goToNextStep();
    }
  }
}
