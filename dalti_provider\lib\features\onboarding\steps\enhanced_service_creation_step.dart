import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/theme_provider.dart';
import '../models/onboarding_models.dart';
import '../providers/onboarding_provider.dart';
import '../components/service/delivery_type_widget.dart';
import '../components/navigation/wizard_navigation_buttons.dart';
import '../providers/wizard_navigation_provider.dart';
import '../../services/models/service_models.dart';
import '../../services/widgets/color_picker_widget.dart';

/// Enhanced Service Creation step with delivery type integration
class EnhancedServiceCreationStep extends ConsumerStatefulWidget {
  const EnhancedServiceCreationStep({super.key});

  @override
  ConsumerState<EnhancedServiceCreationStep> createState() =>
      _EnhancedServiceCreationStepState();
}

class _EnhancedServiceCreationStepState
    extends ConsumerState<EnhancedServiceCreationStep> {
  final List<CreateServiceRequest> _services = [];
  final _formKey = GlobalKey<FormState>();

  // Form controllers for adding new service
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _pointsController = TextEditingController();

  int _duration = 30; // Default 30 minutes
  String? _selectedColor;
  bool _acceptOnline = true;
  bool _acceptNew = true;
  bool _notificationOn = true;
  bool _isPublic = true;
  bool _showAddForm = false;
  DeliveryTypeData _deliveryData = const DeliveryTypeData(
    deliveryType: 'at_location',
    servedRegions: [],
  );

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _pointsController.dispose();
    super.dispose();
  }

  /// Load existing service data
  void _loadExistingData() {
    final onboardingState = ref.read(onboardingNotifierProvider);
    final services = onboardingState.data?.services ?? [];

    if (services.isNotEmpty) {
      // Convert existing services to CreateServiceRequest format
      _services.clear();
      for (final service in services) {
        _services.add(
          CreateServiceRequest(
            title: service.title,
            duration: service.duration,
            price: service.price ?? 0.0,
            description: service.description,
            color: service.color,
            acceptOnline: service.acceptOnline,
            acceptNew: service.acceptNew,
            notificationOn: service.notificationOn,
            pointsRequirements: service.pointsRequirements ?? 1,
            isPublic: service.isServicePublic,
            deliveryType: service.serviceDeliveryType,
            servedRegions: service.servedRegions,
          ),
        );
      }

      // Mark step as completed if services exist
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.markStepCompleted(OnboardingStep.serviceCreation, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: context.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Step header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.colors.surface,
                border: Border(
                  bottom: BorderSide(
                    color: context.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.medical_services,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Service Creation',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Services list
                    if (_services.isNotEmpty) ...[
                      Text(
                        'Your Services (${_services.length})',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Services list
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _services.length,
                        separatorBuilder:
                            (context, index) => const SizedBox(height: 8),
                        itemBuilder:
                            (context, index) =>
                                _buildServiceCard(_services[index], index),
                      ),

                      const SizedBox(height: 24),
                    ],

                    // Add service button or form
                    if (!_showAddForm) ...[
                      // Add service button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showAddForm = true;
                            });
                          },
                          icon: const Icon(Icons.add),
                          label: Text(
                            _services.isEmpty
                                ? 'Add Your First Service'
                                : 'Add Another Service',
                          ),
                        ),
                      ),

                      if (_services.isEmpty) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: theme.colorScheme.primary.withOpacity(0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: theme.colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'Add at least one service to continue. You can add more services later.',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ] else ...[
                      // Add service form
                      _buildAddServiceForm(theme),
                    ],
                  ],
                ),
              ),
            ),

            // Navigation buttons
            WizardNavigationButtons(
              isValid: _services.isNotEmpty,
              validationMessage:
                  _services.isEmpty ? 'Please add at least one service' : null,
              onNext: () => _saveAndProceed(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build service card widget
  Widget _buildServiceCard(CreateServiceRequest service, int index) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Service color indicator
                if (service.color != null)
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Color(
                        int.parse('0xFF${service.color!.replaceAll('#', '')}'),
                      ),
                      shape: BoxShape.circle,
                    ),
                  ),
                if (service.color != null) const SizedBox(width: 8),

                // Service title
                Expanded(
                  child: Text(
                    service.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Remove button
                IconButton(
                  onPressed: () => _removeService(index),
                  icon: const Icon(Icons.delete_outline),
                  iconSize: 20,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),

            if (service.description != null &&
                service.description!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                service.description!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],

            const SizedBox(height: 12),

            // Service details
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildServiceDetail(Icons.schedule, '${service.duration} min'),
                _buildServiceDetail(
                  Icons.attach_money,
                  '${(service.price ?? 0.0).toStringAsFixed(0)} DA',
                ),
                _buildServiceDetail(
                  Icons.local_shipping,
                  _getDeliveryTypeText(service.deliveryType),
                ),
                if (service.isPublic)
                  _buildServiceDetail(Icons.public, 'Public')
                else
                  _buildServiceDetail(Icons.lock, 'Private'),
                if (service.pointsRequirements != null)
                  _buildServiceDetail(
                    Icons.stars,
                    '${service.pointsRequirements} pts',
                  ),
              ],
            ),

            // Served regions (if applicable)
            if (service.servedRegions != null &&
                service.servedRegions!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Served regions: ${service.servedRegions!.join(', ')}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build service detail chip
  Widget _buildServiceDetail(IconData icon, String text) {
    final theme = Theme.of(context);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: theme.colorScheme.onSurface.withOpacity(0.7),
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  String _getDeliveryTypeText(String deliveryType) {
    switch (deliveryType) {
      case 'at_location':
        return 'At Location';
      case 'at_customer':
        return 'At Customer';
      case 'both':
        return 'Both';
      default:
        return 'At Location';
    }
  }

  /// Build add service form
  Widget _buildAddServiceForm(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form header
              Row(
                children: [
                  Icon(
                    Icons.add_circle_outline,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Add New Service',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: _cancelAddService,
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Service title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Service Title *',
                  hintText: 'e.g., Consultation, Haircut, Massage',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.room_service),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Service title is required';
                  }
                  if (value.trim().length < 2) {
                    return 'Title must be at least 2 characters';
                  }
                  return null;
                },
                textCapitalization: TextCapitalization.words,
              ),

              const SizedBox(height: 16),

              // Service description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Describe your service',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
                textCapitalization: TextCapitalization.sentences,
              ),

              const SizedBox(height: 16),

              // Duration and Price row
              Row(
                children: [
                  // Duration
                  Expanded(
                    child: DropdownButtonFormField<int>(
                      value: _duration,
                      decoration: const InputDecoration(
                        labelText: 'Duration *',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.schedule),
                      ),
                      items:
                          [5, 10, 15, 30, 45, 60, 90, 120, 180, 240]
                              .map(
                                (duration) => DropdownMenuItem(
                                  value: duration,
                                  child: Text('$duration min'),
                                ),
                              )
                              .toList(),
                      onChanged: (value) {
                        setState(() {
                          _duration = value ?? 30;
                        });
                      },
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Price
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      decoration: const InputDecoration(
                        labelText: 'Price (DA) *',
                        hintText: '0',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Price is required';
                        }
                        final price = double.tryParse(value);
                        if (price == null || price < 0) {
                          return 'Enter valid price';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Points requirement
              TextFormField(
                controller: _pointsController,
                decoration: const InputDecoration(
                  labelText: 'Credit Points Required *',
                  hintText: 'Credits needed to book this service (minimum 1)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.account_balance_wallet),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Credit points requirement is required';
                  }
                  final points = int.tryParse(value);
                  if (points == null || points < 1) {
                    return 'Credit points must be a positive number';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Delivery type section using reusable component
              DeliveryTypeWidget(
                initialData: _deliveryData,
                onChanged: _onDeliveryDataChanged,
                title: 'Service Delivery',
                subtitle: 'Where do you provide this service?',
              ),

              const SizedBox(height: 16),

              // Color picker
              ColorPickerWidget(
                selectedColor:
                    _selectedColor ?? '#2196F3', // Default blue color
                onColorChanged: (color) {
                  setState(() {
                    _selectedColor = color;
                  });
                },
              ),

              const SizedBox(height: 16),

              // Service options
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Service Options',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),

                  CheckboxListTile(
                    title: const Text('Public Service'),
                    subtitle: const Text('Visible to all customers'),
                    value: _isPublic,
                    onChanged: (value) {
                      setState(() {
                        _isPublic = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Accept Online Bookings'),
                    subtitle: const Text('Allow customers to book online'),
                    value: _acceptOnline,
                    onChanged: (value) {
                      setState(() {
                        _acceptOnline = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Accept New Customers'),
                    subtitle: const Text('Allow new customers to book'),
                    value: _acceptNew,
                    onChanged: (value) {
                      setState(() {
                        _acceptNew = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),

                  CheckboxListTile(
                    title: const Text('Enable Notifications'),
                    subtitle: const Text('Get notified for new bookings'),
                    value: _notificationOn,
                    onChanged: (value) {
                      setState(() {
                        _notificationOn = value ?? true;
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Form buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _cancelAddService,
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _addService,
                      child: const Text('Add Service'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Handle delivery data changes
  void _onDeliveryDataChanged(DeliveryTypeData deliveryData) {
    setState(() {
      _deliveryData = deliveryData;
    });
  }

  /// Add service to list
  void _addService() {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_deliveryData.isValid) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Please select served regions for customer location services',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final service = CreateServiceRequest(
        title: _titleController.text.trim(),
        duration: _duration,
        price: double.tryParse(_priceController.text) ?? 0.0,
        description:
            _descriptionController.text.trim().isNotEmpty
                ? _descriptionController.text.trim()
                : null,
        color: _selectedColor,
        acceptOnline: _acceptOnline,
        acceptNew: _acceptNew,
        notificationOn: _notificationOn,
        pointsRequirements: int.tryParse(_pointsController.text) ?? 1,
        isPublic: _isPublic,
        deliveryType: _deliveryData.deliveryType,
        servedRegions:
            _deliveryData.servedRegions.isNotEmpty
                ? _deliveryData.servedRegions
                : null,
      );

      setState(() {
        _services.add(service);
        _showAddForm = false;
      });

      _clearForm();
      _updateStepCompletion();
    }
  }

  /// Remove service from list
  void _removeService(int index) {
    setState(() {
      _services.removeAt(index);
    });
    _updateStepCompletion();
  }

  /// Cancel add service form
  void _cancelAddService() {
    setState(() {
      _showAddForm = false;
    });
    _clearForm();
  }

  /// Clear form fields
  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _pointsController.clear();
    setState(() {
      _duration = 30;
      _selectedColor = null;
      _acceptOnline = true;
      _acceptNew = true;
      _notificationOn = true;
      _isPublic = true;
      _deliveryData = const DeliveryTypeData(
        deliveryType: 'at_location',
        servedRegions: [],
      );
    });
  }

  /// Update step completion status
  void _updateStepCompletion() {
    final isValid = _services.isNotEmpty;
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
    wizardNotifier.markStepCompleted(OnboardingStep.serviceCreation, isValid);

    if (isValid) {
      _saveServicesData();
    }
  }

  /// Save services data
  void _saveServicesData() {
    // Convert CreateServiceRequest to Service objects
    final services =
        _services.asMap().entries.map((entry) {
          final index = entry.key;
          final request = entry.value;
          return Service.fromCreateRequest(request, id: index + 1);
        }).toList();

    // Save to onboarding provider
    ref.read(onboardingNotifierProvider.notifier).saveServices(services);
  }

  /// Save and proceed to next step
  void _saveAndProceed() {
    if (_services.isNotEmpty) {
      _saveServicesData();
      final wizardNotifier = ref.read(wizardNavigationProvider.notifier);
      wizardNotifier.goToNextStep();
    }
  }
}
