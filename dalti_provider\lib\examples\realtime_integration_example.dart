import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/providers/realtime_providers.dart';
import '../features/messages/models/message_models.dart';
import '../features/notifications/models/notification_models.dart' as notifications;

/// Example screen showing how to integrate real-time WebSocket messaging and notifications
class RealtimeIntegrationExample extends ConsumerWidget {
  const RealtimeIntegrationExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Real-time Integration Example'),
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Real-time WebSocket Integration',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            <PERSON>zed<PERSON>ox(height: 16),
            
            // WebSocket Connection Status
            _ConnectionStatusWidget(),
            <PERSON>zed<PERSON>ox(height: 24),
            
            // Real-time Conversations
            _ConversationsWidget(),
            <PERSON><PERSON><PERSON>ox(height: 24),
            
            // Real-time Notifications
            _NotificationsWidget(),
            SizedBox(height: 24),
            
            // Real-time Queue Updates
            _QueueUpdatesWidget(),
            SizedBox(height: 24),
            
            // Usage Instructions
            _UsageInstructionsWidget(),
          ],
        ),
      ),
    );
  }
}

/// Widget showing WebSocket connection status
class _ConnectionStatusWidget extends ConsumerWidget {
  const _ConnectionStatusWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionState = ref.watch(webSocketConnectionStateProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'WebSocket Connection Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            connectionState.when(
              data: (isConnected) => Row(
                children: [
                  Icon(
                    isConnected ? Icons.wifi : Icons.wifi_off,
                    color: isConnected ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isConnected ? 'Connected' : 'Disconnected',
                    style: TextStyle(
                      color: isConnected ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              loading: () => const Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('Connecting...'),
                ],
              ),
              error: (error, stack) => Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  Text('Error: $error', style: const TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget showing real-time conversations
class _ConversationsWidget extends ConsumerWidget {
  const _ConversationsWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final conversationsStream = ref.watch(realtimeConversationsProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Real-time Conversations',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            conversationsStream.when(
              data: (conversations) => Column(
                children: [
                  Text('Total conversations: ${conversations.length}'),
                  const SizedBox(height: 8),
                  ...conversations.take(3).map((conversation) => 
                    ListTile(
                      leading: const CircleAvatar(child: Icon(Icons.person)),
                      title: Text(conversation.customerName),
                      subtitle: Text(conversation.lastMessage?.content ?? 'No messages'),
                      trailing: conversation.unreadCount > 0 
                        ? CircleAvatar(
                            radius: 10,
                            backgroundColor: Colors.red,
                            child: Text(
                              '${conversation.unreadCount}',
                              style: const TextStyle(color: Colors.white, fontSize: 12),
                            ),
                          )
                        : null,
                    ),
                  ),
                ],
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget showing real-time notifications
class _NotificationsWidget extends ConsumerWidget {
  const _NotificationsWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsStream = ref.watch(realtimeNotificationsProvider);
    final unreadCountStream = ref.watch(realtimeUnreadNotificationsCountProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Real-time Notifications',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                unreadCountStream.when(
                  data: (count) => count > 0 
                    ? CircleAvatar(
                        radius: 12,
                        backgroundColor: Colors.red,
                        child: Text(
                          '$count',
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      )
                    : const SizedBox.shrink(),
                  loading: () => const SizedBox.shrink(),
                  error: (_, __) => const SizedBox.shrink(),
                ),
              ],
            ),
            const SizedBox(height: 8),
            notificationsStream.when(
              data: (notifications) => Column(
                children: notifications.take(3).map((notification) => 
                  ListTile(
                    leading: Icon(
                      notification.isRead ? Icons.mark_email_read : Icons.mark_email_unread,
                      color: notification.isRead ? Colors.grey : Colors.blue,
                    ),
                    title: Text(notification.title),
                    subtitle: Text(notification.message),
                    trailing: Text(
                      notification.createdAt.toString().substring(11, 16),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ).toList(),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget showing real-time queue updates
class _QueueUpdatesWidget extends ConsumerWidget {
  const _QueueUpdatesWidget();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allQueuesStream = ref.watch(realtimeAllQueuesProvider);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Real-time Queue Updates',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            allQueuesStream.when(
              data: (queues) => Column(
                children: queues.entries.take(3).map((entry) => 
                  ListTile(
                    leading: Icon(
                      entry.value.isActive ? Icons.play_circle : Icons.pause_circle,
                      color: entry.value.isActive ? Colors.green : Colors.orange,
                    ),
                    title: Text(entry.value.queueName),
                    subtitle: Text('${entry.value.waitingCount} waiting'),
                    trailing: Text(
                      '${entry.value.averageWaitTime.toInt()}min',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ).toList(),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text('Error: $error'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget showing usage instructions
class _UsageInstructionsWidget extends StatelessWidget {
  const _UsageInstructionsWidget();

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'How to Use Real-time Features',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Initialize real-time services in your app startup:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'ref.read(initializeRealtimeServicesProvider);',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
            const Text(
              '2. Watch real-time providers in your widgets:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'final conversations = ref.watch(realtimeConversationsProvider);',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
            const Text(
              '3. Join conversation rooms for specific conversations:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'ref.read(conversationRoomManagerProvider.notifier).joinRoom(conversationId);',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
