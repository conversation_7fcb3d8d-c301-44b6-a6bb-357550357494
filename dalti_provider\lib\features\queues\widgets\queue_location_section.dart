import 'package:flutter/material.dart';
import '../models/queue_models.dart';
import '../../locations/models/location_models.dart';
import 'queue_card.dart';

class QueueLocationSection extends StatefulWidget {
  final Location location;
  final List<Queue> queues;
  final Function(Queue) onQueueTap;
  final VoidCallback onAddQueue;
  final Function(Queue) onEditQueue;
  final Function(Queue) onDeleteQueue;
  final Function(Queue) onToggleStatus;

  const QueueLocationSection({
    super.key,
    required this.location,
    required this.queues,
    required this.onQueueTap,
    required this.onAddQueue,
    required this.onEditQueue,
    required this.onDeleteQueue,
    required this.onToggleStatus,
  });

  @override
  State<QueueLocationSection> createState() => _QueueLocationSectionState();
}

class _QueueLocationSectionState extends State<QueueLocationSection> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    final activeQueues = widget.queues.where((q) => q.isActive).length;
    final inactiveQueues = widget.queues.length - activeQueues;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color:
          Theme.of(context).brightness == Brightness.light
              ? const Color(0xFFF5F5F5)
              : Theme.of(context).colorScheme.surface.withOpacity(0.7),
      child: Theme(
        data: Theme.of(context).copyWith(
          cardTheme: CardThemeData(
            elevation: 0,
            color:
                Theme.of(context).brightness == Brightness.light
                    ? Colors.white
                    : Theme.of(context).colorScheme.surface,
            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color:
                    Theme.of(context).brightness == Brightness.light
                        ? Colors.grey.shade200
                        : Colors.grey.shade800,
                width: 1,
              ),
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 4),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color:
                        Theme.of(context).brightness == Brightness.light
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.location.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.only(bottom: 8),
              itemCount: widget.queues.length,
              itemBuilder:
                  (context, index) => QueueCard(
                    queue: widget.queues[index],
                    onTap: () => widget.onQueueTap(widget.queues[index]),
                    onEdit: () => widget.onEditQueue(widget.queues[index]),
                    backgroundColor:
                        Theme.of(context).brightness == Brightness.light
                            ? Theme.of(context).cardColor
                            : Theme.of(context).colorScheme.surface,
                    margin: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    onDelete: () => widget.onDeleteQueue(widget.queues[index]),
                    onToggleStatus:
                        () => widget.onToggleStatus(widget.queues[index]),
                    showLocation: false,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

class QueueLocationSummary extends StatelessWidget {
  final Location location;
  final List<Queue> queues;
  final VoidCallback? onTap;

  const QueueLocationSummary({
    super.key,
    required this.location,
    required this.queues,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final activeQueues = queues.where((q) => q.isActive).length;
    final totalServices = queues.fold<int>(
      0,
      (sum, queue) => sum + queue.serviceCount,
    );

    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      location.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                location.address,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildStatItem(
                    context,
                    'Queues',
                    '${queues.length}',
                    Icons.queue,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    context,
                    'Active',
                    '$activeQueues',
                    Icons.play_arrow,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    context,
                    'Services',
                    '$totalServices',
                    Icons.business_center,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color ?? Colors.grey.shade600),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
