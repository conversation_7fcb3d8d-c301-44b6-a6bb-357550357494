import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../queues/widgets/queue_card.dart';
import '../../queues/providers/queue_provider.dart';
import '../../queues/models/queue_models.dart';
import '../../../core/routing/app_routes.dart';
import 'package:go_router/go_router.dart';

class LocationQueuesSection extends ConsumerStatefulWidget {
  final int locationId;
  final VoidCallback? onAddQueue;

  const LocationQueuesSection({
    super.key,
    required this.locationId,
    this.onAddQueue,
  });

  @override
  ConsumerState<LocationQueuesSection> createState() =>
      _LocationQueuesSectionState();
}

class _LocationQueuesSectionState extends ConsumerState<LocationQueuesSection> {
  @override
  void initState() {
    super.initState();
    // Load queues if not loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(queueNotifierProvider.notifier)
          .loadQueuesByLocation(widget.locationId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final queueState = ref.watch(queueNotifierProvider);
    final queues = queueState.queuesByLocation[widget.locationId] ?? [];

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.queue,
                  size: 24,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Service Queues',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: widget.onAddQueue,
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('Add Queue'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Show loading, error, empty state, or queues list
            if (queueState.state == QueueState.loading)
              const Center(child: CircularProgressIndicator())
            else if (queueState.state == QueueState.error &&
                queueState.error != null)
              _buildErrorContent(context, queueState.error!)
            else if (queues.isEmpty)
              _buildPlaceholderContent(context)
            else
              _buildQueuesList(context, queues),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorContent(BuildContext context, String error) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.errorContainer),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 12),
          Text(
            'Error Loading Queues',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              ref
                  .read(queueNotifierProvider.notifier)
                  .loadQueuesByLocation(widget.locationId);
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(Icons.queue, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 12),
          Text(
            'No Queues Yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create service queues to organize your services at this location.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: widget.onAddQueue,
            icon: const Icon(Icons.add),
            label: const Text('Create First Queue'),
          ),
        ],
      ),
    );
  }

  Widget _buildQueuesList(BuildContext context, List<Queue> queues) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: queues.length,
      itemBuilder: (context, index) {
        final queue = queues[index];
        return QueueCard(
          queue: queue,
          onTap: () async {
            // Refresh queue data before navigation
            await ref
                .read(queueNotifierProvider.notifier)
                .loadQueueById(queue.id);
            if (context.mounted) {
              context.push('/queues/${queue.id}/edit');
            }
          },
          onEdit: () async {
            // Refresh queue data before navigation
            await ref
                .read(queueNotifierProvider.notifier)
                .loadQueueById(queue.id);
            if (context.mounted) {
              context.push('/queues/${queue.id}/edit');
            }
          },
          onDelete: () {
            ref.read(queueNotifierProvider.notifier).deleteQueue(queue.id);
          },
          onToggleStatus: () {
            ref
                .read(queueNotifierProvider.notifier)
                .toggleQueueStatus(queue.id, !queue.isActive);
          },
          showLocation:
              false, // Don't show location since we're in location details
          margin: const EdgeInsets.only(bottom: 8),
        );
      },
    );
  }
}
