import '../models/dashboard_models.dart';
import '../services/dashboard_api_service.dart';
import 'dashboard_repository.dart';

/// Concrete implementation of DashboardRepository
class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardApiService _apiService;
  
  // Cache management
  DashboardData? _cachedData;
  DateTime? _lastFetch;
  
  // Cache durations (in minutes)
  static const int _businessMetricsCacheDuration = 5;
  static const int _scheduleCacheDuration = 1; // 30 seconds would be 0.5, using 1 for simplicity
  static const int _notificationsCacheDuration = 0; // No caching for notifications
  static const int _quickStatsCacheDuration = 0; // No caching for quick stats

  DashboardRepositoryImpl({required DashboardApiService apiService})
      : _apiService = apiService;

  @override
  Future<DashboardData> getDashboardData() async {
    try {
      print('[DashboardRepository] Fetching complete dashboard data...');

      // Fetch all data concurrently for better performance
      final results = await Future.wait([
        getBusinessOverview(),
        getTodaySchedule(),
        getNotifications(limit: 5), // Limit to 5 for dashboard
        getQuickStats(),
      ]);

      final businessMetrics = results[0] as BusinessMetrics;
      final scheduleData = results[1] as ScheduleData;
      final notifications = results[2] as List<NotificationItem>;
      final quickStats = results[3] as QuickStats;

      final dashboardData = DashboardData(
        businessMetrics: businessMetrics,
        scheduleData: scheduleData,
        notifications: notifications,
        quickStats: quickStats,
        unreadNotificationCount: notifications.where((n) => !n.isRead).length,
        lastUpdated: DateTime.now(),
      );

      // Cache the complete data
      _cachedData = dashboardData;
      _lastFetch = DateTime.now();

      print('[DashboardRepository] Dashboard data fetched successfully');
      return dashboardData;
    } catch (e) {
      print('[DashboardRepository] Error fetching dashboard data: $e');
      
      // Return cached data if available, otherwise rethrow
      if (_cachedData != null) {
        print('[DashboardRepository] Returning cached data due to error');
        return _cachedData!;
      }
      
      rethrow;
    }
  }

  @override
  Future<BusinessMetrics> getBusinessOverview() async {
    try {
      print('[DashboardRepository] Fetching business overview...');
      
      final response = await _apiService.getBusinessOverview();
      
      if (response.success && response.data != null) {
        print('[DashboardRepository] Business overview fetched successfully');
        return response.data!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch business overview');
      }
    } catch (e) {
      print('[DashboardRepository] Error fetching business overview: $e');
      
      // Return cached business metrics if available
      if (_cachedData?.businessMetrics != null && !_isBusinessMetricsExpired()) {
        print('[DashboardRepository] Returning cached business metrics');
        return _cachedData!.businessMetrics!;
      }
      
      // Return default data if no cache available
      print('[DashboardRepository] Returning default business metrics');
      return const BusinessMetrics();
    }
  }

  @override
  Future<ScheduleData> getTodaySchedule() async {
    try {
      print('[DashboardRepository] Fetching today\'s schedule...');
      
      final response = await _apiService.getTodaySchedule();
      
      if (response.success && response.data != null) {
        print('[DashboardRepository] Schedule data fetched successfully');
        return response.data!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch schedule data');
      }
    } catch (e) {
      print('[DashboardRepository] Error fetching schedule data: $e');
      
      // Return cached schedule data if available
      if (_cachedData?.scheduleData != null && !_isScheduleDataExpired()) {
        print('[DashboardRepository] Returning cached schedule data');
        return _cachedData!.scheduleData!;
      }
      
      // Return default data if no cache available
      print('[DashboardRepository] Returning default schedule data');
      return const ScheduleData();
    }
  }

  @override
  Future<List<NotificationItem>> getNotifications({
    int limit = 10,
    bool unreadOnly = false,
  }) async {
    try {
      print('[DashboardRepository] Fetching notifications...');
      
      final response = await _apiService.getNotifications(
        limit: limit,
        unreadOnly: unreadOnly,
      );
      
      if (response.success) {
        print('[DashboardRepository] Notifications fetched successfully');
        return response.notifications;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch notifications');
      }
    } catch (e) {
      print('[DashboardRepository] Error fetching notifications: $e');
      
      // Return cached notifications if available (no expiration for notifications)
      if (_cachedData?.notifications != null) {
        print('[DashboardRepository] Returning cached notifications');
        return _cachedData!.notifications;
      }
      
      // Return empty list if no cache available
      print('[DashboardRepository] Returning empty notifications list');
      return [];
    }
  }

  @override
  Future<QuickStats> getQuickStats() async {
    try {
      print('[DashboardRepository] Fetching quick stats...');
      
      final response = await _apiService.getQuickStats();
      
      if (response.success && response.data != null) {
        print('[DashboardRepository] Quick stats fetched successfully');
        return response.data!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to fetch quick stats');
      }
    } catch (e) {
      print('[DashboardRepository] Error fetching quick stats: $e');
      
      // Return cached quick stats if available (no expiration for real-time data)
      if (_cachedData?.quickStats != null) {
        print('[DashboardRepository] Returning cached quick stats');
        return _cachedData!.quickStats!;
      }
      
      // Return default data if no cache available
      print('[DashboardRepository] Returning default quick stats');
      return const QuickStats();
    }
  }

  @override
  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      print('[DashboardRepository] Marking notification as read: $notificationId');
      
      final response = await _apiService.markNotificationAsRead(notificationId);
      
      if (response.success) {
        print('[DashboardRepository] Notification marked as read successfully');
        
        // Update cached data if available
        if (_cachedData != null) {
          final updatedNotifications = _cachedData!.notifications.map((notification) {
            if (notification.id == notificationId) {
              return notification.copyWith(isRead: true);
            }
            return notification;
          }).toList();
          
          _cachedData = _cachedData!.copyWith(
            notifications: updatedNotifications,
            unreadNotificationCount: updatedNotifications.where((n) => !n.isRead).length,
          );
        }
      } else {
        throw Exception(response.error?.message ?? 'Failed to mark notification as read');
      }
    } catch (e) {
      print('[DashboardRepository] Error marking notification as read: $e');
      rethrow;
    }
  }

  @override
  Future<EmergencyQueueControlResponse> emergencyQueueControl(
    EmergencyQueueControl request,
  ) async {
    try {
      print('[DashboardRepository] Executing emergency queue control: ${request.action}');
      
      final response = await _apiService.emergencyQueueControl(request);
      
      if (response.success && response.data != null) {
        print('[DashboardRepository] Emergency queue control executed successfully');

        // Clear cache to force refresh of queue data
        await clearCache();

        return response.data!;
      } else {
        throw Exception(response.error?.message ?? 'Failed to execute emergency queue control');
      }
    } catch (e) {
      print('[DashboardRepository] Error executing emergency queue control: $e');
      rethrow;
    }
  }

  @override
  Future<int> getUnreadNotificationCount() async {
    try {
      final notifications = await getNotifications(unreadOnly: true);
      return notifications.length;
    } catch (e) {
      print('[DashboardRepository] Error getting unread notification count: $e');
      return 0;
    }
  }

  @override
  Future<DashboardData> refreshDashboardData() async {
    print('[DashboardRepository] Refreshing dashboard data...');
    
    // Clear cache to force fresh data
    await clearCache();
    
    // Fetch fresh data
    return getDashboardData();
  }

  @override
  bool needsRefresh() {
    if (_lastFetch == null) return true;
    
    final now = DateTime.now();
    final timeSinceLastFetch = now.difference(_lastFetch!);
    
    // Consider data stale after 5 minutes
    return timeSinceLastFetch.inMinutes >= 5;
  }

  @override
  DashboardData? getCachedData() {
    return _cachedData;
  }

  @override
  Future<void> clearCache() async {
    print('[DashboardRepository] Clearing cache...');
    _cachedData = null;
    _lastFetch = null;
  }

  // Private helper methods for cache expiration checks
  bool _isBusinessMetricsExpired() {
    if (_lastFetch == null) return true;
    final now = DateTime.now();
    return now.difference(_lastFetch!).inMinutes >= _businessMetricsCacheDuration;
  }

  bool _isScheduleDataExpired() {
    if (_lastFetch == null) return true;
    final now = DateTime.now();
    return now.difference(_lastFetch!).inMinutes >= _scheduleCacheDuration;
  }
}
