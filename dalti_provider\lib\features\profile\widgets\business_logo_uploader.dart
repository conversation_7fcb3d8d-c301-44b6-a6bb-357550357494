import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/providers/app_providers.dart';
import '../../../core/theme/theme_provider.dart';
import '../services/profile_api_service.dart';

/// A widget for uploading a business logo.
class BusinessLogoUploader extends ConsumerStatefulWidget {
  final String? initialLogoUrl;
  final VoidCallback? onUploadComplete;
  final String title;
  final String? subtitle;

  const BusinessLogoUploader({
    super.key,
    this.initialLogoUrl,
    this.onUploadComplete,
    this.title = 'Business Logo',
    this.subtitle,
  });

  @override
  ConsumerState<BusinessLogoUploader> createState() =>
      _BusinessLogoUploaderState();
}

class _BusinessLogoUploaderState extends ConsumerState<BusinessLogoUploader> {
  String? _currentLogoUrl;
  bool _isUploading = false;
  String? _errorMessage;
  Uint8List? _fileBytes;
  String? _fileName;

  ScaffoldMessengerState? _scaffoldMessenger;

  @override
  void initState() {
    super.initState();
    _currentLogoUrl = widget.initialLogoUrl;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  void dispose() {
    _scaffoldMessenger = null;
    super.dispose();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    final messenger = _scaffoldMessenger ?? ScaffoldMessenger.maybeOf(context);
    if (messenger != null) {
      messenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor:
              isError ? Colors.red : Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (widget.subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                widget.subtitle!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              height: 235,
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      _errorMessage != null
                          ? theme.colorScheme.error
                          : theme.colorScheme.outline,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              ),
              child: _buildUploadContent(),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: theme.colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'Supported formats: PNG, JPG, JPEG • Max size: 5MB',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadContent() {
    if (_isUploading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Uploading logo...'),
          ],
        ),
      );
    }

    if (_currentLogoUrl != null) {
      return _buildUploadedState();
    }

    if (_fileBytes != null) {
      return _buildPreviewState();
    }

    return _buildInitialState();
  }

  Widget _buildInitialState() {
    final theme = Theme.of(context);
    return InkWell(
      onTap: _selectFile,
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_upload_outlined,
              size: 48,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Click to upload logo',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose an image file from your device',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: MemoryImage(_fileBytes!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _fileName ?? 'Unknown file',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton.icon(
                onPressed: _selectFile,
                icon: const Icon(Icons.edit_outlined),
                label: const Text('Change'),
              ),
              FilledButton.icon(
                onPressed: _uploadFile,
                icon: const Icon(Icons.upload_file_outlined),
                label: const Text('Upload'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedState() {
    final theme = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: NetworkImage(_currentLogoUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: theme.colorScheme.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 6),
                  const Flexible(
                    child: Text(
                      'Logo uploaded successfully',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const Spacer(),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextButton.icon(
            onPressed: _selectFile,
            icon: const Icon(Icons.edit_outlined),
            label: const Text('Change Logo'),
          ),
        ),
      ],
    );
  }

  Future<void> _selectFile() async {
    try {
      setState(() => _errorMessage = null);

      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        withData: true,
      );

      if (result == null ||
          result.files.isEmpty ||
          result.files.first.bytes == null) {
        return;
      }

      final file = result.files.first;

      if (file.size > 5 * 1024 * 1024) {
        if (mounted)
          setState(() => _errorMessage = 'File size must be less than 5MB');
        return;
      }

      if (!_isValidImageType(file.extension)) {
        if (mounted)
          setState(
            () =>
                _errorMessage =
                    'Please select a valid image file (PNG, JPG, JPEG)',
          );
        return;
      }

      if (mounted) {
        setState(() {
          _fileBytes = file.bytes;
          _fileName = file.name;
          _errorMessage = null;
        });
      }
    } catch (e) {
      if (mounted) setState(() => _errorMessage = 'Failed to select file: $e');
    }
  }

  bool _isValidImageType(String? extension) {
    if (extension == null) return false;
    final validExtensions = ['png', 'jpg', 'jpeg'];
    return validExtensions.contains(extension.toLowerCase());
  }

  Future<void> _uploadFile() async {
    if (_fileBytes == null || _fileName == null) return;

    try {
      if (!mounted) return;
      setState(() {
        _isUploading = true;
        _errorMessage = null;
      });

      final profileService = ref.read(profileApiServiceProvider);

      final platformFile = PlatformFile(
        name: _fileName!,
        size: _fileBytes!.length,
        bytes: _fileBytes,
        path: kIsWeb ? null : _fileName,
      );

      final result = await profileService.uploadBusinessLogo(platformFile);

      if (!mounted) return;

      if (result.success) {
        setState(() {
          _currentLogoUrl = result.downloadUrl;
          _isUploading = false;
          _fileBytes = null;
          _fileName = null;
        });

        widget.onUploadComplete?.call();
        _showSnackBar('Logo uploaded successfully!');
      } else {
        throw Exception(result.error ?? 'Upload failed');
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isUploading = false;
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });
      _showSnackBar(_errorMessage!, isError: true);
    }
  }
}
