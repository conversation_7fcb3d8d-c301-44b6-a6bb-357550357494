/// Application route definitions and constants
class AppRoutes {
  // Root routes
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String otpVerification = '/otp-verification';
  static const String onboarding = '/onboarding';

  // Password reset routes
  static const String forgotPassword = '/forgot-password';
  static const String verifyResetOtp = '/verify-reset-otp';
  static const String resetPassword = '/reset-password';

  // Main app routes (authenticated)
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String qrScanner = '/qr-scanner';
  static const String serviceSession = '/service';

  // Business management routes
  static const String locations = '/locations';
  static const String locationDetails = '/locations/:id';
  static const String addLocation = '/locations/add';
  static const String editLocation = '/locations/:id/edit';

  static const String services = '/services';
  static const String serviceDetails = '/services/:id';
  static const String addService = '/services/add';
  static const String editService = '/services/:id/edit';

  static const String queues = '/queues';
  static const String queueDetails = '/queues/:id';
  static const String addQueue = '/queues/add';
  static const String editQueue = '/queues/:id/edit';

  // Schedule management routes
  static const String schedules = '/schedules';
  static const String scheduleDetails = '/schedules/:id';
  static const String addSchedule = '/schedules/add';
  static const String editSchedule = '/schedules/:id/edit';

  // Customer management routes
  static const String customers = '/customers';
  static const String customerDetails = '/customers/:id';
  static const String addCustomer = '/customers/add';
  static const String editCustomer = '/customers/:id/edit';

  // Appointment management routes
  static const String appointments = '/appointments';
  static const String appointmentDetails = '/appointments/:id';
  static const String addAppointment = '/appointments/add';
  static const String editAppointment = '/appointments/:id/edit';
  static const String calendar = '/calendar';

  // Reschedule management routes
  static const String reschedules = '/reschedules';
  static const String rescheduleDetails = '/reschedules/:id';

  // Message management routes
  static const String messages = '/messages';
  static const String newConversation = '/messages/new';
  static const String conversation = '/messages/conversation/:conversationId';

  // Notification management routes
  static const String notifications = '/notifications';

  // Settings and help routes
  static const String settings = '/settings';
  static const String help = '/help';
  static const String about = '/about';

  // Error routes
  static const String notFound = '/404';
  static const String error = '/error';

  /// Get route name from path
  static String getRouteName(String path) {
    switch (path) {
      case splash:
        return 'Splash';
      case login:
        return 'Login';
      case register:
        return 'Register';
      case otpVerification:
        return 'OTP Verification';
      case onboarding:
        return 'Onboarding';
      case forgotPassword:
        return 'Forgot Password';
      case verifyResetOtp:
        return 'Verify Reset OTP';
      case resetPassword:
        return 'Reset Password';
      case dashboard:
        return 'Dashboard';
      case profile:
        return 'Profile';
      case qrScanner:
        return 'QR Scanner';
      case locations:
        return 'Locations';
      case services:
        return 'Services';
      case queues:
        return 'Queues';
      case schedules:
        return 'Schedules';
      case customers:
        return 'Customers';
      case appointments:
        return 'Appointments';
      case calendar:
        return 'Calendar';
      case reschedules:
        return 'Reschedules';
      case messages:
        return 'Messages';
      case newConversation:
        return 'New Conversation';
      case notifications:
        return 'Notifications';
      case settings:
        return 'Settings';
      case help:
        return 'Help';
      case about:
        return 'About';
      default:
        return 'Unknown';
    }
  }

  /// Check if route requires authentication
  static bool requiresAuth(String path) {
    const unauthenticatedRoutes = [
      splash,
      login,
      register,
      otpVerification,
      forgotPassword,
      verifyResetOtp,
      resetPassword,
      notFound,
      error,
    ];

    return !unauthenticatedRoutes.contains(path);
  }

  /// Check if route is an onboarding route
  static bool isOnboardingRoute(String path) {
    return path == onboarding;
  }

  /// Check if route requires onboarding to be completed
  static bool requiresOnboardingCompleted(String path) {
    const onboardingRoutes = [
      onboarding,
      splash,
      login,
      register,
      otpVerification,
      forgotPassword,
      verifyResetOtp,
      resetPassword,
      notFound,
      error,
    ];

    return !onboardingRoutes.contains(path);
  }

  /// Get initial route based on authentication state
  static String getInitialRoute(bool isAuthenticated) {
    return isAuthenticated ? dashboard : login;
  }
}
