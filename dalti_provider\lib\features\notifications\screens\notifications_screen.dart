import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/notification_models.dart';
import '../providers/notifications_provider.dart';
import '../widgets/notification_item_widget.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showUnreadOnly = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load notifications when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationsProvider.notifier).loadNotifications();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      // Load more when near bottom
      ref.read(notificationsProvider.notifier).loadMoreNotifications(
        unreadOnly: _showUnreadOnly,
      );
    }
  }

  Future<void> _onRefresh() async {
    await ref.read(notificationsProvider.notifier).refreshNotifications(
      unreadOnly: _showUnreadOnly,
    );
  }

  void _toggleUnreadFilter() {
    setState(() {
      _showUnreadOnly = !_showUnreadOnly;
    });
    ref.read(notificationsProvider.notifier).loadNotifications(
      unreadOnly: _showUnreadOnly,
    );
  }

  void _markAllAsRead() {
    ref.read(notificationsProvider.notifier).markAllAsRead();
  }

  @override
  Widget build(BuildContext context) {
    final notificationsState = ref.watch(notificationsProvider);
    final unreadCount = ref.watch(unreadNotificationsCountProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Notifications',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: _markAllAsRead,
              child: const Text(
                'Mark all read',
                style: TextStyle(
                  color: Colors.blue,
                  fontSize: 14,
                ),
              ),
            ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.black),
            onSelected: (value) {
              if (value == 'filter') {
                _toggleUnreadFilter();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'filter',
                child: Row(
                  children: [
                    Icon(
                      _showUnreadOnly ? Icons.visibility : Icons.visibility_off,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(_showUnreadOnly ? 'Show all' : 'Show unread only'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: _buildBody(notificationsState),
      ),
    );
  }

  Widget _buildBody(NotificationsState state) {
    if (state.isLoading && state.notifications.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state.error != null && state.notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load notifications',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => ref.read(notificationsProvider.notifier).loadNotifications(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _showUnreadOnly ? 'No unread notifications' : 'No notifications',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return _buildNotificationsList(state);
  }

  Widget _buildNotificationsList(NotificationsState state) {
    // Group notifications by date
    final groupedNotifications = _groupNotificationsByDate(state.notifications);

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: groupedNotifications.length + (state.isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= groupedNotifications.length) {
          // Loading indicator at bottom
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final group = groupedNotifications[index];
        return _buildDateGroup(group);
      },
    );
  }

  Widget _buildDateGroup(NotificationGroup group) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            group.dateLabel,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        ...group.notifications.map((notification) => 
          NotificationItemWidget(
            notification: notification,
            onTap: () => _onNotificationTap(notification),
            onMarkAsRead: () => _markAsRead(notification.id),
            onDelete: () => _deleteNotification(notification.id),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  List<NotificationGroup> _groupNotificationsByDate(List<NotificationItem> notifications) {
    final groups = <String, List<NotificationItem>>{};
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    for (final notification in notifications) {
      final notificationDate = DateTime(
        notification.createdAt.year,
        notification.createdAt.month,
        notification.createdAt.day,
      );

      String dateLabel;
      if (notificationDate == today) {
        dateLabel = 'Today';
      } else if (notificationDate == yesterday) {
        dateLabel = 'Yesterday';
      } else {
        dateLabel = DateFormat('dd MMM, yyyy').format(notificationDate);
      }

      groups.putIfAbsent(dateLabel, () => []).add(notification);
    }

    return groups.entries.map((entry) => 
      NotificationGroup(
        dateLabel: entry.key,
        notifications: entry.value,
      ),
    ).toList();
  }

  void _onNotificationTap(NotificationItem notification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      _markAsRead(notification.id);
    }

    // Handle navigation based on notification link
    if (notification.link != null) {
      // TODO: Implement navigation based on link
      print('Navigate to: ${notification.link}');
    }
  }

  void _markAsRead(String notificationId) {
    ref.read(notificationsProvider.notifier).markAsRead(notificationId);
  }

  void _deleteNotification(String notificationId) {
    ref.read(notificationsProvider.notifier).deleteNotification(notificationId);
  }
}

class NotificationGroup {
  final String dateLabel;
  final List<NotificationItem> notifications;

  NotificationGroup({
    required this.dateLabel,
    required this.notifications,
  });
}
