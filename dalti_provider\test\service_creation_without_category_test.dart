import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:dalti_provider/features/services/screens/create_service_screen.dart';
import 'package:dalti_provider/features/services/models/service_models.dart';
import 'package:dalti_provider/features/services/widgets/service_form.dart';

void main() {
  group('Service Creation Without Category Tests', () {
    late GoRouter router;

    setUp(() {
      router = GoRouter(
        initialLocation: '/services/create',
        routes: [
          GoRoute(
            path: '/services/create',
            builder: (context, state) => const CreateServiceScreen(),
          ),
          GoRoute(
            path: '/services',
            builder: (context, state) => const Scaffold(
              body: Text('Services List'),
            ),
          ),
        ],
      );
    });

    test('CreateServiceRequest should not require categoryId', () {
      // Test that CreateServiceRequest can be created without categoryId
      final request = CreateServiceRequest(
        title: 'Test Service',
        duration: 60,
        price: 50.0,
        description: 'Test description',
        color: '#FF0000',
        acceptOnline: true,
        acceptNew: true,
        notificationOn: true,
        pointsRequirements: 10,
      );

      expect(request.title, equals('Test Service'));
      expect(request.duration, equals(60));
      expect(request.price, equals(50.0));
      expect(request.description, equals('Test description'));
      expect(request.color, equals('#FF0000'));
      expect(request.acceptOnline, isTrue);
      expect(request.acceptNew, isTrue);
      expect(request.notificationOn, isTrue);
      expect(request.pointsRequirements, equals(10));
    });

    test('CreateServiceRequest toJson should not include categoryId', () {
      final request = CreateServiceRequest(
        title: 'Test Service',
        duration: 60,
        price: 50.0,
        acceptOnline: true,
        acceptNew: false,
        notificationOn: true,
      );

      final json = request.toJson();

      expect(json['title'], equals('Test Service'));
      expect(json['duration'], equals(60));
      expect(json['price'], equals(50.0));
      expect(json['acceptOnline'], isTrue);
      expect(json['acceptNew'], isFalse);
      expect(json['notificationOn'], isTrue);
      
      // Verify categoryId is not included
      expect(json.containsKey('categoryId'), isFalse);
    });

    test('CreateServiceRequest fromJson should work without categoryId', () {
      final json = {
        'title': 'Test Service',
        'duration': 90,
        'price': 75.5,
        'description': 'Test description',
        'color': '#00FF00',
        'acceptOnline': false,
        'acceptNew': true,
        'notificationOn': false,
        'pointsRequirements': 5,
      };

      final request = CreateServiceRequest.fromJson(json);

      expect(request.title, equals('Test Service'));
      expect(request.duration, equals(90));
      expect(request.price, equals(75.5));
      expect(request.description, equals('Test description'));
      expect(request.color, equals('#00FF00'));
      expect(request.acceptOnline, isFalse);
      expect(request.acceptNew, isTrue);
      expect(request.notificationOn, isFalse);
      expect(request.pointsRequirements, equals(5));
    });

    test('CreateServiceRequest should have default values', () {
      final request = CreateServiceRequest(
        title: 'Minimal Service',
        duration: 30,
        price: 25.0,
      );

      expect(request.title, equals('Minimal Service'));
      expect(request.duration, equals(30));
      expect(request.price, equals(25.0));
      expect(request.acceptOnline, isTrue); // Default value
      expect(request.acceptNew, isTrue); // Default value
      expect(request.notificationOn, isTrue); // Default value
      expect(request.description, isNull);
      expect(request.color, isNull);
      expect(request.pointsRequirements, isNull);
    });

    testWidgets('CreateServiceScreen should display without category field', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the CreateServiceScreen is displayed
      expect(find.byType(CreateServiceScreen), findsOneWidget);
      expect(find.byType(ServiceForm), findsOneWidget);

      // Verify that essential fields are present
      expect(find.text('Service Title *'), findsOneWidget);
      expect(find.text('Description'), findsOneWidget);
      expect(find.text('Duration (minutes) *'), findsOneWidget);
      expect(find.text('Price (\$) *'), findsOneWidget);

      // Verify that category field is NOT present
      expect(find.text('Category *'), findsNothing);
      expect(find.text('Select service category'), findsNothing);

      // Verify that other fields are still present
      expect(find.text('Points Required'), findsOneWidget);
      expect(find.text('Service Settings'), findsOneWidget);
      expect(find.text('Accept Online Bookings'), findsOneWidget);
      expect(find.text('Accept New Customers'), findsOneWidget);
      expect(find.text('Enable Notifications'), findsOneWidget);
    });

    testWidgets('Service form validation should work without category', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Try to save without filling required fields
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Should show validation errors for required fields (but not category)
      expect(find.text('Service title is required'), findsOneWidget);
      expect(find.text('Price is required'), findsOneWidget);
      
      // Should NOT show category validation error
      expect(find.text('Please select a category'), findsNothing);
    });

    testWidgets('Service form should accept valid input without category', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Fill in the required fields
      await tester.enterText(find.byType(TextFormField).first, 'Test Service');
      await tester.enterText(
        find.widgetWithText(TextFormField, '0.00'), 
        '50.00'
      );

      // The form should be valid without category selection
      // Note: We can't easily test the actual form submission in this test
      // environment due to provider dependencies, but we can verify the UI
      expect(find.text('Test Service'), findsOneWidget);
      expect(find.text('50.00'), findsOneWidget);
    });

    test('UpdateServiceRequest should still support categoryId for backward compatibility', () {
      // Even though we removed categoryId from CreateServiceRequest,
      // UpdateServiceRequest should still support it for editing existing services
      final updateRequest = UpdateServiceRequest(
        title: 'Updated Service',
        categoryId: 5, // Should still be supported
        price: 75.0,
      );

      expect(updateRequest.title, equals('Updated Service'));
      expect(updateRequest.categoryId, equals(5));
      expect(updateRequest.price, equals(75.0));

      final json = updateRequest.toJson();
      expect(json['title'], equals('Updated Service'));
      expect(json['categoryId'], equals(5));
      expect(json['price'], equals(75.0));
    });

    test('Service creation flow should work end-to-end without category', () {
      // Test the complete flow of creating a service request
      final request = CreateServiceRequest(
        title: 'Hair Cut',
        duration: 45,
        price: 30.0,
        description: 'Professional hair cutting service',
        color: '#FF5722',
        acceptOnline: true,
        acceptNew: true,
        notificationOn: true,
        pointsRequirements: null,
      );

      // Verify the request is properly formed
      expect(request.title, equals('Hair Cut'));
      expect(request.duration, equals(45));
      expect(request.price, equals(30.0));

      // Verify JSON serialization works
      final json = request.toJson();
      expect(json.containsKey('categoryId'), isFalse);
      expect(json['title'], equals('Hair Cut'));

      // Verify deserialization works
      final recreatedRequest = CreateServiceRequest.fromJson(json);
      expect(recreatedRequest.title, equals(request.title));
      expect(recreatedRequest.duration, equals(request.duration));
      expect(recreatedRequest.price, equals(request.price));
    });
  });
}
