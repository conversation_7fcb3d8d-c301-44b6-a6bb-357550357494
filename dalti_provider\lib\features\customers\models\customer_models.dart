import 'package:freezed_annotation/freezed_annotation.dart';

part 'customer_models.freezed.dart';
part 'customer_models.g.dart';

/// Customer model representing a business customer
@freezed
class Customer with _$Customer {
  const factory Customer({
    required String id,
    required String firstName,
    required String lastName,
    String? email, // Made optional to match backend
    @JsonKey(name: 'mobileNumber')
    String? phoneNumber, // Made optional to match backend (can be null)
    String? nationalId, // Added to match backend
    DateTime? dateOfBirth,
    String? address,
    String? city,
    String? wilaya,
    String? notes,
    @Default([]) List<String> tags,
    required DateTime createdAt,
    DateTime? updatedAt,
    @J<PERSON><PERSON>ey(name: 'appointmentCount')
    @Default(0)
    int totalAppointments, // Map appointmentCount to totalAppointments
    @Default(0.0) double totalSpent,
    DateTime? lastAppointmentDate,
    @Default(CustomerStatus.active) CustomerStatus status,
    CustomerPreferences? preferences,
  }) = _Customer;

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);
}

/// Customer status enum
enum CustomerStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('blocked')
  blocked,
}

/// Customer preferences for notifications and communication
@freezed
class CustomerPreferences with _$CustomerPreferences {
  const factory CustomerPreferences({
    @Default(true) bool emailNotifications,
    @Default(true) bool smsNotifications,
    @Default(true) bool appointmentReminders,
    @Default(true) bool promotionalMessages,
    String? preferredLanguage,
    String? preferredContactMethod,
  }) = _CustomerPreferences;

  factory CustomerPreferences.fromJson(Map<String, dynamic> json) =>
      _$CustomerPreferencesFromJson(json);
}

/// Customer appointment history item
@freezed
class CustomerAppointment with _$CustomerAppointment {
  const factory CustomerAppointment({
    required String id,
    required String customerId,
    required String serviceId,
    required String serviceName,
    required DateTime scheduledTime,
    required AppointmentStatus status,
    String? locationId,
    String? locationName,
    String? providerId,
    String? providerName,
    double? price,
    int? duration, // in minutes
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _CustomerAppointment;

  factory CustomerAppointment.fromJson(Map<String, dynamic> json) =>
      _$CustomerAppointmentFromJson(json);
}

/// Appointment status enum
enum AppointmentStatus {
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('canceled')
  canceled,
  @JsonValue('no_show')
  noShow,
}

/// Customer search filters
@freezed
class CustomerSearchFilters with _$CustomerSearchFilters {
  const factory CustomerSearchFilters({
    String? searchQuery,
    CustomerStatus? status,
    String? wilaya,
    DateTime? createdAfter,
    DateTime? createdBefore,
    @Default(0) int minAppointments,
    @Default(0.0) double minSpent,
    List<String>? tags,
    @Default('firstName') String sortBy,
    @Default(true) bool ascending,
  }) = _CustomerSearchFilters;

  factory CustomerSearchFilters.fromJson(Map<String, dynamic> json) =>
      _$CustomerSearchFiltersFromJson(json);
}

/// Customer list response from API
@freezed
class CustomersResponse with _$CustomersResponse {
  const factory CustomersResponse({
    required bool success,
    @Default([]) List<Customer> customers,
    @Default(0) int totalCount,
    @Default(1) int currentPage,
    @Default(20) int pageSize,
    @Default(0) int totalPages,
    CustomerError? error,
  }) = _CustomersResponse;

  factory CustomersResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomersResponseFromJson(json);
}

/// Single customer response from API
@freezed
class CustomerResponse with _$CustomerResponse {
  const factory CustomerResponse({
    required bool success,
    Customer? customer,
    CustomerError? error,
  }) = _CustomerResponse;

  factory CustomerResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerResponseFromJson(json);
}

/// Customer appointments response from API
@freezed
class CustomerAppointmentsResponse with _$CustomerAppointmentsResponse {
  const factory CustomerAppointmentsResponse({
    required bool success,
    @Default([]) List<CustomerAppointment> appointments,
    @Default(0) int totalCount,
    CustomerError? error,
  }) = _CustomerAppointmentsResponse;

  factory CustomerAppointmentsResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerAppointmentsResponseFromJson(json);
}

/// Customer operation error
@freezed
class CustomerError with _$CustomerError {
  const factory CustomerError({
    required String code,
    required String message,
    String? details,
  }) = _CustomerError;

  factory CustomerError.fromJson(Map<String, dynamic> json) =>
      _$CustomerErrorFromJson(json);
}

/// Customer creation/update request
@freezed
class CustomerRequest with _$CustomerRequest {
  const factory CustomerRequest({
    required String firstName,
    required String lastName,
    String? mobileNumber, // Made optional to match backend
    String? email,
    String? nationalId,
    String? notes,
    // Additional fields for internal use (not sent to backend)
    DateTime? dateOfBirth,
    String? address,
    String? city,
    String? wilaya,
    @Default([]) List<String> tags,
    CustomerPreferences? preferences,
  }) = _CustomerRequest;

  factory CustomerRequest.fromJson(Map<String, dynamic> json) =>
      _$CustomerRequestFromJson(json);
}

/// Extension for CustomerRequest to add backend-specific JSON serialization
extension CustomerRequestExtension on CustomerRequest {
  /// Custom toJson that only includes fields expected by the backend
  Map<String, dynamic> toBackendJson() {
    final json = <String, dynamic>{
      'firstName': firstName,
      'lastName': lastName,
      'mobileNumber': mobileNumber,
    };

    // Add optional fields only if they have values
    if (email?.isNotEmpty == true) {
      json['email'] = email;
    }
    if (nationalId?.isNotEmpty == true) {
      json['nationalId'] = nationalId;
    }
    if (notes?.isNotEmpty == true) {
      json['notes'] = notes;
    }

    return json;
  }
}

/// Customer statistics for dashboard
@freezed
class CustomerStats with _$CustomerStats {
  const factory CustomerStats({
    @Default(0) int totalCustomers,
    @Default(0) int newCustomersThisMonth,
    @Default(0) int activeCustomers,
    @Default(0.0) double averageSpentPerCustomer,
    @Default(0.0) double customerRetentionRate,
    @Default([]) List<CustomersByWilaya> customersByWilaya,
  }) = _CustomerStats;

  factory CustomerStats.fromJson(Map<String, dynamic> json) =>
      _$CustomerStatsFromJson(json);
}

/// Customers grouped by wilaya
@freezed
class CustomersByWilaya with _$CustomersByWilaya {
  const factory CustomersByWilaya({
    required String wilaya,
    required int count,
    @Default(0.0) double percentage,
  }) = _CustomersByWilaya;

  factory CustomersByWilaya.fromJson(Map<String, dynamic> json) =>
      _$CustomersByWilayaFromJson(json);
}
