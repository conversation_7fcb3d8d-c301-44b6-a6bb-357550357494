import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../components/welcome/welcome_step_widget.dart';
import '../providers/wizard_navigation_provider.dart';
import '../models/onboarding_models.dart';
import '../../../core/theme/theme_provider.dart';

/// Welcome step for the onboarding wizard
/// First step that introduces users to the setup process
class WelcomeStep extends ConsumerWidget {
  const WelcomeStep({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final navigationState = ref.watch(wizardNavigationProvider);
    final wizardNotifier = ref.read(wizardNavigationProvider.notifier);

    return Scaffold(
      backgroundColor: context.colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with minimal branding
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.colors.surface,
                border: Border(
                  bottom: BorderSide(
                    color: context.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  // <PERSON><PERSON> logo/icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.medical_services,
                      color: context.colors.onPrimary,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // App name
                  Text(
                    'Dalti Provider',
                    style: context.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.colors.primary,
                    ),
                  ),

                  const Spacer(),

                  // Setup indicator
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: context.colors.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      'Setup',
                      style: context.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: context.colors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Welcome content
            Expanded(
              child: WelcomeStepWidget(
                onGetStarted: () => _handleGetStarted(wizardNotifier),
                enabled: !navigationState.isLoading,
              ),
            ),

            // Footer with navigation
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.colors.surface,
                border: Border(
                  top: BorderSide(
                    color: context.colors.outline.withOpacity(0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Skip setup (optional)
                  TextButton(
                    onPressed:
                        navigationState.isLoading
                            ? null
                            : () => _handleSkipSetup(context),
                    child: Text(
                      'Skip for now',
                      style: TextStyle(
                        color: context.colors.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Get started button
                  ElevatedButton(
                    onPressed:
                        navigationState.isLoading
                            ? null
                            : () => _handleGetStarted(wizardNotifier),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        navigationState.isLoading
                            ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  context.colors.onPrimary,
                                ),
                              ),
                            )
                            : Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'Get Started',
                                  style: TextStyle(fontWeight: FontWeight.w600),
                                ),
                                const SizedBox(width: 8),
                                Icon(
                                  Icons.arrow_forward,
                                  size: 18,
                                  color: context.colors.onPrimary,
                                ),
                              ],
                            ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleGetStarted(WizardNavigationNotifier wizardNotifier) {
    // Mark welcome step as completed and move to business profile
    wizardNotifier.markStepCompleted(OnboardingStep.welcome, true);
    wizardNotifier.goToNextStep();
  }

  void _handleSkipSetup(BuildContext context) {
    // Show confirmation dialog for skipping setup
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Skip Setup?'),
            content: const Text(
              'You can complete your business setup later from the dashboard. '
              'However, some features may be limited until setup is complete.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Continue Setup'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Navigate to dashboard or main app
                  _navigateToMainApp(context);
                },
                child: const Text('Skip for Now'),
              ),
            ],
          ),
    );
  }

  void _navigateToMainApp(BuildContext context) {
    // This would navigate to the main app dashboard
    // For now, we'll just show a placeholder
    Navigator.of(context).pushReplacementNamed('/dashboard');
  }
}

/// Welcome step wrapper that can be used in the main wizard
class WelcomeStepWrapper extends ConsumerWidget {
  final VoidCallback? onNext;
  final bool enabled;

  const WelcomeStepWrapper({super.key, this.onNext, this.enabled = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return WelcomeStepWidget(
      onGetStarted: enabled ? onNext : null,
      enabled: enabled,
    );
  }
}
