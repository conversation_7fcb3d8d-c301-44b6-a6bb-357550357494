import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/notification_service.dart';
import 'app_providers.dart';

part 'notification_provider.g.dart';

/// Provider for notification service initialization (without FCM)
@riverpod
Future<void> initializeNotifications(Ref ref) async {
  final apiService = ref.watch(notificationApiServiceProvider);
  await NotificationService.initialize(apiService: apiService);
}

/// Provider for getting device FCM token
@riverpod
Future<String?> deviceToken(Ref ref) async {
  return await NotificationService.getDeviceToken();
}

/// Provider for checking notification permissions
@riverpod
Future<bool> notificationPermissions(Ref ref) async {
  return await NotificationService.areNotificationsEnabled();
}

/// Provider for managing notification subscriptions
@riverpod
class NotificationSubscriptions extends _$NotificationSubscriptions {
  @override
  Set<String> build() {
    return <String>{};
  }

  /// Subscribe to provider-specific topics
  Future<void> subscribeToProviderTopics(String providerId) async {
    await NotificationService.subscribeToProviderTopics(providerId);

    // Update state with subscribed topics
    final providerTopics = {
      'provider_$providerId',
      'appointments',
      'bookings',
      'messages',
      'general',
    };

    state = {...state, ...providerTopics};
  }

  /// Unsubscribe from provider-specific topics
  Future<void> unsubscribeFromProviderTopics(String providerId) async {
    await NotificationService.unsubscribeFromProviderTopics(providerId);

    // Remove provider topics from state
    final providerTopics = {
      'provider_$providerId',
      'appointments',
      'bookings',
      'messages',
      'general',
    };

    state = state.where((topic) => !providerTopics.contains(topic)).toSet();
  }

  /// Subscribe to a specific topic
  Future<void> subscribeToTopic(String topic) async {
    await NotificationService.subscribeToTopic(topic);
    state = {...state, topic};
  }

  /// Unsubscribe from a specific topic
  Future<void> unsubscribeFromTopic(String topic) async {
    await NotificationService.unsubscribeFromTopic(topic);
    state = state.where((t) => t != topic).toSet();
  }

  /// Register device token with backend
  Future<bool> registerDeviceToken(String userId) async {
    final token = await NotificationService.getDeviceToken();
    if (token != null) {
      return await NotificationService.registerDeviceToken(userId, token);
    }
    return false;
  }

  /// Unregister device token from backend
  Future<bool> unregisterDeviceToken(String userId) async {
    final token = await NotificationService.getDeviceToken();
    if (token != null) {
      return await NotificationService.unregisterDeviceToken(userId, token);
    }
    return false;
  }

  /// Request notification permissions
  Future<bool> requestPermissions({String? userId}) async {
    final granted = await NotificationService.requestNotificationPermissions(
      userId: userId,
    );

    // Refresh the permissions provider
    ref.invalidate(notificationPermissionsProvider);

    return granted;
  }
}
