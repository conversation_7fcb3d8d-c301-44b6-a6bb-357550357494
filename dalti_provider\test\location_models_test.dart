import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/locations/models/location_models.dart';

void main() {
  group('Location Models Tests', () {
    test('Location model can be created and serialized', () {
      const location = Location(
        id: 1,
        name: 'Main Clinic',
        address: '123 Main St',
        city: 'Toronto',
        isMobileHidden: false,
        parking: true,
        elevator: false,
        handicapAccess: true,
      );

      expect(location.id, equals(1));
      expect(location.name, equals('Main Clinic'));
      expect(location.address, equals('123 Main St'));
      expect(location.city, equals('Toronto'));
      expect(location.parking, isTrue);
      expect(location.elevator, isFalse);
      expect(location.handicapAccess, isTrue);
      expect(location.isMobileHidden, isFalse);
    });

    test('Location fromJson works correctly', () {
      final json = {
        'id': 1,
        'name': 'Main Clinic',
        'address': '123 Main St',
        'city': 'Toronto',
        'isMobileHidden': false,
        'parking': true,
        'elevator': false,
        'handicapAccess': true,
      };

      final location = Location.fromJson(json);

      expect(location.id, equals(1));
      expect(location.name, equals('Main Clinic'));
      expect(location.address, equals('123 Main St'));
      expect(location.city, equals('Toronto'));
      expect(location.parking, isTrue);
      expect(location.elevator, isFalse);
      expect(location.handicapAccess, isTrue);
      expect(location.isMobileHidden, isFalse);
    });

    test('Location toJson works correctly', () {
      const location = Location(
        id: 1,
        name: 'Main Clinic',
        address: '123 Main St',
        city: 'Toronto',
        isMobileHidden: false,
        parking: true,
        elevator: false,
        handicapAccess: true,
      );

      final json = location.toJson();

      expect(json['id'], equals(1));
      expect(json['name'], equals('Main Clinic'));
      expect(json['address'], equals('123 Main St'));
      expect(json['city'], equals('Toronto'));
      expect(json['parking'], isTrue);
      expect(json['elevator'], isFalse);
      expect(json['handicapAccess'], isTrue);
      expect(json['isMobileHidden'], isFalse);
    });

    test('CreateLocationRequest works correctly', () {
      const request = CreateLocationRequest(
        name: 'New Clinic',
        address: '456 Oak St',
        city: 'Vancouver',
        country: 'Canada',
        postalCode: 'V6B 1A1',
        latitude: 49.2827,
        longitude: -123.1207,
        parking: true,
        elevator: true,
        handicapAccess: false,
      );

      final json = request.toJson();

      expect(json['name'], equals('New Clinic'));
      expect(json['address'], equals('456 Oak St'));
      expect(json['city'], equals('Vancouver'));
      expect(json['country'], equals('Canada'));
      expect(json['postalCode'], equals('V6B 1A1'));
      expect(json['latitude'], equals(49.2827));
      expect(json['longitude'], equals(-123.1207));
      expect(json['parking'], isTrue);
      expect(json['elevator'], isTrue);
      expect(json['handicapAccess'], isFalse);
    });

    test('UpdateLocationRequest only includes non-null fields', () {
      const request = UpdateLocationRequest(
        name: 'Updated Clinic',
        parking: false,
        // Other fields are null and should not be included
      );

      final json = request.toJson();

      expect(json['name'], equals('Updated Clinic'));
      expect(json['parking'], isFalse);
      expect(json.containsKey('address'), isFalse);
      expect(json.containsKey('city'), isFalse);
      expect(json.containsKey('elevator'), isFalse);
      expect(json.containsKey('handicapAccess'), isFalse);
    });

    test('LocationResponse handles single location correctly', () {
      final json = {
        'success': true,
        'message': 'Location retrieved successfully',
        'data': {
          'id': 1,
          'name': 'Main Clinic',
          'address': '123 Main St',
          'city': 'Toronto',
          'isMobileHidden': false,
          'parking': true,
          'elevator': false,
          'handicapAccess': true,
        },
      };

      final response = LocationResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.message, equals('Location retrieved successfully'));
      expect(response.location, isNotNull);
      expect(response.location!.name, equals('Main Clinic'));
      expect(response.locations, isNull);
    });

    test('LocationResponse handles list of locations correctly', () {
      final json = {
        'success': true,
        'message': 'Locations retrieved successfully',
        'data': [
          {
            'id': 1,
            'name': 'Main Clinic',
            'address': '123 Main St',
            'city': 'Toronto',
            'isMobileHidden': false,
            'parking': true,
            'elevator': false,
            'handicapAccess': true,
          },
          {
            'id': 2,
            'name': 'Branch Clinic',
            'address': '456 Oak St',
            'city': 'Vancouver',
            'isMobileHidden': false,
            'parking': false,
            'elevator': true,
            'handicapAccess': true,
          },
        ],
      };

      final response = LocationResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.message, equals('Locations retrieved successfully'));
      expect(response.location, isNull);
      expect(response.locations, isNotNull);
      expect(response.locations!.length, equals(2));
      expect(response.locations![0].name, equals('Main Clinic'));
      expect(response.locations![1].name, equals('Branch Clinic'));
    });

    test('Location copyWith works correctly', () {
      const original = Location(
        id: 1,
        name: 'Main Clinic',
        address: '123 Main St',
        city: 'Toronto',
        isMobileHidden: false,
        parking: true,
        elevator: false,
        handicapAccess: true,
      );

      final updated = original.copyWith(
        name: 'Updated Clinic',
        parking: false,
      );

      expect(updated.id, equals(1)); // Unchanged
      expect(updated.name, equals('Updated Clinic')); // Changed
      expect(updated.address, equals('123 Main St')); // Unchanged
      expect(updated.city, equals('Toronto')); // Unchanged
      expect(updated.parking, isFalse); // Changed
      expect(updated.elevator, isFalse); // Unchanged
      expect(updated.handicapAccess, isTrue); // Unchanged
      expect(updated.isMobileHidden, isFalse); // Unchanged
    });

    test('Location equality works correctly', () {
      const location1 = Location(
        id: 1,
        name: 'Main Clinic',
        address: '123 Main St',
        city: 'Toronto',
        isMobileHidden: false,
        parking: true,
        elevator: false,
        handicapAccess: true,
      );

      const location2 = Location(
        id: 1,
        name: 'Main Clinic',
        address: '123 Main St',
        city: 'Toronto',
        isMobileHidden: false,
        parking: true,
        elevator: false,
        handicapAccess: true,
      );

      const location3 = Location(
        id: 2,
        name: 'Different Clinic',
        address: '456 Oak St',
        city: 'Vancouver',
        isMobileHidden: false,
        parking: false,
        elevator: true,
        handicapAccess: false,
      );

      expect(location1, equals(location2));
      expect(location1, isNot(equals(location3)));
      expect(location1.hashCode, equals(location2.hashCode));
      expect(location1.hashCode, isNot(equals(location3.hashCode)));
    });
  });
}
