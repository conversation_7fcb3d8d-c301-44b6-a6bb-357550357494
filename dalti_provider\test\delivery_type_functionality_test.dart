import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/onboarding/components/service/delivery_type_widget.dart';

void main() {
  group('Delivery Type Multi-Wilaya Selection Tests', () {
    test('should show wilaya selection when delivery type is at_customer', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: [],
      );
      
      // Should require regions for customer delivery
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isFalse); // Invalid because no regions selected
    });

    test('should show wilaya selection when delivery type is both', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'both',
        servedRegions: [],
      );
      
      // Should require regions for both delivery types
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isFalse); // Invalid because no regions selected
    });

    test('should NOT show wilaya selection when delivery type is at_location', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'at_location',
        servedRegions: [],
      );
      
      // Should NOT require regions for location-only delivery
      expect(deliveryData.requiresRegions, isFalse);
      expect(deliveryData.isValid, isTrue); // Valid even with no regions
    });

    test('should be valid when wilayas are selected for customer delivery', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: ['Alger', 'Oran', 'Constantine'],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isTrue); // Valid because regions are selected
      expect(deliveryData.servedRegions.length, equals(3));
      expect(deliveryData.servedRegions, contains('Alger'));
      expect(deliveryData.servedRegions, contains('Oran'));
      expect(deliveryData.servedRegions, contains('Constantine'));
    });

    test('should be valid when wilayas are selected for both delivery types', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'both',
        servedRegions: ['Blida', 'Annaba'],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isTrue); // Valid because regions are selected
      expect(deliveryData.servedRegions.length, equals(2));
      expect(deliveryData.servedRegions, contains('Blida'));
      expect(deliveryData.servedRegions, contains('Annaba'));
    });

    test('should handle single wilaya selection', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: ['Alger'],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isTrue); // Valid with just one region
      expect(deliveryData.servedRegions.length, equals(1));
      expect(deliveryData.servedRegions.first, equals('Alger'));
    });

    test('should handle multiple wilaya selection', () {
      const deliveryData = DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: [
          'Alger', 'Oran', 'Constantine', 'Annaba', 'Blida',
          'Batna', 'Sétif', 'Tlemcen', 'Béjaïa', 'Skikda'
        ],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.isValid, isTrue);
      expect(deliveryData.servedRegions.length, equals(10));
      
      // Verify all wilayas are present
      expect(deliveryData.servedRegions, contains('Alger'));
      expect(deliveryData.servedRegions, contains('Oran'));
      expect(deliveryData.servedRegions, contains('Constantine'));
      expect(deliveryData.servedRegions, contains('Annaba'));
      expect(deliveryData.servedRegions, contains('Blida'));
    });

    test('should clear regions when switching from customer to location delivery', () {
      // Start with customer delivery and regions
      var deliveryData = const DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: ['Alger', 'Oran'],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.servedRegions.length, equals(2));
      
      // Switch to location-only delivery (regions should be cleared)
      deliveryData = deliveryData.copyWith(
        deliveryType: 'at_location',
        servedRegions: [], // This would be handled by the widget
      );
      
      expect(deliveryData.requiresRegions, isFalse);
      expect(deliveryData.servedRegions.length, equals(0));
      expect(deliveryData.isValid, isTrue);
    });

    test('should maintain regions when switching between customer and both delivery', () {
      // Start with customer delivery and regions
      var deliveryData = const DeliveryTypeData(
        deliveryType: 'at_customer',
        servedRegions: ['Alger', 'Oran', 'Constantine'],
      );
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.servedRegions.length, equals(3));
      
      // Switch to both delivery (regions should be maintained)
      deliveryData = deliveryData.copyWith(deliveryType: 'both');
      
      expect(deliveryData.requiresRegions, isTrue);
      expect(deliveryData.servedRegions.length, equals(3));
      expect(deliveryData.isValid, isTrue);
      expect(deliveryData.servedRegions, contains('Alger'));
      expect(deliveryData.servedRegions, contains('Oran'));
      expect(deliveryData.servedRegions, contains('Constantine'));
    });
  });
}
