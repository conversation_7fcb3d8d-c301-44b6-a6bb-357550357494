import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/providers/realtime_providers.dart';
import '../../auth/providers/auth_provider.dart';
import '../models/message_models.dart';
import '../providers/message_provider.dart';
import '../widgets/message_input.dart';

class ConversationScreen extends ConsumerStatefulWidget {
  final String conversationId;

  const ConversationScreen({super.key, required this.conversationId});

  @override
  ConsumerState<ConversationScreen> createState() => _ConversationScreenState();
}

class _ConversationScreenState extends ConsumerState<ConversationScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  String? _customerName; // Store customer name for message identification

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Auto-scroll to bottom and mark messages as read after initial load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();

      // Mark unread messages as read after a short delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _markUnreadMessagesAsRead();
      });
    });
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _markUnreadMessagesAsRead() {
    // Get current messages from realtime provider
    final messagesAsync = ref.read(
      realtimeMessagesProvider(widget.conversationId),
    );

    messagesAsync.whenData((messages) {
      // Get current user ID from auth provider
      final authData = ref.read(authNotifierProvider);
      final currentUserId = authData.user?.id;

      if (currentUserId == null) return;

      // Find unread messages that are not from the current user
      final unreadMessages =
          messages.where((message) {
            // Only mark messages as read if they are not from the current user
            // and their status is not already 'read'
            return message.status != MessageStatus.read &&
                message.senderId != currentUserId;
          }).toList();

      // Mark each unread message as read using the regular notifier
      for (final message in unreadMessages) {
        ref
            .read(messagesNotifierProvider(widget.conversationId).notifier)
            .markMessageAsRead(message.id);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more messages when near bottom (using regular notifier for pagination)
      ref
          .read(messagesNotifierProvider(widget.conversationId).notifier)
          .loadMoreMessages();
    }
  }

  @override
  Widget build(BuildContext context) {
    final conversationAsync = ref.watch(
      conversationProvider(widget.conversationId),
    );
    // Use realtime messages provider for automatic WebSocket updates
    final messagesAsync = ref.watch(
      realtimeMessagesProvider(widget.conversationId),
    );
    // Keep regular notifier for actions like sending messages
    final messagesState = ref.watch(
      messagesNotifierProvider(widget.conversationId),
    );

    return Scaffold(
      appBar: AppBar(
        elevation: 1,
        backgroundColor: context.colors.surface,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: Icon(Icons.arrow_back, color: context.colors.onSurface),
        ),
        title: conversationAsync.when(
          data: (conversation) {
            // Store customer name for message identification
            if (conversation != null) {
              _customerName = conversation.customerName;
            }

            return conversation != null
                ? Row(
                  children: [
                    // Customer avatar
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.orange,
                      child: Text(
                        _getCustomerInitials(conversation.customerName),
                        style: context.textTheme.titleSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Customer info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            conversation.customerName,
                            style: context.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: context.colors.onSurface,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (conversation.customerPhone != null)
                            Text(
                              conversation.customerPhone!,
                              style: context.textTheme.bodySmall?.copyWith(
                                color: context.colors.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ],
                )
                : const Text('Conversation');
          },
          loading: () => const Text('Loading...'),
          error: (_, __) => const Text('Error'),
        ),
        actions: [
          IconButton(
            onPressed: () => _refreshMessages(),
            icon: Icon(Icons.refresh, color: context.colors.onSurface),
            tooltip: 'Refresh',
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value),
            icon: Icon(Icons.more_vert, color: context.colors.onSurface),
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'archive',
                    child: ListTile(
                      leading: Icon(Icons.archive),
                      title: Text('Archive'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete),
                      title: Text('Delete'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(child: _buildMessagesList(messagesAsync, messagesState)),

          // Message input
          MessageInput(
            controller: _messageController,
            onSend: _sendMessage,
            isLoading: messagesState.isSending,
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(
    AsyncValue<List<Message>> messagesAsync,
    MessagesState state,
  ) {
    return messagesAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: context.colors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading messages',
                  style: context.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _refreshMessages,
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
      data: (messages) {
        if (messages.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 64,
                  color: context.colors.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text('No messages yet', style: context.textTheme.titleMedium),
                const SizedBox(height: 8),
                Text(
                  'Start the conversation by sending a message',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: _getItemCount(messages),
          itemBuilder: (context, index) {
            return _buildMessageItem(context, messages, index);
          },
        );
      },
    );
  }

  bool _isMessageFromProvider(Message message) {
    // Get the current user ID from the app state
    final currentUserId = ref.read(currentUserIdProvider);

    // If we have a current user ID, compare it with the message sender ID
    if (currentUserId != null) {
      return message.senderId == currentUserId;
    }

    // Use the stored customer name from the conversation header
    if (_customerName != null) {
      // If message sender name matches the customer name, it's from customer
      // Otherwise, it's from the provider
      return message.senderName != _customerName;
    }

    // Fallback: For the current demo, we know "dada nasro" is the customer
    // Any message not from "dada nasro" is from the provider
    return message.senderName != "dada nasro";
  }

  String _getCustomerInitials(String customerName) {
    final names = customerName.trim().split(' ');
    if (names.isEmpty) return 'U';
    if (names.length == 1) return names[0][0].toUpperCase();
    return '${names[0][0]}${names[names.length - 1][0]}'.toUpperCase();
  }

  int _getItemCount(List<Message> messages) {
    int count = 0;
    DateTime? lastDate;

    // Count messages and date headers
    for (final message in messages) {
      final messageDate = DateTime(
        message.timestamp.year,
        message.timestamp.month,
        message.timestamp.day,
      );

      if (lastDate == null || !_isSameDay(lastDate, messageDate)) {
        count++; // Date header
        lastDate = messageDate;
      }
      count++; // Message
    }

    return count;
  }

  Widget _buildMessageItem(
    BuildContext context,
    List<Message> messages,
    int index,
  ) {
    int messageIndex = 0;
    int currentIndex = 0;
    DateTime? lastDate;

    // Find the actual message for this index
    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];
      final messageDate = DateTime(
        message.timestamp.year,
        message.timestamp.month,
        message.timestamp.day,
      );

      // Check if we need a date header
      if (lastDate == null || !_isSameDay(lastDate, messageDate)) {
        if (currentIndex == index) {
          return _buildDateHeader(context, messageDate);
        }
        currentIndex++;
        lastDate = messageDate;
      }

      // Check if this is the message we want
      if (currentIndex == index) {
        messageIndex = i;
        break;
      }
      currentIndex++;
    }

    final message = messages[messageIndex];
    final isNextMessageFromSameSender =
        messageIndex < messages.length - 1 &&
        messages[messageIndex + 1].senderId == message.senderId;
    final isPreviousMessageFromSameSender =
        messageIndex > 0 &&
        messages[messageIndex - 1].senderId == message.senderId;

    return _buildMessageBubble(
      context,
      message,
      showSenderName: !isPreviousMessageFromSameSender,
      showTimestamp: !isNextMessageFromSameSender,
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  Widget _buildDateHeader(BuildContext context, DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    String dateText;
    if (_isSameDay(date, today)) {
      dateText = 'Today';
    } else if (_isSameDay(date, yesterday)) {
      dateText = 'Yesterday';
    } else {
      dateText =
          '${_getMonthName(date.month)} ${date.day.toString().padLeft(2, '0')}, ${date.year}';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: context.colors.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            dateText,
            style: context.textTheme.bodySmall?.copyWith(
              color: context.colors.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  Widget _buildMessageBubble(
    BuildContext context,
    Message message, {
    bool showSenderName = true,
    bool showTimestamp = true,
  }) {
    final isFromProvider = _isMessageFromProvider(message);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isFromProvider) ...[
            // Customer avatar
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.orange,
              child: Text(
                _getCustomerInitials(message.senderName),
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],

          // Message content
          Expanded(
            child: Column(
              crossAxisAlignment:
                  isFromProvider
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
              children: [
                // Sender name for customer messages
                if (showSenderName && !isFromProvider)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      message.senderName,
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colors.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                // Message bubble
                Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.75,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  decoration: BoxDecoration(
                    color:
                        isFromProvider
                            ? context.colors.primary
                            : context.colors.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Text(
                    message.content,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color:
                          isFromProvider
                              ? context.colors.onPrimary
                              : context.colors.onSurfaceVariant,
                    ),
                  ),
                ),

                // Timestamp and status
                if (showTimestamp)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatMessageTime(message.timestamp),
                          style: context.textTheme.bodySmall?.copyWith(
                            color: context.colors.onSurfaceVariant,
                          ),
                        ),
                        if (isFromProvider) ...[
                          const SizedBox(width: 4),
                          Icon(
                            _getStatusIcon(message.status),
                            size: 16,
                            color: context.colors.onSurfaceVariant,
                          ),
                        ],
                      ],
                    ),
                  ),
              ],
            ),
          ),

          if (isFromProvider) const SizedBox(width: 24),
        ],
      ),
    );
  }

  String _formatMessageTime(DateTime timestamp) {
    final hour = timestamp.hour;
    final minute = timestamp.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  IconData _getStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error_outline;
    }
  }

  void _refreshMessages() {
    // Invalidate the realtime provider to force a refresh
    ref.invalidate(realtimeMessagesProvider(widget.conversationId));

    // Also refresh the regular notifier for any pending actions
    ref
        .read(messagesNotifierProvider(widget.conversationId).notifier)
        .refreshMessages();

    // Scroll to bottom after refresh
    _scrollToBottom();
  }

  Future<void> _sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final request = SendMessageRequest(
      conversationId: widget.conversationId,
      type: MessageType.text,
      content: content.trim(),
    );

    final success = await ref
        .read(messagesNotifierProvider(widget.conversationId).notifier)
        .sendMessage(request);

    if (success) {
      _messageController.clear();
      // Scroll to bottom to show new message
      _scrollToBottom();
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to send message'),
            backgroundColor: context.colors.error,
          ),
        );
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'archive':
        _archiveConversation();
        break;
      case 'delete':
        _deleteConversation();
        break;
    }
  }

  Future<void> _archiveConversation() async {
    final success = await ref
        .read(conversationsNotifierProvider.notifier)
        .archiveConversation(widget.conversationId);

    if (mounted) {
      if (success) {
        context.pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Conversation archived'),
            backgroundColor: context.colors.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to archive conversation'),
            backgroundColor: context.colors.error,
          ),
        );
      }
    }
  }

  Future<void> _deleteConversation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Conversation'),
            content: const Text(
              'Are you sure you want to delete this conversation? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: context.colors.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(conversationsNotifierProvider.notifier)
          .deleteConversation(widget.conversationId);

      if (mounted) {
        if (success) {
          context.pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Conversation deleted'),
              backgroundColor: context.colors.primary,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Failed to delete conversation'),
              backgroundColor: context.colors.error,
            ),
          );
        }
      }
    }
  }
}
