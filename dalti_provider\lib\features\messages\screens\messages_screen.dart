import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/theme_provider.dart';
import '../../../core/providers/realtime_providers.dart';
import '../models/message_models.dart';
import '../providers/message_provider.dart';
import '../widgets/conversation_card.dart';

class MessagesScreen extends ConsumerStatefulWidget {
  const MessagesScreen({super.key});

  @override
  ConsumerState<MessagesScreen> createState() => _MessagesScreenState();
}

class _MessagesScreenState extends ConsumerState<MessagesScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // No need to manually load conversations - realtime provider handles this
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more conversations when near bottom
      ref.read(conversationsNotifierProvider.notifier).loadMoreConversations();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use realtime conversations provider for automatic WebSocket updates
    final conversationsAsync = ref.watch(realtimeConversationsProvider);
    // Keep regular notifier for actions like search and pagination
    final conversationsState = ref.watch(conversationsNotifierProvider);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Invalidate realtime provider to force refresh
          ref.invalidate(realtimeConversationsProvider);
          await ref
              .read(conversationsNotifierProvider.notifier)
              .refreshConversations();
          await ref
              .read(unreadMessagesCountNotifierProvider.notifier)
              .refreshUnreadCount();
        },
        color: context.colors.primary,
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            // Search and new conversation controls
            SliverToBoxAdapter(child: _buildSearchAndNewConversationBar()),

            // Conversations list
            _buildConversationsListSliver(
              conversationsAsync,
              conversationsState,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndNewConversationBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Search field
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchController.text.isNotEmpty
                        ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            setState(() {}); // Update UI to hide clear button
                            // Clear search and show all conversations
                            ref
                                .read(conversationsNotifierProvider.notifier)
                                .loadConversations();
                          },
                          icon: const Icon(Icons.clear),
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (query) {
                setState(() {}); // Trigger rebuild to show/hide clear button
                if (query.isNotEmpty) {
                  ref
                      .read(conversationsNotifierProvider.notifier)
                      .searchConversations(query);
                } else {
                  // Clear search and show all conversations
                  ref
                      .read(conversationsNotifierProvider.notifier)
                      .loadConversations();
                }
              },
            ),
          ),
          const SizedBox(width: 12),
          // New conversation button
          Container(
            decoration: BoxDecoration(
              color: context.colors.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () => context.push('/messages/new'),
              icon: Icon(Icons.add, color: context.colors.onPrimary),
              tooltip: 'Start new conversation',
              padding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationsList(ConversationsState state) {
    if (state.isLoading && state.conversations.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null && state.conversations.isEmpty) {
      return ListView(
        padding: const EdgeInsets.all(16),
        children: [
          SizedBox(height: MediaQuery.of(context).size.height * 0.3),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: context.colors.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading conversations',
                  style: context.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  state.error!,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _refreshConversations,
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ],
      );
    }

    if (state.conversations.isEmpty) {
      return ListView(
        padding: const EdgeInsets.all(16),
        children: [
          SizedBox(height: MediaQuery.of(context).size.height * 0.3),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 64,
                  color: context.colors.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  'No conversations yet',
                  style: context.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'Start a conversation with your customers',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => context.push('/messages/new'),
                  icon: const Icon(Icons.add_comment),
                  label: const Text('Start conversation'),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: state.conversations.length + (state.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.conversations.length) {
          // Loading indicator for pagination
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final conversation = state.conversations[index];
        return ConversationCard(
          conversation: conversation,
          onTap: () => _openConversation(context, conversation),
          onArchive: () => _archiveConversation(conversation.id),
          onDelete: () => _deleteConversation(conversation.id),
        );
      },
    );
  }

  Widget _buildConversationsListSliver(
    AsyncValue<List<Conversation>> conversationsAsync,
    ConversationsState state,
  ) {
    return conversationsAsync.when(
      loading:
          () => const SliverFillRemaining(
            child: Center(child: CircularProgressIndicator()),
          ),
      error:
          (error, stackTrace) => SliverFillRemaining(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: context.colors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading conversations',
                    style: context.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _refreshConversations,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
      data: (conversations) {
        if (conversations.isEmpty) {
          return SliverFillRemaining(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.chat_bubble_outline,
                    size: 64,
                    color: context.colors.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No conversations yet',
                    style: context.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Start a conversation with your customers',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => context.push('/messages/new'),
                    icon: const Icon(Icons.add_comment),
                    label: const Text('Start conversation'),
                  ),
                ],
              ),
            ),
          );
        }

        return SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            if (index >= conversations.length) {
              // Loading indicator for pagination (from regular state)
              return state.hasMore
                  ? const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(child: CircularProgressIndicator()),
                  )
                  : const SizedBox.shrink();
            }

            final conversation = conversations[index];
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: ConversationCard(
                conversation: conversation,
                onTap: () => _openConversation(context, conversation),
                onArchive: () => _archiveConversation(conversation.id),
                onDelete: () => _deleteConversation(conversation.id),
              ),
            );
          }, childCount: conversations.length + (state.hasMore ? 1 : 0)),
        );
      },
    );
  }

  void _refreshConversations() {
    // Invalidate realtime provider to force refresh
    ref.invalidate(realtimeConversationsProvider);
    ref.read(conversationsNotifierProvider.notifier).refreshConversations();
    ref.read(unreadMessagesCountNotifierProvider.notifier).refreshUnreadCount();
  }

  void _openConversation(BuildContext context, Conversation conversation) {
    context.push('/messages/conversation/${conversation.id}');
  }

  Future<void> _archiveConversation(String conversationId) async {
    final success = await ref
        .read(conversationsNotifierProvider.notifier)
        .archiveConversation(conversationId);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? 'Conversation archived'
                : 'Failed to archive conversation',
          ),
          backgroundColor:
              success ? context.colors.primary : context.colors.error,
        ),
      );
    }
  }

  Future<void> _deleteConversation(String conversationId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Conversation'),
            content: const Text(
              'Are you sure you want to delete this conversation? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: context.colors.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(conversationsNotifierProvider.notifier)
          .deleteConversation(conversationId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Conversation deleted'
                  : 'Failed to delete conversation',
            ),
            backgroundColor:
                success ? context.colors.primary : context.colors.error,
          ),
        );
      }
    }
  }
}
