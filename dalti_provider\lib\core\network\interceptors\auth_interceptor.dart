import 'package:dio/dio.dart';
import '../../auth/jwt_service.dart';

/// Interceptor to handle authentication tokens with JWT service
class AuthInterceptor extends Interceptor {
  final JwtService _jwtService;

  AuthInterceptor(this._jwtService);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    print('[AuthInterceptor] Processing request: ${options.method} ${options.path}');

    // Skip auth for certain endpoints
    if (_shouldSkipAuth(options.path)) {
      print('[AuthInterceptor] Skipping auth for path: ${options.path}');
      return handler.next(options);
    }

    try {
      print('[AuthInterceptor] Checking for valid token...');

      // Ensure we have a valid token
      final hasValidToken = await _jwtService.ensureValidToken();
      print('[AuthInterceptor] Has valid token: $hasValidToken');

      if (hasValidToken) {
        final authHeader = _jwtService.getAuthorizationHeader();
        print('[AuthInterceptor] Auth header: $authHeader');

        if (authHeader != null) {
          options.headers['Authorization'] = authHeader;
          print('[AuthInterceptor] Added Authorization header to request');
        } else {
          print('[AuthInterceptor] Auth header is null!');
        }
      } else {
        print('[AuthInterceptor] No valid token available for request');
      }
    } catch (e) {
      print('[AuthInterceptor] Error in auth interceptor: $e');
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final statusCode = err.response?.statusCode;

    // Handle authentication errors
    if (statusCode == 401 || statusCode == 403) {
      print('[AuthInterceptor] Authentication error detected: $statusCode');

      // Try to refresh token once
      if (await _jwtService.refreshToken()) {
        // Retry the original request with new token
        try {
          final authHeader = _jwtService.getAuthorizationHeader();
          if (authHeader != null) {
            err.requestOptions.headers['Authorization'] = authHeader;
          }

          final dio = Dio();
          final response = await dio.fetch(err.requestOptions);
          return handler.resolve(response);
        } catch (retryError) {
          print('[AuthInterceptor] Retry failed: $retryError');
        }
      }

      // If refresh failed or retry failed, clear token
      await _jwtService.clearToken();
    }

    handler.next(err);
  }

  /// Check if authentication should be skipped for this endpoint
  bool _shouldSkipAuth(String path) {
    const skipAuthPaths = [
      '/api/auth/provider/login',
      '/api/auth/request-email-otp',
      '/api/auth/provider/verify-otp-register',
      '/api/auth/refresh-token',
      '/health',
    ];

    return skipAuthPaths.any((skipPath) => path.contains(skipPath));
  }
}
