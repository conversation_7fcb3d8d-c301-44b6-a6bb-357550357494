import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import '../../schedules/models/opening_hours_models.dart';
import '../../onboarding/widgets/opening_hours_widget.dart' as widget;

/// Queue model based on the API response structure
class Queue extends Equatable {
  final int id;
  final String title;
  final bool isActive;
  final int sProvidingPlaceId; // Location ID where queue operates
  final List<QueueService> services;
  final OpeningHours? openingHours; // Opening hours from API
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Queue({
    required this.id,
    required this.title,
    required this.isActive,
    required this.sProvidingPlaceId,
    required this.services,
    this.openingHours,
    this.createdAt,
    this.updatedAt,
  });

  factory Queue.fromJson(Map<String, dynamic> json) {
    final queueId = json['id'] as int;
    return Queue(
      id: queueId,
      title: json['title'] as String,
      isActive: json['isActive'] as bool,
      sProvidingPlaceId: json['sProvidingPlaceId'] as int,
      services:
          (json['services'] as List<dynamic>?)
              ?.map(
                (service) => QueueService.fromJson(
                  service as Map<String, dynamic>,
                  queueId: queueId,
                ),
              )
              .toList() ??
          [],
      openingHours:
          json['openingHours'] != null
              ? OpeningHours.fromJson(json['openingHours'] as List<dynamic>)
              : null,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'] as String)
              : null,
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isActive': isActive,
      'sProvidingPlaceId': sProvidingPlaceId,
      'services': services.map((service) => service.toJson()).toList(),
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  Queue copyWith({
    int? id,
    String? title,
    bool? isActive,
    int? sProvidingPlaceId,
    List<QueueService>? services,
    OpeningHours? openingHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Queue(
      id: id ?? this.id,
      title: title ?? this.title,
      isActive: isActive ?? this.isActive,
      sProvidingPlaceId: sProvidingPlaceId ?? this.sProvidingPlaceId,
      services: services ?? this.services,
      openingHours: openingHours ?? this.openingHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    isActive,
    sProvidingPlaceId,
    services,
    openingHours,
    createdAt,
    updatedAt,
  ];

  /// Get location ID (alias for sProvidingPlaceId)
  int get locationId => sProvidingPlaceId;

  /// Get service IDs assigned to this queue
  List<int> get serviceIds => services.map((s) => s.serviceId).toList();

  /// Check if queue has any services assigned
  bool get hasServices => services.isNotEmpty;

  /// Get service count
  int get serviceCount => services.length;

  /// Check if a specific service is assigned to this queue
  bool hasService(int serviceId) {
    return services.any((s) => s.serviceId == serviceId);
  }

  /// Get status text
  String get statusText => isActive ? 'Active' : 'Inactive';

  /// Get status color indicator
  String get statusColor => isActive ? 'green' : 'grey';

  /// Convert opening hours to widget format
  Map<String, List<widget.TimeSlot>> getOpeningHoursForWidget() {
    print('[Queue] Converting opening hours to widget format');
    print('[Queue] openingHours is null: ${openingHours == null}');

    if (openingHours == null) {
      print('[Queue] No opening hours data, returning empty map');
      return {};
    }

    print('[Queue] Opening hours days count: ${openingHours!.days.length}');
    final Map<String, List<widget.TimeSlot>> widgetData = {};

    for (final daySchedule in openingHours!.days) {
      print(
        '[Queue] Processing day: ${daySchedule.dayOfWeek}, isActive: ${daySchedule.isActive}, hours count: ${daySchedule.hours.length}',
      );

      // Always add the day to widget data, even if inactive
      if (daySchedule.isActive && daySchedule.hours.isNotEmpty) {
        // Convert API TimeSlot format to widget TimeSlot format
        final List<widget.TimeSlot> timeSlots =
            daySchedule.hours.map((slot) {
              print(
                '[Queue] Converting slot: ${slot.timeFrom} - ${slot.timeTo}',
              );

              // Parse time strings and convert to TimeOfDay
              final fromParts = slot.timeFrom.split(':');
              final toParts = slot.timeTo.split(':');

              final fromHour = int.parse(fromParts[0]);
              final fromMinute = int.parse(fromParts[1]);
              final toHour = int.parse(toParts[0]);
              final toMinute = int.parse(toParts[1]);

              return widget.TimeSlot(
                from: TimeOfDay(hour: fromHour, minute: fromMinute),
                to: TimeOfDay(hour: toHour, minute: toMinute),
              );
            }).toList();

        widgetData[daySchedule.dayOfWeek] = timeSlots;
        print(
          '[Queue] Added ${timeSlots.length} slots for ${daySchedule.dayOfWeek}',
        );
      } else {
        // Add empty list for inactive days or days with no hours
        widgetData[daySchedule.dayOfWeek] = [];
        print(
          '[Queue] Added empty slots for ${daySchedule.dayOfWeek} (inactive or no hours)',
        );
      }
    }

    print('[Queue] Final widget data keys: ${widgetData.keys.toList()}');
    return widgetData;
  }
}

/// Enhanced Queue model with opening hours support for onboarding wizard
class EnhancedQueue extends Equatable {
  final int id;
  final String title;
  final bool isActive;
  final int locationId; // More descriptive than sProvidingPlaceId
  final List<QueueService> services;
  final OpeningHours? openingHours; // null means inherit from location
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const EnhancedQueue({
    required this.id,
    required this.title,
    required this.locationId,
    required this.services,
    this.isActive = true,
    this.openingHours,
    this.createdAt,
    this.updatedAt,
  });

  factory EnhancedQueue.fromJson(Map<String, dynamic> json) {
    final queueId = json['id'] as int;
    return EnhancedQueue(
      id: queueId,
      title: json['title'] as String,
      isActive: json['isActive'] as bool? ?? true,
      locationId:
          json['locationId'] as int? ?? json['sProvidingPlaceId'] as int,
      services:
          (json['services'] as List<dynamic>?)
              ?.map(
                (service) => QueueService.fromJson(
                  service as Map<String, dynamic>,
                  queueId: queueId,
                ),
              )
              .toList() ??
          [],
      openingHours:
          json['openingHours'] != null
              ? OpeningHours.fromJson(json['openingHours'] as List<dynamic>)
              : null,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'] as String)
              : null,
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'isActive': isActive,
      'locationId': locationId,
      'sProvidingPlaceId': locationId, // For API compatibility
      'services': services.map((service) => service.toJson()).toList(),
      'openingHours': openingHours?.toJson(),
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  /// Convert to legacy Queue model for backward compatibility
  Queue toLegacyQueue() {
    return Queue(
      id: id,
      title: title,
      isActive: isActive,
      sProvidingPlaceId: locationId,
      services: services,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  /// Create from legacy Queue model
  factory EnhancedQueue.fromLegacyQueue(
    Queue queue, {
    OpeningHours? openingHours,
  }) {
    return EnhancedQueue(
      id: queue.id,
      title: queue.title,
      isActive: queue.isActive,
      locationId: queue.sProvidingPlaceId,
      services: queue.services,
      openingHours: openingHours,
      createdAt: queue.createdAt,
      updatedAt: queue.updatedAt,
    );
  }

  EnhancedQueue copyWith({
    int? id,
    String? title,
    bool? isActive,
    int? locationId,
    List<QueueService>? services,
    OpeningHours? openingHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EnhancedQueue(
      id: id ?? this.id,
      title: title ?? this.title,
      isActive: isActive ?? this.isActive,
      locationId: locationId ?? this.locationId,
      services: services ?? this.services,
      openingHours: openingHours ?? this.openingHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if queue inherits opening hours from location
  bool get inheritsOpeningHours => openingHours == null;

  /// Check if queue has custom opening hours
  bool get hasCustomOpeningHours => openingHours != null;

  /// Get service IDs assigned to this queue
  List<int> get serviceIds => services.map((s) => s.serviceId).toList();

  /// Check if queue has any services assigned
  bool get hasServices => services.isNotEmpty;

  /// Get service count
  int get serviceCount => services.length;

  /// Check if a specific service is assigned to this queue
  bool hasService(int serviceId) {
    return services.any((s) => s.serviceId == serviceId);
  }

  /// Get status text
  String get statusText => isActive ? 'Active' : 'Inactive';

  /// Get status color indicator
  String get statusColor => isActive ? 'green' : 'grey';

  /// Get opening hours status text
  String get openingHoursStatus {
    if (inheritsOpeningHours) {
      return 'Inherits from location';
    } else {
      return 'Custom hours';
    }
  }

  @override
  List<Object?> get props => [
    id,
    title,
    isActive,
    locationId,
    services,
    openingHours,
    createdAt,
    updatedAt,
  ];

  @override
  String toString() {
    return 'EnhancedQueue(id: $id, title: $title, locationId: $locationId, services: ${serviceCount})';
  }
}

/// Queue service assignment model
class QueueService extends Equatable {
  final int queueId;
  final int serviceId;
  final QueueServiceDetails? service;

  const QueueService({
    required this.queueId,
    required this.serviceId,
    this.service,
  });

  factory QueueService.fromJson(Map<String, dynamic> json, {int? queueId}) {
    // Handle two different API response formats:
    // 1. Direct service data: {"id": 2, "title": "test"}
    // 2. Queue-service relationship: {"queueId": 1, "serviceId": 2, "service": {...}}

    if (json.containsKey('queueId') && json.containsKey('serviceId')) {
      // Format 2: Queue-service relationship
      return QueueService(
        queueId: json['queueId'] as int,
        serviceId: json['serviceId'] as int,
        service:
            json['service'] != null
                ? QueueServiceDetails.fromJson(
                  json['service'] as Map<String, dynamic>,
                )
                : null,
      );
    } else {
      // Format 1: Direct service data (from queue list API)
      final serviceId = json['id'] as int;
      return QueueService(
        queueId: queueId ?? 0, // Use provided queueId or default to 0
        serviceId: serviceId,
        service: QueueServiceDetails.fromJson(json),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'queueId': queueId,
      'serviceId': serviceId,
      if (service != null) 'service': service!.toJson(),
    };
  }

  @override
  List<Object?> get props => [queueId, serviceId, service];
}

/// Service details within queue context (simplified from full Service model)
class QueueServiceDetails extends Equatable {
  final int id;
  final String title;
  final int duration;
  final bool isActive;

  const QueueServiceDetails({
    required this.id,
    required this.title,
    this.duration = 0, // Default to 0 if not provided
    this.isActive = true, // Default to true if not provided
  });

  factory QueueServiceDetails.fromJson(Map<String, dynamic> json) {
    return QueueServiceDetails(
      id: json['id'] as int,
      title: json['title'] as String,
      duration: json['duration'] as int? ?? 0, // Default to 0 if not provided
      isActive:
          json['isActive'] as bool? ?? true, // Default to true if not provided
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'duration': duration,
      'isActive': isActive,
    };
  }

  @override
  List<Object> get props => [id, title, duration, isActive];

  /// Get formatted duration
  String get formattedDuration {
    if (duration < 60) {
      return '${duration}min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}min';
      }
    }
  }
}

/// Request model for creating a new queue
class CreateQueueRequest extends Equatable {
  final String title;
  final int locationId;
  final List<int> serviceIds;
  final List<Map<String, dynamic>>? openingHours;

  const CreateQueueRequest({
    required this.title,
    required this.locationId,
    required this.serviceIds,
    this.openingHours,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'sProvidingPlaceId': locationId,
      'serviceIds': serviceIds,
      'isActive': true,
      if (openingHours != null) 'openingHours': openingHours,
    };
  }

  @override
  List<Object?> get props => [title, locationId, serviceIds, openingHours];
}

/// Enhanced request model for creating a new queue with opening hours
class CreateEnhancedQueueRequest extends Equatable {
  final String title;
  final int locationId;
  final List<int> serviceIds;
  final OpeningHours? customOpeningHours; // null means inherit from location
  final bool isActive;

  const CreateEnhancedQueueRequest({
    required this.title,
    required this.locationId,
    required this.serviceIds,
    this.customOpeningHours,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'locationId': locationId,
      'sProvidingPlaceId': locationId, // For API compatibility
      'serviceIds': serviceIds,
      'isActive': isActive,
      'openingHours': customOpeningHours?.toJson(),
    };
  }

  /// Convert to legacy CreateQueueRequest
  CreateQueueRequest toLegacyRequest() {
    return CreateQueueRequest(
      title: title,
      locationId: locationId,
      serviceIds: serviceIds,
      openingHours: null,
    );
  }

  /// Create from legacy CreateQueueRequest
  factory CreateEnhancedQueueRequest.fromLegacyRequest(
    CreateQueueRequest request, {
    OpeningHours? customOpeningHours,
    bool isActive = true,
  }) {
    return CreateEnhancedQueueRequest(
      title: request.title,
      locationId: request.locationId,
      serviceIds: request.serviceIds,
      customOpeningHours: customOpeningHours,
      isActive: isActive,
    );
  }

  /// Check if queue will inherit opening hours from location
  bool get inheritsOpeningHours => customOpeningHours == null;

  /// Check if queue has custom opening hours
  bool get hasCustomOpeningHours => customOpeningHours != null;

  @override
  List<Object?> get props => [
    title,
    locationId,
    serviceIds,
    customOpeningHours,
    isActive,
  ];
}

/// Request model for updating an existing queue
class UpdateQueueRequest extends Equatable {
  final String title;
  final List<int> serviceIds;
  final List<Map<String, dynamic>>? openingHours;
  final bool? isActive;

  const UpdateQueueRequest({
    required this.title,
    required this.serviceIds,
    this.openingHours,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'serviceIds': serviceIds,
      'openingHours': openingHours,
      if (isActive != null) 'isActive': isActive,
    };
  }

  @override
  List<Object?> get props => [title, serviceIds, openingHours, isActive];
}

/// Enhanced request model for updating an existing queue with opening hours
class UpdateEnhancedQueueRequest extends Equatable {
  final String? title;
  final bool? isActive;
  final List<int>? serviceIds;
  final OpeningHours? customOpeningHours; // null means inherit from location
  final bool?
  clearCustomOpeningHours; // true means remove custom hours and inherit from location

  const UpdateEnhancedQueueRequest({
    this.title,
    this.isActive,
    this.serviceIds,
    this.customOpeningHours,
    this.clearCustomOpeningHours,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (title != null) json['title'] = title;
    if (isActive != null) json['isActive'] = isActive;
    if (serviceIds != null) json['serviceIds'] = serviceIds;

    if (clearCustomOpeningHours == true) {
      json['openingHours'] = null; // Explicitly clear custom hours
    } else if (customOpeningHours != null) {
      json['openingHours'] = customOpeningHours!.toJson();
    }

    return json;
  }

  /// Convert to legacy UpdateQueueRequest
  UpdateQueueRequest toLegacyRequest() {
    return UpdateQueueRequest(
      title: title ?? '',
      serviceIds: serviceIds ?? [],
      openingHours: null,
      isActive: null,
    );
  }

  /// Create from legacy UpdateQueueRequest
  factory UpdateEnhancedQueueRequest.fromLegacyRequest(
    UpdateQueueRequest request, {
    OpeningHours? customOpeningHours,
    bool? clearCustomOpeningHours,
  }) {
    return UpdateEnhancedQueueRequest(
      title: request.title,
      isActive: request.isActive,
      serviceIds: request.serviceIds,
      customOpeningHours: customOpeningHours,
      clearCustomOpeningHours: clearCustomOpeningHours,
    );
  }

  /// Check if request has any fields to update
  bool get hasUpdates {
    return title != null ||
        isActive != null ||
        serviceIds != null ||
        customOpeningHours != null ||
        clearCustomOpeningHours == true;
  }

  @override
  List<Object?> get props => [
    title,
    isActive,
    serviceIds,
    customOpeningHours,
    clearCustomOpeningHours,
  ];
}

/// Request model for assigning a service to a queue
class AssignServiceRequest extends Equatable {
  final int serviceId;

  const AssignServiceRequest({required this.serviceId});

  Map<String, dynamic> toJson() {
    return {'serviceId': serviceId};
  }

  @override
  List<Object> get props => [serviceId];
}

/// Queue data state for provider
class QueueData extends Equatable {
  final QueueState state;
  final List<Queue> queues;
  final String? error;
  final bool isLoading;
  final Map<int, List<Queue>> queuesByLocation;
  final Map<int, Queue> queuesById;

  const QueueData({
    required this.state,
    this.queues = const [],
    this.error,
    this.isLoading = false,
    this.queuesByLocation = const {},
    this.queuesById = const {},
  });

  QueueData copyWith({
    QueueState? state,
    List<Queue>? queues,
    String? error,
    bool? isLoading,
    Map<int, List<Queue>>? queuesByLocation,
    Map<int, Queue>? queuesById,
    bool clearError = false,
  }) {
    return QueueData(
      state: state ?? this.state,
      queues: queues ?? this.queues,
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
      queuesByLocation: queuesByLocation ?? this.queuesByLocation,
      queuesById: queuesById ?? this.queuesById,
    );
  }

  @override
  List<Object?> get props => [
    state,
    queues,
    error,
    isLoading,
    queuesByLocation,
    queuesById,
  ];
}

/// Queue state enum
enum QueueState { initial, loading, loaded, error }

/// Queue statistics model
class QueueStats extends Equatable {
  final int totalQueues;
  final int activeQueues;
  final int inactiveQueues;
  final int totalServices;
  final Map<int, int> servicesByQueue;
  final Map<int, int> queuesByLocation;

  const QueueStats({
    required this.totalQueues,
    required this.activeQueues,
    required this.inactiveQueues,
    required this.totalServices,
    required this.servicesByQueue,
    required this.queuesByLocation,
  });

  @override
  List<Object> get props => [
    totalQueues,
    activeQueues,
    inactiveQueues,
    totalServices,
    servicesByQueue,
    queuesByLocation,
  ];

  /// Calculate statistics from a list of queues
  factory QueueStats.fromQueues(List<Queue> queues) {
    final activeQueues = queues.where((q) => q.isActive).length;
    final inactiveQueues = queues.length - activeQueues;

    final servicesByQueue = <int, int>{};
    final queuesByLocation = <int, int>{};
    int totalServices = 0;

    for (final queue in queues) {
      servicesByQueue[queue.id] = queue.serviceCount;
      totalServices += queue.serviceCount;

      queuesByLocation[queue.locationId] =
          (queuesByLocation[queue.locationId] ?? 0) + 1;
    }

    return QueueStats(
      totalQueues: queues.length,
      activeQueues: activeQueues,
      inactiveQueues: inactiveQueues,
      totalServices: totalServices,
      servicesByQueue: servicesByQueue,
      queuesByLocation: queuesByLocation,
    );
  }
}

/// Queue validation result
class QueueValidationResult extends Equatable {
  final bool isValid;
  final String? errorMessage;
  final List<String> warnings;

  const QueueValidationResult({
    required this.isValid,
    this.errorMessage,
    this.warnings = const [],
  });

  factory QueueValidationResult.valid({List<String> warnings = const []}) {
    return QueueValidationResult(isValid: true, warnings: warnings);
  }

  factory QueueValidationResult.invalid(
    String message, {
    List<String> warnings = const [],
  }) {
    return QueueValidationResult(
      isValid: false,
      errorMessage: message,
      warnings: warnings,
    );
  }

  @override
  List<Object?> get props => [isValid, errorMessage, warnings];
}
