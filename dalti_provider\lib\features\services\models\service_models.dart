/// Service model representing a business service
class Service {
  final int id;
  final String title;
  final int duration; // in minutes
  final String? color; // hex color code
  final bool acceptOnline;
  final bool acceptNew;
  final bool notificationOn;
  final int? pointsRequirements;

  // Fields available in create/update but not in GET responses
  final String? description;
  final double? price;
  final bool? isActive;
  final int? categoryId;

  // Enhanced fields for new onboarding wizard
  final bool? isPublic; // Public/private visibility for booking
  final String? deliveryType; // "at_location", "at_customer", "both"
  final List<String>? servedRegions; // For "at_customer" delivery

  const Service({
    required this.id,
    required this.title,
    required this.duration,
    this.color,
    this.acceptOnline = true,
    this.acceptNew = true,
    this.notificationOn = true,
    this.pointsRequirements,
    this.description,
    this.price,
    this.isActive,
    this.categoryId,
    this.isPublic,
    this.deliveryType,
    this.servedRegions,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      duration: json['duration'] ?? 0,
      color: json['color'],
      acceptOnline: json['acceptOnline'] ?? true,
      acceptNew: json['acceptNew'] ?? true,
      notificationOn: json['notificationOn'] ?? true,
      pointsRequirements: json['pointsRequirements'],
      description: json['description'],
      price: json['price']?.toDouble(),
      isActive: json['isActive'] ?? true, // Default to true if not provided by API
      categoryId: json['categoryId'],
      isPublic: json['isPublic'],
      deliveryType: json['deliveryType'],
      servedRegions: json['servedRegions'] != null
          ? List<String>.from(json['servedRegions'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'duration': duration,
      'color': color,
      'acceptOnline': acceptOnline,
      'acceptNew': acceptNew,
      'notificationOn': notificationOn,
      'pointsRequirements': pointsRequirements,
      'description': description,
      'price': price,
      'isActive': isActive,
      'categoryId': categoryId,
      'isPublic': isPublic,
      'deliveryType': deliveryType,
      'servedRegions': servedRegions,
    };
  }

  Service copyWith({
    int? id,
    String? title,
    int? duration,
    String? color,
    bool? acceptOnline,
    bool? acceptNew,
    bool? notificationOn,
    int? pointsRequirements,
    String? description,
    double? price,
    bool? isActive,
    int? categoryId,
    bool? isPublic,
    String? deliveryType,
    List<String>? servedRegions,
  }) {
    return Service(
      id: id ?? this.id,
      title: title ?? this.title,
      duration: duration ?? this.duration,
      color: color ?? this.color,
      acceptOnline: acceptOnline ?? this.acceptOnline,
      acceptNew: acceptNew ?? this.acceptNew,
      notificationOn: notificationOn ?? this.notificationOn,
      pointsRequirements: pointsRequirements ?? this.pointsRequirements,
      description: description ?? this.description,
      price: price ?? this.price,
      isActive: isActive ?? this.isActive,
      categoryId: categoryId ?? this.categoryId,
      isPublic: isPublic ?? this.isPublic,
      deliveryType: deliveryType ?? this.deliveryType,
      servedRegions: servedRegions ?? this.servedRegions,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Service && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Service(id: $id, title: $title, duration: $duration, price: $price)';
  }

  /// Get formatted duration string
  String get formattedDuration {
    if (duration < 60) {
      return '${duration}min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}min';
      }
    }
  }

  /// Get formatted price string
  String get formattedPrice {
    if (price == null) return 'Price not set';
    return '\$${price!.toStringAsFixed(2)}';
  }

  /// Check if service has all required fields for display
  bool get isComplete {
    return title.isNotEmpty && duration > 0;
  }

  /// Check if service is active (defaults to true if not specified)
  bool get isServiceActive {
    return isActive ?? true;
  }

  /// Check if service is public (defaults to true if not specified)
  bool get isServicePublic {
    return isPublic ?? true;
  }

  /// Get delivery type (defaults to "at_location" if not specified)
  String get serviceDeliveryType {
    return deliveryType ?? 'at_location';
  }

  /// Check if service is delivered at business location
  bool get isAtLocation {
    final type = serviceDeliveryType;
    return type == 'at_location' || type == 'both';
  }

  /// Check if service is delivered at customer location
  bool get isAtCustomer {
    final type = serviceDeliveryType;
    return type == 'at_customer' || type == 'both';
  }

  /// Check if service has served regions defined
  bool get hasServedRegions {
    return servedRegions != null && servedRegions!.isNotEmpty;
  }

  /// Get served regions (empty list if not defined)
  List<String> get serviceServedRegions {
    return servedRegions ?? [];
  }

  /// Create Service from CreateServiceRequest
  factory Service.fromCreateRequest(CreateServiceRequest request, {int id = 0}) {
    return Service(
      id: id,
      title: request.title,
      duration: request.duration,
      description: request.description,
      price: request.price,
      color: request.color,
      acceptOnline: request.acceptOnline,
      acceptNew: request.acceptNew,
      notificationOn: request.notificationOn,
      pointsRequirements: request.pointsRequirements,
      isActive: true, // New services are active by default
      isPublic: request.isPublic,
      deliveryType: request.deliveryType,
      servedRegions: request.servedRegions,
    );
  }

  /// Convert to CreateServiceRequest
  CreateServiceRequest toCreateRequest() {
    return CreateServiceRequest(
      title: title,
      duration: duration,
      pointsRequirements: pointsRequirements ?? 1,
      price: price,
      isPublic: isServicePublic,
      deliveryType: serviceDeliveryType,
      servedRegions: servedRegions,
      description: description,
      color: color,
      acceptOnline: acceptOnline,
      acceptNew: acceptNew,
      notificationOn: notificationOn,
    );
  }
}

/// Request model for creating a new service
class CreateServiceRequest {
  final String title;
  final int duration;
  final double? price;
  final int pointsRequirements; // Required for API
  final bool isPublic;
  final String deliveryType; // "at_location", "at_customer", "both"
  final List<String>? servedRegions; // For "at_customer" delivery
  final String? description;
  final String? color;
  final bool acceptOnline;
  final bool acceptNew;
  final bool notificationOn;

  const CreateServiceRequest({
    required this.title,
    required this.duration,
    required this.pointsRequirements,
    this.price,
    this.isPublic = true,
    this.deliveryType = 'at_location',
    this.servedRegions,
    this.description,
    this.color,
    this.acceptOnline = true,
    this.acceptNew = true,
    this.notificationOn = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'duration': duration,
      'price': price,
      'pointsRequirements': pointsRequirements,
      'isPublic': isPublic,
      'deliveryType': deliveryType,
      'servedRegions': servedRegions,
      'description': description,
      'color': color,
      'acceptOnline': acceptOnline,
      'acceptNew': acceptNew,
      'notificationOn': notificationOn,
    };
  }

  factory CreateServiceRequest.fromJson(Map<String, dynamic> json) {
    return CreateServiceRequest(
      title: json['title'] ?? '',
      duration: json['duration'] ?? 0,
      price: json['price']?.toDouble() ?? 0.0,
      description: json['description'],
      color: json['color'],
      acceptOnline: json['acceptOnline'] ?? true,
      acceptNew: json['acceptNew'] ?? true,
      notificationOn: json['notificationOn'] ?? true,
      pointsRequirements: json['pointsRequirements'],
    );
  }
}

/// Request model for updating an existing service
class UpdateServiceRequest {
  final String? title;
  final int? duration;
  final double? price;
  final int? categoryId;
  final String? description;
  final String? color;
  final bool? acceptOnline;
  final bool? acceptNew;
  final bool? notificationOn;
  final int? pointsRequirements;
  final bool? isActive;
  final bool? isPublic;
  final String? deliveryType;
  final List<String>? servedRegions;

  const UpdateServiceRequest({
    this.title,
    this.duration,
    this.price,
    this.categoryId,
    this.description,
    this.color,
    this.acceptOnline,
    this.acceptNew,
    this.notificationOn,
    this.pointsRequirements,
    this.isActive,
    this.isPublic,
    this.deliveryType,
    this.servedRegions,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    if (title != null) json['title'] = title;
    if (duration != null) json['duration'] = duration;
    if (price != null) json['price'] = price;
    if (categoryId != null) json['categoryId'] = categoryId;
    if (description != null) json['description'] = description;
    if (color != null) json['color'] = color;
    if (acceptOnline != null) json['acceptOnline'] = acceptOnline;
    if (acceptNew != null) json['acceptNew'] = acceptNew;
    if (notificationOn != null) json['notificationOn'] = notificationOn;
    if (pointsRequirements != null) json['pointsRequirements'] = pointsRequirements;
    if (isActive != null) json['isActive'] = isActive;
    if (isPublic != null) json['isPublic'] = isPublic;
    if (deliveryType != null) json['deliveryType'] = deliveryType;
    if (servedRegions != null) json['servedRegions'] = servedRegions;

    return json;
  }

  factory UpdateServiceRequest.fromJson(Map<String, dynamic> json) {
    return UpdateServiceRequest(
      title: json['title'],
      duration: json['duration'],
      price: json['price']?.toDouble(),
      categoryId: json['categoryId'],
      description: json['description'],
      color: json['color'],
      acceptOnline: json['acceptOnline'],
      acceptNew: json['acceptNew'],
      notificationOn: json['notificationOn'],
      pointsRequirements: json['pointsRequirements'],
      isActive: json['isActive'],
      isPublic: json['isPublic'],
      deliveryType: json['deliveryType'],
      servedRegions: json['servedRegions'] != null
          ? List<String>.from(json['servedRegions'])
          : null,
    );
  }

  /// Check if request has any fields to update
  bool get hasUpdates {
    return title != null ||
        duration != null ||
        price != null ||
        categoryId != null ||
        description != null ||
        color != null ||
        acceptOnline != null ||
        acceptNew != null ||
        notificationOn != null ||
        pointsRequirements != null ||
        isActive != null ||
        isPublic != null ||
        deliveryType != null ||
        servedRegions != null;
  }
}
