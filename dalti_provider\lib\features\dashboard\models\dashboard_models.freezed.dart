// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BusinessMetrics _$BusinessMetricsFromJson(Map<String, dynamic> json) {
  return _BusinessMetrics.fromJson(json);
}

/// @nodoc
mixin _$BusinessMetrics {
  double get todayRevenue => throw _privateConstructorUsedError;
  int get todayAppointments => throw _privateConstructorUsedError;
  int get completedAppointments => throw _privateConstructorUsedError;
  int get cancelledAppointments => throw _privateConstructorUsedError;
  int get activeQueues => throw _privateConstructorUsedError;
  int get totalCustomersToday => throw _privateConstructorUsedError;
  double get customerSatisfaction => throw _privateConstructorUsedError;
  double get averageWaitTime => throw _privateConstructorUsedError;
  List<double> get weeklyRevenueData => throw _privateConstructorUsedError;
  List<int> get weeklyAppointmentData => throw _privateConstructorUsedError;
  double get revenueChange => throw _privateConstructorUsedError;
  double get appointmentsChange => throw _privateConstructorUsedError;

  /// Serializes this BusinessMetrics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BusinessMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessMetricsCopyWith<BusinessMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessMetricsCopyWith<$Res> {
  factory $BusinessMetricsCopyWith(
          BusinessMetrics value, $Res Function(BusinessMetrics) then) =
      _$BusinessMetricsCopyWithImpl<$Res, BusinessMetrics>;
  @useResult
  $Res call(
      {double todayRevenue,
      int todayAppointments,
      int completedAppointments,
      int cancelledAppointments,
      int activeQueues,
      int totalCustomersToday,
      double customerSatisfaction,
      double averageWaitTime,
      List<double> weeklyRevenueData,
      List<int> weeklyAppointmentData,
      double revenueChange,
      double appointmentsChange});
}

/// @nodoc
class _$BusinessMetricsCopyWithImpl<$Res, $Val extends BusinessMetrics>
    implements $BusinessMetricsCopyWith<$Res> {
  _$BusinessMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayRevenue = null,
    Object? todayAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
    Object? activeQueues = null,
    Object? totalCustomersToday = null,
    Object? customerSatisfaction = null,
    Object? averageWaitTime = null,
    Object? weeklyRevenueData = null,
    Object? weeklyAppointmentData = null,
    Object? revenueChange = null,
    Object? appointmentsChange = null,
  }) {
    return _then(_value.copyWith(
      todayRevenue: null == todayRevenue
          ? _value.todayRevenue
          : todayRevenue // ignore: cast_nullable_to_non_nullable
              as double,
      todayAppointments: null == todayAppointments
          ? _value.todayAppointments
          : todayAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      activeQueues: null == activeQueues
          ? _value.activeQueues
          : activeQueues // ignore: cast_nullable_to_non_nullable
              as int,
      totalCustomersToday: null == totalCustomersToday
          ? _value.totalCustomersToday
          : totalCustomersToday // ignore: cast_nullable_to_non_nullable
              as int,
      customerSatisfaction: null == customerSatisfaction
          ? _value.customerSatisfaction
          : customerSatisfaction // ignore: cast_nullable_to_non_nullable
              as double,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      weeklyRevenueData: null == weeklyRevenueData
          ? _value.weeklyRevenueData
          : weeklyRevenueData // ignore: cast_nullable_to_non_nullable
              as List<double>,
      weeklyAppointmentData: null == weeklyAppointmentData
          ? _value.weeklyAppointmentData
          : weeklyAppointmentData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      revenueChange: null == revenueChange
          ? _value.revenueChange
          : revenueChange // ignore: cast_nullable_to_non_nullable
              as double,
      appointmentsChange: null == appointmentsChange
          ? _value.appointmentsChange
          : appointmentsChange // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BusinessMetricsImplCopyWith<$Res>
    implements $BusinessMetricsCopyWith<$Res> {
  factory _$$BusinessMetricsImplCopyWith(_$BusinessMetricsImpl value,
          $Res Function(_$BusinessMetricsImpl) then) =
      __$$BusinessMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double todayRevenue,
      int todayAppointments,
      int completedAppointments,
      int cancelledAppointments,
      int activeQueues,
      int totalCustomersToday,
      double customerSatisfaction,
      double averageWaitTime,
      List<double> weeklyRevenueData,
      List<int> weeklyAppointmentData,
      double revenueChange,
      double appointmentsChange});
}

/// @nodoc
class __$$BusinessMetricsImplCopyWithImpl<$Res>
    extends _$BusinessMetricsCopyWithImpl<$Res, _$BusinessMetricsImpl>
    implements _$$BusinessMetricsImplCopyWith<$Res> {
  __$$BusinessMetricsImplCopyWithImpl(
      _$BusinessMetricsImpl _value, $Res Function(_$BusinessMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BusinessMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? todayRevenue = null,
    Object? todayAppointments = null,
    Object? completedAppointments = null,
    Object? cancelledAppointments = null,
    Object? activeQueues = null,
    Object? totalCustomersToday = null,
    Object? customerSatisfaction = null,
    Object? averageWaitTime = null,
    Object? weeklyRevenueData = null,
    Object? weeklyAppointmentData = null,
    Object? revenueChange = null,
    Object? appointmentsChange = null,
  }) {
    return _then(_$BusinessMetricsImpl(
      todayRevenue: null == todayRevenue
          ? _value.todayRevenue
          : todayRevenue // ignore: cast_nullable_to_non_nullable
              as double,
      todayAppointments: null == todayAppointments
          ? _value.todayAppointments
          : todayAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      activeQueues: null == activeQueues
          ? _value.activeQueues
          : activeQueues // ignore: cast_nullable_to_non_nullable
              as int,
      totalCustomersToday: null == totalCustomersToday
          ? _value.totalCustomersToday
          : totalCustomersToday // ignore: cast_nullable_to_non_nullable
              as int,
      customerSatisfaction: null == customerSatisfaction
          ? _value.customerSatisfaction
          : customerSatisfaction // ignore: cast_nullable_to_non_nullable
              as double,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      weeklyRevenueData: null == weeklyRevenueData
          ? _value._weeklyRevenueData
          : weeklyRevenueData // ignore: cast_nullable_to_non_nullable
              as List<double>,
      weeklyAppointmentData: null == weeklyAppointmentData
          ? _value._weeklyAppointmentData
          : weeklyAppointmentData // ignore: cast_nullable_to_non_nullable
              as List<int>,
      revenueChange: null == revenueChange
          ? _value.revenueChange
          : revenueChange // ignore: cast_nullable_to_non_nullable
              as double,
      appointmentsChange: null == appointmentsChange
          ? _value.appointmentsChange
          : appointmentsChange // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BusinessMetricsImpl implements _BusinessMetrics {
  const _$BusinessMetricsImpl(
      {this.todayRevenue = 0.0,
      this.todayAppointments = 0,
      this.completedAppointments = 0,
      this.cancelledAppointments = 0,
      this.activeQueues = 0,
      this.totalCustomersToday = 0,
      this.customerSatisfaction = 0.0,
      this.averageWaitTime = 0.0,
      final List<double> weeklyRevenueData = const [],
      final List<int> weeklyAppointmentData = const [],
      this.revenueChange = 0.0,
      this.appointmentsChange = 0.0})
      : _weeklyRevenueData = weeklyRevenueData,
        _weeklyAppointmentData = weeklyAppointmentData;

  factory _$BusinessMetricsImpl.fromJson(Map<String, dynamic> json) =>
      _$$BusinessMetricsImplFromJson(json);

  @override
  @JsonKey()
  final double todayRevenue;
  @override
  @JsonKey()
  final int todayAppointments;
  @override
  @JsonKey()
  final int completedAppointments;
  @override
  @JsonKey()
  final int cancelledAppointments;
  @override
  @JsonKey()
  final int activeQueues;
  @override
  @JsonKey()
  final int totalCustomersToday;
  @override
  @JsonKey()
  final double customerSatisfaction;
  @override
  @JsonKey()
  final double averageWaitTime;
  final List<double> _weeklyRevenueData;
  @override
  @JsonKey()
  List<double> get weeklyRevenueData {
    if (_weeklyRevenueData is EqualUnmodifiableListView)
      return _weeklyRevenueData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weeklyRevenueData);
  }

  final List<int> _weeklyAppointmentData;
  @override
  @JsonKey()
  List<int> get weeklyAppointmentData {
    if (_weeklyAppointmentData is EqualUnmodifiableListView)
      return _weeklyAppointmentData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weeklyAppointmentData);
  }

  @override
  @JsonKey()
  final double revenueChange;
  @override
  @JsonKey()
  final double appointmentsChange;

  @override
  String toString() {
    return 'BusinessMetrics(todayRevenue: $todayRevenue, todayAppointments: $todayAppointments, completedAppointments: $completedAppointments, cancelledAppointments: $cancelledAppointments, activeQueues: $activeQueues, totalCustomersToday: $totalCustomersToday, customerSatisfaction: $customerSatisfaction, averageWaitTime: $averageWaitTime, weeklyRevenueData: $weeklyRevenueData, weeklyAppointmentData: $weeklyAppointmentData, revenueChange: $revenueChange, appointmentsChange: $appointmentsChange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessMetricsImpl &&
            (identical(other.todayRevenue, todayRevenue) ||
                other.todayRevenue == todayRevenue) &&
            (identical(other.todayAppointments, todayAppointments) ||
                other.todayAppointments == todayAppointments) &&
            (identical(other.completedAppointments, completedAppointments) ||
                other.completedAppointments == completedAppointments) &&
            (identical(other.cancelledAppointments, cancelledAppointments) ||
                other.cancelledAppointments == cancelledAppointments) &&
            (identical(other.activeQueues, activeQueues) ||
                other.activeQueues == activeQueues) &&
            (identical(other.totalCustomersToday, totalCustomersToday) ||
                other.totalCustomersToday == totalCustomersToday) &&
            (identical(other.customerSatisfaction, customerSatisfaction) ||
                other.customerSatisfaction == customerSatisfaction) &&
            (identical(other.averageWaitTime, averageWaitTime) ||
                other.averageWaitTime == averageWaitTime) &&
            const DeepCollectionEquality()
                .equals(other._weeklyRevenueData, _weeklyRevenueData) &&
            const DeepCollectionEquality()
                .equals(other._weeklyAppointmentData, _weeklyAppointmentData) &&
            (identical(other.revenueChange, revenueChange) ||
                other.revenueChange == revenueChange) &&
            (identical(other.appointmentsChange, appointmentsChange) ||
                other.appointmentsChange == appointmentsChange));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      todayRevenue,
      todayAppointments,
      completedAppointments,
      cancelledAppointments,
      activeQueues,
      totalCustomersToday,
      customerSatisfaction,
      averageWaitTime,
      const DeepCollectionEquality().hash(_weeklyRevenueData),
      const DeepCollectionEquality().hash(_weeklyAppointmentData),
      revenueChange,
      appointmentsChange);

  /// Create a copy of BusinessMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessMetricsImplCopyWith<_$BusinessMetricsImpl> get copyWith =>
      __$$BusinessMetricsImplCopyWithImpl<_$BusinessMetricsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BusinessMetricsImplToJson(
      this,
    );
  }
}

abstract class _BusinessMetrics implements BusinessMetrics {
  const factory _BusinessMetrics(
      {final double todayRevenue,
      final int todayAppointments,
      final int completedAppointments,
      final int cancelledAppointments,
      final int activeQueues,
      final int totalCustomersToday,
      final double customerSatisfaction,
      final double averageWaitTime,
      final List<double> weeklyRevenueData,
      final List<int> weeklyAppointmentData,
      final double revenueChange,
      final double appointmentsChange}) = _$BusinessMetricsImpl;

  factory _BusinessMetrics.fromJson(Map<String, dynamic> json) =
      _$BusinessMetricsImpl.fromJson;

  @override
  double get todayRevenue;
  @override
  int get todayAppointments;
  @override
  int get completedAppointments;
  @override
  int get cancelledAppointments;
  @override
  int get activeQueues;
  @override
  int get totalCustomersToday;
  @override
  double get customerSatisfaction;
  @override
  double get averageWaitTime;
  @override
  List<double> get weeklyRevenueData;
  @override
  List<int> get weeklyAppointmentData;
  @override
  double get revenueChange;
  @override
  double get appointmentsChange;

  /// Create a copy of BusinessMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessMetricsImplCopyWith<_$BusinessMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ScheduleData _$ScheduleDataFromJson(Map<String, dynamic> json) {
  return _ScheduleData.fromJson(json);
}

/// @nodoc
mixin _$ScheduleData {
  NextAppointment? get nextAppointment => throw _privateConstructorUsedError;
  List<QueueStatus> get queueStatuses => throw _privateConstructorUsedError;
  WorkingHours? get todayHours => throw _privateConstructorUsedError;
  int get totalAppointments => throw _privateConstructorUsedError;
  int get completedAppointments => throw _privateConstructorUsedError;
  int get upcomingAppointments => throw _privateConstructorUsedError;
  int get cancelledAppointments => throw _privateConstructorUsedError;

  /// Serializes this ScheduleData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ScheduleDataCopyWith<ScheduleData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleDataCopyWith<$Res> {
  factory $ScheduleDataCopyWith(
          ScheduleData value, $Res Function(ScheduleData) then) =
      _$ScheduleDataCopyWithImpl<$Res, ScheduleData>;
  @useResult
  $Res call(
      {NextAppointment? nextAppointment,
      List<QueueStatus> queueStatuses,
      WorkingHours? todayHours,
      int totalAppointments,
      int completedAppointments,
      int upcomingAppointments,
      int cancelledAppointments});

  $NextAppointmentCopyWith<$Res>? get nextAppointment;
  $WorkingHoursCopyWith<$Res>? get todayHours;
}

/// @nodoc
class _$ScheduleDataCopyWithImpl<$Res, $Val extends ScheduleData>
    implements $ScheduleDataCopyWith<$Res> {
  _$ScheduleDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextAppointment = freezed,
    Object? queueStatuses = null,
    Object? todayHours = freezed,
    Object? totalAppointments = null,
    Object? completedAppointments = null,
    Object? upcomingAppointments = null,
    Object? cancelledAppointments = null,
  }) {
    return _then(_value.copyWith(
      nextAppointment: freezed == nextAppointment
          ? _value.nextAppointment
          : nextAppointment // ignore: cast_nullable_to_non_nullable
              as NextAppointment?,
      queueStatuses: null == queueStatuses
          ? _value.queueStatuses
          : queueStatuses // ignore: cast_nullable_to_non_nullable
              as List<QueueStatus>,
      todayHours: freezed == todayHours
          ? _value.todayHours
          : todayHours // ignore: cast_nullable_to_non_nullable
              as WorkingHours?,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      upcomingAppointments: null == upcomingAppointments
          ? _value.upcomingAppointments
          : upcomingAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NextAppointmentCopyWith<$Res>? get nextAppointment {
    if (_value.nextAppointment == null) {
      return null;
    }

    return $NextAppointmentCopyWith<$Res>(_value.nextAppointment!, (value) {
      return _then(_value.copyWith(nextAppointment: value) as $Val);
    });
  }

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorkingHoursCopyWith<$Res>? get todayHours {
    if (_value.todayHours == null) {
      return null;
    }

    return $WorkingHoursCopyWith<$Res>(_value.todayHours!, (value) {
      return _then(_value.copyWith(todayHours: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ScheduleDataImplCopyWith<$Res>
    implements $ScheduleDataCopyWith<$Res> {
  factory _$$ScheduleDataImplCopyWith(
          _$ScheduleDataImpl value, $Res Function(_$ScheduleDataImpl) then) =
      __$$ScheduleDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {NextAppointment? nextAppointment,
      List<QueueStatus> queueStatuses,
      WorkingHours? todayHours,
      int totalAppointments,
      int completedAppointments,
      int upcomingAppointments,
      int cancelledAppointments});

  @override
  $NextAppointmentCopyWith<$Res>? get nextAppointment;
  @override
  $WorkingHoursCopyWith<$Res>? get todayHours;
}

/// @nodoc
class __$$ScheduleDataImplCopyWithImpl<$Res>
    extends _$ScheduleDataCopyWithImpl<$Res, _$ScheduleDataImpl>
    implements _$$ScheduleDataImplCopyWith<$Res> {
  __$$ScheduleDataImplCopyWithImpl(
      _$ScheduleDataImpl _value, $Res Function(_$ScheduleDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextAppointment = freezed,
    Object? queueStatuses = null,
    Object? todayHours = freezed,
    Object? totalAppointments = null,
    Object? completedAppointments = null,
    Object? upcomingAppointments = null,
    Object? cancelledAppointments = null,
  }) {
    return _then(_$ScheduleDataImpl(
      nextAppointment: freezed == nextAppointment
          ? _value.nextAppointment
          : nextAppointment // ignore: cast_nullable_to_non_nullable
              as NextAppointment?,
      queueStatuses: null == queueStatuses
          ? _value._queueStatuses
          : queueStatuses // ignore: cast_nullable_to_non_nullable
              as List<QueueStatus>,
      todayHours: freezed == todayHours
          ? _value.todayHours
          : todayHours // ignore: cast_nullable_to_non_nullable
              as WorkingHours?,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      completedAppointments: null == completedAppointments
          ? _value.completedAppointments
          : completedAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      upcomingAppointments: null == upcomingAppointments
          ? _value.upcomingAppointments
          : upcomingAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledAppointments: null == cancelledAppointments
          ? _value.cancelledAppointments
          : cancelledAppointments // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ScheduleDataImpl implements _ScheduleData {
  const _$ScheduleDataImpl(
      {this.nextAppointment,
      final List<QueueStatus> queueStatuses = const [],
      this.todayHours,
      this.totalAppointments = 0,
      this.completedAppointments = 0,
      this.upcomingAppointments = 0,
      this.cancelledAppointments = 0})
      : _queueStatuses = queueStatuses;

  factory _$ScheduleDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ScheduleDataImplFromJson(json);

  @override
  final NextAppointment? nextAppointment;
  final List<QueueStatus> _queueStatuses;
  @override
  @JsonKey()
  List<QueueStatus> get queueStatuses {
    if (_queueStatuses is EqualUnmodifiableListView) return _queueStatuses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_queueStatuses);
  }

  @override
  final WorkingHours? todayHours;
  @override
  @JsonKey()
  final int totalAppointments;
  @override
  @JsonKey()
  final int completedAppointments;
  @override
  @JsonKey()
  final int upcomingAppointments;
  @override
  @JsonKey()
  final int cancelledAppointments;

  @override
  String toString() {
    return 'ScheduleData(nextAppointment: $nextAppointment, queueStatuses: $queueStatuses, todayHours: $todayHours, totalAppointments: $totalAppointments, completedAppointments: $completedAppointments, upcomingAppointments: $upcomingAppointments, cancelledAppointments: $cancelledAppointments)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleDataImpl &&
            (identical(other.nextAppointment, nextAppointment) ||
                other.nextAppointment == nextAppointment) &&
            const DeepCollectionEquality()
                .equals(other._queueStatuses, _queueStatuses) &&
            (identical(other.todayHours, todayHours) ||
                other.todayHours == todayHours) &&
            (identical(other.totalAppointments, totalAppointments) ||
                other.totalAppointments == totalAppointments) &&
            (identical(other.completedAppointments, completedAppointments) ||
                other.completedAppointments == completedAppointments) &&
            (identical(other.upcomingAppointments, upcomingAppointments) ||
                other.upcomingAppointments == upcomingAppointments) &&
            (identical(other.cancelledAppointments, cancelledAppointments) ||
                other.cancelledAppointments == cancelledAppointments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      nextAppointment,
      const DeepCollectionEquality().hash(_queueStatuses),
      todayHours,
      totalAppointments,
      completedAppointments,
      upcomingAppointments,
      cancelledAppointments);

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleDataImplCopyWith<_$ScheduleDataImpl> get copyWith =>
      __$$ScheduleDataImplCopyWithImpl<_$ScheduleDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ScheduleDataImplToJson(
      this,
    );
  }
}

abstract class _ScheduleData implements ScheduleData {
  const factory _ScheduleData(
      {final NextAppointment? nextAppointment,
      final List<QueueStatus> queueStatuses,
      final WorkingHours? todayHours,
      final int totalAppointments,
      final int completedAppointments,
      final int upcomingAppointments,
      final int cancelledAppointments}) = _$ScheduleDataImpl;

  factory _ScheduleData.fromJson(Map<String, dynamic> json) =
      _$ScheduleDataImpl.fromJson;

  @override
  NextAppointment? get nextAppointment;
  @override
  List<QueueStatus> get queueStatuses;
  @override
  WorkingHours? get todayHours;
  @override
  int get totalAppointments;
  @override
  int get completedAppointments;
  @override
  int get upcomingAppointments;
  @override
  int get cancelledAppointments;

  /// Create a copy of ScheduleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleDataImplCopyWith<_$ScheduleDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NextAppointment _$NextAppointmentFromJson(Map<String, dynamic> json) {
  return _NextAppointment.fromJson(json);
}

/// @nodoc
mixin _$NextAppointment {
  String get id => throw _privateConstructorUsedError;
  String get customerName => throw _privateConstructorUsedError;
  String get serviceName => throw _privateConstructorUsedError;
  DateTime get scheduledTime => throw _privateConstructorUsedError;
  int get estimatedDuration => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  /// Serializes this NextAppointment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NextAppointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NextAppointmentCopyWith<NextAppointment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NextAppointmentCopyWith<$Res> {
  factory $NextAppointmentCopyWith(
          NextAppointment value, $Res Function(NextAppointment) then) =
      _$NextAppointmentCopyWithImpl<$Res, NextAppointment>;
  @useResult
  $Res call(
      {String id,
      String customerName,
      String serviceName,
      DateTime scheduledTime,
      int estimatedDuration,
      String status});
}

/// @nodoc
class _$NextAppointmentCopyWithImpl<$Res, $Val extends NextAppointment>
    implements $NextAppointmentCopyWith<$Res> {
  _$NextAppointmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NextAppointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerName = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? estimatedDuration = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      estimatedDuration: null == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NextAppointmentImplCopyWith<$Res>
    implements $NextAppointmentCopyWith<$Res> {
  factory _$$NextAppointmentImplCopyWith(_$NextAppointmentImpl value,
          $Res Function(_$NextAppointmentImpl) then) =
      __$$NextAppointmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String customerName,
      String serviceName,
      DateTime scheduledTime,
      int estimatedDuration,
      String status});
}

/// @nodoc
class __$$NextAppointmentImplCopyWithImpl<$Res>
    extends _$NextAppointmentCopyWithImpl<$Res, _$NextAppointmentImpl>
    implements _$$NextAppointmentImplCopyWith<$Res> {
  __$$NextAppointmentImplCopyWithImpl(
      _$NextAppointmentImpl _value, $Res Function(_$NextAppointmentImpl) _then)
      : super(_value, _then);

  /// Create a copy of NextAppointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerName = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? estimatedDuration = null,
    Object? status = null,
  }) {
    return _then(_$NextAppointmentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerName: null == customerName
          ? _value.customerName
          : customerName // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      estimatedDuration: null == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NextAppointmentImpl implements _NextAppointment {
  const _$NextAppointmentImpl(
      {required this.id,
      required this.customerName,
      required this.serviceName,
      required this.scheduledTime,
      this.estimatedDuration = 30,
      this.status = 'confirmed'});

  factory _$NextAppointmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$NextAppointmentImplFromJson(json);

  @override
  final String id;
  @override
  final String customerName;
  @override
  final String serviceName;
  @override
  final DateTime scheduledTime;
  @override
  @JsonKey()
  final int estimatedDuration;
  @override
  @JsonKey()
  final String status;

  @override
  String toString() {
    return 'NextAppointment(id: $id, customerName: $customerName, serviceName: $serviceName, scheduledTime: $scheduledTime, estimatedDuration: $estimatedDuration, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NextAppointmentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customerName, customerName) ||
                other.customerName == customerName) &&
            (identical(other.serviceName, serviceName) ||
                other.serviceName == serviceName) &&
            (identical(other.scheduledTime, scheduledTime) ||
                other.scheduledTime == scheduledTime) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, customerName, serviceName,
      scheduledTime, estimatedDuration, status);

  /// Create a copy of NextAppointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NextAppointmentImplCopyWith<_$NextAppointmentImpl> get copyWith =>
      __$$NextAppointmentImplCopyWithImpl<_$NextAppointmentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NextAppointmentImplToJson(
      this,
    );
  }
}

abstract class _NextAppointment implements NextAppointment {
  const factory _NextAppointment(
      {required final String id,
      required final String customerName,
      required final String serviceName,
      required final DateTime scheduledTime,
      final int estimatedDuration,
      final String status}) = _$NextAppointmentImpl;

  factory _NextAppointment.fromJson(Map<String, dynamic> json) =
      _$NextAppointmentImpl.fromJson;

  @override
  String get id;
  @override
  String get customerName;
  @override
  String get serviceName;
  @override
  DateTime get scheduledTime;
  @override
  int get estimatedDuration;
  @override
  String get status;

  /// Create a copy of NextAppointment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NextAppointmentImplCopyWith<_$NextAppointmentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QueueStatus _$QueueStatusFromJson(Map<String, dynamic> json) {
  return _QueueStatus.fromJson(json);
}

/// @nodoc
mixin _$QueueStatus {
  String get queueId => throw _privateConstructorUsedError;
  String get queueName => throw _privateConstructorUsedError;
  String get locationName => throw _privateConstructorUsedError;
  int get waitingCount => throw _privateConstructorUsedError;
  double get averageWaitTime => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get nextAvailableSlot => throw _privateConstructorUsedError;

  /// Serializes this QueueStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QueueStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QueueStatusCopyWith<QueueStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueueStatusCopyWith<$Res> {
  factory $QueueStatusCopyWith(
          QueueStatus value, $Res Function(QueueStatus) then) =
      _$QueueStatusCopyWithImpl<$Res, QueueStatus>;
  @useResult
  $Res call(
      {String queueId,
      String queueName,
      String locationName,
      int waitingCount,
      double averageWaitTime,
      bool isActive,
      DateTime? nextAvailableSlot});
}

/// @nodoc
class _$QueueStatusCopyWithImpl<$Res, $Val extends QueueStatus>
    implements $QueueStatusCopyWith<$Res> {
  _$QueueStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QueueStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? queueId = null,
    Object? queueName = null,
    Object? locationName = null,
    Object? waitingCount = null,
    Object? averageWaitTime = null,
    Object? isActive = null,
    Object? nextAvailableSlot = freezed,
  }) {
    return _then(_value.copyWith(
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String,
      queueName: null == queueName
          ? _value.queueName
          : queueName // ignore: cast_nullable_to_non_nullable
              as String,
      locationName: null == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String,
      waitingCount: null == waitingCount
          ? _value.waitingCount
          : waitingCount // ignore: cast_nullable_to_non_nullable
              as int,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      nextAvailableSlot: freezed == nextAvailableSlot
          ? _value.nextAvailableSlot
          : nextAvailableSlot // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QueueStatusImplCopyWith<$Res>
    implements $QueueStatusCopyWith<$Res> {
  factory _$$QueueStatusImplCopyWith(
          _$QueueStatusImpl value, $Res Function(_$QueueStatusImpl) then) =
      __$$QueueStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String queueId,
      String queueName,
      String locationName,
      int waitingCount,
      double averageWaitTime,
      bool isActive,
      DateTime? nextAvailableSlot});
}

/// @nodoc
class __$$QueueStatusImplCopyWithImpl<$Res>
    extends _$QueueStatusCopyWithImpl<$Res, _$QueueStatusImpl>
    implements _$$QueueStatusImplCopyWith<$Res> {
  __$$QueueStatusImplCopyWithImpl(
      _$QueueStatusImpl _value, $Res Function(_$QueueStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of QueueStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? queueId = null,
    Object? queueName = null,
    Object? locationName = null,
    Object? waitingCount = null,
    Object? averageWaitTime = null,
    Object? isActive = null,
    Object? nextAvailableSlot = freezed,
  }) {
    return _then(_$QueueStatusImpl(
      queueId: null == queueId
          ? _value.queueId
          : queueId // ignore: cast_nullable_to_non_nullable
              as String,
      queueName: null == queueName
          ? _value.queueName
          : queueName // ignore: cast_nullable_to_non_nullable
              as String,
      locationName: null == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String,
      waitingCount: null == waitingCount
          ? _value.waitingCount
          : waitingCount // ignore: cast_nullable_to_non_nullable
              as int,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      nextAvailableSlot: freezed == nextAvailableSlot
          ? _value.nextAvailableSlot
          : nextAvailableSlot // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QueueStatusImpl implements _QueueStatus {
  const _$QueueStatusImpl(
      {required this.queueId,
      required this.queueName,
      required this.locationName,
      this.waitingCount = 0,
      this.averageWaitTime = 0.0,
      this.isActive = true,
      this.nextAvailableSlot});

  factory _$QueueStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$QueueStatusImplFromJson(json);

  @override
  final String queueId;
  @override
  final String queueName;
  @override
  final String locationName;
  @override
  @JsonKey()
  final int waitingCount;
  @override
  @JsonKey()
  final double averageWaitTime;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? nextAvailableSlot;

  @override
  String toString() {
    return 'QueueStatus(queueId: $queueId, queueName: $queueName, locationName: $locationName, waitingCount: $waitingCount, averageWaitTime: $averageWaitTime, isActive: $isActive, nextAvailableSlot: $nextAvailableSlot)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueueStatusImpl &&
            (identical(other.queueId, queueId) || other.queueId == queueId) &&
            (identical(other.queueName, queueName) ||
                other.queueName == queueName) &&
            (identical(other.locationName, locationName) ||
                other.locationName == locationName) &&
            (identical(other.waitingCount, waitingCount) ||
                other.waitingCount == waitingCount) &&
            (identical(other.averageWaitTime, averageWaitTime) ||
                other.averageWaitTime == averageWaitTime) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.nextAvailableSlot, nextAvailableSlot) ||
                other.nextAvailableSlot == nextAvailableSlot));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, queueId, queueName, locationName,
      waitingCount, averageWaitTime, isActive, nextAvailableSlot);

  /// Create a copy of QueueStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QueueStatusImplCopyWith<_$QueueStatusImpl> get copyWith =>
      __$$QueueStatusImplCopyWithImpl<_$QueueStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QueueStatusImplToJson(
      this,
    );
  }
}

abstract class _QueueStatus implements QueueStatus {
  const factory _QueueStatus(
      {required final String queueId,
      required final String queueName,
      required final String locationName,
      final int waitingCount,
      final double averageWaitTime,
      final bool isActive,
      final DateTime? nextAvailableSlot}) = _$QueueStatusImpl;

  factory _QueueStatus.fromJson(Map<String, dynamic> json) =
      _$QueueStatusImpl.fromJson;

  @override
  String get queueId;
  @override
  String get queueName;
  @override
  String get locationName;
  @override
  int get waitingCount;
  @override
  double get averageWaitTime;
  @override
  bool get isActive;
  @override
  DateTime? get nextAvailableSlot;

  /// Create a copy of QueueStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QueueStatusImplCopyWith<_$QueueStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorkingHours _$WorkingHoursFromJson(Map<String, dynamic> json) {
  return _WorkingHours.fromJson(json);
}

/// @nodoc
mixin _$WorkingHours {
  String get openTime => throw _privateConstructorUsedError;
  String get closeTime => throw _privateConstructorUsedError;
  bool get isOpen => throw _privateConstructorUsedError;
  List<BreakTime> get breakTimes => throw _privateConstructorUsedError;

  /// Serializes this WorkingHours to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkingHoursCopyWith<WorkingHours> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkingHoursCopyWith<$Res> {
  factory $WorkingHoursCopyWith(
          WorkingHours value, $Res Function(WorkingHours) then) =
      _$WorkingHoursCopyWithImpl<$Res, WorkingHours>;
  @useResult
  $Res call(
      {String openTime,
      String closeTime,
      bool isOpen,
      List<BreakTime> breakTimes});
}

/// @nodoc
class _$WorkingHoursCopyWithImpl<$Res, $Val extends WorkingHours>
    implements $WorkingHoursCopyWith<$Res> {
  _$WorkingHoursCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? openTime = null,
    Object? closeTime = null,
    Object? isOpen = null,
    Object? breakTimes = null,
  }) {
    return _then(_value.copyWith(
      openTime: null == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as String,
      closeTime: null == closeTime
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as String,
      isOpen: null == isOpen
          ? _value.isOpen
          : isOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      breakTimes: null == breakTimes
          ? _value.breakTimes
          : breakTimes // ignore: cast_nullable_to_non_nullable
              as List<BreakTime>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkingHoursImplCopyWith<$Res>
    implements $WorkingHoursCopyWith<$Res> {
  factory _$$WorkingHoursImplCopyWith(
          _$WorkingHoursImpl value, $Res Function(_$WorkingHoursImpl) then) =
      __$$WorkingHoursImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String openTime,
      String closeTime,
      bool isOpen,
      List<BreakTime> breakTimes});
}

/// @nodoc
class __$$WorkingHoursImplCopyWithImpl<$Res>
    extends _$WorkingHoursCopyWithImpl<$Res, _$WorkingHoursImpl>
    implements _$$WorkingHoursImplCopyWith<$Res> {
  __$$WorkingHoursImplCopyWithImpl(
      _$WorkingHoursImpl _value, $Res Function(_$WorkingHoursImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? openTime = null,
    Object? closeTime = null,
    Object? isOpen = null,
    Object? breakTimes = null,
  }) {
    return _then(_$WorkingHoursImpl(
      openTime: null == openTime
          ? _value.openTime
          : openTime // ignore: cast_nullable_to_non_nullable
              as String,
      closeTime: null == closeTime
          ? _value.closeTime
          : closeTime // ignore: cast_nullable_to_non_nullable
              as String,
      isOpen: null == isOpen
          ? _value.isOpen
          : isOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      breakTimes: null == breakTimes
          ? _value._breakTimes
          : breakTimes // ignore: cast_nullable_to_non_nullable
              as List<BreakTime>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkingHoursImpl implements _WorkingHours {
  const _$WorkingHoursImpl(
      {required this.openTime,
      required this.closeTime,
      this.isOpen = true,
      final List<BreakTime> breakTimes = const []})
      : _breakTimes = breakTimes;

  factory _$WorkingHoursImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkingHoursImplFromJson(json);

  @override
  final String openTime;
  @override
  final String closeTime;
  @override
  @JsonKey()
  final bool isOpen;
  final List<BreakTime> _breakTimes;
  @override
  @JsonKey()
  List<BreakTime> get breakTimes {
    if (_breakTimes is EqualUnmodifiableListView) return _breakTimes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_breakTimes);
  }

  @override
  String toString() {
    return 'WorkingHours(openTime: $openTime, closeTime: $closeTime, isOpen: $isOpen, breakTimes: $breakTimes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkingHoursImpl &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            (identical(other.isOpen, isOpen) || other.isOpen == isOpen) &&
            const DeepCollectionEquality()
                .equals(other._breakTimes, _breakTimes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, openTime, closeTime, isOpen,
      const DeepCollectionEquality().hash(_breakTimes));

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkingHoursImplCopyWith<_$WorkingHoursImpl> get copyWith =>
      __$$WorkingHoursImplCopyWithImpl<_$WorkingHoursImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkingHoursImplToJson(
      this,
    );
  }
}

abstract class _WorkingHours implements WorkingHours {
  const factory _WorkingHours(
      {required final String openTime,
      required final String closeTime,
      final bool isOpen,
      final List<BreakTime> breakTimes}) = _$WorkingHoursImpl;

  factory _WorkingHours.fromJson(Map<String, dynamic> json) =
      _$WorkingHoursImpl.fromJson;

  @override
  String get openTime;
  @override
  String get closeTime;
  @override
  bool get isOpen;
  @override
  List<BreakTime> get breakTimes;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkingHoursImplCopyWith<_$WorkingHoursImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BreakTime _$BreakTimeFromJson(Map<String, dynamic> json) {
  return _BreakTime.fromJson(json);
}

/// @nodoc
mixin _$BreakTime {
  String get startTime => throw _privateConstructorUsedError;
  String get endTime => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;

  /// Serializes this BreakTime to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BreakTime
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BreakTimeCopyWith<BreakTime> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BreakTimeCopyWith<$Res> {
  factory $BreakTimeCopyWith(BreakTime value, $Res Function(BreakTime) then) =
      _$BreakTimeCopyWithImpl<$Res, BreakTime>;
  @useResult
  $Res call({String startTime, String endTime, String description});
}

/// @nodoc
class _$BreakTimeCopyWithImpl<$Res, $Val extends BreakTime>
    implements $BreakTimeCopyWith<$Res> {
  _$BreakTimeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BreakTime
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startTime = null,
    Object? endTime = null,
    Object? description = null,
  }) {
    return _then(_value.copyWith(
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BreakTimeImplCopyWith<$Res>
    implements $BreakTimeCopyWith<$Res> {
  factory _$$BreakTimeImplCopyWith(
          _$BreakTimeImpl value, $Res Function(_$BreakTimeImpl) then) =
      __$$BreakTimeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String startTime, String endTime, String description});
}

/// @nodoc
class __$$BreakTimeImplCopyWithImpl<$Res>
    extends _$BreakTimeCopyWithImpl<$Res, _$BreakTimeImpl>
    implements _$$BreakTimeImplCopyWith<$Res> {
  __$$BreakTimeImplCopyWithImpl(
      _$BreakTimeImpl _value, $Res Function(_$BreakTimeImpl) _then)
      : super(_value, _then);

  /// Create a copy of BreakTime
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startTime = null,
    Object? endTime = null,
    Object? description = null,
  }) {
    return _then(_$BreakTimeImpl(
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BreakTimeImpl implements _BreakTime {
  const _$BreakTimeImpl(
      {required this.startTime,
      required this.endTime,
      required this.description});

  factory _$BreakTimeImpl.fromJson(Map<String, dynamic> json) =>
      _$$BreakTimeImplFromJson(json);

  @override
  final String startTime;
  @override
  final String endTime;
  @override
  final String description;

  @override
  String toString() {
    return 'BreakTime(startTime: $startTime, endTime: $endTime, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BreakTimeImpl &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, startTime, endTime, description);

  /// Create a copy of BreakTime
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BreakTimeImplCopyWith<_$BreakTimeImpl> get copyWith =>
      __$$BreakTimeImplCopyWithImpl<_$BreakTimeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BreakTimeImplToJson(
      this,
    );
  }
}

abstract class _BreakTime implements BreakTime {
  const factory _BreakTime(
      {required final String startTime,
      required final String endTime,
      required final String description}) = _$BreakTimeImpl;

  factory _BreakTime.fromJson(Map<String, dynamic> json) =
      _$BreakTimeImpl.fromJson;

  @override
  String get startTime;
  @override
  String get endTime;
  @override
  String get description;

  /// Create a copy of BreakTime
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BreakTimeImplCopyWith<_$BreakTimeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

NotificationItem _$NotificationItemFromJson(Map<String, dynamic> json) {
  return _NotificationItem.fromJson(json);
}

/// @nodoc
mixin _$NotificationItem {
  String get id => throw _privateConstructorUsedError;
  NotificationType get type => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  bool get isRead => throw _privateConstructorUsedError;
  String get priority => throw _privateConstructorUsedError;
  Map<String, dynamic>? get actionData => throw _privateConstructorUsedError;

  /// Serializes this NotificationItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationItemCopyWith<NotificationItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationItemCopyWith<$Res> {
  factory $NotificationItemCopyWith(
          NotificationItem value, $Res Function(NotificationItem) then) =
      _$NotificationItemCopyWithImpl<$Res, NotificationItem>;
  @useResult
  $Res call(
      {String id,
      NotificationType type,
      String title,
      String message,
      DateTime timestamp,
      bool isRead,
      String priority,
      Map<String, dynamic>? actionData});
}

/// @nodoc
class _$NotificationItemCopyWithImpl<$Res, $Val extends NotificationItem>
    implements $NotificationItemCopyWith<$Res> {
  _$NotificationItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? title = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
    Object? priority = null,
    Object? actionData = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as NotificationType,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as String,
      actionData: freezed == actionData
          ? _value.actionData
          : actionData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NotificationItemImplCopyWith<$Res>
    implements $NotificationItemCopyWith<$Res> {
  factory _$$NotificationItemImplCopyWith(_$NotificationItemImpl value,
          $Res Function(_$NotificationItemImpl) then) =
      __$$NotificationItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      NotificationType type,
      String title,
      String message,
      DateTime timestamp,
      bool isRead,
      String priority,
      Map<String, dynamic>? actionData});
}

/// @nodoc
class __$$NotificationItemImplCopyWithImpl<$Res>
    extends _$NotificationItemCopyWithImpl<$Res, _$NotificationItemImpl>
    implements _$$NotificationItemImplCopyWith<$Res> {
  __$$NotificationItemImplCopyWithImpl(_$NotificationItemImpl _value,
      $Res Function(_$NotificationItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? type = null,
    Object? title = null,
    Object? message = null,
    Object? timestamp = null,
    Object? isRead = null,
    Object? priority = null,
    Object? actionData = freezed,
  }) {
    return _then(_$NotificationItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as NotificationType,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isRead: null == isRead
          ? _value.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as String,
      actionData: freezed == actionData
          ? _value._actionData
          : actionData // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationItemImpl implements _NotificationItem {
  const _$NotificationItemImpl(
      {required this.id,
      required this.type,
      required this.title,
      required this.message,
      required this.timestamp,
      this.isRead = false,
      this.priority = 'medium',
      final Map<String, dynamic>? actionData})
      : _actionData = actionData;

  factory _$NotificationItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationItemImplFromJson(json);

  @override
  final String id;
  @override
  final NotificationType type;
  @override
  final String title;
  @override
  final String message;
  @override
  final DateTime timestamp;
  @override
  @JsonKey()
  final bool isRead;
  @override
  @JsonKey()
  final String priority;
  final Map<String, dynamic>? _actionData;
  @override
  Map<String, dynamic>? get actionData {
    final value = _actionData;
    if (value == null) return null;
    if (_actionData is EqualUnmodifiableMapView) return _actionData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'NotificationItem(id: $id, type: $type, title: $title, message: $message, timestamp: $timestamp, isRead: $isRead, priority: $priority, actionData: $actionData)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            const DeepCollectionEquality()
                .equals(other._actionData, _actionData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      type,
      title,
      message,
      timestamp,
      isRead,
      priority,
      const DeepCollectionEquality().hash(_actionData));

  /// Create a copy of NotificationItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationItemImplCopyWith<_$NotificationItemImpl> get copyWith =>
      __$$NotificationItemImplCopyWithImpl<_$NotificationItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationItemImplToJson(
      this,
    );
  }
}

abstract class _NotificationItem implements NotificationItem {
  const factory _NotificationItem(
      {required final String id,
      required final NotificationType type,
      required final String title,
      required final String message,
      required final DateTime timestamp,
      final bool isRead,
      final String priority,
      final Map<String, dynamic>? actionData}) = _$NotificationItemImpl;

  factory _NotificationItem.fromJson(Map<String, dynamic> json) =
      _$NotificationItemImpl.fromJson;

  @override
  String get id;
  @override
  NotificationType get type;
  @override
  String get title;
  @override
  String get message;
  @override
  DateTime get timestamp;
  @override
  bool get isRead;
  @override
  String get priority;
  @override
  Map<String, dynamic>? get actionData;

  /// Create a copy of NotificationItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationItemImplCopyWith<_$NotificationItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuickStats _$QuickStatsFromJson(Map<String, dynamic> json) {
  return _QuickStats.fromJson(json);
}

/// @nodoc
mixin _$QuickStats {
  int get currentWaitingCustomers => throw _privateConstructorUsedError;
  int get activeQueues => throw _privateConstructorUsedError;
  double get todayRevenue => throw _privateConstructorUsedError;
  int get unreadNotifications => throw _privateConstructorUsedError;
  int get nextAppointmentIn => throw _privateConstructorUsedError; // minutes
  double get averageWaitTime => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this QuickStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuickStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuickStatsCopyWith<QuickStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickStatsCopyWith<$Res> {
  factory $QuickStatsCopyWith(
          QuickStats value, $Res Function(QuickStats) then) =
      _$QuickStatsCopyWithImpl<$Res, QuickStats>;
  @useResult
  $Res call(
      {int currentWaitingCustomers,
      int activeQueues,
      double todayRevenue,
      int unreadNotifications,
      int nextAppointmentIn,
      double averageWaitTime,
      DateTime? lastUpdated});
}

/// @nodoc
class _$QuickStatsCopyWithImpl<$Res, $Val extends QuickStats>
    implements $QuickStatsCopyWith<$Res> {
  _$QuickStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuickStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentWaitingCustomers = null,
    Object? activeQueues = null,
    Object? todayRevenue = null,
    Object? unreadNotifications = null,
    Object? nextAppointmentIn = null,
    Object? averageWaitTime = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      currentWaitingCustomers: null == currentWaitingCustomers
          ? _value.currentWaitingCustomers
          : currentWaitingCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      activeQueues: null == activeQueues
          ? _value.activeQueues
          : activeQueues // ignore: cast_nullable_to_non_nullable
              as int,
      todayRevenue: null == todayRevenue
          ? _value.todayRevenue
          : todayRevenue // ignore: cast_nullable_to_non_nullable
              as double,
      unreadNotifications: null == unreadNotifications
          ? _value.unreadNotifications
          : unreadNotifications // ignore: cast_nullable_to_non_nullable
              as int,
      nextAppointmentIn: null == nextAppointmentIn
          ? _value.nextAppointmentIn
          : nextAppointmentIn // ignore: cast_nullable_to_non_nullable
              as int,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuickStatsImplCopyWith<$Res>
    implements $QuickStatsCopyWith<$Res> {
  factory _$$QuickStatsImplCopyWith(
          _$QuickStatsImpl value, $Res Function(_$QuickStatsImpl) then) =
      __$$QuickStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentWaitingCustomers,
      int activeQueues,
      double todayRevenue,
      int unreadNotifications,
      int nextAppointmentIn,
      double averageWaitTime,
      DateTime? lastUpdated});
}

/// @nodoc
class __$$QuickStatsImplCopyWithImpl<$Res>
    extends _$QuickStatsCopyWithImpl<$Res, _$QuickStatsImpl>
    implements _$$QuickStatsImplCopyWith<$Res> {
  __$$QuickStatsImplCopyWithImpl(
      _$QuickStatsImpl _value, $Res Function(_$QuickStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuickStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentWaitingCustomers = null,
    Object? activeQueues = null,
    Object? todayRevenue = null,
    Object? unreadNotifications = null,
    Object? nextAppointmentIn = null,
    Object? averageWaitTime = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$QuickStatsImpl(
      currentWaitingCustomers: null == currentWaitingCustomers
          ? _value.currentWaitingCustomers
          : currentWaitingCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      activeQueues: null == activeQueues
          ? _value.activeQueues
          : activeQueues // ignore: cast_nullable_to_non_nullable
              as int,
      todayRevenue: null == todayRevenue
          ? _value.todayRevenue
          : todayRevenue // ignore: cast_nullable_to_non_nullable
              as double,
      unreadNotifications: null == unreadNotifications
          ? _value.unreadNotifications
          : unreadNotifications // ignore: cast_nullable_to_non_nullable
              as int,
      nextAppointmentIn: null == nextAppointmentIn
          ? _value.nextAppointmentIn
          : nextAppointmentIn // ignore: cast_nullable_to_non_nullable
              as int,
      averageWaitTime: null == averageWaitTime
          ? _value.averageWaitTime
          : averageWaitTime // ignore: cast_nullable_to_non_nullable
              as double,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuickStatsImpl implements _QuickStats {
  const _$QuickStatsImpl(
      {this.currentWaitingCustomers = 0,
      this.activeQueues = 0,
      this.todayRevenue = 0.0,
      this.unreadNotifications = 0,
      this.nextAppointmentIn = 0,
      this.averageWaitTime = 0.0,
      this.lastUpdated});

  factory _$QuickStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuickStatsImplFromJson(json);

  @override
  @JsonKey()
  final int currentWaitingCustomers;
  @override
  @JsonKey()
  final int activeQueues;
  @override
  @JsonKey()
  final double todayRevenue;
  @override
  @JsonKey()
  final int unreadNotifications;
  @override
  @JsonKey()
  final int nextAppointmentIn;
// minutes
  @override
  @JsonKey()
  final double averageWaitTime;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'QuickStats(currentWaitingCustomers: $currentWaitingCustomers, activeQueues: $activeQueues, todayRevenue: $todayRevenue, unreadNotifications: $unreadNotifications, nextAppointmentIn: $nextAppointmentIn, averageWaitTime: $averageWaitTime, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuickStatsImpl &&
            (identical(
                    other.currentWaitingCustomers, currentWaitingCustomers) ||
                other.currentWaitingCustomers == currentWaitingCustomers) &&
            (identical(other.activeQueues, activeQueues) ||
                other.activeQueues == activeQueues) &&
            (identical(other.todayRevenue, todayRevenue) ||
                other.todayRevenue == todayRevenue) &&
            (identical(other.unreadNotifications, unreadNotifications) ||
                other.unreadNotifications == unreadNotifications) &&
            (identical(other.nextAppointmentIn, nextAppointmentIn) ||
                other.nextAppointmentIn == nextAppointmentIn) &&
            (identical(other.averageWaitTime, averageWaitTime) ||
                other.averageWaitTime == averageWaitTime) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      currentWaitingCustomers,
      activeQueues,
      todayRevenue,
      unreadNotifications,
      nextAppointmentIn,
      averageWaitTime,
      lastUpdated);

  /// Create a copy of QuickStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickStatsImplCopyWith<_$QuickStatsImpl> get copyWith =>
      __$$QuickStatsImplCopyWithImpl<_$QuickStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuickStatsImplToJson(
      this,
    );
  }
}

abstract class _QuickStats implements QuickStats {
  const factory _QuickStats(
      {final int currentWaitingCustomers,
      final int activeQueues,
      final double todayRevenue,
      final int unreadNotifications,
      final int nextAppointmentIn,
      final double averageWaitTime,
      final DateTime? lastUpdated}) = _$QuickStatsImpl;

  factory _QuickStats.fromJson(Map<String, dynamic> json) =
      _$QuickStatsImpl.fromJson;

  @override
  int get currentWaitingCustomers;
  @override
  int get activeQueues;
  @override
  double get todayRevenue;
  @override
  int get unreadNotifications;
  @override
  int get nextAppointmentIn; // minutes
  @override
  double get averageWaitTime;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of QuickStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuickStatsImplCopyWith<_$QuickStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DashboardData _$DashboardDataFromJson(Map<String, dynamic> json) {
  return _DashboardData.fromJson(json);
}

/// @nodoc
mixin _$DashboardData {
  BusinessMetrics? get businessMetrics => throw _privateConstructorUsedError;
  ScheduleData? get scheduleData => throw _privateConstructorUsedError;
  List<NotificationItem> get notifications =>
      throw _privateConstructorUsedError;
  QuickStats? get quickStats => throw _privateConstructorUsedError;
  int get unreadNotificationCount => throw _privateConstructorUsedError;
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this DashboardData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardDataCopyWith<DashboardData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardDataCopyWith<$Res> {
  factory $DashboardDataCopyWith(
          DashboardData value, $Res Function(DashboardData) then) =
      _$DashboardDataCopyWithImpl<$Res, DashboardData>;
  @useResult
  $Res call(
      {BusinessMetrics? businessMetrics,
      ScheduleData? scheduleData,
      List<NotificationItem> notifications,
      QuickStats? quickStats,
      int unreadNotificationCount,
      DateTime? lastUpdated});

  $BusinessMetricsCopyWith<$Res>? get businessMetrics;
  $ScheduleDataCopyWith<$Res>? get scheduleData;
  $QuickStatsCopyWith<$Res>? get quickStats;
}

/// @nodoc
class _$DashboardDataCopyWithImpl<$Res, $Val extends DashboardData>
    implements $DashboardDataCopyWith<$Res> {
  _$DashboardDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessMetrics = freezed,
    Object? scheduleData = freezed,
    Object? notifications = null,
    Object? quickStats = freezed,
    Object? unreadNotificationCount = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      businessMetrics: freezed == businessMetrics
          ? _value.businessMetrics
          : businessMetrics // ignore: cast_nullable_to_non_nullable
              as BusinessMetrics?,
      scheduleData: freezed == scheduleData
          ? _value.scheduleData
          : scheduleData // ignore: cast_nullable_to_non_nullable
              as ScheduleData?,
      notifications: null == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationItem>,
      quickStats: freezed == quickStats
          ? _value.quickStats
          : quickStats // ignore: cast_nullable_to_non_nullable
              as QuickStats?,
      unreadNotificationCount: null == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BusinessMetricsCopyWith<$Res>? get businessMetrics {
    if (_value.businessMetrics == null) {
      return null;
    }

    return $BusinessMetricsCopyWith<$Res>(_value.businessMetrics!, (value) {
      return _then(_value.copyWith(businessMetrics: value) as $Val);
    });
  }

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ScheduleDataCopyWith<$Res>? get scheduleData {
    if (_value.scheduleData == null) {
      return null;
    }

    return $ScheduleDataCopyWith<$Res>(_value.scheduleData!, (value) {
      return _then(_value.copyWith(scheduleData: value) as $Val);
    });
  }

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickStatsCopyWith<$Res>? get quickStats {
    if (_value.quickStats == null) {
      return null;
    }

    return $QuickStatsCopyWith<$Res>(_value.quickStats!, (value) {
      return _then(_value.copyWith(quickStats: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$DashboardDataImplCopyWith<$Res>
    implements $DashboardDataCopyWith<$Res> {
  factory _$$DashboardDataImplCopyWith(
          _$DashboardDataImpl value, $Res Function(_$DashboardDataImpl) then) =
      __$$DashboardDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BusinessMetrics? businessMetrics,
      ScheduleData? scheduleData,
      List<NotificationItem> notifications,
      QuickStats? quickStats,
      int unreadNotificationCount,
      DateTime? lastUpdated});

  @override
  $BusinessMetricsCopyWith<$Res>? get businessMetrics;
  @override
  $ScheduleDataCopyWith<$Res>? get scheduleData;
  @override
  $QuickStatsCopyWith<$Res>? get quickStats;
}

/// @nodoc
class __$$DashboardDataImplCopyWithImpl<$Res>
    extends _$DashboardDataCopyWithImpl<$Res, _$DashboardDataImpl>
    implements _$$DashboardDataImplCopyWith<$Res> {
  __$$DashboardDataImplCopyWithImpl(
      _$DashboardDataImpl _value, $Res Function(_$DashboardDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessMetrics = freezed,
    Object? scheduleData = freezed,
    Object? notifications = null,
    Object? quickStats = freezed,
    Object? unreadNotificationCount = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$DashboardDataImpl(
      businessMetrics: freezed == businessMetrics
          ? _value.businessMetrics
          : businessMetrics // ignore: cast_nullable_to_non_nullable
              as BusinessMetrics?,
      scheduleData: freezed == scheduleData
          ? _value.scheduleData
          : scheduleData // ignore: cast_nullable_to_non_nullable
              as ScheduleData?,
      notifications: null == notifications
          ? _value._notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationItem>,
      quickStats: freezed == quickStats
          ? _value.quickStats
          : quickStats // ignore: cast_nullable_to_non_nullable
              as QuickStats?,
      unreadNotificationCount: null == unreadNotificationCount
          ? _value.unreadNotificationCount
          : unreadNotificationCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DashboardDataImpl implements _DashboardData {
  const _$DashboardDataImpl(
      {this.businessMetrics,
      this.scheduleData,
      final List<NotificationItem> notifications = const [],
      this.quickStats,
      this.unreadNotificationCount = 0,
      this.lastUpdated})
      : _notifications = notifications;

  factory _$DashboardDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardDataImplFromJson(json);

  @override
  final BusinessMetrics? businessMetrics;
  @override
  final ScheduleData? scheduleData;
  final List<NotificationItem> _notifications;
  @override
  @JsonKey()
  List<NotificationItem> get notifications {
    if (_notifications is EqualUnmodifiableListView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notifications);
  }

  @override
  final QuickStats? quickStats;
  @override
  @JsonKey()
  final int unreadNotificationCount;
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'DashboardData(businessMetrics: $businessMetrics, scheduleData: $scheduleData, notifications: $notifications, quickStats: $quickStats, unreadNotificationCount: $unreadNotificationCount, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardDataImpl &&
            (identical(other.businessMetrics, businessMetrics) ||
                other.businessMetrics == businessMetrics) &&
            (identical(other.scheduleData, scheduleData) ||
                other.scheduleData == scheduleData) &&
            const DeepCollectionEquality()
                .equals(other._notifications, _notifications) &&
            (identical(other.quickStats, quickStats) ||
                other.quickStats == quickStats) &&
            (identical(
                    other.unreadNotificationCount, unreadNotificationCount) ||
                other.unreadNotificationCount == unreadNotificationCount) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      businessMetrics,
      scheduleData,
      const DeepCollectionEquality().hash(_notifications),
      quickStats,
      unreadNotificationCount,
      lastUpdated);

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardDataImplCopyWith<_$DashboardDataImpl> get copyWith =>
      __$$DashboardDataImplCopyWithImpl<_$DashboardDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardDataImplToJson(
      this,
    );
  }
}

abstract class _DashboardData implements DashboardData {
  const factory _DashboardData(
      {final BusinessMetrics? businessMetrics,
      final ScheduleData? scheduleData,
      final List<NotificationItem> notifications,
      final QuickStats? quickStats,
      final int unreadNotificationCount,
      final DateTime? lastUpdated}) = _$DashboardDataImpl;

  factory _DashboardData.fromJson(Map<String, dynamic> json) =
      _$DashboardDataImpl.fromJson;

  @override
  BusinessMetrics? get businessMetrics;
  @override
  ScheduleData? get scheduleData;
  @override
  List<NotificationItem> get notifications;
  @override
  QuickStats? get quickStats;
  @override
  int get unreadNotificationCount;
  @override
  DateTime? get lastUpdated;

  /// Create a copy of DashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardDataImplCopyWith<_$DashboardDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BusinessMetricsResponse _$BusinessMetricsResponseFromJson(
    Map<String, dynamic> json) {
  return _BusinessMetricsResponse.fromJson(json);
}

/// @nodoc
mixin _$BusinessMetricsResponse {
  bool get success => throw _privateConstructorUsedError;
  BusinessMetrics? get data => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this BusinessMetricsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessMetricsResponseCopyWith<BusinessMetricsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessMetricsResponseCopyWith<$Res> {
  factory $BusinessMetricsResponseCopyWith(BusinessMetricsResponse value,
          $Res Function(BusinessMetricsResponse) then) =
      _$BusinessMetricsResponseCopyWithImpl<$Res, BusinessMetricsResponse>;
  @useResult
  $Res call({bool success, BusinessMetrics? data, DashboardError? error});

  $BusinessMetricsCopyWith<$Res>? get data;
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$BusinessMetricsResponseCopyWithImpl<$Res,
        $Val extends BusinessMetricsResponse>
    implements $BusinessMetricsResponseCopyWith<$Res> {
  _$BusinessMetricsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as BusinessMetrics?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BusinessMetricsCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $BusinessMetricsCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BusinessMetricsResponseImplCopyWith<$Res>
    implements $BusinessMetricsResponseCopyWith<$Res> {
  factory _$$BusinessMetricsResponseImplCopyWith(
          _$BusinessMetricsResponseImpl value,
          $Res Function(_$BusinessMetricsResponseImpl) then) =
      __$$BusinessMetricsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, BusinessMetrics? data, DashboardError? error});

  @override
  $BusinessMetricsCopyWith<$Res>? get data;
  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$BusinessMetricsResponseImplCopyWithImpl<$Res>
    extends _$BusinessMetricsResponseCopyWithImpl<$Res,
        _$BusinessMetricsResponseImpl>
    implements _$$BusinessMetricsResponseImplCopyWith<$Res> {
  __$$BusinessMetricsResponseImplCopyWithImpl(
      _$BusinessMetricsResponseImpl _value,
      $Res Function(_$BusinessMetricsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_$BusinessMetricsResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as BusinessMetrics?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BusinessMetricsResponseImpl implements _BusinessMetricsResponse {
  const _$BusinessMetricsResponseImpl(
      {required this.success, this.data, this.error});

  factory _$BusinessMetricsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BusinessMetricsResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final BusinessMetrics? data;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'BusinessMetricsResponse(success: $success, data: $data, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessMetricsResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, error);

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessMetricsResponseImplCopyWith<_$BusinessMetricsResponseImpl>
      get copyWith => __$$BusinessMetricsResponseImplCopyWithImpl<
          _$BusinessMetricsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BusinessMetricsResponseImplToJson(
      this,
    );
  }
}

abstract class _BusinessMetricsResponse implements BusinessMetricsResponse {
  const factory _BusinessMetricsResponse(
      {required final bool success,
      final BusinessMetrics? data,
      final DashboardError? error}) = _$BusinessMetricsResponseImpl;

  factory _BusinessMetricsResponse.fromJson(Map<String, dynamic> json) =
      _$BusinessMetricsResponseImpl.fromJson;

  @override
  bool get success;
  @override
  BusinessMetrics? get data;
  @override
  DashboardError? get error;

  /// Create a copy of BusinessMetricsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessMetricsResponseImplCopyWith<_$BusinessMetricsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

ScheduleDataResponse _$ScheduleDataResponseFromJson(Map<String, dynamic> json) {
  return _ScheduleDataResponse.fromJson(json);
}

/// @nodoc
mixin _$ScheduleDataResponse {
  bool get success => throw _privateConstructorUsedError;
  ScheduleData? get data => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this ScheduleDataResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ScheduleDataResponseCopyWith<ScheduleDataResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ScheduleDataResponseCopyWith<$Res> {
  factory $ScheduleDataResponseCopyWith(ScheduleDataResponse value,
          $Res Function(ScheduleDataResponse) then) =
      _$ScheduleDataResponseCopyWithImpl<$Res, ScheduleDataResponse>;
  @useResult
  $Res call({bool success, ScheduleData? data, DashboardError? error});

  $ScheduleDataCopyWith<$Res>? get data;
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$ScheduleDataResponseCopyWithImpl<$Res,
        $Val extends ScheduleDataResponse>
    implements $ScheduleDataResponseCopyWith<$Res> {
  _$ScheduleDataResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ScheduleData?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ScheduleDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $ScheduleDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ScheduleDataResponseImplCopyWith<$Res>
    implements $ScheduleDataResponseCopyWith<$Res> {
  factory _$$ScheduleDataResponseImplCopyWith(_$ScheduleDataResponseImpl value,
          $Res Function(_$ScheduleDataResponseImpl) then) =
      __$$ScheduleDataResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, ScheduleData? data, DashboardError? error});

  @override
  $ScheduleDataCopyWith<$Res>? get data;
  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$ScheduleDataResponseImplCopyWithImpl<$Res>
    extends _$ScheduleDataResponseCopyWithImpl<$Res, _$ScheduleDataResponseImpl>
    implements _$$ScheduleDataResponseImplCopyWith<$Res> {
  __$$ScheduleDataResponseImplCopyWithImpl(_$ScheduleDataResponseImpl _value,
      $Res Function(_$ScheduleDataResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_$ScheduleDataResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as ScheduleData?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ScheduleDataResponseImpl implements _ScheduleDataResponse {
  const _$ScheduleDataResponseImpl(
      {required this.success, this.data, this.error});

  factory _$ScheduleDataResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ScheduleDataResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final ScheduleData? data;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'ScheduleDataResponse(success: $success, data: $data, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleDataResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, error);

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleDataResponseImplCopyWith<_$ScheduleDataResponseImpl>
      get copyWith =>
          __$$ScheduleDataResponseImplCopyWithImpl<_$ScheduleDataResponseImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ScheduleDataResponseImplToJson(
      this,
    );
  }
}

abstract class _ScheduleDataResponse implements ScheduleDataResponse {
  const factory _ScheduleDataResponse(
      {required final bool success,
      final ScheduleData? data,
      final DashboardError? error}) = _$ScheduleDataResponseImpl;

  factory _ScheduleDataResponse.fromJson(Map<String, dynamic> json) =
      _$ScheduleDataResponseImpl.fromJson;

  @override
  bool get success;
  @override
  ScheduleData? get data;
  @override
  DashboardError? get error;

  /// Create a copy of ScheduleDataResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleDataResponseImplCopyWith<_$ScheduleDataResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

NotificationsResponse _$NotificationsResponseFromJson(
    Map<String, dynamic> json) {
  return _NotificationsResponse.fromJson(json);
}

/// @nodoc
mixin _$NotificationsResponse {
  bool get success => throw _privateConstructorUsedError;
  List<NotificationItem> get notifications =>
      throw _privateConstructorUsedError;
  int get unreadCount => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this NotificationsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NotificationsResponseCopyWith<NotificationsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NotificationsResponseCopyWith<$Res> {
  factory $NotificationsResponseCopyWith(NotificationsResponse value,
          $Res Function(NotificationsResponse) then) =
      _$NotificationsResponseCopyWithImpl<$Res, NotificationsResponse>;
  @useResult
  $Res call(
      {bool success,
      List<NotificationItem> notifications,
      int unreadCount,
      int totalCount,
      DashboardError? error});

  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$NotificationsResponseCopyWithImpl<$Res,
        $Val extends NotificationsResponse>
    implements $NotificationsResponseCopyWith<$Res> {
  _$NotificationsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? notifications = null,
    Object? unreadCount = null,
    Object? totalCount = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      notifications: null == notifications
          ? _value.notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationItem>,
      unreadCount: null == unreadCount
          ? _value.unreadCount
          : unreadCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$NotificationsResponseImplCopyWith<$Res>
    implements $NotificationsResponseCopyWith<$Res> {
  factory _$$NotificationsResponseImplCopyWith(
          _$NotificationsResponseImpl value,
          $Res Function(_$NotificationsResponseImpl) then) =
      __$$NotificationsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      List<NotificationItem> notifications,
      int unreadCount,
      int totalCount,
      DashboardError? error});

  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$NotificationsResponseImplCopyWithImpl<$Res>
    extends _$NotificationsResponseCopyWithImpl<$Res,
        _$NotificationsResponseImpl>
    implements _$$NotificationsResponseImplCopyWith<$Res> {
  __$$NotificationsResponseImplCopyWithImpl(_$NotificationsResponseImpl _value,
      $Res Function(_$NotificationsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? notifications = null,
    Object? unreadCount = null,
    Object? totalCount = null,
    Object? error = freezed,
  }) {
    return _then(_$NotificationsResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      notifications: null == notifications
          ? _value._notifications
          : notifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationItem>,
      unreadCount: null == unreadCount
          ? _value.unreadCount
          : unreadCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$NotificationsResponseImpl implements _NotificationsResponse {
  const _$NotificationsResponseImpl(
      {required this.success,
      final List<NotificationItem> notifications = const [],
      this.unreadCount = 0,
      this.totalCount = 0,
      this.error})
      : _notifications = notifications;

  factory _$NotificationsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$NotificationsResponseImplFromJson(json);

  @override
  final bool success;
  final List<NotificationItem> _notifications;
  @override
  @JsonKey()
  List<NotificationItem> get notifications {
    if (_notifications is EqualUnmodifiableListView) return _notifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_notifications);
  }

  @override
  @JsonKey()
  final int unreadCount;
  @override
  @JsonKey()
  final int totalCount;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'NotificationsResponse(success: $success, notifications: $notifications, unreadCount: $unreadCount, totalCount: $totalCount, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotificationsResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality()
                .equals(other._notifications, _notifications) &&
            (identical(other.unreadCount, unreadCount) ||
                other.unreadCount == unreadCount) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      const DeepCollectionEquality().hash(_notifications),
      unreadCount,
      totalCount,
      error);

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotificationsResponseImplCopyWith<_$NotificationsResponseImpl>
      get copyWith => __$$NotificationsResponseImplCopyWithImpl<
          _$NotificationsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$NotificationsResponseImplToJson(
      this,
    );
  }
}

abstract class _NotificationsResponse implements NotificationsResponse {
  const factory _NotificationsResponse(
      {required final bool success,
      final List<NotificationItem> notifications,
      final int unreadCount,
      final int totalCount,
      final DashboardError? error}) = _$NotificationsResponseImpl;

  factory _NotificationsResponse.fromJson(Map<String, dynamic> json) =
      _$NotificationsResponseImpl.fromJson;

  @override
  bool get success;
  @override
  List<NotificationItem> get notifications;
  @override
  int get unreadCount;
  @override
  int get totalCount;
  @override
  DashboardError? get error;

  /// Create a copy of NotificationsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotificationsResponseImplCopyWith<_$NotificationsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

QuickStatsResponse _$QuickStatsResponseFromJson(Map<String, dynamic> json) {
  return _QuickStatsResponse.fromJson(json);
}

/// @nodoc
mixin _$QuickStatsResponse {
  bool get success => throw _privateConstructorUsedError;
  QuickStats? get data => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this QuickStatsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuickStatsResponseCopyWith<QuickStatsResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuickStatsResponseCopyWith<$Res> {
  factory $QuickStatsResponseCopyWith(
          QuickStatsResponse value, $Res Function(QuickStatsResponse) then) =
      _$QuickStatsResponseCopyWithImpl<$Res, QuickStatsResponse>;
  @useResult
  $Res call({bool success, QuickStats? data, DashboardError? error});

  $QuickStatsCopyWith<$Res>? get data;
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$QuickStatsResponseCopyWithImpl<$Res, $Val extends QuickStatsResponse>
    implements $QuickStatsResponseCopyWith<$Res> {
  _$QuickStatsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as QuickStats?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuickStatsCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $QuickStatsCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$QuickStatsResponseImplCopyWith<$Res>
    implements $QuickStatsResponseCopyWith<$Res> {
  factory _$$QuickStatsResponseImplCopyWith(_$QuickStatsResponseImpl value,
          $Res Function(_$QuickStatsResponseImpl) then) =
      __$$QuickStatsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, QuickStats? data, DashboardError? error});

  @override
  $QuickStatsCopyWith<$Res>? get data;
  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$QuickStatsResponseImplCopyWithImpl<$Res>
    extends _$QuickStatsResponseCopyWithImpl<$Res, _$QuickStatsResponseImpl>
    implements _$$QuickStatsResponseImplCopyWith<$Res> {
  __$$QuickStatsResponseImplCopyWithImpl(_$QuickStatsResponseImpl _value,
      $Res Function(_$QuickStatsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_$QuickStatsResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as QuickStats?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuickStatsResponseImpl implements _QuickStatsResponse {
  const _$QuickStatsResponseImpl(
      {required this.success, this.data, this.error});

  factory _$QuickStatsResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuickStatsResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final QuickStats? data;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'QuickStatsResponse(success: $success, data: $data, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuickStatsResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, error);

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickStatsResponseImplCopyWith<_$QuickStatsResponseImpl> get copyWith =>
      __$$QuickStatsResponseImplCopyWithImpl<_$QuickStatsResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuickStatsResponseImplToJson(
      this,
    );
  }
}

abstract class _QuickStatsResponse implements QuickStatsResponse {
  const factory _QuickStatsResponse(
      {required final bool success,
      final QuickStats? data,
      final DashboardError? error}) = _$QuickStatsResponseImpl;

  factory _QuickStatsResponse.fromJson(Map<String, dynamic> json) =
      _$QuickStatsResponseImpl.fromJson;

  @override
  bool get success;
  @override
  QuickStats? get data;
  @override
  DashboardError? get error;

  /// Create a copy of QuickStatsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuickStatsResponseImplCopyWith<_$QuickStatsResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BasicResponse _$BasicResponseFromJson(Map<String, dynamic> json) {
  return _BasicResponse.fromJson(json);
}

/// @nodoc
mixin _$BasicResponse {
  bool get success => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this BasicResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BasicResponseCopyWith<BasicResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BasicResponseCopyWith<$Res> {
  factory $BasicResponseCopyWith(
          BasicResponse value, $Res Function(BasicResponse) then) =
      _$BasicResponseCopyWithImpl<$Res, BasicResponse>;
  @useResult
  $Res call({bool success, String? message, DashboardError? error});

  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$BasicResponseCopyWithImpl<$Res, $Val extends BasicResponse>
    implements $BasicResponseCopyWith<$Res> {
  _$BasicResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BasicResponseImplCopyWith<$Res>
    implements $BasicResponseCopyWith<$Res> {
  factory _$$BasicResponseImplCopyWith(
          _$BasicResponseImpl value, $Res Function(_$BasicResponseImpl) then) =
      __$$BasicResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, String? message, DashboardError? error});

  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$BasicResponseImplCopyWithImpl<$Res>
    extends _$BasicResponseCopyWithImpl<$Res, _$BasicResponseImpl>
    implements _$$BasicResponseImplCopyWith<$Res> {
  __$$BasicResponseImplCopyWithImpl(
      _$BasicResponseImpl _value, $Res Function(_$BasicResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? error = freezed,
  }) {
    return _then(_$BasicResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BasicResponseImpl implements _BasicResponse {
  const _$BasicResponseImpl({required this.success, this.message, this.error});

  factory _$BasicResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BasicResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String? message;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'BasicResponse(success: $success, message: $message, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BasicResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, message, error);

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BasicResponseImplCopyWith<_$BasicResponseImpl> get copyWith =>
      __$$BasicResponseImplCopyWithImpl<_$BasicResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BasicResponseImplToJson(
      this,
    );
  }
}

abstract class _BasicResponse implements BasicResponse {
  const factory _BasicResponse(
      {required final bool success,
      final String? message,
      final DashboardError? error}) = _$BasicResponseImpl;

  factory _BasicResponse.fromJson(Map<String, dynamic> json) =
      _$BasicResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String? get message;
  @override
  DashboardError? get error;

  /// Create a copy of BasicResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BasicResponseImplCopyWith<_$BasicResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EmergencyControlResponse _$EmergencyControlResponseFromJson(
    Map<String, dynamic> json) {
  return _EmergencyControlResponse.fromJson(json);
}

/// @nodoc
mixin _$EmergencyControlResponse {
  bool get success => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  EmergencyQueueControlResponse? get data => throw _privateConstructorUsedError;
  DashboardError? get error => throw _privateConstructorUsedError;

  /// Serializes this EmergencyControlResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmergencyControlResponseCopyWith<EmergencyControlResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmergencyControlResponseCopyWith<$Res> {
  factory $EmergencyControlResponseCopyWith(EmergencyControlResponse value,
          $Res Function(EmergencyControlResponse) then) =
      _$EmergencyControlResponseCopyWithImpl<$Res, EmergencyControlResponse>;
  @useResult
  $Res call(
      {bool success,
      String? message,
      EmergencyQueueControlResponse? data,
      DashboardError? error});

  $EmergencyQueueControlResponseCopyWith<$Res>? get data;
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$EmergencyControlResponseCopyWithImpl<$Res,
        $Val extends EmergencyControlResponse>
    implements $EmergencyControlResponseCopyWith<$Res> {
  _$EmergencyControlResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as EmergencyQueueControlResponse?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ) as $Val);
  }

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EmergencyQueueControlResponseCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $EmergencyQueueControlResponseCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DashboardErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $DashboardErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$EmergencyControlResponseImplCopyWith<$Res>
    implements $EmergencyControlResponseCopyWith<$Res> {
  factory _$$EmergencyControlResponseImplCopyWith(
          _$EmergencyControlResponseImpl value,
          $Res Function(_$EmergencyControlResponseImpl) then) =
      __$$EmergencyControlResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      String? message,
      EmergencyQueueControlResponse? data,
      DashboardError? error});

  @override
  $EmergencyQueueControlResponseCopyWith<$Res>? get data;
  @override
  $DashboardErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$EmergencyControlResponseImplCopyWithImpl<$Res>
    extends _$EmergencyControlResponseCopyWithImpl<$Res,
        _$EmergencyControlResponseImpl>
    implements _$$EmergencyControlResponseImplCopyWith<$Res> {
  __$$EmergencyControlResponseImplCopyWithImpl(
      _$EmergencyControlResponseImpl _value,
      $Res Function(_$EmergencyControlResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? data = freezed,
    Object? error = freezed,
  }) {
    return _then(_$EmergencyControlResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as EmergencyQueueControlResponse?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as DashboardError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EmergencyControlResponseImpl implements _EmergencyControlResponse {
  const _$EmergencyControlResponseImpl(
      {required this.success, this.message, this.data, this.error});

  factory _$EmergencyControlResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmergencyControlResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String? message;
  @override
  final EmergencyQueueControlResponse? data;
  @override
  final DashboardError? error;

  @override
  String toString() {
    return 'EmergencyControlResponse(success: $success, message: $message, data: $data, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmergencyControlResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, message, data, error);

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmergencyControlResponseImplCopyWith<_$EmergencyControlResponseImpl>
      get copyWith => __$$EmergencyControlResponseImplCopyWithImpl<
          _$EmergencyControlResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmergencyControlResponseImplToJson(
      this,
    );
  }
}

abstract class _EmergencyControlResponse implements EmergencyControlResponse {
  const factory _EmergencyControlResponse(
      {required final bool success,
      final String? message,
      final EmergencyQueueControlResponse? data,
      final DashboardError? error}) = _$EmergencyControlResponseImpl;

  factory _EmergencyControlResponse.fromJson(Map<String, dynamic> json) =
      _$EmergencyControlResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String? get message;
  @override
  EmergencyQueueControlResponse? get data;
  @override
  DashboardError? get error;

  /// Create a copy of EmergencyControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmergencyControlResponseImplCopyWith<_$EmergencyControlResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

DashboardError _$DashboardErrorFromJson(Map<String, dynamic> json) {
  return _DashboardError.fromJson(json);
}

/// @nodoc
mixin _$DashboardError {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;

  /// Serializes this DashboardError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardErrorCopyWith<DashboardError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardErrorCopyWith<$Res> {
  factory $DashboardErrorCopyWith(
          DashboardError value, $Res Function(DashboardError) then) =
      _$DashboardErrorCopyWithImpl<$Res, DashboardError>;
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class _$DashboardErrorCopyWithImpl<$Res, $Val extends DashboardError>
    implements $DashboardErrorCopyWith<$Res> {
  _$DashboardErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DashboardErrorImplCopyWith<$Res>
    implements $DashboardErrorCopyWith<$Res> {
  factory _$$DashboardErrorImplCopyWith(_$DashboardErrorImpl value,
          $Res Function(_$DashboardErrorImpl) then) =
      __$$DashboardErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class __$$DashboardErrorImplCopyWithImpl<$Res>
    extends _$DashboardErrorCopyWithImpl<$Res, _$DashboardErrorImpl>
    implements _$$DashboardErrorImplCopyWith<$Res> {
  __$$DashboardErrorImplCopyWithImpl(
      _$DashboardErrorImpl _value, $Res Function(_$DashboardErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of DashboardError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_$DashboardErrorImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DashboardErrorImpl implements _DashboardError {
  const _$DashboardErrorImpl(
      {required this.code, required this.message, this.details});

  factory _$DashboardErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardErrorImplFromJson(json);

  @override
  final String code;
  @override
  final String message;
  @override
  final String? details;

  @override
  String toString() {
    return 'DashboardError(code: $code, message: $message, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardErrorImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message, details);

  /// Create a copy of DashboardError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardErrorImplCopyWith<_$DashboardErrorImpl> get copyWith =>
      __$$DashboardErrorImplCopyWithImpl<_$DashboardErrorImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardErrorImplToJson(
      this,
    );
  }
}

abstract class _DashboardError implements DashboardError {
  const factory _DashboardError(
      {required final String code,
      required final String message,
      final String? details}) = _$DashboardErrorImpl;

  factory _DashboardError.fromJson(Map<String, dynamic> json) =
      _$DashboardErrorImpl.fromJson;

  @override
  String get code;
  @override
  String get message;
  @override
  String? get details;

  /// Create a copy of DashboardError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardErrorImplCopyWith<_$DashboardErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EmergencyQueueControl _$EmergencyQueueControlFromJson(
    Map<String, dynamic> json) {
  return _EmergencyQueueControl.fromJson(json);
}

/// @nodoc
mixin _$EmergencyQueueControl {
  String get action =>
      throw _privateConstructorUsedError; // 'pause' or 'resume'
  String get reason => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  int? get estimatedDuration => throw _privateConstructorUsedError;

  /// Serializes this EmergencyQueueControl to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmergencyQueueControl
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmergencyQueueControlCopyWith<EmergencyQueueControl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmergencyQueueControlCopyWith<$Res> {
  factory $EmergencyQueueControlCopyWith(EmergencyQueueControl value,
          $Res Function(EmergencyQueueControl) then) =
      _$EmergencyQueueControlCopyWithImpl<$Res, EmergencyQueueControl>;
  @useResult
  $Res call(
      {String action,
      String reason,
      DateTime timestamp,
      int? estimatedDuration});
}

/// @nodoc
class _$EmergencyQueueControlCopyWithImpl<$Res,
        $Val extends EmergencyQueueControl>
    implements $EmergencyQueueControlCopyWith<$Res> {
  _$EmergencyQueueControlCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmergencyQueueControl
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? reason = null,
    Object? timestamp = null,
    Object? estimatedDuration = freezed,
  }) {
    return _then(_value.copyWith(
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EmergencyQueueControlImplCopyWith<$Res>
    implements $EmergencyQueueControlCopyWith<$Res> {
  factory _$$EmergencyQueueControlImplCopyWith(
          _$EmergencyQueueControlImpl value,
          $Res Function(_$EmergencyQueueControlImpl) then) =
      __$$EmergencyQueueControlImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String action,
      String reason,
      DateTime timestamp,
      int? estimatedDuration});
}

/// @nodoc
class __$$EmergencyQueueControlImplCopyWithImpl<$Res>
    extends _$EmergencyQueueControlCopyWithImpl<$Res,
        _$EmergencyQueueControlImpl>
    implements _$$EmergencyQueueControlImplCopyWith<$Res> {
  __$$EmergencyQueueControlImplCopyWithImpl(_$EmergencyQueueControlImpl _value,
      $Res Function(_$EmergencyQueueControlImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmergencyQueueControl
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? reason = null,
    Object? timestamp = null,
    Object? estimatedDuration = freezed,
  }) {
    return _then(_$EmergencyQueueControlImpl(
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      estimatedDuration: freezed == estimatedDuration
          ? _value.estimatedDuration
          : estimatedDuration // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EmergencyQueueControlImpl implements _EmergencyQueueControl {
  const _$EmergencyQueueControlImpl(
      {required this.action,
      required this.reason,
      required this.timestamp,
      this.estimatedDuration});

  factory _$EmergencyQueueControlImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmergencyQueueControlImplFromJson(json);

  @override
  final String action;
// 'pause' or 'resume'
  @override
  final String reason;
  @override
  final DateTime timestamp;
  @override
  final int? estimatedDuration;

  @override
  String toString() {
    return 'EmergencyQueueControl(action: $action, reason: $reason, timestamp: $timestamp, estimatedDuration: $estimatedDuration)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmergencyQueueControlImpl &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.estimatedDuration, estimatedDuration) ||
                other.estimatedDuration == estimatedDuration));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, action, reason, timestamp, estimatedDuration);

  /// Create a copy of EmergencyQueueControl
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmergencyQueueControlImplCopyWith<_$EmergencyQueueControlImpl>
      get copyWith => __$$EmergencyQueueControlImplCopyWithImpl<
          _$EmergencyQueueControlImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmergencyQueueControlImplToJson(
      this,
    );
  }
}

abstract class _EmergencyQueueControl implements EmergencyQueueControl {
  const factory _EmergencyQueueControl(
      {required final String action,
      required final String reason,
      required final DateTime timestamp,
      final int? estimatedDuration}) = _$EmergencyQueueControlImpl;

  factory _EmergencyQueueControl.fromJson(Map<String, dynamic> json) =
      _$EmergencyQueueControlImpl.fromJson;

  @override
  String get action; // 'pause' or 'resume'
  @override
  String get reason;
  @override
  DateTime get timestamp;
  @override
  int? get estimatedDuration;

  /// Create a copy of EmergencyQueueControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmergencyQueueControlImplCopyWith<_$EmergencyQueueControlImpl>
      get copyWith => throw _privateConstructorUsedError;
}

EmergencyQueueControlResponse _$EmergencyQueueControlResponseFromJson(
    Map<String, dynamic> json) {
  return _EmergencyQueueControlResponse.fromJson(json);
}

/// @nodoc
mixin _$EmergencyQueueControlResponse {
  bool get success => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  int get affectedQueues => throw _privateConstructorUsedError;
  DateTime? get estimatedResumeTime => throw _privateConstructorUsedError;

  /// Serializes this EmergencyQueueControlResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmergencyQueueControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmergencyQueueControlResponseCopyWith<EmergencyQueueControlResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmergencyQueueControlResponseCopyWith<$Res> {
  factory $EmergencyQueueControlResponseCopyWith(
          EmergencyQueueControlResponse value,
          $Res Function(EmergencyQueueControlResponse) then) =
      _$EmergencyQueueControlResponseCopyWithImpl<$Res,
          EmergencyQueueControlResponse>;
  @useResult
  $Res call(
      {bool success,
      String message,
      int affectedQueues,
      DateTime? estimatedResumeTime});
}

/// @nodoc
class _$EmergencyQueueControlResponseCopyWithImpl<$Res,
        $Val extends EmergencyQueueControlResponse>
    implements $EmergencyQueueControlResponseCopyWith<$Res> {
  _$EmergencyQueueControlResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmergencyQueueControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? affectedQueues = null,
    Object? estimatedResumeTime = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      affectedQueues: null == affectedQueues
          ? _value.affectedQueues
          : affectedQueues // ignore: cast_nullable_to_non_nullable
              as int,
      estimatedResumeTime: freezed == estimatedResumeTime
          ? _value.estimatedResumeTime
          : estimatedResumeTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EmergencyQueueControlResponseImplCopyWith<$Res>
    implements $EmergencyQueueControlResponseCopyWith<$Res> {
  factory _$$EmergencyQueueControlResponseImplCopyWith(
          _$EmergencyQueueControlResponseImpl value,
          $Res Function(_$EmergencyQueueControlResponseImpl) then) =
      __$$EmergencyQueueControlResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      String message,
      int affectedQueues,
      DateTime? estimatedResumeTime});
}

/// @nodoc
class __$$EmergencyQueueControlResponseImplCopyWithImpl<$Res>
    extends _$EmergencyQueueControlResponseCopyWithImpl<$Res,
        _$EmergencyQueueControlResponseImpl>
    implements _$$EmergencyQueueControlResponseImplCopyWith<$Res> {
  __$$EmergencyQueueControlResponseImplCopyWithImpl(
      _$EmergencyQueueControlResponseImpl _value,
      $Res Function(_$EmergencyQueueControlResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmergencyQueueControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? affectedQueues = null,
    Object? estimatedResumeTime = freezed,
  }) {
    return _then(_$EmergencyQueueControlResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      affectedQueues: null == affectedQueues
          ? _value.affectedQueues
          : affectedQueues // ignore: cast_nullable_to_non_nullable
              as int,
      estimatedResumeTime: freezed == estimatedResumeTime
          ? _value.estimatedResumeTime
          : estimatedResumeTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EmergencyQueueControlResponseImpl
    implements _EmergencyQueueControlResponse {
  const _$EmergencyQueueControlResponseImpl(
      {required this.success,
      required this.message,
      this.affectedQueues = 0,
      this.estimatedResumeTime});

  factory _$EmergencyQueueControlResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$EmergencyQueueControlResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final String message;
  @override
  @JsonKey()
  final int affectedQueues;
  @override
  final DateTime? estimatedResumeTime;

  @override
  String toString() {
    return 'EmergencyQueueControlResponse(success: $success, message: $message, affectedQueues: $affectedQueues, estimatedResumeTime: $estimatedResumeTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmergencyQueueControlResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.affectedQueues, affectedQueues) ||
                other.affectedQueues == affectedQueues) &&
            (identical(other.estimatedResumeTime, estimatedResumeTime) ||
                other.estimatedResumeTime == estimatedResumeTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, success, message, affectedQueues, estimatedResumeTime);

  /// Create a copy of EmergencyQueueControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmergencyQueueControlResponseImplCopyWith<
          _$EmergencyQueueControlResponseImpl>
      get copyWith => __$$EmergencyQueueControlResponseImplCopyWithImpl<
          _$EmergencyQueueControlResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmergencyQueueControlResponseImplToJson(
      this,
    );
  }
}

abstract class _EmergencyQueueControlResponse
    implements EmergencyQueueControlResponse {
  const factory _EmergencyQueueControlResponse(
          {required final bool success,
          required final String message,
          final int affectedQueues,
          final DateTime? estimatedResumeTime}) =
      _$EmergencyQueueControlResponseImpl;

  factory _EmergencyQueueControlResponse.fromJson(Map<String, dynamic> json) =
      _$EmergencyQueueControlResponseImpl.fromJson;

  @override
  bool get success;
  @override
  String get message;
  @override
  int get affectedQueues;
  @override
  DateTime? get estimatedResumeTime;

  /// Create a copy of EmergencyQueueControlResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmergencyQueueControlResponseImplCopyWith<
          _$EmergencyQueueControlResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
