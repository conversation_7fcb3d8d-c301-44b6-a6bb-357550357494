targets:
  $default:
    builders:
      # Riverpod code generation configuration
      riverpod_generator:
        options:
          # Provider naming conventions
          provider_name_prefix: ""
          provider_name_suffix: "Provider"
          provider_family_name_prefix: ""
          provider_family_name_suffix: "Provider"
        generate_for:
          - lib/**
          - test/**
      
      # JSON serialization configuration
      json_serializable:
        options:
          # Use explicit toJson return type
          explicit_to_json: true
          # Include if null for optional fields
          include_if_null: false
        generate_for:
          - lib/shared/models/**
          - lib/features/**/models/**
      
      # Source generation configuration
      source_gen|combining_builder:
        options:
          ignore_for_file:
            - "type=lint"
            - "avoid_redundant_argument_values"
            - "avoid_unused_constructor_parameters"
            - "invalid_annotation_target"
        generate_for:
          - lib/**
