import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../services/firebase_messaging_service.dart';

part 'firebase_messaging_provider.g.dart';

/// Provider for Firebase messaging service initialization
@riverpod
Future<void> initializeFirebaseMessaging(InitializeFirebaseMessagingRef ref) async {
  await FirebaseMessagingService.initialize();
}

/// Provider for getting FCM token
@riverpod
Future<String?> fcmToken(FcmTokenRef ref) async {
  return await FirebaseMessagingService.getToken();
}

/// Provider for subscribing to topics
@riverpod
class TopicSubscription extends _$TopicSubscription {
  @override
  Set<String> build() {
    return <String>{};
  }

  /// Subscribe to a topic
  Future<void> subscribe(String topic) async {
    await FirebaseMessagingService.subscribeToTopic(topic);
    state = {...state, topic};
  }

  /// Unsubscribe from a topic
  Future<void> unsubscribe(String topic) async {
    await FirebaseMessagingService.unsubscribeFromTopic(topic);
    state = state.where((t) => t != topic).toSet();
  }

  /// Subscribe to provider-specific topics
  Future<void> subscribeToProviderTopics(String providerId) async {
    final topics = [
      'provider_$providerId',
      'appointments',
      'bookings',
      'messages',
    ];

    for (final topic in topics) {
      await subscribe(topic);
    }
  }

  /// Unsubscribe from all topics
  Future<void> unsubscribeFromAll() async {
    for (final topic in state) {
      await FirebaseMessagingService.unsubscribeFromTopic(topic);
    }
    state = <String>{};
  }
}
