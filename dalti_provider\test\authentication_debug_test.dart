import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/network/interceptors/auth_interceptor.dart';
import 'package:dalti_provider/core/auth/models/jwt_token.dart';
import 'package:dalti_provider/features/locations/services/location_api_service.dart';

void main() {
  group('Authentication Debug Tests', () {
    test('Complete authentication flow with LocationApiService', () async {
      // 1. Create HTTP client and JWT service
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);

      // 2. Simulate login response (sessionId from Dalti Provider API)
      final loginResponse = {
        'access_token': 'test-session-id-12345',
        'refresh_token': 'test-refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // 3. Store the token (this happens after successful login)
      await jwtService.storeTokenFromResponse(loginResponse);

      // 4. Verify JWT service state
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentToken, isNotNull);
      expect(jwtService.currentToken!.accessToken, equals('test-session-id-12345'));

      // 5. Verify authorization header
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, equals('Bearer test-session-id-12345'));

      // 6. Create authenticated HTTP client
      final authInterceptor = AuthInterceptor(jwtService);
      final authenticatedHttpClient = HttpClient(authInterceptor: authInterceptor);

      // 7. Create LocationApiService with authenticated client
      final locationApiService = LocationApiService(authenticatedHttpClient);

      // 8. Verify the service is properly configured
      expect(locationApiService.httpClient, equals(authenticatedHttpClient));

      print('✅ Authentication flow test completed successfully');
      print('📋 Summary:');
      print('   - JWT Service authenticated: ${jwtService.isAuthenticated}');
      print('   - Session ID: ${jwtService.currentToken!.accessToken}');
      print('   - Auth header: $authHeader');
      print('   - LocationApiService configured with authenticated client');
    });

    test('AuthInterceptor adds headers correctly', () async {
      // Create a JWT service with a valid token
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);

      // Store a test token
      final testToken = JwtToken(
        accessToken: 'test-session-abc123',
        refreshToken: 'test-refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      await jwtService.storeToken(testToken);

      // Verify the token is stored and valid
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.getAuthorizationHeader(), equals('Bearer test-session-abc123'));

      print('✅ AuthInterceptor test completed successfully');
      print('📋 Token details:');
      print('   - Access token: ${testToken.accessToken}');
      print('   - Token type: ${testToken.tokenType}');
      print('   - Is valid: ${testToken.isValid}');
      print('   - Authorization header: ${jwtService.getAuthorizationHeader()}');
    });

    test('Endpoint authentication requirements', () {
      // Test which endpoints require authentication
      final protectedEndpoints = [
        '/api/auth/providers/locations',
        '/api/auth/providers/locations/1',
        '/api/auth/providers/locations/1/queues',
        '/api/auth/providers/schedules',
      ];

      final publicEndpoints = [
        '/api/auth/provider/login',
        '/api/auth/request-email-otp',
        '/api/auth/provider/verify-otp-register',
        '/api/auth/refresh-token',
        '/health',
      ];

      print('✅ Endpoint authentication test');
      print('🔒 Protected endpoints (require Authorization header):');
      for (final endpoint in protectedEndpoints) {
        print('   - $endpoint');
      }

      print('🌐 Public endpoints (no authentication required):');
      for (final endpoint in publicEndpoints) {
        print('   - $endpoint');
      }

      // Verify that location endpoints are protected
      for (final endpoint in protectedEndpoints) {
        expect(endpoint.startsWith('/api/auth/providers/'), isTrue,
            reason: 'Endpoint $endpoint should be protected');
      }
    });

    test('Debug authentication state', () async {
      print('🔍 Debugging authentication state...');

      // Create services
      final httpClient = HttpClient();
      final jwtService = JwtService(httpClient);

      print('   - Initial authentication state: ${jwtService.isAuthenticated}');
      print('   - Current token: ${jwtService.currentToken}');

      // Simulate storing a token
      final tokenData = {
        'access_token': 'debug-session-id',
        'refresh_token': 'debug-refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      await jwtService.storeTokenFromResponse(tokenData);

      print('   - After storing token:');
      print('     * Authenticated: ${jwtService.isAuthenticated}');
      print('     * Token exists: ${jwtService.currentToken != null}');
      print('     * Token valid: ${jwtService.currentToken?.isValid}');
      print('     * Auth header: ${jwtService.getAuthorizationHeader()}');

      // Test authorization header format
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, isNotNull);
      expect(authHeader!.startsWith('Bearer '), isTrue);
      expect(authHeader.contains('debug-session-id'), isTrue);

      print('✅ Authentication debug completed');
    });
  });
}
