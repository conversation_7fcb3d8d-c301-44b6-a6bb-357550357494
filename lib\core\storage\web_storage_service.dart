import 'dart:html' as html;

/// Service for managing web storage operations
class WebStorageService {
  // Onboarding related keys
  static const String _onboardingSkippedKey = 'onboarding_skipped';
  static const String _onboardingCompletedKey = 'onboarding_completed';

  /// Cache operations
  static T? getCache<T>(String key) {
    final value = html.window.localStorage[key];
    if (value == null) return null;

    if (T == String) return value as T;
    if (T == bool) return (value == 'true') as T;
    if (T == int) return int.tryParse(value) as T?;

    return value as T?;
  }

  static Future<void> saveCache(String key, dynamic value) async {
    html.window.localStorage[key] = value.toString();
  }

  static Future<void> removeCache(String key) async {
    html.window.localStorage.remove(key);
  }

  /// Settings operations
  static T? getSetting<T>(String key, {T? defaultValue}) {
    final value = html.window.localStorage[key];
    if (value == null) return defaultValue;

    if (T == String) return value as T;
    if (T == bool) return (value == 'true') as T;
    if (T == int) return int.tryParse(value) as T?;

    return value as T? ?? defaultValue;
  }

  static Future<void> saveSetting(String key, dynamic value) async {
    html.window.localStorage[key] = value.toString();
  }

  /// Auth operations (for compatibility)
  static T? getAuth<T>(String key) {
    return getCache<T>(key);
  }

  static Future<void> saveAuth(String key, dynamic value) async {
    await saveCache(key, value);
  }

  static Future<void> removeAuth(String key) async {
    await removeCache(key);
  }

  /// Onboarding skip functionality
  static bool get isOnboardingSkipped {
    return getSetting<bool>(_onboardingSkippedKey, defaultValue: false) ??
        false;
  }

  static Future<void> setOnboardingSkipped(bool skipped) async {
    await saveSetting(_onboardingSkippedKey, skipped);
  }

  static bool get isOnboardingCompleted {
    return getSetting<bool>(_onboardingCompletedKey, defaultValue: false) ??
        false;
  }

  static Future<void> setOnboardingCompleted(bool completed) async {
    await saveSetting(_onboardingCompletedKey, completed);
  }

  /// Check if user needs onboarding (not skipped and not completed)
  static bool get needsOnboarding {
    return !isOnboardingSkipped && !isOnboardingCompleted;
  }

  /// Clear all onboarding related data
  static Future<void> clearOnboardingData() async {
    await removeCache(_onboardingSkippedKey);
    await removeCache(_onboardingCompletedKey);
  }

  /// Reset skip flag (allow user to restart onboarding)
  static Future<void> resetSkipFlag() async {
    await removeCache(_onboardingSkippedKey);
  }
}
