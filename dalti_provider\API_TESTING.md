# API Testing Guide

This document explains how to test the real API integration for the Dalti Provider authentication system.

## API Configuration

The app is configured to use the real Dalti Provider API endpoints:

- **Development**: `https://dapi-test.adscloud.org:8443/api/v1`
- **Production**: `https://dapi.adscloud.org/api/v1`

Currently, the app uses the **development environment** by default.

## API Endpoints

### 0. Get Provider Categories (`GET /api/auth/provider/categories`)

**Description:** Fetch all available provider categories in hierarchical structure

**Request:**
```http
GET /api/auth/provider/categories
Content-Type: application/json
```

**Actual Response (Status: 200 OK):**
```json
[
  {
    "id": 1,
    "title": "",
    "parentId": null
  },
  {
    "id": 2,
    "title": "",
    "parentId": null
  }
  // ... 15 parent categories total (IDs 1-15)
  // ... 66 child categories total (IDs 16-81)
]
```

**Note:**
- The API returns 15 parent categories and 66 child categories
- Category titles are currently empty strings in the API response
- The app falls back to hardcoded categories when API data is incomplete
- Parent categories have `parentId: null`, child categories reference their parent's ID

## Authentication Endpoints

### 1. Request Email OTP (`POST /api/auth/request-email-otp`)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "password": "password123",
  "isProviderRegistration": true,
  "providerCategoryId": 1,
  "businessName": "My Business",
  "phone": "+**********"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "OTP sent successfully"
}
```

### 2. Complete Provider Registration (`POST /api/auth/provider/verify-otp-register`)

**Request Body:**
```json
{
  "otp": "123456",
  "identifier": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "providerCategoryId": 1,
  "businessName": "My Business",
  "phone": "+**********",
  "email": "<EMAIL>"
}
```

**Expected Response (Status: 201 Created):**
```json
{
  "message": "Provider registered successfully",
  "user": {
    "id": "f63f6d7f-4fde-46d8-98f6-b35209418f5f",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CUSTOMER"
  },
  "provider": {
    "id": 2,
    "userId": "f63f6d7f-4fde-46d8-98f6-b35209418f5f",
    "providerCategoryId": 1,
    "title": "My Business",
    "phone": "**********",
    "isSetupComplete": false
  },
  "sessionId": "r62dicmfc7hgto6oqllprwm3oyzt44sjqitax6we"
}
```

### 3. Provider Login (`POST /api/auth/provider/login`)

**Request Body:**
```json
{
  "identifier": "<EMAIL>",
  "password": "password123"
}
```

**Expected Response:**
```json
{
  "sessionId": "gfpyz37dllbkyc5n6s2fl7oiqm5upfcxgii65lla",
  "user": {
    "id": "c318cff7-b668-40eb-a8c8-c1ac05fc2593",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "CUSTOMER"
  },
  "provider": {
    "id": 1,
    "userId": "c318cff7-b668-40eb-a8c8-c1ac05fc2593",
    "title": "My Business",
    "phone": "**********",
    "providerCategoryId": 1,
    "category": {
      "id": 1,
      "title": "Healthcare",
      "parentId": null
    }
  }
}
```

### 4. Token Refresh (`POST /api/auth/refresh-token`)

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "sessionId": "new_jwt_token_here"
}
```

## Error Handling

The app handles various HTTP status codes:

- **400**: Bad Request (validation errors, invalid data)
- **401**: Unauthorized (invalid credentials)
- **403**: Forbidden (access denied)
- **409**: Conflict (email already exists)
- **410**: Gone (OTP expired)
- **429**: Too Many Requests (rate limiting)
- **500**: Internal Server Error

## Testing the Integration

### 1. Network Connectivity
- Ensure your device/emulator has internet access
- The app will show connection status on the login screen

### 2. Provider Registration Flow
1. Open the app
2. Click "Don't have an account? Sign up"
3. Fill out the registration form:
   - First Name, Last Name
   - Business Name
   - **Business Category** (select from dropdown)
   - Email address
   - Phone number
   - Password and confirmation
4. Submit the form (sends OTP to email)
5. Check your email for the OTP code
6. Enter the 6-digit OTP on verification screen
7. Should complete registration and automatically log in
8. Redirect to dashboard

### 3. Provider Login Flow
1. Open the app (or logout if already logged in)
2. Enter your registered email/phone and password
3. Click "Sign In"
4. Should redirect to dashboard on success

### 4. Error Testing
- Try invalid credentials for login
- Try registering with an existing email
- Try invalid OTP codes
- Test network disconnection scenarios

## Debugging

### Console Logs
The app logs all API requests and responses in debug mode:
- Request details (URL, headers, body)
- Response status and data
- Error messages and stack traces

### Network Inspector
Use Flutter's network inspector or tools like Charles Proxy to monitor HTTP traffic.

### Error Messages
The app shows user-friendly error messages in snackbars:
- Green for success
- Red for errors
- Orange for warnings

## API Quirks (From Documentation)

The Dalti Provider API has some unusual behavior:
- Returns **500** for validation errors (instead of 400)
- Returns **400** for auth errors (instead of 401)

The app's error handling accounts for these quirks.

## Switching Environments

To switch between development and production:

1. Edit `lib/core/config/app_config.dart`
2. Change the `environment` getter to return `Environment.production`
3. Rebuild the app

## Security Notes

- JWT tokens are stored securely using Hive
- Tokens are automatically refreshed before expiration
- Failed refresh attempts clear stored tokens
- All API calls include proper authentication headers
