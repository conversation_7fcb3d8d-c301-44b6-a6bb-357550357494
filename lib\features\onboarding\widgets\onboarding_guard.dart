import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/routing/app_routes.dart';
import '../providers/onboarding_provider.dart';

/// Widget that guards routes requiring onboarding completion
class OnboardingGuard extends ConsumerWidget {
  final Widget child;
  final bool requiresOnboardingCompleted;

  const OnboardingGuard({
    super.key,
    required this.child,
    this.requiresOnboardingCompleted = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (!requiresOnboardingCompleted) {
      return child;
    }

    final needsOnboardingAsync = ref.watch(needsOnboardingProvider);

    return needsOnboardingAsync.when(
      data: (needsOnboarding) {
        if (needsOnboarding) {
          // Redirect to onboarding
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.go(AppRoutes.onboarding);
          });
          
          // Show loading while redirecting
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        // User has completed onboarding, show the protected content
        return child;
      },
      loading: () => const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) {
        // On error, allow access but log the error
        print('[OnboardingGuard] Error checking onboarding status: $error');
        return child;
      },
    );
  }
}

/// Mixin for screens that require onboarding completion
mixin OnboardingRequiredMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  void _checkOnboardingStatus() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final needsOnboarding = await ref.read(needsOnboardingProvider.future);
        if (needsOnboarding && mounted) {
          context.go(AppRoutes.onboarding);
        }
      } catch (e) {
        print('[OnboardingRequiredMixin] Error checking onboarding status: $e');
        // Continue to allow access if check fails
      }
    });
  }
}

/// Helper function to check if onboarding is needed
Future<bool> checkOnboardingStatus(WidgetRef ref) async {
  try {
    return await ref.read(needsOnboardingProvider.future);
  } catch (e) {
    print('[OnboardingHelper] Error checking onboarding status: $e');
    return false; // Assume onboarding is not needed on error
  }
}
