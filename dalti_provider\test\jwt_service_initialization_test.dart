import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/auth/models/jwt_token.dart';

void main() {
  group('JWT Service Initialization Tests', () {
    late JwtService jwtService;
    late HttpClient httpClient;

    setUp(() {
      httpClient = HttpClient();
      jwtService = JwtService(httpClient);
    });

    test('JWT service initializes correctly', () {
      expect(jwtService, isNotNull);
      expect(jwtService.isAuthenticated, isFalse);
      expect(jwtService.currentToken, isNull);
    });

    test('JWT service can store and retrieve tokens', () async {
      // Create a test token
      final testToken = JwtToken(
        accessToken: 'test-session-id-12345',
        refreshToken: 'test-refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      // Store the token
      await jwtService.storeToken(testToken);

      // Verify token is stored
      expect(jwtService.currentToken, isNotNull);
      expect(jwtService.currentToken!.accessToken, equals('test-session-id-12345'));
      expect(jwtService.isAuthenticated, isTrue);

      // Verify authorization header
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, equals('Bearer test-session-id-12345'));
    });

    test('JWT service can store token from API response format', () async {
      // Simulate API response format
      final apiResponse = {
        'access_token': 'api-session-id-67890',
        'refresh_token': 'api-refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // Store token from response
      await jwtService.storeTokenFromResponse(apiResponse);

      // Verify token is stored correctly
      expect(jwtService.currentToken, isNotNull);
      expect(jwtService.currentToken!.accessToken, equals('api-session-id-67890'));
      expect(jwtService.isAuthenticated, isTrue);

      // Verify authorization header
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, equals('Bearer api-session-id-67890'));
    });

    test('JWT service handles token clearing', () async {
      // First store a token
      final testToken = JwtToken(
        accessToken: 'test-token-to-clear',
        refreshToken: 'test-refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      await jwtService.storeToken(testToken);
      expect(jwtService.isAuthenticated, isTrue);

      // Clear the token
      await jwtService.clearToken();

      // Verify token is cleared
      expect(jwtService.currentToken, isNull);
      expect(jwtService.isAuthenticated, isFalse);
      expect(jwtService.getAuthorizationHeader(), isNull);
    });

    test('JWT service validates token expiration', () async {
      // Create an expired token
      final expiredToken = JwtToken(
        accessToken: 'expired-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
      );

      await jwtService.storeToken(expiredToken);

      // Even though token is stored, service should recognize it's invalid
      expect(jwtService.currentToken, isNotNull);
      expect(jwtService.currentToken!.isExpired, isTrue);
      expect(jwtService.currentToken!.isValid, isFalse);
      expect(jwtService.isAuthenticated, isFalse);
    });

    test('JWT service ensures valid token before API calls', () async {
      // Store a valid token
      final validToken = JwtToken(
        accessToken: 'valid-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      await jwtService.storeToken(validToken);

      // Ensure valid token should return true
      final hasValidToken = await jwtService.ensureValidToken();
      expect(hasValidToken, isTrue);

      // Store an expired token
      final expiredToken = JwtToken(
        accessToken: 'expired-token',
        refreshToken: 'refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
      );

      await jwtService.storeToken(expiredToken);

      // Ensure valid token should attempt refresh and may return false
      // (since we don't have a real refresh endpoint in tests)
      final hasValidTokenAfterExpiry = await jwtService.ensureValidToken();
      // This may be false because refresh will fail in test environment
      expect(hasValidTokenAfterExpiry, isA<bool>());
    });

    test('Complete authentication flow simulation', () async {
      // 1. Start with no authentication
      expect(jwtService.isAuthenticated, isFalse);

      // 2. Simulate login response from Dalti Provider API
      final loginResponse = {
        'access_token': 'dalti-session-abc123',  // This would be the sessionId from API
        'refresh_token': 'dalti-refresh-xyz789',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // 3. Store the token (this is what happens after successful login)
      await jwtService.storeTokenFromResponse(loginResponse);

      // 4. Verify authentication state
      expect(jwtService.isAuthenticated, isTrue);
      expect(jwtService.currentToken!.accessToken, equals('dalti-session-abc123'));

      // 5. Get authorization header for API calls
      final authHeader = jwtService.getAuthorizationHeader();
      expect(authHeader, equals('Bearer dalti-session-abc123'));

      // 6. This header will be automatically added to all location API calls:
      //    GET /api/auth/providers/locations
      //    Authorization: Bearer dalti-session-abc123
      //
      //    POST /api/auth/providers/locations
      //    Authorization: Bearer dalti-session-abc123
      //    Content-Type: application/json
      //    { "name": "New Location", ... }

      // 7. Simulate logout
      await jwtService.clearToken();
      expect(jwtService.isAuthenticated, isFalse);
      expect(jwtService.getAuthorizationHeader(), isNull);
    });
  });
}
