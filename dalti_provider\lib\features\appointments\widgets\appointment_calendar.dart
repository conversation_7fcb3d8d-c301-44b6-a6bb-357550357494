import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/appointment_models.dart';
import '../providers/appointment_provider.dart';
import 'daily_calendar_view.dart';
import 'weekly_calendar_view.dart';
import 'monthly_calendar_view.dart';
import 'calendar_header.dart';

/// Main appointment calendar widget that combines all view types
class AppointmentCalendar extends ConsumerStatefulWidget {
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;
  final String initialViewType;

  const AppointmentCalendar({
    super.key,
    this.onAppointmentTap,
    this.onAddAppointment,
    this.initialViewType = 'month',
  });

  @override
  ConsumerState<AppointmentCalendar> createState() => _AppointmentCalendarState();
}

class _AppointmentCalendarState extends ConsumerState<AppointmentCalendar> {
  late DateTime _selectedDate;
  late String _currentViewType;

  @override
  void initState() {
    super.initState();
    _selectedDate = DateTime.now();
    _currentViewType = widget.initialViewType;
    
    // Load appointments when calendar initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAppointments();
    });
  }

  void _loadAppointments() {
    // Load appointments based on current view
    final filters = _getFiltersForCurrentView();
    ref.read(appointmentNotifierProvider.notifier).loadAppointments(
      filters: filters,
      page: 1,
      pageSize: 100, // Load more for calendar views
    );
  }

  AppointmentSearchFilters _getFiltersForCurrentView() {
    DateTime startDate;
    DateTime endDate;

    switch (_currentViewType) {
      case 'day':
        startDate = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        endDate = startDate.add(const Duration(days: 1));
        break;
      case 'week':
        final weekday = _selectedDate.weekday;
        startDate = _selectedDate.subtract(Duration(days: weekday - 1));
        endDate = startDate.add(const Duration(days: 7));
        break;
      case 'month':
      default:
        startDate = DateTime(_selectedDate.year, _selectedDate.month, 1);
        endDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
        // Extend to show full calendar grid
        final firstWeekday = startDate.weekday;
        startDate = startDate.subtract(Duration(days: firstWeekday - 1));
        final lastWeekday = endDate.weekday;
        endDate = endDate.add(Duration(days: 7 - lastWeekday));
        break;
    }

    return AppointmentSearchFilters(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Calendar view
          Expanded(
            child: _buildCurrentView(),
          ),
          
          // Legend (only for month view)
          if (_currentViewType == 'month')
            CalendarLegend(),
        ],
      ),
      
      // Floating action button for adding appointments
      floatingActionButton: widget.onAddAppointment != null
          ? FloatingActionButton(
              onPressed: widget.onAddAppointment,
              child: const Icon(Icons.add),
              tooltip: 'Add Appointment',
            )
          : null,
    );
  }

  Widget _buildCurrentView() {
    switch (_currentViewType) {
      case 'day':
        return DailyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
      case 'week':
        return WeeklyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
      case 'month':
      default:
        return MonthlyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onDayTap: _onDayTap,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
    }
  }

  void _onDateChanged(DateTime newDate) {
    setState(() {
      _selectedDate = newDate;
    });
    _loadAppointments();
  }

  void _onViewChanged(String newViewType) {
    setState(() {
      _currentViewType = newViewType;
    });
    _loadAppointments();
  }

  void _onDayTap(DateTime date) {
    // Switch to day view when tapping a day in month view
    setState(() {
      _selectedDate = date;
      _currentViewType = 'day';
    });
    _loadAppointments();
  }
}

/// Standalone calendar widget with header for use in other screens
class CalendarWidget extends ConsumerStatefulWidget {
  final DateTime? initialDate;
  final String initialViewType;
  final Function(Appointment)? onAppointmentTap;
  final VoidCallback? onAddAppointment;
  final bool showHeader;

  const CalendarWidget({
    super.key,
    this.initialDate,
    this.initialViewType = 'month',
    this.onAppointmentTap,
    this.onAddAppointment,
    this.showHeader = true,
  });

  @override
  ConsumerState<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends ConsumerState<CalendarWidget> {
  late DateTime _selectedDate;
  late String _currentViewType;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate ?? DateTime.now();
    _currentViewType = widget.initialViewType;
    
    // Load appointments when calendar initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAppointments();
    });
  }

  void _loadAppointments() {
    final filters = _getFiltersForCurrentView();
    ref.read(appointmentNotifierProvider.notifier).loadAppointments(
      filters: filters,
      page: 1,
      pageSize: 100,
    );
  }

  AppointmentSearchFilters _getFiltersForCurrentView() {
    DateTime startDate;
    DateTime endDate;

    switch (_currentViewType) {
      case 'day':
        startDate = DateTime(_selectedDate.year, _selectedDate.month, _selectedDate.day);
        endDate = startDate.add(const Duration(days: 1));
        break;
      case 'week':
        final weekday = _selectedDate.weekday;
        startDate = _selectedDate.subtract(Duration(days: weekday - 1));
        endDate = startDate.add(const Duration(days: 7));
        break;
      case 'month':
      default:
        startDate = DateTime(_selectedDate.year, _selectedDate.month, 1);
        endDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
        break;
    }

    return AppointmentSearchFilters(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Optional header
        if (widget.showHeader)
          CalendarHeader(
            currentDate: _selectedDate,
            viewType: _currentViewType,
            onPreviousPressed: _onPrevious,
            onNextPressed: _onNext,
            onTodayPressed: _onToday,
            onViewChanged: _onViewChanged,
            onAddAppointment: widget.onAddAppointment,
          ),
        
        // Calendar content
        Expanded(
          child: _buildCurrentView(),
        ),
      ],
    );
  }

  Widget _buildCurrentView() {
    switch (_currentViewType) {
      case 'day':
        return DailyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
      case 'week':
        return WeeklyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
      case 'month':
      default:
        return MonthlyCalendarView(
          selectedDate: _selectedDate,
          onDateChanged: _onDateChanged,
          onDayTap: _onDayTap,
          onAppointmentTap: widget.onAppointmentTap,
          onAddAppointment: widget.onAddAppointment,
        );
    }
  }

  void _onDateChanged(DateTime newDate) {
    setState(() {
      _selectedDate = newDate;
    });
    _loadAppointments();
  }

  void _onViewChanged(String newViewType) {
    setState(() {
      _currentViewType = newViewType;
    });
    _loadAppointments();
  }

  void _onDayTap(DateTime date) {
    setState(() {
      _selectedDate = date;
      _currentViewType = 'day';
    });
    _loadAppointments();
  }

  void _onPrevious() {
    DateTime newDate;
    switch (_currentViewType) {
      case 'day':
        newDate = _selectedDate.subtract(const Duration(days: 1));
        break;
      case 'week':
        newDate = _selectedDate.subtract(const Duration(days: 7));
        break;
      case 'month':
      default:
        newDate = DateTime(_selectedDate.year, _selectedDate.month - 1, 1);
        break;
    }
    _onDateChanged(newDate);
  }

  void _onNext() {
    DateTime newDate;
    switch (_currentViewType) {
      case 'day':
        newDate = _selectedDate.add(const Duration(days: 1));
        break;
      case 'week':
        newDate = _selectedDate.add(const Duration(days: 7));
        break;
      case 'month':
      default:
        newDate = DateTime(_selectedDate.year, _selectedDate.month + 1, 1);
        break;
    }
    _onDateChanged(newDate);
  }

  void _onToday() {
    _onDateChanged(DateTime.now());
  }
}
