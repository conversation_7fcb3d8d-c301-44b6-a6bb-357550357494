import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/provider_category.dart';
import '../../../core/providers/app_providers.dart';
import '../../onboarding/providers/onboarding_provider.dart';

class HierarchicalCategoryDropdown extends ConsumerStatefulWidget {
  final ProviderCategory? selectedCategory;
  final ValueChanged<ProviderCategory?> onChanged;
  final String? Function(ProviderCategory?)? validator;
  final InputDecoration? decoration;

  const HierarchicalCategoryDropdown({
    super.key,
    this.selectedCategory,
    required this.onChanged,
    this.validator,
    this.decoration,
  });

  @override
  ConsumerState<HierarchicalCategoryDropdown> createState() =>
      _HierarchicalCategoryDropdownState();
}

class _HierarchicalCategoryDropdownState
    extends ConsumerState<HierarchicalCategoryDropdown>
    with TickerProviderStateMixin {
  final Map<int, bool> _expandedCategories = {};
  final Map<int, AnimationController> _animationControllers = {};
  bool _isDropdownOpen = false;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  final GlobalKey _dropdownKey = GlobalKey();
  List<ProviderCategory> _categories = [];
  bool _isLoading = true;
  late AnimationController _dropdownAnimationController;
  late Animation<double> _dropdownAnimation;

  @override
  void initState() {
    super.initState();
    _dropdownAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _dropdownAnimation = CurvedAnimation(
      parent: _dropdownAnimationController,
      curve: Curves.easeOutCubic,
    );
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      // Check if onboarding completion is in progress
      final onboardingNotifier = ref.read(onboardingNotifierProvider.notifier);
      if (onboardingNotifier.isCompletionInProgress) {
        print(
          '[HierarchicalCategoryDropdown] Completion in progress, skipping category load',
        );
        if (mounted) {
          setState(() {
            _isLoading = false;
            // Use fallback categories to prevent UI issues
            _categories = ProviderCategories.getParentCategories();
          });
        }
        return;
      }

      final categoryService = ref.read(categoryServiceProvider);
      final categories = await categoryService.getCategories();

      // Double-check completion status after async call
      if (onboardingNotifier.isCompletionInProgress) {
        print(
          '[HierarchicalCategoryDropdown] Completion started during API call, aborting',
        );
        return;
      }

      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoading = false;

          // Initialize all parent categories as collapsed
          for (final category in categories) {
            _expandedCategories[category.id] = false;
            _animationControllers[category.id] = AnimationController(
              duration: const Duration(milliseconds: 300),
              vsync: this,
            );
          }

          // If there's a selected category, expand its parent
          if (widget.selectedCategory != null &&
              widget.selectedCategory!.parentId != null) {
            _expandedCategories[widget.selectedCategory!.parentId!] = true;
            _animationControllers[widget.selectedCategory!.parentId!]
                ?.forward();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Use fallback categories on error
          _categories = ProviderCategories.getParentCategories();

          // Initialize expanded state for fallback categories
          for (final category in _categories) {
            _expandedCategories[category.id] = false;
            _animationControllers[category.id] = AnimationController(
              duration: const Duration(milliseconds: 300),
              vsync: this,
            );
          }

          if (widget.selectedCategory != null &&
              widget.selectedCategory!.parentId != null) {
            _expandedCategories[widget.selectedCategory!.parentId!] = true;
            _animationControllers[widget.selectedCategory!.parentId!]
                ?.forward();
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _dropdownAnimationController.dispose();
    for (final controller in _animationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _removeOverlay();
    } else {
      _showOverlay();
    }
  }

  void _showOverlay() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    _dropdownAnimationController.forward();
    setState(() {
      _isDropdownOpen = true;
    });
  }

  void _removeOverlay() {
    if (_overlayEntry != null) {
      _dropdownAnimationController.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
      });
    }
    if (mounted) {
      setState(() {
        _isDropdownOpen = false;
      });
    } else {
      _isDropdownOpen = false;
    }
  }

  void _refreshOverlay() {
    if (_isDropdownOpen && _overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox =
        _dropdownKey.currentContext!.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return OverlayEntry(
      builder:
          (context) => Positioned(
            left: offset.dx,
            top: offset.dy + size.height + 8,
            width: size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0, size.height + 8),
              child: AnimatedBuilder(
                animation: _dropdownAnimation,
                builder:
                    (context, child) => Transform.scale(
                      scale: _dropdownAnimation.value,
                      alignment: Alignment.topCenter,
                      child: Opacity(
                        opacity: _dropdownAnimation.value,
                        child: Material(
                          elevation: 12,
                          borderRadius: BorderRadius.circular(16),
                          shadowColor: colorScheme.shadow.withOpacity(0.2),
                          child: Container(
                            constraints: const BoxConstraints(maxHeight: 320),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: colorScheme.surface,
                              border: Border.all(
                                color: colorScheme.outline.withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: StatefulBuilder(
                                builder:
                                    (context, setOverlayState) =>
                                        _buildCategoryList(),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
              ),
            ),
          ),
    );
  }

  Widget _buildCategoryList() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return Container(
        height: 120,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Loading categories...',
              style: TextStyle(
                color: colorScheme.onSurface.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_categories.isEmpty) {
      return Container(
        height: 120,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 32,
              color: colorScheme.onSurface.withOpacity(0.4),
            ),
            const SizedBox(height: 8),
            Text(
              'No categories available',
              style: TextStyle(
                color: colorScheme.onSurface.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: 8),
      children:
          _categories.expand((parent) => _buildCategoryItems(parent)).toList(),
    );
  }

  List<Widget> _buildCategoryItems(ProviderCategory parent) {
    final List<Widget> items = [];

    // Add parent category header
    items.add(_buildParentCategoryItem(parent));

    // Add child categories with proper animation
    final isExpanded = _expandedCategories[parent.id] == true;
    final controller = _animationControllers[parent.id];

    if (controller != null) {
      items.add(
        AnimatedBuilder(
          animation: controller,
          builder: (context, child) {
            return SizeTransition(
              sizeFactor: CurvedAnimation(
                parent: controller,
                curve: Curves.easeOutCubic,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children:
                    parent.children
                        .map((child) => _buildChildCategoryItem(child))
                        .toList(),
              ),
            );
          },
        ),
      );
    }

    return items;
  }

  Widget _buildParentCategoryItem(ProviderCategory category) {
    final isExpanded = _expandedCategories[category.id] == true;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          final wasExpanded = isExpanded;
          setState(() {
            _expandedCategories[category.id] = !wasExpanded;
          });

          final controller = _animationControllers[category.id];
          if (wasExpanded) {
            controller?.reverse();
          } else {
            controller?.forward();
          }

          // Refresh the overlay to show the updated state
          _refreshOverlay();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: colorScheme.surfaceVariant.withOpacity(0.3),
            border: Border(
              bottom: BorderSide(
                color: colorScheme.outline.withOpacity(0.1),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              AnimatedRotation(
                turns: isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOutCubic,
                child: Icon(
                  Icons.expand_more_rounded,
                  color: colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Container(
                width: 4,
                height: 32,
                decoration: BoxDecoration(
                  color: colorScheme.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  category.name,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: colorScheme.onSurface,
                    letterSpacing: -0.2,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${category.children.length}',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChildCategoryItem(ProviderCategory category) {
    final isSelected = widget.selectedCategory?.id == category.id;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          print(
            '[HierarchicalCategoryDropdown] Category selected: ${category.name} (ID: ${category.id}, isSelectable: ${category.isSelectable})',
          );
          widget.onChanged(category);
          _removeOverlay();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? colorScheme.primaryContainer.withOpacity(0.3)
                    : Colors.transparent,
            border: Border(
              bottom: BorderSide(
                color: colorScheme.outline.withOpacity(0.05),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              const SizedBox(width: 32), // Indent for hierarchy
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? colorScheme.primary
                          : colorScheme.outline.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  category.name,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 14,
                    color:
                        isSelected
                            ? colorScheme.onPrimaryContainer
                            : colorScheme.onSurface,
                    letterSpacing: -0.1,
                  ),
                ),
              ),
              if (isSelected)
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.check_rounded,
                    color: colorScheme.onPrimary,
                    size: 14,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CompositedTransformTarget(
      link: _layerLink,
      child: FormField<ProviderCategory>(
        initialValue: widget.selectedCategory,
        validator: widget.validator,
        builder: (FormFieldState<ProviderCategory> field) {
          // Update the field value when selectedCategory changes
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (field.value != widget.selectedCategory) {
              field.didChange(widget.selectedCategory);
            }
          });

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  key: _dropdownKey,
                  onTap: _toggleDropdown,
                  borderRadius: BorderRadius.circular(12),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            _isDropdownOpen
                                ? colorScheme.primary
                                : field.hasError
                                ? colorScheme.error
                                : colorScheme.outline.withOpacity(0.5),
                        width: _isDropdownOpen ? 2 : 1,
                      ),
                      color:
                          _isDropdownOpen
                              ? colorScheme.primaryContainer.withOpacity(0.1)
                              : colorScheme.surface,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                    child: Row(
                      children: [
                        if (widget.selectedCategory != null) ...[
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer.withOpacity(
                                0.3,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                widget.selectedCategory!.icon ?? '📋',
                                style: const TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              widget.selectedCategory!.name,
                              style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                                color: colorScheme.onSurface,
                                letterSpacing: -0.1,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ] else ...[
                          Icon(
                            Icons.category_outlined,
                            color: colorScheme.onSurfaceVariant,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Select your business category',
                              style: TextStyle(
                                color: colorScheme.onSurfaceVariant,
                                fontSize: 15,
                                letterSpacing: -0.1,
                              ),
                            ),
                          ),
                        ],
                        AnimatedRotation(
                          turns: _isDropdownOpen ? 0.5 : 0,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeOutCubic,
                          child: Icon(
                            Icons.expand_more_rounded,
                            color:
                                _isDropdownOpen
                                    ? colorScheme.primary
                                    : colorScheme.onSurfaceVariant,
                            size: 20,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (field.hasError) ...[
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    field.errorText!,
                    style: TextStyle(color: colorScheme.error, fontSize: 12),
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }
}
