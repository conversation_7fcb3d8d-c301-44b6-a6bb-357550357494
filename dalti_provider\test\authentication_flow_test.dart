import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/core/auth/jwt_service.dart';
import 'package:dalti_provider/core/auth/models/jwt_token.dart';
import 'package:dalti_provider/core/network/http_client.dart';
import 'package:dalti_provider/core/network/interceptors/auth_interceptor.dart';
import 'package:dalti_provider/features/auth/models/auth_models.dart';

void main() {
  group('Authentication Flow Tests', () {
    test('AuthResponse correctly maps sessionId to accessToken', () {
      // Simulate API response with sessionId (as per Dalti Provider API)
      final apiResponse = {
        'sessionId': 'test-session-id-12345',
        'refreshToken': 'test-refresh-token',
        'user': {
          'id': 'user-123',
          'email': '<EMAIL>',
        },
        'provider': {
          'id': 1,
          'businessName': 'Test Business',
        },
      };

      final authResponse = AuthResponse.fromJson(apiResponse);

      expect(authResponse.success, isTrue);
      expect(authResponse.accessToken, equals('test-session-id-12345'));
      expect(authResponse.refreshToken, equals('test-refresh-token'));
      expect(authResponse.user, isNotNull);
      expect(authResponse.provider, isNotNull);
    });

    test('JwtToken correctly stores sessionId as accessToken', () {
      // Simulate the token data format used by JwtService
      final tokenData = {
        'access_token': 'test-session-id-12345',
        'refresh_token': 'test-refresh-token',
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      final jwtToken = JwtToken.fromJson(tokenData);

      expect(jwtToken.accessToken, equals('test-session-id-12345'));
      expect(jwtToken.refreshToken, equals('test-refresh-token'));
      expect(jwtToken.tokenType, equals('Bearer'));
      expect(jwtToken.isValid, isTrue);
    });

    test('JwtToken generates correct Authorization header', () {
      final jwtToken = JwtToken(
        accessToken: 'test-session-id-12345',
        refreshToken: 'test-refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      final authHeader = '${jwtToken.tokenType} ${jwtToken.accessToken}';

      expect(authHeader, equals('Bearer test-session-id-12345'));
    });

    test('Public endpoints should skip authentication', () {
      // Test the logic that determines which endpoints skip auth
      final publicPaths = [
        '/api/auth/provider/login',
        '/api/auth/request-email-otp',
        '/api/auth/provider/verify-otp-register',
        '/api/auth/refresh-token',
        '/health',
      ];

      for (final path in publicPaths) {
        // Simulate the logic from AuthInterceptor._shouldSkipAuth
        final shouldSkip = publicPaths.any((skipPath) => path.contains(skipPath));
        expect(shouldSkip, isTrue, reason: 'Path $path should skip auth');
      }
    });

    test('Protected endpoints should require authentication', () {
      final publicPaths = [
        '/api/auth/provider/login',
        '/api/auth/request-email-otp',
        '/api/auth/provider/verify-otp-register',
        '/api/auth/refresh-token',
        '/health',
      ];

      // Test protected endpoints that should require authentication
      final protectedPaths = [
        '/api/auth/providers/locations',
        '/api/auth/providers/locations/1',
        '/api/auth/providers/queues',
        '/api/auth/providers/schedules',
        '/api/auth/profile',
      ];

      for (final path in protectedPaths) {
        // Simulate the logic from AuthInterceptor._shouldSkipAuth
        final shouldSkip = publicPaths.any((skipPath) => path.contains(skipPath));
        expect(shouldSkip, isFalse, reason: 'Path $path should require auth');
      }
    });

    test('Complete authentication flow simulation', () {
      // 1. Simulate login API response
      final loginApiResponse = {
        'sessionId': 'session-abc123',
        'refreshToken': 'refresh-xyz789',
        'user': {
          'id': 'user-456',
          'email': '<EMAIL>',
        },
        'provider': {
          'id': 2,
          'businessName': 'My Clinic',
          'category': {
            'id': 1,
            'name': 'Healthcare',
          },
        },
      };

      // 2. Parse AuthResponse
      final authResponse = AuthResponse.fromJson(loginApiResponse);
      expect(authResponse.success, isTrue);
      expect(authResponse.accessToken, equals('session-abc123'));

      // 3. Convert to JWT token format
      final tokenData = {
        'access_token': authResponse.accessToken,
        'refresh_token': authResponse.refreshToken,
        'expires_in': 3600,
        'token_type': 'Bearer',
      };

      // 4. Create JWT token
      final jwtToken = JwtToken.fromJson(tokenData);
      expect(jwtToken.accessToken, equals('session-abc123'));
      expect(jwtToken.isValid, isTrue);

      // 5. Generate authorization header
      final authHeader = '${jwtToken.tokenType} ${jwtToken.accessToken}';
      expect(authHeader, equals('Bearer session-abc123'));

      // 6. Verify this is what would be sent to location API
      // When calling /api/auth/providers/locations, the header should be:
      // Authorization: Bearer session-abc123
      expect(authHeader.startsWith('Bearer '), isTrue);
      expect(authHeader.contains('session-abc123'), isTrue);
    });

    test('Token expiration logic works correctly', () {
      // Test expired token
      final expiredToken = JwtToken(
        accessToken: 'expired-token',
        expiresAt: DateTime.now().subtract(const Duration(hours: 1)),
      );
      expect(expiredToken.isExpired, isTrue);
      expect(expiredToken.isValid, isFalse);

      // Test valid token
      final validToken = JwtToken(
        accessToken: 'valid-token',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );
      expect(validToken.isExpired, isFalse);
      expect(validToken.isValid, isTrue);

      // Test token that will expire soon
      final soonToExpireToken = JwtToken(
        accessToken: 'soon-to-expire-token',
        expiresAt: DateTime.now().add(const Duration(minutes: 2)),
      );
      expect(soonToExpireToken.willExpireSoon, isTrue);
      expect(soonToExpireToken.isValid, isTrue);
    });

    test('Token storage and retrieval format', () {
      final originalToken = JwtToken(
        accessToken: 'test-session-id',
        refreshToken: 'test-refresh-token',
        tokenType: 'Bearer',
        expiresAt: DateTime.now().add(const Duration(hours: 1)),
      );

      // Convert to storage format
      final storageJson = originalToken.toJson();
      expect(storageJson['access_token'], equals('test-session-id'));
      expect(storageJson['refresh_token'], equals('test-refresh-token'));
      expect(storageJson['token_type'], equals('Bearer'));

      // Restore from storage
      final restoredToken = JwtToken.fromStoredJson(storageJson);
      expect(restoredToken.accessToken, equals(originalToken.accessToken));
      expect(restoredToken.refreshToken, equals(originalToken.refreshToken));
      expect(restoredToken.tokenType, equals(originalToken.tokenType));
      expect(restoredToken.isValid, equals(originalToken.isValid));
    });
  });
}
