import '../../../core/network/http_client.dart';
import '../models/message_models.dart';

/// API service for message and conversation operations matching the actual backend endpoints
class MessageApiService {
  final HttpClient _httpClient;

  MessageApiService(this._httpClient);

  /// Get conversations - List all conversations
  /// GET /api/auth/mobile/conversations
  Future<ConversationsResponse> getConversations({
    String? searchQuery,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      print('[MessageApiService] Fetching conversations');

      final response = await _httpClient.get('/api/auth/mobile/conversations');

      print(
        '[MessageApiService] Conversations response: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        print('[MessageApiService] Raw response data: ${response.data}');

        // The API returns an array directly
        final List<dynamic> conversationsData = response.data as List<dynamic>;
        final conversations = <Conversation>[];
        int totalUnreadCount = 0;

        for (final conversationJson in conversationsData) {
          final conversation = _parseConversationFromApi(conversationJson);
          conversations.add(conversation);
          totalUnreadCount += conversation.unreadCount;
        }

        // Apply client-side filtering if needed
        List<Conversation> filteredConversations = conversations;
        if (searchQuery?.isNotEmpty == true) {
          filteredConversations =
              conversations
                  .where(
                    (conv) => conv.customerName.toLowerCase().contains(
                      searchQuery!.toLowerCase(),
                    ),
                  )
                  .toList();
        }

        return ConversationsResponse(
          success: true,
          conversations: filteredConversations,
          totalCount: filteredConversations.length,
          unreadCount: totalUnreadCount,
        );
      } else {
        return ConversationsResponse(
          success: false,
          error: MessageError(
            code: 'HTTP_ERROR',
            message: 'HTTP ${response.statusCode}: ${response.statusMessage}',
            details: response.data?.toString(),
          ),
        );
      }
    } catch (e) {
      print('[MessageApiService] Conversations error: $e');
      return ConversationsResponse(
        success: false,
        error: MessageError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get messages in a conversation
  /// GET /api/auth/mobile/messages/:conversationId
  Future<MessagesResponse> getMessages(
    String conversationId, {
    String? searchQuery,
    int page = 1,
    int pageSize = 50,
  }) async {
    try {
      print(
        '[MessageApiService] Fetching messages for conversation: $conversationId',
      );

      final response = await _httpClient.get(
        '/api/auth/mobile/messages/$conversationId',
      );

      print('[MessageApiService] Messages response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[MessageApiService] Raw response data: ${response.data}');

        // The API returns an array directly
        final List<dynamic> messagesData = response.data as List<dynamic>;
        final messages = <Message>[];

        for (final messageJson in messagesData) {
          final message = _parseMessageFromApi(messageJson, conversationId);
          messages.add(message);
        }

        // Apply client-side filtering if needed
        List<Message> filteredMessages = messages;
        if (searchQuery?.isNotEmpty == true) {
          filteredMessages =
              messages
                  .where(
                    (msg) => msg.content.toLowerCase().contains(
                      searchQuery!.toLowerCase(),
                    ),
                  )
                  .toList();
        }

        return MessagesResponse(
          success: true,
          messages: filteredMessages,
          totalCount: filteredMessages.length,
        );
      } else {
        return MessagesResponse(
          success: false,
          error: MessageError(
            code: 'HTTP_ERROR',
            message: 'HTTP ${response.statusCode}: ${response.statusMessage}',
            details: response.data?.toString(),
          ),
        );
      }
    } catch (e) {
      print('[MessageApiService] Messages error: $e');
      return MessagesResponse(
        success: false,
        error: MessageError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Send a message
  /// POST /api/auth/mobile/messages
  Future<SendMessageResponse> sendMessage(SendMessageRequest request) async {
    try {
      print(
        '[MessageApiService] Sending message to conversation: ${request.conversationId}',
      );

      final response = await _httpClient.post(
        '/api/auth/mobile/messages',
        data: {
          'conversationId':
              int.tryParse(request.conversationId) ?? request.conversationId,
          'content': request.content,
        },
      );

      print(
        '[MessageApiService] Send message response: ${response.statusCode}',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('[MessageApiService] Raw response data: ${response.data}');

        final messageData = response.data as Map<String, dynamic>;
        final message = _parseMessageFromApi(
          messageData,
          request.conversationId,
        );

        return SendMessageResponse(success: true, message: message);
      } else {
        return SendMessageResponse(
          success: false,
          error: MessageError(
            code: 'HTTP_ERROR',
            message: 'HTTP ${response.statusCode}: ${response.statusMessage}',
            details: response.data?.toString(),
          ),
        );
      }
    } catch (e) {
      print('[MessageApiService] Send message error: $e');
      return SendMessageResponse(
        success: false,
        error: MessageError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Mark message as read
  /// POST /api/auth/mobile/messages/read
  Future<bool> markMessagesAsRead(
    String conversationId,
    List<String> messageIds,
  ) async {
    try {
      print(
        '[MessageApiService] Marking messages as read in conversation: $conversationId',
      );

      // For now, mark the first message ID (API expects single message)
      if (messageIds.isEmpty) return true;

      final response = await _httpClient.post(
        '/api/auth/mobile/messages/read',
        data: {
          'messageId': int.parse(messageIds.first),
          'conversationId': int.parse(conversationId),
        },
      );

      print(
        '[MessageApiService] Mark as read response: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        return responseData['success'] ?? false;
      }

      return false;
    } catch (e) {
      print('[MessageApiService] Mark as read error: $e');
      return false;
    }
  }

  /// Start new conversation
  /// POST /api/auth/mobile/conversations/start
  Future<ConversationResponse> createConversation(
    CreateConversationRequest request,
  ) async {
    try {
      print(
        '[MessageApiService] Creating conversation with customer: ${request.customerId}',
      );

      final response = await _httpClient.post(
        '/api/auth/mobile/conversations/start',
        data: {
          'otherUserIds': [request.customerId],
          'isGroup': false,
        },
      );

      print(
        '[MessageApiService] Create conversation response: ${response.statusCode}',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('[MessageApiService] Raw response data: ${response.data}');

        final conversationData = response.data as Map<String, dynamic>;
        final conversation = _parseNewConversationFromApi(conversationData);

        return ConversationResponse(success: true, conversation: conversation);
      } else {
        return ConversationResponse(
          success: false,
          error: MessageError(
            code: 'HTTP_ERROR',
            message: 'HTTP ${response.statusCode}: ${response.statusMessage}',
            details: response.data?.toString(),
          ),
        );
      }
    } catch (e) {
      print('[MessageApiService] Create conversation error: $e');
      return ConversationResponse(
        success: false,
        error: MessageError(
          code: 'NETWORK_ERROR',
          message: 'Network error occurred',
          details: e.toString(),
        ),
      );
    }
  }

  /// Get customers for starting conversations
  /// GET /api/auth/providers/customers
  Future<List<CustomerForConversation>> getCustomersForConversation() async {
    try {
      print('[MessageApiService] Fetching customers for conversation');

      final response = await _httpClient.get('/api/auth/providers/customers');

      print('[MessageApiService] Customers response: ${response.statusCode}');

      if (response.statusCode == 200) {
        print('[MessageApiService] Raw response data: ${response.data}');

        // The API response has nested structure: { "data": { "data": [...] } }
        final responseData = response.data as Map<String, dynamic>;
        final dataWrapper = responseData['data'] as Map<String, dynamic>;
        final List<dynamic> customersData =
            dataWrapper['data'] as List<dynamic>;
        final customers = <CustomerForConversation>[];

        for (final customerJson in customersData) {
          final customer = _parseCustomerFromApi(customerJson);
          customers.add(customer);
        }

        return customers;
      } else {
        throw Exception(
          'HTTP ${response.statusCode}: ${response.statusMessage}',
        );
      }
    } catch (e) {
      print('[MessageApiService] Customers error: $e');
      throw Exception('Failed to fetch customers: $e');
    }
  }

  /// Parse conversation from API response format
  /// API Response: {"id": 1, "displayName": "John Doe", "unreadCount": 2, "lastMessage": {...}}
  Conversation _parseConversationFromApi(Map<String, dynamic> json) {
    final lastMessageData = json['lastMessage'] as Map<String, dynamic>?;
    Message? lastMessage;

    if (lastMessageData != null) {
      lastMessage = Message(
        id: 'last_msg_${json['id']}', // Generate ID since API doesn't provide it
        conversationId: json['id'].toString(),
        senderId:
            'unknown', // API doesn't provide sender ID in conversation list
        senderName: lastMessageData['senderName'] ?? 'Unknown',
        type: MessageType.text, // Assume text for now
        content: lastMessageData['content'] ?? '',
        timestamp: DateTime.parse(lastMessageData['createdAt']),
        status: MessageStatus.delivered, // Default status
      );
    }

    return Conversation(
      id: json['id'].toString(),
      customerId:
          'customer_${json['id']}', // Generate customer ID from conversation
      customerName: json['displayName'] ?? 'Unknown Customer',
      createdAt:
          DateTime.now(), // API doesn't provide this in conversation list
      updatedAt: lastMessage?.timestamp,
      unreadCount: json['unreadCount'] ?? 0,
      lastMessage: lastMessage,
      status: ConversationStatus.active,
    );
  }

  /// Parse message from API response format
  /// API Response: {"id": 1, "content": "Hello", "createdAt": "...", "sender": {...}, "status": "DELIVERED"}
  Message _parseMessageFromApi(
    Map<String, dynamic> json,
    String conversationId,
  ) {
    final senderData = json['sender'] as Map<String, dynamic>? ?? {};
    final statusString = json['status'] as String? ?? 'SENT';

    MessageStatus status;
    switch (statusString.toUpperCase()) {
      case 'SENT':
        status = MessageStatus.sent;
        break;
      case 'DELIVERED':
        status = MessageStatus.delivered;
        break;
      case 'READ':
        status = MessageStatus.read;
        break;
      case 'FAILED':
        status = MessageStatus.failed;
        break;
      default:
        status = MessageStatus.sent;
    }

    return Message(
      id: json['id'].toString(),
      conversationId: conversationId,
      senderId: senderData['id'] ?? 'unknown',
      senderName: senderData['name'] ?? 'Unknown',
      type: MessageType.text, // API doesn't specify type, assume text
      content: json['content'] ?? '',
      timestamp: DateTime.parse(json['createdAt']),
      status: status,
    );
  }

  /// Parse new conversation from API response format
  /// API Response: {"id": 3, "name": null, "isGroup": false, "displayName": "Direct Message", "createdAt": "...", "updatedAt": "..."}
  Conversation _parseNewConversationFromApi(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'].toString(),
      customerId: 'customer_${json['id']}', // Generate customer ID
      customerName: json['displayName'] ?? json['name'] ?? 'New Conversation',
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      unreadCount: 0,
      status: ConversationStatus.active,
    );
  }

  /// Parse customer from API response format
  /// API Response: {"id": "uuid", "firstName": "John", "lastName": "Doe", "email": "...", "mobileNumber": "..."}
  CustomerForConversation _parseCustomerFromApi(Map<String, dynamic> json) {
    return CustomerForConversation(
      id: json['id'],
      folderId:
          json['id'], // Use the same ID as folder ID since API doesn't provide separate folder ID
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      email: json['email'],
      mobileNumber: json['mobileNumber'],
    );
  }

  /// Get unread messages count (derived from conversations)
  Future<int> getUnreadMessagesCount() async {
    try {
      final response = await getConversations();
      if (response.success) {
        return response.unreadCount;
      }
      return 0;
    } catch (e) {
      print('[MessageApiService] Unread count error: $e');
      return 0;
    }
  }

  /// Archive conversation (not provided by API, return false for now)
  Future<bool> archiveConversation(String conversationId) async {
    print('[MessageApiService] Archive conversation not implemented in API');
    return false;
  }

  /// Delete conversation (not provided by API, return false for now)
  Future<bool> deleteConversation(String conversationId) async {
    print('[MessageApiService] Delete conversation not implemented in API');
    return false;
  }

  /// Get single conversation (not provided by API, derive from conversations list)
  Future<ConversationResponse> getConversation(String conversationId) async {
    try {
      final response = await getConversations();
      if (response.success) {
        final conversation = response.conversations.firstWhere(
          (conv) => conv.id == conversationId,
          orElse: () => throw Exception('Conversation not found'),
        );

        return ConversationResponse(success: true, conversation: conversation);
      } else {
        return ConversationResponse(success: false, error: response.error);
      }
    } catch (e) {
      return ConversationResponse(
        success: false,
        error: MessageError(
          code: 'NOT_FOUND',
          message: 'Conversation not found',
          details: e.toString(),
        ),
      );
    }
  }

  /// Mark a message as read
  /// POST /api/auth/mobile/messages/read
  Future<MarkAsReadResponse> markMessageAsRead(
    MarkAsReadRequest request,
  ) async {
    try {
      print('[MessageApiService] Marking message as read: ${request.toJson()}');

      final response = await _httpClient.post(
        '/api/auth/mobile/messages/read',
        data: request.toJson(),
      );

      print(
        '[MessageApiService] Mark as read response: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        return MarkAsReadResponse(success: true);
      } else {
        return MarkAsReadResponse(
          success: false,
          error: MessageError(
            code: 'MARK_READ_FAILED',
            message: 'Failed to mark message as read',
            details: 'Status code: ${response.statusCode}',
          ),
        );
      }
    } catch (e) {
      print('[MessageApiService] Error marking message as read: $e');
      return MarkAsReadResponse(
        success: false,
        error: MessageError(
          code: 'MARK_READ_ERROR',
          message: 'Error marking message as read',
          details: e.toString(),
        ),
      );
    }
  }
}
