import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dalti_provider/features/profile/screens/profile_screen.dart';
import 'package:dalti_provider/features/profile/models/profile_models.dart';
import 'package:dalti_provider/features/profile/providers/profile_provider.dart';

void main() {
  group('Profile Screen Assertion Fix Tests', () {
    testWidgets('Profile screen should not have ref.listen in initState', (WidgetTester tester) async {
      // This test verifies that the profile screen no longer uses ref.listen in initState
      // which was causing the assertion error
      
      // Create a mock profile data
      final mockProfile = ProfileData(
        id: 'test-id',
        title: 'Test Provider',
        phone: '+213123456789',
        presentation: 'Test presentation',
        isVerified: false,
        averageRating: 4.5,
        totalReviews: 10,
        category: null,
      );

      // Override the profile provider to return mock data
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            profileProvider.overrideWith((ref) => AsyncValue.data(mockProfile)),
          ],
          child: MaterialApp(
            home: ProfileScreen(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the profile screen is displayed
      expect(find.byType(ProfileScreen), findsOneWidget);
      expect(find.text('Test Provider'), findsOneWidget);
      
      // Verify that edit button is present
      expect(find.byIcon(Icons.edit), findsOneWidget);
      
      // Tap the edit button to enter edit mode
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();
      
      // Verify that save and cancel buttons appear
      expect(find.text('Save Changes'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      
      // The test passes if no assertion errors are thrown during these operations
      expect(true, isTrue);
    });

    testWidgets('Profile screen should handle save operation without assertion error', (WidgetTester tester) async {
      // This test specifically verifies that the save operation doesn't cause assertion errors
      
      final mockProfile = ProfileData(
        id: 'test-id',
        title: 'Test Provider',
        phone: '+213123456789',
        presentation: 'Test presentation',
        isVerified: false,
        averageRating: 4.5,
        totalReviews: 10,
        category: null,
      );

      // Create a notifier that we can control
      final mockNotifier = ProfileNotifier();
      
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            profileProvider.overrideWith((ref) => AsyncValue.data(mockProfile)),
          ],
          child: MaterialApp(
            home: ProfileScreen(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Enter edit mode
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();

      // Modify the title field
      await tester.enterText(find.byType(TextFormField).first, 'Updated Provider Name');
      await tester.pumpAndSettle();

      // Tap save button - this should not cause assertion errors
      await tester.tap(find.text('Save Changes'));
      await tester.pump(); // Just pump once to trigger the save operation
      
      // The test passes if no assertion errors are thrown
      expect(true, isTrue);
    });

    test('ProfileFormController should handle profile updates correctly', () {
      // Test the form controller logic separately
      final controller = ProfileFormController();
      
      final profile = ProfileData(
        id: 'test-id',
        title: 'Test Provider',
        phone: '+213123456789',
        presentation: 'Test presentation',
        isVerified: false,
        averageRating: 4.5,
        totalReviews: 10,
        category: null,
      );

      // Update from profile
      controller.updateFromProfile(profile);
      
      // Verify controllers are populated
      expect(controller.titleController.text, 'Test Provider');
      expect(controller.phoneController.text, '+213123456789');
      expect(controller.presentationController.text, 'Test presentation');
      
      // Test request generation
      final request = controller.toRequest();
      expect(request.title, 'Test Provider');
      expect(request.phone, '+213123456789');
      expect(request.presentation, 'Test presentation');
      
      // Clean up
      controller.dispose();
    });
  });
}
