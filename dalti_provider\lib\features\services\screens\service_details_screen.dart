import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/service_provider.dart';
import '../models/service_models.dart';

class ServiceDetailsScreen extends ConsumerStatefulWidget {
  final String serviceId;

  const ServiceDetailsScreen({super.key, required this.serviceId});

  @override
  ConsumerState<ServiceDetailsScreen> createState() =>
      _ServiceDetailsScreenState();
}

class _ServiceDetailsScreenState extends ConsumerState<ServiceDetailsScreen> {
  Service? _service;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // Use addPostFrameCallback to avoid modifying provider during widget tree building
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadService();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadService() async {
    try {
      final serviceId = int.tryParse(widget.serviceId);
      if (serviceId != null) {
        await ref
            .read(serviceNotifierProvider.notifier)
            .selectService(serviceId);
        final serviceState = ref.read(serviceNotifierProvider);
        if (mounted) {
          setState(() {
            _service = serviceState.selectedService;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshService() async {
    try {
      final serviceId = int.tryParse(widget.serviceId);
      if (serviceId != null) {
        await ref
            .read(serviceNotifierProvider.notifier)
            .refreshService(serviceId);
        final serviceState = ref.read(serviceNotifierProvider);
        if (mounted) {
          setState(() {
            _service = serviceState.selectedService;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Service Details')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_service == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Service Details')),
        body: const Center(child: Text('Service not found')),
      );
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(_service!.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshService,
            tooltip: 'Refresh Service',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () async {
              await context.push('/services/${widget.serviceId}/edit');
              // When we return from edit, refresh the service
              if (mounted) {
                _refreshService();
              }
            },
            tooltip: 'Edit Service',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'delete':
                  _showDeleteConfirmation();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshService,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Service header card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // Color indicator
                          Container(
                            width: 6,
                            height: 40,
                            decoration: BoxDecoration(
                              color:
                                  _parseColor(_service!.color) ??
                                  colorScheme.primary,
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          const SizedBox(width: 16),

                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _service!.title,
                                  style: theme.textTheme.headlineSmall
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _service!.formattedDuration,
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.onSurface.withValues(
                                      alpha: 0.7,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Price
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primaryContainer,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _service!.formattedPrice,
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: colorScheme.onPrimaryContainer,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      if (_service!.description != null &&
                          _service!.description!.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          _service!.description!,
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Service settings
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Service Settings',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),

                      _buildSettingRow(
                        icon: Icons.online_prediction,
                        title: 'Online Bookings',
                        value: _service!.acceptOnline,
                        color: Colors.green,
                      ),

                      _buildSettingRow(
                        icon: Icons.person_add,
                        title: 'New Customers',
                        value: _service!.acceptNew,
                        color: Colors.blue,
                      ),

                      _buildSettingRow(
                        icon: Icons.notifications_active,
                        title: 'Notifications',
                        value: _service!.notificationOn,
                        color: Colors.orange,
                      ),

                      if (_service!.isActive != null)
                        _buildSettingRow(
                          icon: Icons.check_circle,
                          title: 'Active',
                          value: _service!.isActive!,
                          color:
                              _service!.isActive! ? Colors.green : Colors.grey,
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Additional info
              if (_service!.pointsRequirements != null &&
                  _service!.pointsRequirements! > 0)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(Icons.account_balance_wallet, color: Colors.blue),
                        const SizedBox(width: 12),
                        Text(
                          'Requires ${_service!.pointsRequirements} credit points to book',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingRow({
    required IconData icon,
    required String title,
    required bool value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: value ? color : Colors.grey, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Icon(
            value ? Icons.check : Icons.close,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
        ],
      ),
    );
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;

    try {
      String hexColor = colorString.replaceAll('#', '');
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return null;
    }
  }

  Future<void> _showDeleteConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Service'),
            content: Text(
              'Are you sure you want to delete "${_service!.title}"?\n\n'
              'This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(serviceNotifierProvider.notifier)
          .deleteService(_service!.id);

      if (mounted) {
        if (success) {
          // Refresh the services list to reflect the deletion
          await ref
              .read(serviceNotifierProvider.notifier)
              .loadServices(forceRefresh: true);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Service "${_service!.title}" deleted successfully',
              ),
              backgroundColor: Colors.green,
            ),
          );
          context.pop(); // Go back to services list
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete service "${_service!.title}"'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
