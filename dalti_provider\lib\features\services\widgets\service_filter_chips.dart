import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../auth/models/provider_category.dart';
import '../../auth/services/category_service.dart';
import '../../../core/providers/app_providers.dart';

class ServiceFilterChips extends ConsumerStatefulWidget {
  final int? selectedCategoryId;
  final bool showActiveOnly;
  final Function(int?) onCategoryChanged;
  final Function(bool) onActiveFilterChanged;

  const ServiceFilterChips({
    super.key,
    this.selectedCategoryId,
    required this.showActiveOnly,
    required this.onCategoryChanged,
    required this.onActiveFilterChanged,
  });

  @override
  ConsumerState<ServiceFilterChips> createState() => _ServiceFilterChipsState();
}

class _ServiceFilterChipsState extends ConsumerState<ServiceFilterChips> {
  List<ProviderCategory> _categories = [];
  bool _isLoadingCategories = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categoryService = ref.read(categoryServiceProvider);
      final categories = await categoryService.getSelectableCategories();
      
      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
      }
    } catch (e) {
      print('[ServiceFilterChips] Error loading categories: $e');
      if (mounted) {
        setState(() {
          _isLoadingCategories = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // Active/All filter
          FilterChip(
            label: Text(widget.showActiveOnly ? 'Active Only' : 'All Services'),
            selected: widget.showActiveOnly,
            onSelected: (selected) {
              widget.onActiveFilterChanged(selected);
            },
            avatar: Icon(
              widget.showActiveOnly ? Icons.check_circle : Icons.all_inclusive,
              size: 18,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Category filters
          if (_isLoadingCategories)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else ...[
            // All categories chip
            FilterChip(
              label: const Text('All Categories'),
              selected: widget.selectedCategoryId == null,
              onSelected: (selected) {
                if (selected) {
                  widget.onCategoryChanged(null);
                }
              },
              avatar: const Icon(Icons.category, size: 18),
            ),
            
            const SizedBox(width: 8),
            
            // Individual category chips
            ..._categories.map((category) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(category.name),
                selected: widget.selectedCategoryId == category.id,
                onSelected: (selected) {
                  widget.onCategoryChanged(selected ? category.id : null);
                },
                avatar: category.icon != null 
                    ? Text(category.icon!, style: const TextStyle(fontSize: 14))
                    : const Icon(Icons.business, size: 18),
              ),
            )),
          ],
        ],
      ),
    );
  }
}
