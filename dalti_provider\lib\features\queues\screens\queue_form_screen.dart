import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/queue_provider.dart';
import '../models/queue_models.dart';
import '../../../core/errors/api_exceptions.dart';
import '../../locations/providers/location_provider.dart';
import '../../locations/models/location_models.dart';
import '../../services/providers/service_provider.dart';
import '../../services/models/service_models.dart';
import '../../onboarding/widgets/opening_hours_widget.dart';

import '../../../core/theme/theme_provider.dart';

class QueueFormScreen extends ConsumerStatefulWidget {
  final int? queueId; // null for add, non-null for edit
  final int? initialLocationId; // for pre-selecting location when adding

  const QueueFormScreen({super.key, this.queueId, this.initialLocationId});

  @override
  ConsumerState<QueueFormScreen> createState() => _QueueFormScreenState();
}

class _QueueFormScreenState extends ConsumerState<QueueFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();

  int? _selectedLocationId;
  List<int> _selectedServiceIds = [];
  bool _isLoading = false;

  Map<String, List<TimeSlot>> _openingHours = {};

  @override
  void initState() {
    super.initState();

    // Set initial location if provided
    _selectedLocationId = widget.initialLocationId;

    // Initialize default opening hours
    _openingHours = _getDefaultOpeningHours();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(locationNotifierProvider.notifier).loadLocations();
      // Load services using the same pattern as Services screen
      _loadServicesForForm();

      if (widget.queueId != null) {
        _loadExistingQueue();
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _loadExistingQueue() async {
    try {
      setState(() => _isLoading = true);

      final queueNotifier = ref.read(queueNotifierProvider.notifier);
      final queue = queueNotifier.getQueueById(widget.queueId!);

      if (queue != null) {
        print('[QueueFormScreen] Loading existing queue: ${queue.id}');
        print(
          '[QueueFormScreen] Queue has opening hours: ${queue.openingHours != null}',
        );

        final openingHoursWidget = queue.getOpeningHoursForWidget();
        print(
          '[QueueFormScreen] Converted opening hours for widget: ${openingHoursWidget.keys.toList()}',
        );

        setState(() {
          _selectedLocationId = queue.locationId;
          _selectedServiceIds = queue.serviceIds;
          _titleController.text = queue.title;
          // Populate opening hours from queue data
          _openingHours = openingHoursWidget;
          _isLoading = false;
        });

        print(
          '[QueueFormScreen] State updated with opening hours: ${_openingHours.keys.toList()}',
        );
      } else {
        // Queue not found in current state, try to load it
        await queueNotifier.loadQueues(forceRefresh: true);
        final refreshedQueue = queueNotifier.getQueueById(widget.queueId!);

        if (refreshedQueue != null) {
          print(
            '[QueueFormScreen] Loading refreshed queue: ${refreshedQueue.id}',
          );
          print(
            '[QueueFormScreen] Refreshed queue has opening hours: ${refreshedQueue.openingHours != null}',
          );

          final refreshedOpeningHours =
              refreshedQueue.getOpeningHoursForWidget();
          print(
            '[QueueFormScreen] Converted refreshed opening hours: ${refreshedOpeningHours.keys.toList()}',
          );

          setState(() {
            _selectedLocationId = refreshedQueue.locationId;
            _selectedServiceIds = refreshedQueue.serviceIds;
            _titleController.text = refreshedQueue.title;
            // Populate opening hours from refreshed queue data
            _openingHours = refreshedOpeningHours;
            _isLoading = false;
          });

          print(
            '[QueueFormScreen] State updated with refreshed opening hours: ${_openingHours.keys.toList()}',
          );
        } else {
          throw Exception('Queue not found');
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading queue: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationNotifierProvider);
    final serviceState = ref.watch(serviceNotifierProvider);
    final isEditing = widget.queueId != null;

    return Scaffold(
      backgroundColor:
          context.isDarkMode
              ? context.backgroundColor
              : const Color(0xFFF5F5F5),
      appBar: AppBar(
        title: Text(isEditing ? 'Edit Queue' : 'Create Queue'),
        backgroundColor:
            context.isDarkMode ? context.surfaceColor : context.primaryColor,
        foregroundColor:
            context.isDarkMode ? context.colors.onSurface : Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(
          color: context.isDarkMode ? context.colors.onSurface : Colors.white,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshServices,
            tooltip: 'Refresh Services',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header section
                    if (!isEditing) ...[
                      Text(
                        'Create New Queue',
                        style: context.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: context.colors.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Set up a queue to organize your services and manage appointments efficiently.',
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.colors.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Main form card
                    Card(
                      elevation: 2,
                      color: context.backgroundColor,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Form header
                              Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: Row(
                                  children: [
                                    Icon(
                                      isEditing
                                          ? Icons.edit
                                          : Icons.add_circle_outline,
                                      color: context.colors.primary,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      isEditing
                                          ? 'Queue Information'
                                          : 'Basic Information',
                                      style: context.textTheme.titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                            color: context.colors.onSurface,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Queue title
                              TextFormField(
                                controller: _titleController,
                                decoration: const InputDecoration(
                                  labelText: 'Queue Name *',
                                  filled: true,
                                  fillColor: Colors.transparent,
                                  hintText:
                                      'e.g., General Queue, VIP Queue, Walk-ins',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(Icons.queue),
                                ),
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return 'Queue name is required';
                                  }
                                  if (value.trim().length < 2) {
                                    return 'Name must be at least 2 characters';
                                  }
                                  if (value.trim().length > 100) {
                                    return 'Queue name cannot exceed 100 characters';
                                  }
                                  return null;
                                },
                                textCapitalization: TextCapitalization.words,
                              ),
                              const SizedBox(height: 16),

                              // Location selection
                              _buildLocationDropdown(locationState.locations),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Service selection card
                    Card(
                      elevation: 2,
                      color: context.backgroundColor,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: _buildServiceSelection(
                          serviceState.services,
                          serviceState,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Opening hours card
                    Card(
                      elevation: 2,
                      color: context.backgroundColor,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Card header
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time,
                                  color: context.colors.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Queue Operating Hours',
                                  style: context.textTheme.titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: context.colors.onSurface,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Set when this queue is available for appointments',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.colors.onSurface.withOpacity(
                                  0.6,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            Padding(
                              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                              child: OpeningHoursWidget(
                                initialData: _openingHours,
                                onChanged: (openingHours) {
                                  setState(() {
                                    _openingHours = openingHours;
                                  });
                                },
                                showHeader: false,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => context.pop(),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _canSave() ? _saveQueue : null,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: Text(
                              isEditing ? 'Update Queue' : 'Create Queue',
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Delete button for editing
                    if (isEditing) ...[
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: _deleteQueue,
                          icon: const Icon(
                            Icons.delete_outline,
                            color: Colors.red,
                          ),
                          label: const Text(
                            'Delete Queue',
                            style: TextStyle(color: Colors.red),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.red),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
    );
  }

  Widget _buildLocationDropdown(List<Location> locations) {
    return DropdownButtonFormField<int>(
      value: _selectedLocationId,
      decoration: const InputDecoration(
        labelText: 'Location',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.location_on),
      ),
      items:
          locations.map((location) {
            return DropdownMenuItem<int>(
              value: location.id,
              child: Text(location.name),
            );
          }).toList(),
      onChanged:
          widget.queueId != null
              ? null
              : (value) {
                // Disable for editing
                setState(() {
                  _selectedLocationId = value;
                });
              },
      validator: (value) {
        if (value == null) {
          return 'Please select a location';
        }
        return null;
      },
    );
  }

  Widget _buildServiceSelection(
    List<Service> services,
    ServiceData serviceState,
  ) {
    // Filter services to show only active ones (defaults to true if not specified by API)
    final availableServices = services.where((s) => s.isServiceActive).toList();

    print(
      '[QueueFormScreen] Total services: ${services.length}, Active services: ${availableServices.length}',
    );
    print(
      '[QueueFormScreen] Service details: ${services.map((s) => 'ID:${s.id}, Title:${s.title}, isActive:${s.isActive}, isServiceActive:${s.isServiceActive}').join(', ')}',
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.business_center,
              color: context.colors.primary,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              'Queue Services',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: context.colors.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Select services that will be available in this queue',
          style: context.textTheme.bodyMedium?.copyWith(
            color: context.colors.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 16),

        // Show loading indicator if services are being loaded
        if (serviceState.isLoading && serviceState.services.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: const Center(
              child: Column(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 12),
                  Text('Loading services...'),
                ],
              ),
            ),
          )
        // Show error state if there's an error
        else if (serviceState.hasError)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.errorContainer.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: context.colors.error.withOpacity(0.3)),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48,
                    color: context.colors.error,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Error loading services',
                    style: context.textTheme.titleMedium?.copyWith(
                      color: context.colors.error,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    serviceState.error ?? 'Unknown error occurred',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.colors.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _refreshServices,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.colors.error,
                      foregroundColor: context.colors.onError,
                    ),
                  ),
                ],
              ),
            ),
          )
        else if (availableServices.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: context.colors.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.business_center_outlined,
                  size: 64,
                  color: context.colors.onSurfaceVariant,
                ),
                const SizedBox(height: 16),
                Text(
                  'No services available',
                  style: context.textTheme.titleMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create services first to assign them to queues.',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => context.push('/services'),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Services'),
                ),
              ],
            ),
          )
        else ...[
          // Services list
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: context.colors.outline.withOpacity(0.2),
              ),
            ),
            child: Column(
              children: [
                // Select all/none controls
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.colors.surfaceVariant.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        '${_selectedServiceIds.length} of ${availableServices.length} selected',
                        style: context.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            if (_selectedServiceIds.length ==
                                availableServices.length) {
                              _selectedServiceIds.clear();
                            } else {
                              _selectedServiceIds =
                                  availableServices.map((s) => s.id).toList();
                            }
                          });
                        },
                        child: Text(
                          _selectedServiceIds.length == availableServices.length
                              ? 'Clear All'
                              : 'Select All',
                        ),
                      ),
                    ],
                  ),
                ),

                // Service items
                ...availableServices.asMap().entries.map((entry) {
                  final index = entry.key;
                  final service = entry.value;
                  final isSelected = _selectedServiceIds.contains(service.id);
                  final isLast = index == availableServices.length - 1;

                  return Container(
                    decoration: BoxDecoration(
                      border:
                          isLast
                              ? null
                              : Border(
                                bottom: BorderSide(
                                  color: context.colors.outline.withOpacity(
                                    0.1,
                                  ),
                                  width: 1,
                                ),
                              ),
                    ),
                    child: CheckboxListTile(
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedServiceIds.add(service.id);
                          } else {
                            _selectedServiceIds.remove(service.id);
                          }
                        });
                      },
                      title: Text(
                        service.title,
                        style: context.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            service.formattedDuration,
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.colors.onSurface.withOpacity(0.6),
                            ),
                          ),
                          Text(
                            '${service.pointsRequirements} credits • \$${(service.price ?? 0.0).toStringAsFixed(2)}',
                            style: context.textTheme.bodySmall?.copyWith(
                              color: context.colors.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
          ),

          // Validation message
          if (_selectedServiceIds.isEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 16,
                    color: context.colors.error,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Please select at least one service',
                    style: context.textTheme.bodySmall?.copyWith(
                      color: context.colors.error,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildValidationWarnings() {
    if (_selectedServiceIds.length > 10) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          border: Border.all(color: Colors.orange.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange.shade600),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'This queue has many services (${_selectedServiceIds.length}). Consider splitting into multiple queues for better organization.',
                style: TextStyle(color: Colors.orange.shade700),
              ),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// Convert widget opening hours to API format
  List<Map<String, dynamic>> _convertOpeningHoursToApiFormat(
    Map<String, List<TimeSlot>> widgetData,
  ) {
    final List<Map<String, dynamic>> apiHours = [];

    for (final entry in widgetData.entries) {
      final dayOfWeek = entry.key;
      final timeSlots = entry.value;

      // Convert TimeSlot objects to API format
      final hours =
          timeSlots
              .map(
                (slot) => {
                  'timeFrom':
                      '${slot.from.hour.toString().padLeft(2, '0')}:${slot.from.minute.toString().padLeft(2, '0')}',
                  'timeTo':
                      '${slot.to.hour.toString().padLeft(2, '0')}:${slot.to.minute.toString().padLeft(2, '0')}',
                },
              )
              .toList();

      apiHours.add({
        'dayOfWeek': dayOfWeek,
        'isActive': timeSlots.isNotEmpty,
        'hours': hours,
      });
    }

    return apiHours;
  }

  Future<void> _saveQueue() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final queueNotifier = ref.read(queueNotifierProvider.notifier);

      if (widget.queueId != null) {
        // Update existing queue with opening hours
        final apiOpeningHours = _convertOpeningHoursToApiFormat(_openingHours);
        final currentQueue = queueNotifier.getQueueById(widget.queueId!);
        final request = UpdateQueueRequest(
          title: _titleController.text.trim(),
          serviceIds: _selectedServiceIds,
          openingHours: apiOpeningHours,
          isActive: currentQueue?.isActive,
        );

        await queueNotifier.updateQueue(widget.queueId!, request);

        // Refresh queues list
        await queueNotifier.loadQueues(forceRefresh: true);

        if (mounted) {
          context.pop(true); // Pass true to indicate refresh needed
        }
      } else {
        // Create new queue with opening hours
        final apiOpeningHours = _convertOpeningHoursToApiFormat(_openingHours);
        final request = CreateQueueRequest(
          title: _titleController.text.trim(),
          locationId: _selectedLocationId!,
          serviceIds: _selectedServiceIds,
          openingHours: apiOpeningHours,
        );

        await queueNotifier.createQueue(request);

        // Refresh queues list
        await queueNotifier.loadQueues(forceRefresh: true);

        if (mounted) {
          context.pop(true); // Pass true to indicate refresh needed
        }
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving queue: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteQueue() async {
    if (widget.queueId == null) return;

    final shouldDelete = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Queue'),
            content: Text(
              'Are you sure you want to delete the queue "${_titleController.text}"?\n\nThis action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (shouldDelete == true) {
      setState(() => _isLoading = true);

      try {
        await ref
            .read(queueNotifierProvider.notifier)
            .deleteQueue(widget.queueId!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Queue deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
          context.pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting queue: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Load services for the form (ensures services are available)
  Future<void> _loadServicesForForm() async {
    final serviceNotifier = ref.read(serviceNotifierProvider.notifier);
    final currentState = ref.read(serviceNotifierProvider);

    // If no services are loaded or there's an error, force a refresh
    if (currentState.services.isEmpty || currentState.hasError) {
      print(
        '[QueueFormScreen] No services loaded or error state, forcing refresh',
      );
      await serviceNotifier.refresh();
    } else {
      print(
        '[QueueFormScreen] Services already loaded (${currentState.services.length} services)',
      );
      // Just trigger a normal load to ensure fresh data if needed
      await serviceNotifier.loadServices();
    }
  }

  /// Refresh services (same pattern as Services screen)
  Future<void> _refreshServices() async {
    await ref.read(serviceNotifierProvider.notifier).refresh();
  }

  Map<String, List<TimeSlot>> _getDefaultOpeningHours() {
    final defaultSlot = TimeSlot(
      from: const TimeOfDay(hour: 9, minute: 0),
      to: const TimeOfDay(hour: 17, minute: 0),
    );

    return {
      'Monday': [defaultSlot],
      'Tuesday': [defaultSlot],
      'Wednesday': [defaultSlot],
      'Thursday': [defaultSlot],
      'Friday': [defaultSlot],
      'Saturday': [defaultSlot],
      'Sunday': [defaultSlot],
    };
  }

  bool _canSave() {
    return _titleController.text.trim().isNotEmpty &&
        _selectedLocationId != null &&
        _selectedServiceIds.isNotEmpty;
  }
}
