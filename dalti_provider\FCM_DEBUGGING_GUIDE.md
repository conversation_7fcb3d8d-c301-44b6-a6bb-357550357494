# FCM Debugging Guide - Step by Step

## 🔍 Systematic Debugging Approach

I've enhanced the Firebase messaging service with comprehensive debugging. Here's how to identify why notifications aren't working:

## 📋 Debugging Checklist

### Step 1: Check FCM Initialization
Run the app and look for these logs:
```
✅ [FCM_DEBUG] 🚀 Starting Firebase Messaging initialization...
✅ [FCM_DEBUG] 🔐 Permission status: AuthorizationStatus.authorized
✅ [FCM_DEBUG] 🔧 Configuring message handlers...
✅ [FCM_DEBUG] ✅ Message handlers configured successfully
✅ [FCM_DEBUG] 🎫 Starting FCM token registration...
```

**If you see errors here**: FCM setup is broken

### Step 2: Check Token Generation
Look for:
```
✅ [FCM_DEBUG] 🌐 Requesting web FCM token...
✅ [FCM_DEBUG] ✅ New FCM Token (web): eyJhbGciOiJFUzI1NiIs...
✅ [FCM_DEBUG] 💾 Storing token locally...
```

**If token generation fails**: VAPID key or Firebase config issue

### Step 3: Check Server Registration
Look for:
```
✅ [FCM_DEBUG] 🌐 Preparing to send token to server...
✅ [FCM_DEBUG] ✅ Session ID found: 5dx725thkc...
✅ [FCM_DEBUG] 📱 Device type: web
✅ [FCM_DEBUG] 📤 Sending request to: https://dapi-test.adscloud.org:8443/api/auth/notifications/mobile/save-fcm-token
✅ [FCM_DEBUG] 📥 Response status: 200
✅ [FCM_DEBUG] ✅ FCM token saved to server successfully!
```

**If server registration fails**: Backend API issue

### Step 4: Test Local Notifications (Mobile Only)
Look for:
```
✅ [FCM_DEBUG] 🧪 Testing local notification...
✅ [FCM_DEBUG] ✅ Test local notification sent
```

**If test notification fails**: Local notification setup issue

## 🚨 Common Issues & Solutions

### Issue 1: No FCM Token Generated
**Symptoms**: `❌ Web FCM token failed` or `❌ Failed to get new FCM token`

**Solutions**:
1. **VAPID Key Missing**: Set up proper VAPID key in Firebase Console
2. **Firebase Config**: Check `firebase_options.dart` configuration
3. **Permissions**: Ensure notification permissions are granted

### Issue 2: Server Registration Fails
**Symptoms**: `❌ Session ID not found` or `❌ Failed to save FCM token to server`

**Solutions**:
1. **Login Required**: User must be logged in before FCM initialization
2. **API Endpoint**: Check if backend API is accessible
3. **Authentication**: Verify session token is valid

### Issue 3: No Notifications Received
**Symptoms**: Token registered successfully but no notifications appear

**Solutions**:
1. **Test with Firebase Console**: Send test notification from Firebase Console
2. **Check Message Handlers**: Look for `📨 FOREGROUND message received!` logs
3. **Service Worker**: For web, check if service worker is registered
4. **Background Processing**: Check if app is properly handling background messages

## 🧪 Manual Testing Steps

### Test 1: Firebase Console Test
1. Go to Firebase Console → Cloud Messaging
2. Send test notification to your FCM token
3. Check if notification appears and logs show message received

### Test 2: Backend API Test
1. Use Postman/curl to test your notification API
2. Send notification to your FCM token via your backend
3. Verify backend is calling Firebase correctly

### Test 3: Local Notification Test
The enhanced service now automatically shows a test notification on mobile platforms.
If you see this notification, local notifications are working.

## 📱 Platform-Specific Debugging

### Web Platform
- Check browser console for service worker errors
- Verify notification permissions in browser settings
- Test in different browsers (Chrome, Firefox, Safari)

### Mobile Platform
- Check device notification settings
- Test on different devices/OS versions
- Verify app is not in battery optimization

## 🔧 Debug Commands

### Get Current FCM Token
```dart
final token = await FirebaseMessagingService.getToken();
print('Current FCM Token: $token');
```

### Force Token Refresh
```dart
// Clear stored token to force new registration
final prefs = await SharedPreferences.getInstance();
await prefs.remove('fcm_token');
await FirebaseMessagingService.initialize();
```

### Check Notification Permissions
```dart
final settings = await FirebaseMessaging.instance.getNotificationSettings();
print('Permission status: ${settings.authorizationStatus}');
```

## 📊 Expected Debug Output

When everything works correctly, you should see:
```
[FCM_DEBUG] 🚀 Starting Firebase Messaging initialization...
[FCM_DEBUG] 🔐 Permission status: AuthorizationStatus.authorized
[FCM_DEBUG] 🌐 Web platform - using service worker notifications
[FCM_DEBUG] 🔧 Configuring message handlers...
[FCM_DEBUG] ✅ Message handlers configured successfully
[FCM_DEBUG] 🎫 Starting FCM token registration...
[FCM_DEBUG] 🌐 Requesting web FCM token...
[FCM_DEBUG] ✅ New FCM Token (web): eyJhbGciOiJFUzI1NiIs...
[FCM_DEBUG] 💾 Storing token locally...
[FCM_DEBUG] 🌐 Preparing to send token to server...
[FCM_DEBUG] ✅ Session ID found: 5dx725thkc...
[FCM_DEBUG] 📱 Device type: web
[FCM_DEBUG] 📤 Sending request to: https://dapi-test.adscloud.org:8443/api/auth/notifications/mobile/save-fcm-token
[FCM_DEBUG] 📥 Response status: 200
[FCM_DEBUG] ✅ FCM token saved to server successfully!
[FCM_DEBUG] ✅ FCM token registration completed
[FCM_DEBUG] 🧪 Testing notification setup...
[FCM_DEBUG] ✅ FCM token available: eyJhbGciOiJFUzI1NiIs...
[FCM_DEBUG] 🌐 Web platform - checking service worker...
[FCM_DEBUG] ✅ Firebase Messaging service initialized successfully
```

## 🎯 Next Steps

1. **Run the app** and check the console for debug logs
2. **Identify which step fails** using the checklist above
3. **Apply the appropriate solution** based on the failure point
4. **Test notifications** using Firebase Console or your backend API

The enhanced debugging will pinpoint exactly where the notification pipeline is breaking!
