import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/providers/notification_provider.dart';
import '../../../core/debug/storage_debug.dart';
import '../../../core/services/firebase_messaging_service.dart';
import '../../../core/storage/web_storage_service.dart';
import '../models/auth_models.dart';
import '../repository/auth_repository.dart';
import '../repository/auth_repository_impl.dart';

part 'auth_provider.g.dart';

/// Authentication state
enum AuthState { initial, loading, authenticated, unauthenticated, error }

/// Authentication data model
class AuthData {
  final AuthState state;
  final String? error;
  final UserData? user;
  final ProviderData? provider;
  final bool isLoading;

  const AuthData({
    required this.state,
    this.error,
    this.user,
    this.provider,
    this.isLoading = false,
  });

  AuthData copyWith({
    AuthState? state,
    String? error,
    UserData? user,
    ProviderData? provider,
    bool? isLoading,
  }) {
    return AuthData(
      state: state ?? this.state,
      error: error ?? this.error,
      user: user ?? this.user,
      provider: provider ?? this.provider,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get isAuthenticated => state == AuthState.authenticated;
  bool get hasError => state == AuthState.error;
}

/// Auth repository provider
@riverpod
AuthRepository authRepository(Ref ref) {
  final apiService = ref.watch(authApiServiceProvider);
  final jwtService = ref.watch(jwtServiceProvider);

  return AuthRepositoryImpl(apiService: apiService, jwtService: jwtService);
}

/// Auth provider for managing authentication state
@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  AuthData build() {
    // Initialize with repository state
    final repository = ref.read(authRepositoryProvider);

    if (repository.isAuthenticated) {
      // Try to restore user and provider data from storage
      final userData = _restoreUserData();
      final providerData = _restoreProviderData();

      return AuthData(
        state: AuthState.authenticated,
        user: userData,
        provider: providerData,
      );
    } else {
      return const AuthData(state: AuthState.unauthenticated);
    }
  }

  /// Restore user data from storage
  UserData? _restoreUserData() {
    try {
      final userData = WebStorageService.getAuth<Map<String, dynamic>>(
        'user_data',
      );
      if (userData != null) {
        return UserData.fromJson(userData);
      }
    } catch (e) {
      print('[AuthNotifier] Error restoring user data: $e');
    }
    return null;
  }

  /// Restore provider data from storage
  ProviderData? _restoreProviderData() {
    try {
      final providerData = WebStorageService.getAuth<Map<String, dynamic>>(
        'provider_data',
      );
      if (providerData != null) {
        return ProviderData.fromJson(providerData);
      }
    } catch (e) {
      print('[AuthNotifier] Error restoring provider data: $e');
    }
    return null;
  }

  /// Store user and provider data for persistence
  Future<void> _storeUserAndProviderData(
    UserData? user,
    ProviderData? provider,
  ) async {
    try {
      if (user != null) {
        await WebStorageService.saveAuth('user_data', user.toJson());
        print('[AuthNotifier] Stored user data: ${user.email}');
      }

      if (provider != null) {
        await WebStorageService.saveAuth('provider_data', provider.toJson());
        print('[AuthNotifier] Stored provider data: ${provider.title}');
      }
    } catch (e) {
      print('[AuthNotifier] Error storing user/provider data: $e');
    }
  }

  /// Request email OTP for registration
  Future<bool> requestEmailOtp(
    EmailOtpRequest request, {
    required int providerCategoryId,
    required String businessName,
    required String phone,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      print(().toString());

      final repository = ref.read(authRepositoryProvider);
      final response = await repository.requestEmailOtp(
        request,
        providerCategoryId: providerCategoryId,
        businessName: businessName,
        phone: phone,
      );

      state = state.copyWith(isLoading: false);

      if (response.success) {
        print('[] ');
        return true;
      } else {
        print('[] ');
        state = state.copyWith(error: response.message);
        return false;
      }
    } catch (e) {
      print('[] ');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to request OTP. Please try again.',
      );
      return false;
    }
  }

  /// Verify OTP and complete registration
  Future<bool> verifyOtpAndRegister(ProviderRegistrationRequest request) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      print(().toString());

      final repository = ref.read(authRepositoryProvider);
      final response = await repository.verifyOtpAndRegister(request);

      if (response.success) {
        state = state.copyWith(
          state: AuthState.authenticated,
          isLoading: false,
          user: response.user,
          provider: response.provider,
        );

        // Update app state
        await ref.read(appStateNotifierProvider.notifier).setAuthenticated({
          'access_token': response.accessToken,
          'refresh_token': response.refreshToken,
          'expires_in': 3600,
          'user': response.user?.toJson(),
        });

        print('[] ');
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
        print('[] ');
        return false;
      }
    } catch (e) {
      print('[] ');
      state = state.copyWith(
        isLoading: false,
        error: 'Registration failed. Please try again.',
      );
      return false;
    }
  }

  /// Login with email/phone and password
  Future<bool> login(LoginRequest request) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      print(().toString());

      final repository = ref.read(authRepositoryProvider);
      final response = await repository.login(request);

      if (response.success) {
        // Store user and provider data in storage for persistence
        await _storeUserAndProviderData(response.user, response.provider);

        state = state.copyWith(
          state: AuthState.authenticated,
          isLoading: false,
          user: response.user,
          provider: response.provider,
        );

        // Debug storage before setting authenticated
        print('[] ');
        StorageDebug.printAllAuthData();

        // Update app state
        await ref.read(appStateNotifierProvider.notifier).setAuthenticated({
          'access_token': response.accessToken,
          'refresh_token': response.refreshToken,
          'expires_in': 3600,
          'user': response.user?.toJson(),
        });

        // Subscribe to notification topics for the provider
        if (response.provider?.id != null) {
          try {
            final notificationSubscriptions = ref.read(
              notificationSubscriptionsProvider.notifier,
            );
            await notificationSubscriptions.subscribeToProviderTopics(
              response.provider!.id.toString(),
            );

            // Register device token with backend if user ID is available
            if (response.user?.id != null) {
              await notificationSubscriptions.registerDeviceToken(
                response.user!.id.toString(),
              );
            }

            print(
              '[AuthNotifier] Subscribed to notification topics for provider: ${response.provider!.id}',
            );
          } catch (e) {
            print('[AuthNotifier] Error subscribing to notifications: $e');
            // Don't fail login if notification subscription fails
          }
        }

        // Debug storage after setting authenticated
        print('[] ');
        StorageDebug.printAllAuthData();

        // Compare JWT service and storage
        final jwtService = ref.read(jwtServiceProvider);
        StorageDebug.compareJwtServiceAndStorage(jwtService);

        // Send FCM token to server after successful login (when session is available)
        try {
          print('[AuthNotifier] Sending FCM token to server after login...');
          await FirebaseMessagingService.sendTokenToServerAfterLogin();
          print(
            '[AuthNotifier] FCM token sent to server successfully after login',
          );
        } catch (e) {
          print('[AuthNotifier] FCM token sending failed after login: $e');
          // Don't fail login if FCM token sending fails
        }

        print('[] ');
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
        print('[] ');
        return false;
      }
    } catch (e) {
      print('[] ');
      state = state.copyWith(
        isLoading: false,
        error: 'Login failed. Please try again.',
      );
      return false;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      print('[] ');

      final repository = ref.read(authRepositoryProvider);
      await repository.logout();

      // Unsubscribe from notification topics
      if (state.provider?.id != null) {
        try {
          final notificationSubscriptions = ref.read(
            notificationSubscriptionsProvider.notifier,
          );
          await notificationSubscriptions.unsubscribeFromProviderTopics(
            state.provider!.id.toString(),
          );

          // Unregister device token from backend if user ID is available
          if (state.user?.id != null) {
            await notificationSubscriptions.unregisterDeviceToken(
              state.user!.id.toString(),
            );
          }

          print(
            '[AuthNotifier] Unsubscribed from notification topics for provider: ${state.provider!.id}',
          );
        } catch (e) {
          print('[AuthNotifier] Error unsubscribing from notifications: $e');
          // Don't fail logout if notification unsubscription fails
        }
      }

      // Clear stored user and provider data
      await _clearStoredUserData();

      state = const AuthData(state: AuthState.unauthenticated);

      // Update app state
      await ref.read(appStateNotifierProvider.notifier).setUnauthenticated();

      print('[] ');
    } catch (e) {
      print('[] ');

      // Even if logout fails, clear local state and stored data
      await _clearStoredUserData();
      state = const AuthData(state: AuthState.unauthenticated);
      await ref.read(appStateNotifierProvider.notifier).setUnauthenticated();
    }
  }

  /// Clear stored user and provider data
  Future<void> _clearStoredUserData() async {
    try {
      await WebStorageService.removeAuth('user_data');
      await WebStorageService.removeAuth('provider_data');
      print('[AuthNotifier] Cleared stored user and provider data');
    } catch (e) {
      print('[AuthNotifier] Error clearing stored user data: $e');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}
