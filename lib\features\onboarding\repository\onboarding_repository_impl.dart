import 'dart:convert';
import '../../../core/storage/web_storage_service.dart';
import '../../../core/network/api_service.dart';
import '../models/onboarding_models.dart';
import '../services/onboarding_api_service.dart';
import 'onboarding_repository.dart';

/// Concrete implementation of OnboardingRepository using Hive for local storage
class OnboardingRepositoryImpl implements OnboardingRepository {
  final OnboardingApiService _apiService;

  // Storage keys
  static const String _onboardingDataKey = 'onboarding_data';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _businessProfileKey = 'business_profile';
  static const String _currentStepKey = 'current_step';
  static const String _onboardingStatsKey = 'onboarding_stats';

  OnboardingRepositoryImpl({required OnboardingApiService apiService})
    : _apiService = apiService;

  @override
  Future<void> saveProgress(OnboardingData data) async {
    try {
      print('[OnboardingRepository] Saving onboarding progress');

      final updatedData = data.copyWith(lastUpdatedAt: DateTime.now());

      final jsonData = updatedData.toJson();
      await WebStorageService.saveCache(
        _onboardingDataKey,
        jsonEncode(jsonData),
      );

      // Also save current step separately for quick access
      await saveCurrentStep(updatedData.currentStep);

      print('[OnboardingRepository] Onboarding progress saved successfully');
    } catch (e) {
      print('[OnboardingRepository] Error saving onboarding progress: $e');
      rethrow;
    }
  }

  @override
  Future<OnboardingData?> loadProgress() async {
    try {
      print('[OnboardingRepository] Loading onboarding progress');

      final jsonString = WebStorageService.getCache<String>(_onboardingDataKey);
      if (jsonString == null) {
        print('[OnboardingRepository] No onboarding progress found');
        return null;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      final data = OnboardingData.fromJson(jsonData);

      print(
        '[OnboardingRepository] Onboarding progress loaded: ${data.currentStep}',
      );
      return data;
    } catch (e) {
      print('[OnboardingRepository] Error loading onboarding progress: $e');
      return null;
    }
  }

  @override
  Future<void> clearProgress() async {
    try {
      print('[OnboardingRepository] Clearing onboarding progress');

      await Future.wait([
        WebStorageService.removeCache(_onboardingDataKey),
        WebStorageService.removeCache(_businessProfileKey),
        WebStorageService.removeCache(_currentStepKey),
        WebStorageService.removeCache(_onboardingStatsKey),
      ]);

      print('[OnboardingRepository] Onboarding progress cleared');
    } catch (e) {
      print('[OnboardingRepository] Error clearing onboarding progress: $e');
      rethrow;
    }
  }

  @override
  Future<bool> isOnboardingCompleted() async {
    try {
      final completed = WebStorageService.getSetting<bool>(
        _onboardingCompletedKey,
        defaultValue: false,
      );
      print('[OnboardingRepository] Onboarding completed: $completed');
      return completed ?? false;
    } catch (e) {
      print('[OnboardingRepository] Error checking onboarding completion: $e');
      return false;
    }
  }

  @override
  Future<void> markAsCompleted() async {
    try {
      print('[OnboardingRepository] Marking onboarding as completed');

      await WebStorageService.saveSetting(_onboardingCompletedKey, true);

      // Update the progress data to mark as completed
      final progress = await loadProgress();
      if (progress != null) {
        final completedData = progress.copyWith(
          isCompleted: true,
          currentStep: OnboardingStep.completed,
          completedAt: DateTime.now(),
        );
        await saveProgress(completedData);
      }

      print('[OnboardingRepository] Onboarding marked as completed');
    } catch (e) {
      print('[OnboardingRepository] Error marking onboarding as completed: $e');
      rethrow;
    }
  }

  @override
  Future<void> saveBusinessProfile(BusinessProfile profile) async {
    try {
      print('[OnboardingRepository] Saving business profile');

      final jsonData = profile.toJson();
      await WebStorageService.saveCache(
        _businessProfileKey,
        jsonEncode(jsonData),
      );

      print('[OnboardingRepository] Business profile saved');
    } catch (e) {
      print('[OnboardingRepository] Error saving business profile: $e');
      rethrow;
    }
  }

  @override
  Future<BusinessProfile?> loadBusinessProfile() async {
    try {
      print('[OnboardingRepository] Loading business profile');

      final jsonString = WebStorageService.getCache<String>(
        _businessProfileKey,
      );
      if (jsonString == null) {
        print('[OnboardingRepository] No business profile found');
        return null;
      }

      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      final profile = BusinessProfile.fromJson(jsonData);

      print(
        '[OnboardingRepository] Business profile loaded: ${profile.businessName}',
      );
      return profile;
    } catch (e) {
      print('[OnboardingRepository] Error loading business profile: $e');
      return null;
    }
  }

  @override
  Future<void> saveCurrentStep(OnboardingStep step) async {
    try {
      print('[OnboardingRepository] Saving current step: $step');

      await WebStorageService.saveCache(_currentStepKey, step.name);

      print('[OnboardingRepository] Current step saved');
    } catch (e) {
      print('[OnboardingRepository] Error saving current step: $e');
      rethrow;
    }
  }

  @override
  Future<OnboardingStep?> loadCurrentStep() async {
    try {
      print('[OnboardingRepository] Loading current step');

      final stepName = WebStorageService.getCache<String>(_currentStepKey);
      if (stepName == null) {
        print('[OnboardingRepository] No current step found');
        return null;
      }

      final step = OnboardingStep.values.firstWhere(
        (s) => s.name == stepName,
        orElse: () => OnboardingStep.businessProfile,
      );

      print('[OnboardingRepository] Current step loaded: $step');
      return step;
    } catch (e) {
      print('[OnboardingRepository] Error loading current step: $e');
      return OnboardingStep.businessProfile;
    }
  }

  @override
  Future<Map<String, dynamic>> getOnboardingStats() async {
    try {
      print('[OnboardingRepository] Getting onboarding stats');

      final progress = await loadProgress();
      final isCompleted = await isOnboardingCompleted();

      final stats = {
        'isCompleted': isCompleted,
        'currentStep': progress?.currentStep.name,
        'stepIndex': progress?.stepIndex ?? 0,
        'completionPercentage': progress?.completionPercentage ?? 0.0,
        'startedAt': progress?.startedAt?.toIso8601String(),
        'completedAt': progress?.completedAt?.toIso8601String(),
        'duration': progress?.duration?.inMinutes,
        'canComplete': progress?.canComplete ?? false,
      };

      // Cache stats for quick access
      await WebStorageService.saveCache(_onboardingStatsKey, jsonEncode(stats));

      print('[OnboardingRepository] Onboarding stats: $stats');
      return stats;
    } catch (e) {
      print('[OnboardingRepository] Error getting onboarding stats: $e');
      return {};
    }
  }

  @override
  Future<OnboardingCompletion> submitOnboarding(OnboardingData data) async {
    try {
      print('[OnboardingRepository] Submitting onboarding data to backend');

      // Submit to backend API
      final completion = await _apiService.submitOnboarding(data);

      // Mark as completed locally
      await markAsCompleted();

      print('[OnboardingRepository] Onboarding submitted successfully');
      return completion;
    } catch (e) {
      print('[OnboardingRepository] Error submitting onboarding: $e');
      rethrow;
    }
  }

  @override
  Future<StepValidationResult> validateStep(
    OnboardingStep step,
    Map<String, dynamic> data,
  ) async {
    try {
      print('[OnboardingRepository] Validating step: $step');

      // Perform local validation first
      final localValidation = _validateStepLocally(step, data);
      if (!localValidation.isValid) {
        return localValidation;
      }

      // Perform server-side validation if needed
      final serverValidation = await _apiService.validateStep(step, data);

      print(
        '[OnboardingRepository] Step validation result: ${serverValidation.isValid}',
      );
      return serverValidation;
    } catch (e) {
      print('[OnboardingRepository] Error validating step: $e');
      // Return local validation on error
      return _validateStepLocally(step, data);
    }
  }

  @override
  Future<bool> needsOnboarding() async {
    try {
      print('[OnboardingRepository] Checking if user needs onboarding');

      // Check if onboarding was skipped
      final isSkipped = WebStorageService.isOnboardingSkipped;
      if (isSkipped) {
        print('[OnboardingRepository] Onboarding was skipped');
        return false;
      }

      // Check if onboarding is completed
      final isCompleted = await isOnboardingCompleted();
      final needsOnboarding = !isCompleted;

      print('[OnboardingRepository] User needs onboarding: $needsOnboarding');
      return needsOnboarding;
    } catch (e) {
      print('[OnboardingRepository] Error checking onboarding need: $e');
      return true; // Assume needs onboarding on error
    }
  }

  @override
  Future<void> resetOnboarding() async {
    try {
      print('[OnboardingRepository] Resetting onboarding state');

      await Future.wait([
        clearProgress(),
        WebStorageService.saveSetting(_onboardingCompletedKey, false),
      ]);

      print('[OnboardingRepository] Onboarding state reset');
    } catch (e) {
      print('[OnboardingRepository] Error resetting onboarding: $e');
      rethrow;
    }
  }

  /// Perform local validation for a step
  StepValidationResult _validateStepLocally(
    OnboardingStep step,
    Map<String, dynamic> data,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    switch (step) {
      case OnboardingStep.businessProfile:
        if (data['businessName']?.toString().trim().isEmpty ?? true) {
          errors.add('Business name is required');
        }
        if (data['description']?.toString().trim().isEmpty ?? true) {
          errors.add('Business description is required');
        }
        if (data['categoryId'] == null) {
          errors.add('Business category is required');
        }
        break;

      case OnboardingStep.locationSetup:
        if (data['name']?.toString().trim().isEmpty ?? true) {
          errors.add('Location name is required');
        }
        if (data['city']?.toString().trim().isEmpty ?? true) {
          errors.add('City is required');
        }
        if (data['address']?.toString().trim().isEmpty ?? true) {
          errors.add('Address is required');
        }
        break;

      case OnboardingStep.serviceCreation:
        final services = data['services'] as List?;
        if (services == null || services.isEmpty) {
          errors.add('At least one service is required');
        }
        break;

      case OnboardingStep.scheduleSetup:
        final schedules = data['schedules'] as List?;
        if (schedules == null || schedules.isEmpty) {
          errors.add('At least one schedule is required');
        }
        break;

      case OnboardingStep.queueManagement:
        final queues = data['queues'] as List?;
        if (queues == null || queues.isEmpty) {
          errors.add('At least one queue is required');
        }
        break;

      default:
        break;
    }

    return StepValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}
