import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:dalti_provider/features/dashboard/widgets/notifications_panel.dart';
import 'package:dalti_provider/features/dashboard/models/dashboard_models.dart';
import 'package:dalti_provider/features/dashboard/providers/dashboard_provider.dart';

// Mock dashboard state for testing
class MockDashboardState extends DashboardState {
  const MockDashboardState({
    super.data,
    super.isLoading = false,
    super.isRefreshing = false,
    super.error,
    super.lastUpdated,
  });
}

void main() {
  group('NotificationsPanel Widget Tests', () {
    late List<NotificationItem> mockNotifications;

    setUp(() {
      mockNotifications = [
        NotificationItem(
          id: 'notif_1',
          title: 'New Appointment',
          message: '<PERSON> has booked an appointment for tomorrow',
          type: NotificationType.appointment,
          timestamp: DateTime(2024, 1, 15, 10, 30),
          isRead: false,
        ),
        NotificationItem(
          id: 'notif_2',
          title: 'Reschedule Request',
          message: '<PERSON> wants to reschedule her appointment',
          type: NotificationType.reschedule,
          timestamp: DateTime(2024, 1, 15, 11, 15),
          isRead: true,
        ),
        NotificationItem(
          id: 'notif_3',
          title: 'System Alert',
          message: 'Queue capacity has reached 90%',
          type: NotificationType.alert,
          timestamp: DateTime(2024, 1, 15, 12, 0),
          isRead: false,
        ),
      ];
    });

    Widget createTestWidget({
      bool isExpanded = false,
      VoidCallback? onToggle,
      List<NotificationItem>? notifications,
    }) {
      return ProviderScope(
        overrides: [
          dashboardNotifierProvider.overrideWith((ref) {
            return MockDashboardNotifier(
              notifications: notifications ?? mockNotifications,
            );
          }),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: NotificationsPanel(
              isExpanded: isExpanded,
              onToggle: onToggle,
            ),
          ),
        ),
      );
    }

    testWidgets('should display notifications header with correct count', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.byIcon(Icons.notifications_outlined), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // 2 unread notifications
    });

    testWidgets('should display expand/collapse icon based on state', (tester) async {
      // Test collapsed state
      await tester.pumpWidget(createTestWidget(isExpanded: false));
      expect(find.byIcon(Icons.expand_more), findsOneWidget);

      // Test expanded state
      await tester.pumpWidget(createTestWidget(isExpanded: true));
      await tester.pump();
      expect(find.byIcon(Icons.expand_less), findsOneWidget);
    });

    testWidgets('should call onToggle when header is tapped', (tester) async {
      // Arrange
      bool toggleCalled = false;
      void onToggle() => toggleCalled = true;

      // Act
      await tester.pumpWidget(createTestWidget(onToggle: onToggle));
      await tester.tap(find.byType(InkWell));

      // Assert
      expect(toggleCalled, true);
    });

    testWidgets('should display notifications list when expanded', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: true));

      // Assert
      expect(find.text('New Appointment'), findsOneWidget);
      expect(find.text('Reschedule Request'), findsOneWidget);
      expect(find.text('System Alert'), findsOneWidget);
      expect(find.text('John Doe has booked an appointment for tomorrow'), findsOneWidget);
    });

    testWidgets('should not display notifications list when collapsed', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: false));

      // Assert
      expect(find.text('New Appointment'), findsNothing);
      expect(find.text('Reschedule Request'), findsNothing);
      expect(find.text('System Alert'), findsNothing);
    });

    testWidgets('should display empty state when no notifications', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(
        isExpanded: true,
        notifications: [],
      ));

      // Assert
      expect(find.text('No notifications'), findsOneWidget);
      expect(find.text('You\'re all caught up!'), findsOneWidget);
      expect(find.byIcon(Icons.notifications_none), findsOneWidget);
    });

    testWidgets('should display correct notification icons based on type', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: true));

      // Assert
      expect(find.byIcon(Icons.event), findsOneWidget); // appointment
      expect(find.byIcon(Icons.schedule), findsOneWidget); // reschedule
      expect(find.byIcon(Icons.warning_amber), findsOneWidget); // alert
    });

    testWidgets('should show unread indicator for unread notifications', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: true));

      // Assert
      // Find containers that represent unread indicators (small circles)
      final unreadIndicators = find.byWidgetPredicate((widget) =>
          widget is Container &&
          widget.decoration is BoxDecoration &&
          (widget.decoration as BoxDecoration).shape == BoxShape.circle);
      
      expect(unreadIndicators, findsWidgets);
    });

    testWidgets('should display mark as read button for unread notifications', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: true));

      // Assert
      expect(find.byIcon(Icons.mark_email_read_outlined), findsWidgets);
    });

    testWidgets('should format timestamps correctly', (tester) async {
      // Arrange
      final recentNotification = NotificationItem(
        id: 'notif_recent',
        title: 'Recent Notification',
        message: 'This just happened',
        type: NotificationType.system,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        isRead: false,
      );

      // Act
      await tester.pumpWidget(createTestWidget(
        isExpanded: true,
        notifications: [recentNotification],
      ));

      // Assert
      expect(find.textContaining('ago'), findsOneWidget);
    });

    testWidgets('should handle notification tap', (tester) async {
      // Act
      await tester.pumpWidget(createTestWidget(isExpanded: true));
      
      // Find the first notification tile and tap it
      final notificationTile = find.byType(InkWell).at(1); // Skip header InkWell
      await tester.tap(notificationTile);
      await tester.pump();

      // Assert - should not throw any errors
      expect(tester.takeException(), isNull);
    });

    testWidgets('should display loading indicator when refreshing', (tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            dashboardNotifierProvider.overrideWith((ref) {
              return MockDashboardNotifier(
                notifications: mockNotifications,
                isRefreshing: true,
              );
            }),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: NotificationsPanel(isExpanded: false),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should limit notifications list height', (tester) async {
      // Arrange - Create many notifications
      final manyNotifications = List.generate(20, (index) =>
        NotificationItem(
          id: 'notif_$index',
          title: 'Notification $index',
          message: 'Message $index',
          type: NotificationType.system,
          timestamp: DateTime.now(),
          isRead: false,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget(
        isExpanded: true,
        notifications: manyNotifications,
      ));

      // Assert - Should find a Container with maxHeight constraint
      final constrainedContainer = find.byWidgetPredicate((widget) =>
          widget is Container &&
          widget.constraints?.maxHeight == 300);
      
      expect(constrainedContainer, findsOneWidget);
    });
  });
}

// Mock dashboard notifier for testing
class MockDashboardNotifier extends StateNotifier<DashboardState> {
  MockDashboardNotifier({
    List<NotificationItem>? notifications,
    bool isRefreshing = false,
  }) : super(MockDashboardState(
          data: DashboardData(
            businessMetrics: BusinessMetrics(
              todayRevenue: 1000.0,
              todayAppointments: 10,
              completedAppointments: 8,
              activeQueues: 2,
              totalCustomersToday: 30,
              customerSatisfaction: 4.2,
              revenueChange: 5.0,
            ),
            scheduleData: ScheduleData(
              nextAppointment: null,
              todayHours: null,
              queueStatuses: [],
              totalAppointments: 10,
              completedAppointments: 8,
              upcomingAppointments: 2,
            ),
            notifications: notifications ?? [],
            quickStats: QuickStats(
              currentWaitingCustomers: 5,
              unreadNotifications: 2,
              activeAppointments: 3,
              todayRevenue: 1000.0,
            ),
          ),
          isRefreshing: isRefreshing,
        ));

  void markNotificationAsRead(String notificationId) {
    // Mock implementation
  }
}
