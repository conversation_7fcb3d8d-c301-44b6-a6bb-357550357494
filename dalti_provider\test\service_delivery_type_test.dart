import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/onboarding/components/service/delivery_type_widget.dart';
import 'package:dalti_provider/features/locations/constants/algerian_cities.dart';

void main() {
  group('Service Delivery Type Tests', () {
    group('DeliveryTypeData', () {
      test('should require regions for at_customer delivery type', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'at_customer',
          servedRegions: [],
        );
        
        expect(deliveryData.requiresRegions, isTrue);
        expect(deliveryData.isValid, isFalse);
      });

      test('should require regions for both delivery type', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'both',
          servedRegions: [],
        );
        
        expect(deliveryData.requiresRegions, isTrue);
        expect(deliveryData.isValid, isFalse);
      });

      test('should not require regions for at_location delivery type', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'at_location',
          servedRegions: [],
        );
        
        expect(deliveryData.requiresRegions, isFalse);
        expect(deliveryData.isValid, isTrue);
      });

      test('should be valid when regions are provided for customer delivery', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'at_customer',
          servedRegions: ['Alger', 'Oran'],
        );
        
        expect(deliveryData.requiresRegions, isTrue);
        expect(deliveryData.isValid, isTrue);
      });

      test('should be valid when regions are provided for both delivery', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'both',
          servedRegions: ['Constantine', 'Annaba'],
        );
        
        expect(deliveryData.requiresRegions, isTrue);
        expect(deliveryData.isValid, isTrue);
      });
    });

    group('Algerian Cities Integration', () {
      test('should use all 58+ Algerian cities for region selection', () {
        // Verify we have a comprehensive list of Algerian cities
        expect(AlgerianCities.cities.length, greaterThanOrEqualTo(58));
        
        // Verify major cities are included
        expect(AlgerianCities.cities, contains('Alger'));
        expect(AlgerianCities.cities, contains('Oran'));
        expect(AlgerianCities.cities, contains('Constantine'));
        expect(AlgerianCities.cities, contains('Annaba'));
        expect(AlgerianCities.cities, contains('Blida'));
      });

      test('should provide sorted cities for better UX', () {
        final sortedCities = AlgerianCities.sortedCities;
        final manualSort = List<String>.from(AlgerianCities.cities)..sort();
        
        expect(sortedCities, equals(manualSort));
      });

      test('should include popular cities subset', () {
        final popularCities = AlgerianCities.popularCities;
        
        expect(popularCities.length, greaterThan(0));
        expect(popularCities.length, lessThanOrEqualTo(AlgerianCities.cities.length));
        
        // All popular cities should be in the main cities list
        for (final city in popularCities) {
          expect(AlgerianCities.cities, contains(city));
        }
      });
    });

    group('Backend API Compatibility', () {
      test('should format served regions as string array for API', () {
        const deliveryData = DeliveryTypeData(
          deliveryType: 'at_customer',
          servedRegions: ['Alger', 'Oran', 'Constantine'],
        );
        
        // Verify the regions are stored as strings (as expected by backend)
        expect(deliveryData.servedRegions, isA<List<String>>());
        expect(deliveryData.servedRegions.length, equals(3));
        expect(deliveryData.servedRegions, contains('Alger'));
        expect(deliveryData.servedRegions, contains('Oran'));
        expect(deliveryData.servedRegions, contains('Constantine'));
      });

      test('should handle delivery type enum values correctly', () {
        const atLocation = DeliveryTypeData(
          deliveryType: 'at_location',
          servedRegions: [],
        );
        
        const atCustomer = DeliveryTypeData(
          deliveryType: 'at_customer',
          servedRegions: ['Alger'],
        );
        
        const both = DeliveryTypeData(
          deliveryType: 'both',
          servedRegions: ['Oran'],
        );
        
        // Verify enum values match backend expectations
        expect(atLocation.deliveryType, equals('at_location'));
        expect(atCustomer.deliveryType, equals('at_customer'));
        expect(both.deliveryType, equals('both'));
      });
    });
  });
}
