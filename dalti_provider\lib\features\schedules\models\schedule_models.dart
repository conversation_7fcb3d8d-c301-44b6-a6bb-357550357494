import 'package:equatable/equatable.dart';

/// Schedule model based on the API response structure
class Schedule extends Equatable {
  final int id;
  final int dayOfWeek; // 0=Sunday, 6=Saturday
  final String startTime; // HH:MM format
  final String endTime; // HH:MM format
  final bool isActive;
  final int locationId;
  final ScheduleLocation? location;

  const Schedule({
    required this.id,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.isActive,
    required this.locationId,
    this.location,
  });

  factory Schedule.fromJson(Map<String, dynamic> json) {
    return Schedule(
      id: json['id'] as int,
      dayOfWeek: json['dayOfWeek'] as int,
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      isActive: json['isActive'] as bool,
      locationId: json['locationId'] as int,
      location: json['location'] != null
          ? ScheduleLocation.fromJson(json['location'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dayOfWeek': dayOfWeek,
      'startTime': startTime,
      'endTime': endTime,
      'isActive': isActive,
      'locationId': locationId,
      if (location != null) 'location': location!.toJson(),
    };
  }

  Schedule copyWith({
    int? id,
    int? dayOfWeek,
    String? startTime,
    String? endTime,
    bool? isActive,
    int? locationId,
    ScheduleLocation? location,
  }) {
    return Schedule(
      id: id ?? this.id,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isActive: isActive ?? this.isActive,
      locationId: locationId ?? this.locationId,
      location: location ?? this.location,
    );
  }

  @override
  List<Object?> get props => [
        id,
        dayOfWeek,
        startTime,
        endTime,
        isActive,
        locationId,
        location,
      ];

  /// Get day name from dayOfWeek index
  String get dayName {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayOfWeek];
  }

  /// Get short day name from dayOfWeek index
  String get shortDayName {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[dayOfWeek];
  }

  /// Check if this schedule conflicts with another schedule
  bool conflictsWith(Schedule other) {
    if (dayOfWeek != other.dayOfWeek || locationId != other.locationId) {
      return false;
    }

    final thisStart = _timeToMinutes(startTime);
    final thisEnd = _timeToMinutes(endTime);
    final otherStart = _timeToMinutes(other.startTime);
    final otherEnd = _timeToMinutes(other.endTime);

    return !(thisEnd <= otherStart || thisStart >= otherEnd);
  }

  /// Convert time string (HH:MM) to minutes since midnight
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  }

  /// Get formatted time range
  String get timeRange => '$startTime - $endTime';

  /// Check if schedule is for today
  bool get isToday {
    final now = DateTime.now();
    return dayOfWeek == now.weekday % 7; // Convert to 0-6 format
  }
}

/// Schedule location model (simplified from full Location model)
class ScheduleLocation extends Equatable {
  final int id;
  final String name;

  const ScheduleLocation({
    required this.id,
    required this.name,
  });

  factory ScheduleLocation.fromJson(Map<String, dynamic> json) {
    return ScheduleLocation(
      id: json['id'] as int,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }

  @override
  List<Object> get props => [id, name];
}

/// Request model for creating a new schedule
class CreateScheduleRequest extends Equatable {
  final int dayOfWeek;
  final String startTime;
  final String endTime;
  final int locationId;

  const CreateScheduleRequest({
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.locationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'dayOfWeek': dayOfWeek,
      'startTime': startTime,
      'endTime': endTime,
      'locationId': locationId,
    };
  }

  @override
  List<Object> get props => [dayOfWeek, startTime, endTime, locationId];
}

/// Request model for updating an existing schedule
class UpdateScheduleRequest extends Equatable {
  final int? dayOfWeek;
  final String? startTime;
  final String? endTime;
  final int? locationId;

  const UpdateScheduleRequest({
    this.dayOfWeek,
    this.startTime,
    this.endTime,
    this.locationId,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (dayOfWeek != null) json['dayOfWeek'] = dayOfWeek;
    if (startTime != null) json['startTime'] = startTime;
    if (endTime != null) json['endTime'] = endTime;
    if (locationId != null) json['locationId'] = locationId;
    return json;
  }

  @override
  List<Object?> get props => [dayOfWeek, startTime, endTime, locationId];
}

/// Schedule data state for provider
class ScheduleData extends Equatable {
  final ScheduleState state;
  final List<Schedule> schedules;
  final String? error;
  final bool isLoading;
  final Map<int, List<Schedule>> schedulesByLocation;
  final Map<int, List<Schedule>> schedulesByDay;

  const ScheduleData({
    required this.state,
    this.schedules = const [],
    this.error,
    this.isLoading = false,
    this.schedulesByLocation = const {},
    this.schedulesByDay = const {},
  });

  ScheduleData copyWith({
    ScheduleState? state,
    List<Schedule>? schedules,
    String? error,
    bool? isLoading,
    Map<int, List<Schedule>>? schedulesByLocation,
    Map<int, List<Schedule>>? schedulesByDay,
    bool clearError = false,
  }) {
    return ScheduleData(
      state: state ?? this.state,
      schedules: schedules ?? this.schedules,
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
      schedulesByLocation: schedulesByLocation ?? this.schedulesByLocation,
      schedulesByDay: schedulesByDay ?? this.schedulesByDay,
    );
  }

  @override
  List<Object?> get props => [
        state,
        schedules,
        error,
        isLoading,
        schedulesByLocation,
        schedulesByDay,
      ];
}

/// Schedule state enum
enum ScheduleState {
  initial,
  loading,
  loaded,
  error,
}

/// Time slot model for schedule creation
class TimeSlot extends Equatable {
  final String startTime;
  final String endTime;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  @override
  List<Object> get props => [startTime, endTime];

  /// Check if this time slot conflicts with another
  bool conflictsWith(TimeSlot other) {
    final thisStart = _timeToMinutes(startTime);
    final thisEnd = _timeToMinutes(endTime);
    final otherStart = _timeToMinutes(other.startTime);
    final otherEnd = _timeToMinutes(other.endTime);

    return !(thisEnd <= otherStart || thisStart >= otherEnd);
  }

  /// Convert time string (HH:MM) to minutes since midnight
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  }

  /// Get formatted time range
  String get timeRange => '$startTime - $endTime';
}
