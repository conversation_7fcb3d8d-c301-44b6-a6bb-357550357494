import 'package:flutter/material.dart';
import '../../../core/theme/theme_provider.dart';

class SearchableMultiSelect extends StatefulWidget {
  final List<String> items;
  final List<String> selectedItems;
  final String hintText;
  final String searchHintText;
  final Function(List<String>) onSelectionChanged;
  final String? labelText;
  final Widget? prefixIcon;
  final bool enabled;

  const SearchableMultiSelect({
    super.key,
    required this.items,
    required this.selectedItems,
    required this.onSelectionChanged,
    this.hintText = 'Select items',
    this.searchHintText = 'Search...',
    this.labelText,
    this.prefixIcon,
    this.enabled = true,
  });

  @override
  State<SearchableMultiSelect> createState() => _SearchableMultiSelectState();
}

class _SearchableMultiSelectState extends State<SearchableMultiSelect> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _filteredItems = [];
  bool _isDropdownOpen = false;

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems =
          widget.items
              .where((item) => item.toLowerCase().contains(query))
              .toList();
    });
  }

  void _toggleSelection(String item) {
    final newSelection = List<String>.from(widget.selectedItems);
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    widget.onSelectionChanged(newSelection);
  }

  void _clearSelection() {
    widget.onSelectionChanged([]);
  }

  void _selectAll() {
    widget.onSelectionChanged(List<String>.from(widget.items));
  }

  String get _displayText {
    if (widget.selectedItems.isEmpty) {
      return widget.hintText;
    } else if (widget.selectedItems.length == 1) {
      return widget.selectedItems.first;
    } else {
      return '${widget.selectedItems.length} wilayas selected';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main input field
        GestureDetector(
          onTap:
              widget.enabled
                  ? () {
                    setState(() {
                      _isDropdownOpen = !_isDropdownOpen;
                    });
                  }
                  : null,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    _isDropdownOpen
                        ? context.colors.primary
                        : context.colors.outline,
                width: _isDropdownOpen ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: widget.labelText,
                hintText: widget.hintText,
                prefixIcon: widget.prefixIcon,
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.selectedItems.isNotEmpty)
                      IconButton(
                        icon: Icon(
                          Icons.clear,
                          color: context.colors.onSurface.withOpacity(0.6),
                          size: 20,
                        ),
                        onPressed: widget.enabled ? _clearSelection : null,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(
                          minWidth: 24,
                          minHeight: 24,
                        ),
                      ),
                    Icon(
                      _isDropdownOpen
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: context.colors.onSurface.withOpacity(0.6),
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
                border: InputBorder.none,
                enabled: widget.enabled,
              ),
              child: Text(
                _displayText,
                style: context.textTheme.bodyLarge?.copyWith(
                  color:
                      widget.selectedItems.isEmpty
                          ? context.colors.onSurface.withOpacity(0.6)
                          : context.colors.onSurface,
                ),
              ),
            ),
          ),
        ),

        // Dropdown content
        if (_isDropdownOpen)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: context.colors.surface,
              border: Border.all(color: context.colors.outline),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Search bar
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: widget.searchHintText,
                      prefixIcon: Icon(
                        Icons.search,
                        color: context.colors.onSurface.withOpacity(0.6),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(
                          color: context.colors.outline.withOpacity(0.5),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(color: context.colors.primary),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      isDense: true,
                    ),
                  ),
                ),

                // Action buttons
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    children: [
                      TextButton.icon(
                        onPressed: _selectAll,
                        icon: const Icon(Icons.select_all, size: 16),
                        label: const Text('Select All'),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                      const SizedBox(width: 8),
                      TextButton.icon(
                        onPressed: _clearSelection,
                        icon: const Icon(Icons.clear_all, size: 16),
                        label: const Text('Clear All'),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${widget.selectedItems.length}/${widget.items.length}',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.colors.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),

                const Divider(height: 1),

                // Items list
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _filteredItems.length,
                    itemBuilder: (context, index) {
                      final item = _filteredItems[index];
                      final isSelected = widget.selectedItems.contains(item);

                      return InkWell(
                        onTap: () => _toggleSelection(item),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          child: Row(
                            children: [
                              Checkbox(
                                value: isSelected,
                                onChanged: (_) => _toggleSelection(item),
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                visualDensity: VisualDensity.compact,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  item,
                                  style: context.textTheme.bodyMedium?.copyWith(
                                    color:
                                        isSelected
                                            ? context.colors.primary
                                            : context.colors.onSurface,
                                    fontWeight:
                                        isSelected
                                            ? FontWeight.w500
                                            : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // No results message
                if (_filteredItems.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      'No wilayas found',
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.colors.onSurface.withOpacity(0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
              ],
            ),
          ),

        // Selected items preview (chips)
        if (widget.selectedItems.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children:
                  widget.selectedItems.map((item) {
                    return Chip(
                      label: Text(item, style: context.textTheme.bodySmall),
                      deleteIcon: Icon(
                        Icons.close,
                        size: 16,
                        color: context.colors.onSurface.withOpacity(0.6),
                      ),
                      onDeleted: () => _toggleSelection(item),
                      backgroundColor: context.colors.primary.withOpacity(0.1),
                      side: BorderSide(
                        color: context.colors.primary.withOpacity(0.3),
                      ),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    );
                  }).toList(),
            ),
          ),
      ],
    );
  }
}
