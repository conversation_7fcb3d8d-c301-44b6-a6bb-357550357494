import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/onboarding_models.dart';
import '../../providers/wizard_navigation_provider.dart';
import '../../../../core/theme/theme_provider.dart';

/// Progress indicator for the onboarding wizard
/// Shows all 6 steps with current progress
class WizardProgressIndicator extends ConsumerWidget {
  final bool showStepNumbers;
  final bool showStepTitles;
  final bool isCompact;

  const WizardProgressIndicator({
    super.key,
    this.showStepNumbers = true,
    this.showStepTitles = false,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    final navigationState = ref.watch(wizardNavigationProvider);
    final allSteps = navigationState.allSteps;
    
    if (isCompact) {
      return _buildCompactIndicator(theme, navigationState, allSteps);
    } else {
      return _buildFullIndicator(theme, navigationState, allSteps);
    }
  }

  Widget _buildCompactIndicator(
    ThemeData theme,
    WizardNavigationState navigationState,
    List<OnboardingStep> allSteps,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Progress bar
          Expanded(
            child: LinearProgressIndicator(
              value: navigationState.progress,
              backgroundColor: theme.colors.outline.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(theme.colors.primary),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // Step counter
          Text(
            '${navigationState.currentStepIndex + 1} / ${allSteps.length}',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullIndicator(
    ThemeData theme,
    WizardNavigationState navigationState,
    List<OnboardingStep> allSteps,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: theme.colors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Setup Progress',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${navigationState.currentStepIndex + 1} of ${allSteps.length}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colors.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Progress bar
          LinearProgressIndicator(
            value: navigationState.progress,
            backgroundColor: theme.colors.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(theme.colors.primary),
          ),
          
          const SizedBox(height: 16),
          
          // Step indicators
          Row(
            children: allSteps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isLast = index == allSteps.length - 1;
              
              return Expanded(
                child: _buildStepIndicator(
                  theme,
                  step,
                  index,
                  navigationState,
                  isLast,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(
    ThemeData theme,
    OnboardingStep step,
    int index,
    WizardNavigationState navigationState,
    bool isLast,
  ) {
    final isCurrent = navigationState.currentStep == step;
    final isCompleted = navigationState.isStepCompleted(step);
    final isPast = index < navigationState.currentStepIndex;
    final isFuture = index > navigationState.currentStepIndex;
    
    // Determine colors and states
    Color indicatorColor;
    Color textColor;
    IconData? indicatorIcon;
    
    if (isCompleted || isPast) {
      indicatorColor = theme.colors.primary;
      textColor = theme.colors.primary;
      indicatorIcon = Icons.check;
    } else if (isCurrent) {
      indicatorColor = theme.colors.primary;
      textColor = theme.colors.primary;
    } else {
      indicatorColor = theme.colors.outline.withOpacity(0.3);
      textColor = theme.colors.onSurface.withOpacity(0.5);
    }
    
    return Row(
      children: [
        // Step indicator
        Expanded(
          child: Column(
            children: [
              // Circle indicator
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: (isCompleted || isPast || isCurrent) 
                      ? indicatorColor 
                      : Colors.transparent,
                  border: Border.all(
                    color: indicatorColor,
                    width: 2,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: indicatorIcon != null
                      ? Icon(
                          indicatorIcon,
                          size: 16,
                          color: theme.colors.onPrimary,
                        )
                      : showStepNumbers
                          ? Text(
                              (index + 1).toString(), // Use array index, not enum index
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: (isCompleted || isPast || isCurrent)
                                    ? theme.colors.onPrimary
                                    : indicatorColor,
                              ),
                            )
                          : null,
                ),
              ),
              
              // Step title (if enabled)
              if (showStepTitles) ...[
                const SizedBox(height: 8),
                Text(
                  step.title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: textColor,
                    fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        
        // Connecting line (except for last step)
        if (!isLast) ...[
          Expanded(
            flex: 2,
            child: Container(
              height: 2,
              color: (isCompleted || isPast) 
                  ? theme.colors.primary 
                  : theme.colors.outline.withOpacity(0.3),
            ),
          ),
        ],
      ],
    );
  }
}

/// Compact wizard stepper for headers
class WizardStepper extends ConsumerWidget {
  const WizardStepper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    final navigationState = ref.watch(wizardNavigationProvider);
    final allSteps = navigationState.allSteps;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colors.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colors.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: allSteps.asMap().entries.map((entry) {
          final index = entry.key;
          final step = entry.value;
          final isCurrent = navigationState.currentStep == step;
          final isCompleted = navigationState.isStepCompleted(step);
          final isPast = index < navigationState.currentStepIndex;
          final isLast = index == allSteps.length - 1;
          
          return Expanded(
            child: Row(
              children: [
                // Step number
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: (isCurrent || isCompleted || isPast)
                          ? theme.colors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: isCurrent
                          ? Border.all(
                              color: theme.colors.primary.withOpacity(0.3),
                            )
                          : null,
                    ),
                    child: Text(
                      (index + 1).toString(), // Use array index, not enum index
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: (isCurrent || isCompleted || isPast)
                            ? theme.colors.primary
                            : theme.colors.onSurface.withOpacity(0.5),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                
                // Separator (except for last step)
                if (!isLast) ...[
                  Container(
                    width: 1,
                    height: 24,
                    color: theme.colors.outline.withOpacity(0.3),
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ],
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Mini progress indicator for tight spaces
class MiniWizardProgress extends ConsumerWidget {
  const MiniWizardProgress({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeProvider);
    final navigationState = ref.watch(wizardNavigationProvider);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.colors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.timeline,
            size: 14,
            color: theme.colors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            '${navigationState.currentStepIndex + 1}/${navigationState.allSteps.length}',
            style: theme.textTheme.bodySmall?.copyWith(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: theme.colors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
