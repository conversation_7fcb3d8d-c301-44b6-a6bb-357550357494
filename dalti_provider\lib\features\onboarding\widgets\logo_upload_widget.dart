import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../../core/theme/theme_provider.dart';
import '../services/logo_upload_service.dart';
import '../../../core/providers/app_providers.dart';

/// Simple logo upload widget that works on all platforms
class LogoUploadWidget extends ConsumerStatefulWidget {
  final String? initialLogoUrl;
  final Function(String? logoUrl, String? fileId)? onLogoChanged;
  final String title;
  final String? subtitle;

  const LogoUploadWidget({
    super.key,
    this.initialLogoUrl,
    this.onLogoChanged,
    this.title = 'Business Logo',
    this.subtitle,
  });

  @override
  ConsumerState<LogoUploadWidget> createState() => _LogoUploadWidgetState();
}

class _LogoUploadWidgetState extends ConsumerState<LogoUploadWidget> {
  String? _currentLogoUrl;
  bool _isUploading = false;
  String? _errorMessage;
  Uint8List? _webFileBytes;
  String? _fileName;

  // Add a ScaffoldMessengerState reference
  ScaffoldMessengerState? _scaffoldMessenger;

  @override
  void initState() {
    super.initState();
    _currentLogoUrl = widget.initialLogoUrl;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Save reference to ScaffoldMessengerState to avoid context lookup issues
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  void dispose() {
    // Reset the ScaffoldMessenger reference
    _scaffoldMessenger = null;
    super.dispose();
  }

  /// Safely show SnackBar with proper context checking
  void _showSnackBar(String message, {bool isError = false}) {
    // Triple check for safety
    if (!mounted) return;

    try {
      // Use saved reference if available, otherwise try context lookup
      final messenger =
          _scaffoldMessenger ?? ScaffoldMessenger.maybeOf(context);
      if (messenger != null) {
        messenger.showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: isError ? Colors.red : Colors.green,
          ),
        );
      }
    } catch (e) {
      // If SnackBar fails, just print to debug console
      print('[LogoUploadWidget] SnackBar error: $e, Message: $message');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (widget.subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                widget.subtitle!,
                style: context.textTheme.bodySmall?.copyWith(
                  color: context.colors.onSurfaceVariant,
                ),
              ),
            ],
            const SizedBox(height: 16),

            // Upload area
            Container(
              width: double.infinity,
              height: 235,
              decoration: BoxDecoration(
                border: Border.all(
                  color:
                      _errorMessage != null
                          ? context.colors.error
                          : context.colors.outline,
                  width: 2,
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(12),
                color: context.colors.surfaceVariant.withOpacity(0.3),
              ),
              child: _buildUploadContent(),
            ),

            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: context.colors.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: context.colors.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: context.colors.onErrorContainer,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 8),
            Text(
              'Supported formats: PNG, JPG, JPEG • Max size: 5MB',
              style: context.textTheme.bodySmall?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadContent() {
    if (_isUploading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Uploading logo...'),
          ],
        ),
      );
    }

    if (_currentLogoUrl != null) {
      return _buildUploadedState();
    }

    if (_webFileBytes != null) {
      return _buildPreviewState();
    }

    return _buildInitialState();
  }

  Widget _buildInitialState() {
    return InkWell(
      onTap: _selectFile,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_upload_outlined,
              size: 48,
              color: context.colors.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Click to upload logo',
              style: context.textTheme.titleMedium?.copyWith(
                color: context.colors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose an image file from your device',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colors.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Logo preview area - using fixed height instead of Expanded
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: MemoryImage(_webFileBytes!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Flexible(
                child: Text(
                  _fileName ?? 'Selected image',
                  style: context.textTheme.bodySmall,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // Action buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectFile,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('Change'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _uploadFile,
                  icon: const Icon(Icons.upload, size: 18),
                  label: const Text('Upload'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUploadedState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Logo preview area - using fixed height instead of Expanded
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: NetworkImage(_currentLogoUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: context.colors.primary,
                    size: 18,
                  ),
                  const SizedBox(width: 6),
                  Flexible(
                    child: Text(
                      'Logo uploaded successfully',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // Action buttons
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectFile,
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('Change Logo'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _removeFile,
                  icon: const Icon(Icons.delete_outline, size: 18),
                  label: const Text('Remove'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.colors.error,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _selectFile() async {
    try {
      setState(() {
        _errorMessage = null;
      });

      // Use cross-platform file picker
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        return;
      }

      final file = result.files.first;

      // Validate file
      if (file.bytes == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to read file data';
          });
        }
        return;
      }

      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        if (mounted) {
          setState(() {
            _errorMessage = 'File size must be less than 5MB';
          });
        }
        return;
      }

      // Validate file type
      if (!_isValidImageType(file.extension)) {
        if (mounted) {
          setState(() {
            _errorMessage = 'Please select a valid image file (PNG, JPG, JPEG)';
          });
        }
        return;
      }

      if (mounted) {
        setState(() {
          _webFileBytes = file.bytes;
          _fileName = file.name;
          _errorMessage = null;
        });
      }
    } catch (e) {
      print('[LogoUploadWidget] Error selecting file: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to select file: $e';
        });
      }
    }
  }

  bool _isValidImageType(String? extension) {
    if (extension == null) return false;
    final validExtensions = ['png', 'jpg', 'jpeg'];
    return validExtensions.contains(extension.toLowerCase());
  }

  Future<void> _uploadFile() async {
    if (_webFileBytes == null || _fileName == null) return;

    try {
      // Check if widget is still mounted before starting
      if (!mounted) return;

      setState(() {
        _isUploading = true;
        _errorMessage = null;
      });

      // Get JWT service from provider
      final jwtService = ref.read(jwtServiceProvider);

      // Check if user is authenticated
      if (!jwtService.isAuthenticated) {
        throw Exception('Authentication required. Please login again.');
      }

      // Create PlatformFile object for the service
      final platformFile = PlatformFile(
        name: _fileName!,
        size: _webFileBytes!.length,
        bytes: _webFileBytes,
        path: kIsWeb ? null : _fileName, // Path not available on web
      );

      // Use the real logo upload service
      final logoService = ProviderLogoService(jwtService);
      final result = await logoService.uploadLogo(platformFile);

      // Check again after async operation
      if (!mounted) return;

      if (result.success) {
        setState(() {
          _currentLogoUrl = result.downloadUrl;
          _isUploading = false;
          _webFileBytes = null;
          _fileName = null;
        });

        // Notify parent widget
        widget.onLogoChanged?.call(result.downloadUrl, result.fileId);

        // Show success message
        _showSnackBar('Logo uploaded successfully!');
      } else {
        throw Exception(result.error ?? 'Upload failed');
      }
    } catch (e) {
      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isUploading = false;
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });

      // Show error message
      _showSnackBar(_errorMessage!, isError: true);
    }
  }

  Future<void> _removeFile() async {
    try {
      // Check if widget is still mounted before starting
      if (!mounted) return;

      setState(() {
        _isUploading = true;
        _errorMessage = null;
      });

      // Reset the widget state
      setState(() {
        _webFileBytes = null;
        _fileName = null;
        _currentLogoUrl = null;
        _isUploading = false;
      });

      // Notify parent widget that logo was removed
      widget.onLogoChanged?.call(null, null);

      // Show success message
      _showSnackBar('Logo removed successfully!');
    } catch (e) {
      // Check if widget is still mounted before updating state
      if (!mounted) return;

      setState(() {
        _isUploading = false;
        _errorMessage = e.toString().replaceFirst('Exception: ', '');
      });

      // Show error message
      _showSnackBar(_errorMessage!, isError: true);
    }
  }
}
