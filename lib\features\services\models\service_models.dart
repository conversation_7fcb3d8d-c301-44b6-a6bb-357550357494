/// Service model representing a business service
class Service {
  final int id;
  final String title;
  final int duration; // in minutes
  final String? color; // hex color code
  final bool acceptOnline;
  final bool acceptNew;
  final bool notificationOn;
  final int? pointsRequirements;
  
  // Fields available in create/update but not in GET responses
  final String? description;
  final double? price;
  final bool? isActive;
  final int? categoryId;

  const Service({
    required this.id,
    required this.title,
    required this.duration,
    this.color,
    this.acceptOnline = true,
    this.acceptNew = true,
    this.notificationOn = true,
    this.pointsRequirements,
    this.description,
    this.price,
    this.isActive,
    this.categoryId,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      duration: json['duration'] ?? 0,
      color: json['color'],
      acceptOnline: json['acceptOnline'] ?? true,
      acceptNew: json['acceptNew'] ?? true,
      notificationOn: json['notificationOn'] ?? true,
      pointsRequirements: json['pointsRequirements'],
      description: json['description'],
      price: json['price']?.toDouble(),
      isActive: json['isActive'],
      categoryId: json['categoryId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'duration': duration,
      'color': color,
      'acceptOnline': acceptOnline,
      'acceptNew': acceptNew,
      'notificationOn': notificationOn,
      'pointsRequirements': pointsRequirements,
      'description': description,
      'price': price,
      'isActive': isActive,
      'categoryId': categoryId,
    };
  }

  Service copyWith({
    int? id,
    String? title,
    int? duration,
    String? color,
    bool? acceptOnline,
    bool? acceptNew,
    bool? notificationOn,
    int? pointsRequirements,
    String? description,
    double? price,
    bool? isActive,
    int? categoryId,
  }) {
    return Service(
      id: id ?? this.id,
      title: title ?? this.title,
      duration: duration ?? this.duration,
      color: color ?? this.color,
      acceptOnline: acceptOnline ?? this.acceptOnline,
      acceptNew: acceptNew ?? this.acceptNew,
      notificationOn: notificationOn ?? this.notificationOn,
      pointsRequirements: pointsRequirements ?? this.pointsRequirements,
      description: description ?? this.description,
      price: price ?? this.price,
      isActive: isActive ?? this.isActive,
      categoryId: categoryId ?? this.categoryId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Service && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Service(id: $id, title: $title, duration: $duration, price: $price)';
  }

  /// Get formatted duration string
  String get formattedDuration {
    if (duration < 60) {
      return '${duration}min';
    } else {
      final hours = duration ~/ 60;
      final minutes = duration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}min';
      }
    }
  }

  /// Get formatted price string
  String get formattedPrice {
    if (price == null) return 'Price not set';
    return '\$${price!.toStringAsFixed(2)}';
  }

  /// Check if service has all required fields for display
  bool get isComplete {
    return title.isNotEmpty && duration > 0;
  }
}

/// Request model for creating a new service
class CreateServiceRequest {
  final String title;
  final int duration;
  final double price;
  final int categoryId;
  final String? description;
  final String? color;
  final bool acceptOnline;
  final bool acceptNew;
  final bool notificationOn;
  final int? pointsRequirements;

  const CreateServiceRequest({
    required this.title,
    required this.duration,
    required this.price,
    required this.categoryId,
    this.description,
    this.color,
    this.acceptOnline = true,
    this.acceptNew = true,
    this.notificationOn = true,
    this.pointsRequirements,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'duration': duration,
      'price': price,
      'categoryId': categoryId,
      'description': description,
      'color': color,
      'acceptOnline': acceptOnline,
      'acceptNew': acceptNew,
      'notificationOn': notificationOn,
      'pointsRequirements': pointsRequirements,
    };
  }

  factory CreateServiceRequest.fromJson(Map<String, dynamic> json) {
    return CreateServiceRequest(
      title: json['title'] ?? '',
      duration: json['duration'] ?? 0,
      price: json['price']?.toDouble() ?? 0.0,
      categoryId: json['categoryId'] ?? 0,
      description: json['description'],
      color: json['color'],
      acceptOnline: json['acceptOnline'] ?? true,
      acceptNew: json['acceptNew'] ?? true,
      notificationOn: json['notificationOn'] ?? true,
      pointsRequirements: json['pointsRequirements'],
    );
  }
}

/// Request model for updating an existing service
class UpdateServiceRequest {
  final String? title;
  final int? duration;
  final double? price;
  final int? categoryId;
  final String? description;
  final String? color;
  final bool? acceptOnline;
  final bool? acceptNew;
  final bool? notificationOn;
  final int? pointsRequirements;
  final bool? isActive;

  const UpdateServiceRequest({
    this.title,
    this.duration,
    this.price,
    this.categoryId,
    this.description,
    this.color,
    this.acceptOnline,
    this.acceptNew,
    this.notificationOn,
    this.pointsRequirements,
    this.isActive,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};
    
    if (title != null) json['title'] = title;
    if (duration != null) json['duration'] = duration;
    if (price != null) json['price'] = price;
    if (categoryId != null) json['categoryId'] = categoryId;
    if (description != null) json['description'] = description;
    if (color != null) json['color'] = color;
    if (acceptOnline != null) json['acceptOnline'] = acceptOnline;
    if (acceptNew != null) json['acceptNew'] = acceptNew;
    if (notificationOn != null) json['notificationOn'] = notificationOn;
    if (pointsRequirements != null) json['pointsRequirements'] = pointsRequirements;
    if (isActive != null) json['isActive'] = isActive;
    
    return json;
  }

  factory UpdateServiceRequest.fromJson(Map<String, dynamic> json) {
    return UpdateServiceRequest(
      title: json['title'],
      duration: json['duration'],
      price: json['price']?.toDouble(),
      categoryId: json['categoryId'],
      description: json['description'],
      color: json['color'],
      acceptOnline: json['acceptOnline'],
      acceptNew: json['acceptNew'],
      notificationOn: json['notificationOn'],
      pointsRequirements: json['pointsRequirements'],
      isActive: json['isActive'],
    );
  }

  /// Check if request has any fields to update
  bool get hasUpdates {
    return title != null ||
        duration != null ||
        price != null ||
        categoryId != null ||
        description != null ||
        color != null ||
        acceptOnline != null ||
        acceptNew != null ||
        notificationOn != null ||
        pointsRequirements != null ||
        isActive != null;
  }
}
