import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/dashboard_models.dart';
import '../../appointments/providers/appointment_provider.dart';
import '../../appointments/models/appointment_models.dart';
import '../../../core/routing/app_routes.dart';

/// Today's schedule card showing appointments and queue status
class TodayScheduleCard extends ConsumerWidget {
  const TodayScheduleCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final todaysAppointmentsAsync = ref.watch(
      todaysAppointmentsNotifierProvider,
    );
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.today, color: theme.colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Today\'s Schedule',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (todaysAppointmentsAsync.isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.primary,
                    ),
                  )
                else
                  TextButton(
                    onPressed: () => context.push(AppRoutes.appointments),
                    child: Text(
                      'View All',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Content
            todaysAppointmentsAsync.when(
              loading: () => _buildLoadingState(context),
              error: (error, stack) => _buildErrorState(context, error),
              data:
                  (appointments) =>
                      _buildAppointmentsContent(context, appointments),
            ),
          ],
        ),
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(BuildContext context, Object error) {
    final theme = Theme.of(context);

    // Log the error for debugging
    print('[TodayScheduleCard] Error loading appointments: $error');

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 48, color: theme.colorScheme.error),
          const SizedBox(height: 12),
          Text(
            'Failed to load appointments',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Please try again later',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          // Show error details in debug mode
          if (error.toString().isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Error: ${error.toString()}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.error,
                fontSize: 10,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// Build appointments content
  Widget _buildAppointmentsContent(
    BuildContext context,
    List<Appointment> appointments,
  ) {
    if (appointments.isEmpty) {
      return _buildNoAppointments(context);
    }

    // Get next appointment (first upcoming one)
    final now = DateTime.now();
    final upcomingAppointments =
        appointments.where((apt) => apt.scheduledTime.isAfter(now)).toList()
          ..sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));

    final nextAppointment =
        upcomingAppointments.isNotEmpty ? upcomingAppointments.first : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Next appointment section
        if (nextAppointment != null)
          _buildNextAppointmentFromReal(context, nextAppointment)
        else
          _buildNoUpcomingAppointments(context),

        const SizedBox(height: 16),

        // Appointments summary
        _buildAppointmentsSummaryFromReal(context, appointments),
      ],
    );
  }

  /// Build schedule content (legacy - keeping for compatibility)
  Widget _buildScheduleContent(BuildContext context, ScheduleData data) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Next appointment section
        if (data.nextAppointment != null)
          _buildNextAppointment(context, data.nextAppointment!)
        else
          _buildNoAppointments(context),

        const SizedBox(height: 16),

        // Working hours
        if (data.todayHours != null)
          _buildWorkingHours(context, data.todayHours!),

        const SizedBox(height: 16),

        // Queue status
        if (data.queueStatuses.isNotEmpty)
          _buildQueueStatus(context, data.queueStatuses)
        else
          _buildNoQueues(context),

        const SizedBox(height: 12),

        // Appointments summary
        _buildAppointmentsSummary(context, data),
      ],
    );
  }

  /// Build next appointment section
  Widget _buildNextAppointment(
    BuildContext context,
    NextAppointment appointment,
  ) {
    final theme = Theme.of(context);
    final timeString = _formatTime(appointment.scheduledTime);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.person,
              color: theme.colorScheme.onPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next: ${appointment.customerName}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${appointment.serviceName} at $timeString',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(appointment.status).withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              appointment.status.toUpperCase(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStatusColor(appointment.status),
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build no appointments message
  Widget _buildNoAppointments(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_available,
            color: theme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            'No upcoming appointments',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build working hours section
  Widget _buildWorkingHours(BuildContext context, WorkingHours hours) {
    final theme = Theme.of(context);
    final statusColor = hours.isOpen ? Colors.green : Colors.red;

    return Row(
      children: [
        Icon(
          hours.isOpen ? Icons.access_time : Icons.access_time_filled,
          color: statusColor,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          hours.isOpen ? 'Open' : 'Closed',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: statusColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '${hours.openTime} - ${hours.closeTime}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Build queue status section
  Widget _buildQueueStatus(BuildContext context, List<QueueStatus> queues) {
    final theme = Theme.of(context);
    final activeQueues = queues.where((q) => q.isActive).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Queue Status',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        ...activeQueues
            .take(2)
            .map(
              (queue) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: queue.isActive ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        queue.queueName,
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                    Text(
                      '${queue.waitingCount} waiting',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        if (queues.length > 2)
          Text(
            '+${queues.length - 2} more queues',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
      ],
    );
  }

  /// Build no queues message
  Widget _buildNoQueues(BuildContext context) {
    final theme = Theme.of(context);

    return Text(
      'No active queues',
      style: theme.textTheme.bodyMedium?.copyWith(
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Build appointments summary
  Widget _buildAppointmentsSummary(BuildContext context, ScheduleData data) {
    final theme = Theme.of(context);

    return Row(
      children: [
        _SummaryItem(
          label: 'Total',
          value: '${data.totalAppointments}',
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 16),
        _SummaryItem(
          label: 'Completed',
          value: '${data.completedAppointments}',
          color: Colors.green,
        ),
        const SizedBox(width: 16),
        _SummaryItem(
          label: 'Upcoming',
          value: '${data.upcomingAppointments}',
          color: Colors.orange,
        ),
      ],
    );
  }

  /// Build loading state
  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          height: 60,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
  }

  /// Build next appointment from real appointment data
  Widget _buildNextAppointmentFromReal(
    BuildContext context,
    Appointment appointment,
  ) {
    final theme = Theme.of(context);
    final timeString = _formatTime(appointment.scheduledTime);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.primary.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              Icons.person,
              color: theme.colorScheme.onPrimary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Next: ${appointment.customerName}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${appointment.serviceName} at $timeString',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getAppointmentStatusColor(
                appointment.status.name,
              ).withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              appointment.status.name.toUpperCase(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getAppointmentStatusColor(appointment.status.name),
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build no upcoming appointments state
  Widget _buildNoUpcomingAppointments(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule,
            color: theme.colorScheme.onSurfaceVariant,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'No upcoming appointments',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'All appointments for today are completed',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build appointments summary from real data
  Widget _buildAppointmentsSummaryFromReal(
    BuildContext context,
    List<Appointment> appointments,
  ) {
    final theme = Theme.of(context);

    // Calculate stats
    final total = appointments.length;
    final completed =
        appointments
            .where((apt) => apt.status == AppointmentStatus.completed)
            .length;
    final pending =
        appointments
            .where((apt) => apt.status == AppointmentStatus.pending)
            .length;
    final scheduled =
        appointments
            .where((apt) => apt.status == AppointmentStatus.scheduled)
            .length;
    final confirmed =
        appointments
            .where((apt) => apt.status == AppointmentStatus.confirmed)
            .length;
    final inProgress =
        appointments
            .where((apt) => apt.status == AppointmentStatus.inProgress)
            .length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Summary',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _SummaryItem(
                label: 'Total',
                value: total.toString(),
                color: theme.colorScheme.primary,
              ),
            ),
            Expanded(
              child: _SummaryItem(
                label: 'Completed',
                value: completed.toString(),
                color: Colors.green,
              ),
            ),
            Expanded(
              child: _SummaryItem(
                label: 'Pending',
                value:
                    (pending + scheduled + confirmed + inProgress).toString(),
                color: Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Get appointment status color
  Color _getAppointmentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'scheduled':
        return Colors.blue;
      case 'confirmed':
        return Colors.green;
      case 'inprogress':
      case 'in-progress':
        return Colors.purple;
      case 'completed':
        return Colors.teal;
      case 'canceled':
      case 'cancelled':
        return Colors.red;
      case 'no_show':
      case 'noshow':
        return Colors.grey;
      case 'rescheduled':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  /// Format time from DateTime
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// Get status color
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'scheduled':
        return Colors.blue;
      case 'confirmed':
        return Colors.green;
      case 'inprogress':
      case 'in-progress':
        return Colors.purple;
      case 'completed':
        return Colors.teal;
      case 'canceled':
      case 'cancelled':
        return Colors.red;
      case 'no_show':
      case 'noshow':
        return Colors.grey;
      case 'rescheduled':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }
}

/// Summary item widget
class _SummaryItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _SummaryItem({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
