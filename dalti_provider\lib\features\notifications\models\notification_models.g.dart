// GENERATED CODE - DO NOT MODIFY BY HAND

// ignore_for_file: type=lint, avoid_redundant_argument_values, avoid_unused_constructor_parameters, invalid_annotation_target

part of 'notification_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$NotificationItemImpl _$$NotificationItemImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationItemImpl(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      userId: json['userId'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      isRead: json['isRead'] as bool,
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      link: json['link'] as String?,
      actorId: json['actorId'] as String?,
      actor: json['actor'] == null
          ? null
          : NotificationActor.fromJson(json['actor'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$NotificationItemImplToJson(
        _$NotificationItemImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'userId': instance.userId,
      'type': instance.type,
      'title': instance.title,
      'message': instance.message,
      'isRead': instance.isRead,
      if (instance.readAt?.toIso8601String() case final value?) 'readAt': value,
      if (instance.link case final value?) 'link': value,
      if (instance.actorId case final value?) 'actorId': value,
      if (instance.actor?.toJson() case final value?) 'actor': value,
    };

_$NotificationActorImpl _$$NotificationActorImplFromJson(
        Map<String, dynamic> json) =>
    _$NotificationActorImpl(
      id: json['id'] as String,
      username: json['username'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );

Map<String, dynamic> _$$NotificationActorImplToJson(
        _$NotificationActorImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      if (instance.username case final value?) 'username': value,
      if (instance.firstName case final value?) 'firstName': value,
      if (instance.lastName case final value?) 'lastName': value,
    };
