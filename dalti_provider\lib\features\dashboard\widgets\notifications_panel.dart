import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/routing/app_routes.dart';
import '../../notifications/providers/notifications_provider.dart';
import '../../notifications/models/notification_models.dart';

/// Notifications panel widget with real-time updates and interactive features
class NotificationsPanel extends ConsumerWidget {
  final bool isExpanded;
  final VoidCallback? onToggle;

  const NotificationsPanel({super.key, this.isExpanded = false, this.onToggle});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsState = ref.watch(notificationsProvider);
    final theme = Theme.of(context);
    // Filter for unread notifications only and take first 5
    final notifications =
        notificationsState.notifications
            .where((notification) => !notification.isRead)
            .take(5)
            .toList();
    final unreadCount = ref.watch(unreadNotificationsCountProvider);

    return Card(
      child: Column(
        children: [
          // Header with notification count
          InkWell(
            onTap: onToggle,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Stack(
                    children: [
                      Icon(
                        Icons.notifications_outlined,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                      if (unreadCount > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.error,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              unreadCount > 99 ? '99+' : '$unreadCount',
                              style: TextStyle(
                                color: theme.colorScheme.onError,
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Notifications',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  // View All button
                  TextButton(
                    onPressed: () => context.push(AppRoutes.notifications),
                    child: Text(
                      'View All',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (notificationsState.isLoading)
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: theme.colorScheme.primary,
                      ),
                    )
                  else
                    Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                ],
              ),
            ),
          ),

          // Expandable content
          if (isExpanded) ...[
            const Divider(height: 1),
            if (notifications.isEmpty)
              _buildEmptyState(context)
            else
              _buildNotificationsList(context, ref, notifications),
          ],
        ],
      ),
    );
  }

  /// Build empty state when no notifications
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.notifications_none,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          const SizedBox(height: 12),
          Text(
            'No unread notifications',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'You\'re all caught up!',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Build notifications list
  Widget _buildNotificationsList(
    BuildContext context,
    WidgetRef ref,
    List<NotificationItem> notifications,
  ) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: notifications.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _NotificationTile(
            notification: notification,
            onTap: () => _handleNotificationTap(ref, notification),
            onMarkAsRead: () => _markAsRead(ref, notification.id),
          );
        },
      ),
    );
  }

  /// Handle notification tap
  void _handleNotificationTap(WidgetRef ref, NotificationItem notification) {
    // Mark as read if unread
    if (!notification.isRead) {
      _markAsRead(ref, notification.id);
    }

    // TODO: Navigate to relevant screen based on notification type
    print('[NotificationsPanel] Notification tapped: ${notification.title}');
  }

  /// Mark notification as read
  void _markAsRead(WidgetRef ref, String notificationId) {
    ref.read(notificationsProvider.notifier).markAsRead(notificationId);
  }
}

/// Individual notification tile widget
class _NotificationTile extends StatelessWidget {
  final NotificationItem notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;

  const _NotificationTile({
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUnread = !notification.isRead;

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isUnread
                  ? theme.colorScheme.primaryContainer.withOpacity(0.3)
                  : null,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getNotificationColor(
                  notification.type,
                ).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getNotificationIcon(notification.type),
                color: _getNotificationColor(notification.type),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and unread indicator
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          notification.title,
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight:
                                isUnread ? FontWeight.w600 : FontWeight.w500,
                          ),
                        ),
                      ),
                      if (isUnread)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Message
                  Text(
                    notification.message,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Timestamp
                  Text(
                    _formatTimestamp(notification.createdAt),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant.withOpacity(
                        0.7,
                      ),
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),

            // Actions
            if (isUnread && onMarkAsRead != null)
              IconButton(
                onPressed: onMarkAsRead,
                icon: Icon(
                  Icons.mark_email_read_outlined,
                  size: 18,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                tooltip: 'Mark as read',
              ),
          ],
        ),
      ),
    );
  }

  /// Get notification icon based on type
  IconData _getNotificationIcon(String type) {
    switch (type.toUpperCase()) {
      case 'APPOINTMENT_CONFIRMED_CUSTOMER':
      case 'APPOINTMENT_BOOKED_SUCCESS_CUSTOMER':
        return Icons.event_available;
      case 'APPOINTMENT_COMPLETED_CUSTOMER':
        return Icons.check_circle;
      case 'APPOINTMENT_CANCELLED_CUSTOMER':
        return Icons.event_busy;
      case 'APPOINTMENT_RESCHEDULED_CUSTOMER':
        return Icons.schedule;
      case 'MESSAGE_RECEIVED':
        return Icons.message;
      case 'SYSTEM_NOTIFICATION':
        return Icons.info_outline;
      default:
        return Icons.notifications_outlined;
    }
  }

  /// Get notification color based on type
  Color _getNotificationColor(String type) {
    switch (type.toUpperCase()) {
      case 'APPOINTMENT_CONFIRMED_CUSTOMER':
      case 'APPOINTMENT_BOOKED_SUCCESS_CUSTOMER':
        return Colors.green;
      case 'APPOINTMENT_COMPLETED_CUSTOMER':
        return Colors.blue;
      case 'APPOINTMENT_CANCELLED_CUSTOMER':
        return Colors.red;
      case 'APPOINTMENT_RESCHEDULED_CUSTOMER':
        return Colors.orange;
      case 'MESSAGE_RECEIVED':
        return Colors.purple;
      case 'SYSTEM_NOTIFICATION':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  /// Format timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
