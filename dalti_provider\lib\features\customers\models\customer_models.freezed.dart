// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'customer_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Customer _$CustomerFromJson(Map<String, dynamic> json) {
  return _Customer.fromJson(json);
}

/// @nodoc
mixin _$Customer {
  String get id => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String? get email =>
      throw _privateConstructorUsedError; // Made optional to match backend
  @JsonKey(name: 'mobileNumber')
  String? get phoneNumber =>
      throw _privateConstructorUsedError; // Made optional to match backend (can be null)
  String? get nationalId =>
      throw _privateConstructorUsedError; // Added to match backend
  DateTime? get dateOfBirth => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get wilaya => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'appointmentCount')
  int get totalAppointments =>
      throw _privateConstructorUsedError; // Map appointmentCount to totalAppointments
  double get totalSpent => throw _privateConstructorUsedError;
  DateTime? get lastAppointmentDate => throw _privateConstructorUsedError;
  CustomerStatus get status => throw _privateConstructorUsedError;
  CustomerPreferences? get preferences => throw _privateConstructorUsedError;

  /// Serializes this Customer to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerCopyWith<Customer> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerCopyWith<$Res> {
  factory $CustomerCopyWith(Customer value, $Res Function(Customer) then) =
      _$CustomerCopyWithImpl<$Res, Customer>;
  @useResult
  $Res call(
      {String id,
      String firstName,
      String lastName,
      String? email,
      @JsonKey(name: 'mobileNumber') String? phoneNumber,
      String? nationalId,
      DateTime? dateOfBirth,
      String? address,
      String? city,
      String? wilaya,
      String? notes,
      List<String> tags,
      DateTime createdAt,
      DateTime? updatedAt,
      @JsonKey(name: 'appointmentCount') int totalAppointments,
      double totalSpent,
      DateTime? lastAppointmentDate,
      CustomerStatus status,
      CustomerPreferences? preferences});

  $CustomerPreferencesCopyWith<$Res>? get preferences;
}

/// @nodoc
class _$CustomerCopyWithImpl<$Res, $Val extends Customer>
    implements $CustomerCopyWith<$Res> {
  _$CustomerCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? nationalId = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? wilaya = freezed,
    Object? notes = freezed,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? totalAppointments = null,
    Object? totalSpent = null,
    Object? lastAppointmentDate = freezed,
    Object? status = null,
    Object? preferences = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      nationalId: freezed == nationalId
          ? _value.nationalId
          : nationalId // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
      lastAppointmentDate: freezed == lastAppointmentDate
          ? _value.lastAppointmentDate
          : lastAppointmentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CustomerStatus,
      preferences: freezed == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as CustomerPreferences?,
    ) as $Val);
  }

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerPreferencesCopyWith<$Res>? get preferences {
    if (_value.preferences == null) {
      return null;
    }

    return $CustomerPreferencesCopyWith<$Res>(_value.preferences!, (value) {
      return _then(_value.copyWith(preferences: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomerImplCopyWith<$Res>
    implements $CustomerCopyWith<$Res> {
  factory _$$CustomerImplCopyWith(
          _$CustomerImpl value, $Res Function(_$CustomerImpl) then) =
      __$$CustomerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String firstName,
      String lastName,
      String? email,
      @JsonKey(name: 'mobileNumber') String? phoneNumber,
      String? nationalId,
      DateTime? dateOfBirth,
      String? address,
      String? city,
      String? wilaya,
      String? notes,
      List<String> tags,
      DateTime createdAt,
      DateTime? updatedAt,
      @JsonKey(name: 'appointmentCount') int totalAppointments,
      double totalSpent,
      DateTime? lastAppointmentDate,
      CustomerStatus status,
      CustomerPreferences? preferences});

  @override
  $CustomerPreferencesCopyWith<$Res>? get preferences;
}

/// @nodoc
class __$$CustomerImplCopyWithImpl<$Res>
    extends _$CustomerCopyWithImpl<$Res, _$CustomerImpl>
    implements _$$CustomerImplCopyWith<$Res> {
  __$$CustomerImplCopyWithImpl(
      _$CustomerImpl _value, $Res Function(_$CustomerImpl) _then)
      : super(_value, _then);

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? nationalId = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? wilaya = freezed,
    Object? notes = freezed,
    Object? tags = null,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? totalAppointments = null,
    Object? totalSpent = null,
    Object? lastAppointmentDate = freezed,
    Object? status = null,
    Object? preferences = freezed,
  }) {
    return _then(_$CustomerImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      nationalId: freezed == nationalId
          ? _value.nationalId
          : nationalId // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalAppointments: null == totalAppointments
          ? _value.totalAppointments
          : totalAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as double,
      lastAppointmentDate: freezed == lastAppointmentDate
          ? _value.lastAppointmentDate
          : lastAppointmentDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CustomerStatus,
      preferences: freezed == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as CustomerPreferences?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerImpl implements _Customer {
  const _$CustomerImpl(
      {required this.id,
      required this.firstName,
      required this.lastName,
      this.email,
      @JsonKey(name: 'mobileNumber') this.phoneNumber,
      this.nationalId,
      this.dateOfBirth,
      this.address,
      this.city,
      this.wilaya,
      this.notes,
      final List<String> tags = const [],
      required this.createdAt,
      this.updatedAt,
      @JsonKey(name: 'appointmentCount') this.totalAppointments = 0,
      this.totalSpent = 0.0,
      this.lastAppointmentDate,
      this.status = CustomerStatus.active,
      this.preferences})
      : _tags = tags;

  factory _$CustomerImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerImplFromJson(json);

  @override
  final String id;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String? email;
// Made optional to match backend
  @override
  @JsonKey(name: 'mobileNumber')
  final String? phoneNumber;
// Made optional to match backend (can be null)
  @override
  final String? nationalId;
// Added to match backend
  @override
  final DateTime? dateOfBirth;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? wilaya;
  @override
  final String? notes;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  @JsonKey(name: 'appointmentCount')
  final int totalAppointments;
// Map appointmentCount to totalAppointments
  @override
  @JsonKey()
  final double totalSpent;
  @override
  final DateTime? lastAppointmentDate;
  @override
  @JsonKey()
  final CustomerStatus status;
  @override
  final CustomerPreferences? preferences;

  @override
  String toString() {
    return 'Customer(id: $id, firstName: $firstName, lastName: $lastName, email: $email, phoneNumber: $phoneNumber, nationalId: $nationalId, dateOfBirth: $dateOfBirth, address: $address, city: $city, wilaya: $wilaya, notes: $notes, tags: $tags, createdAt: $createdAt, updatedAt: $updatedAt, totalAppointments: $totalAppointments, totalSpent: $totalSpent, lastAppointmentDate: $lastAppointmentDate, status: $status, preferences: $preferences)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.nationalId, nationalId) ||
                other.nationalId == nationalId) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.wilaya, wilaya) || other.wilaya == wilaya) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.totalAppointments, totalAppointments) ||
                other.totalAppointments == totalAppointments) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent) &&
            (identical(other.lastAppointmentDate, lastAppointmentDate) ||
                other.lastAppointmentDate == lastAppointmentDate) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.preferences, preferences) ||
                other.preferences == preferences));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        firstName,
        lastName,
        email,
        phoneNumber,
        nationalId,
        dateOfBirth,
        address,
        city,
        wilaya,
        notes,
        const DeepCollectionEquality().hash(_tags),
        createdAt,
        updatedAt,
        totalAppointments,
        totalSpent,
        lastAppointmentDate,
        status,
        preferences
      ]);

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerImplCopyWith<_$CustomerImpl> get copyWith =>
      __$$CustomerImplCopyWithImpl<_$CustomerImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerImplToJson(
      this,
    );
  }
}

abstract class _Customer implements Customer {
  const factory _Customer(
      {required final String id,
      required final String firstName,
      required final String lastName,
      final String? email,
      @JsonKey(name: 'mobileNumber') final String? phoneNumber,
      final String? nationalId,
      final DateTime? dateOfBirth,
      final String? address,
      final String? city,
      final String? wilaya,
      final String? notes,
      final List<String> tags,
      required final DateTime createdAt,
      final DateTime? updatedAt,
      @JsonKey(name: 'appointmentCount') final int totalAppointments,
      final double totalSpent,
      final DateTime? lastAppointmentDate,
      final CustomerStatus status,
      final CustomerPreferences? preferences}) = _$CustomerImpl;

  factory _Customer.fromJson(Map<String, dynamic> json) =
      _$CustomerImpl.fromJson;

  @override
  String get id;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String? get email; // Made optional to match backend
  @override
  @JsonKey(name: 'mobileNumber')
  String? get phoneNumber; // Made optional to match backend (can be null)
  @override
  String? get nationalId; // Added to match backend
  @override
  DateTime? get dateOfBirth;
  @override
  String? get address;
  @override
  String? get city;
  @override
  String? get wilaya;
  @override
  String? get notes;
  @override
  List<String> get tags;
  @override
  DateTime get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  @JsonKey(name: 'appointmentCount')
  int get totalAppointments; // Map appointmentCount to totalAppointments
  @override
  double get totalSpent;
  @override
  DateTime? get lastAppointmentDate;
  @override
  CustomerStatus get status;
  @override
  CustomerPreferences? get preferences;

  /// Create a copy of Customer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerImplCopyWith<_$CustomerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerPreferences _$CustomerPreferencesFromJson(Map<String, dynamic> json) {
  return _CustomerPreferences.fromJson(json);
}

/// @nodoc
mixin _$CustomerPreferences {
  bool get emailNotifications => throw _privateConstructorUsedError;
  bool get smsNotifications => throw _privateConstructorUsedError;
  bool get appointmentReminders => throw _privateConstructorUsedError;
  bool get promotionalMessages => throw _privateConstructorUsedError;
  String? get preferredLanguage => throw _privateConstructorUsedError;
  String? get preferredContactMethod => throw _privateConstructorUsedError;

  /// Serializes this CustomerPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerPreferencesCopyWith<CustomerPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerPreferencesCopyWith<$Res> {
  factory $CustomerPreferencesCopyWith(
          CustomerPreferences value, $Res Function(CustomerPreferences) then) =
      _$CustomerPreferencesCopyWithImpl<$Res, CustomerPreferences>;
  @useResult
  $Res call(
      {bool emailNotifications,
      bool smsNotifications,
      bool appointmentReminders,
      bool promotionalMessages,
      String? preferredLanguage,
      String? preferredContactMethod});
}

/// @nodoc
class _$CustomerPreferencesCopyWithImpl<$Res, $Val extends CustomerPreferences>
    implements $CustomerPreferencesCopyWith<$Res> {
  _$CustomerPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailNotifications = null,
    Object? smsNotifications = null,
    Object? appointmentReminders = null,
    Object? promotionalMessages = null,
    Object? preferredLanguage = freezed,
    Object? preferredContactMethod = freezed,
  }) {
    return _then(_value.copyWith(
      emailNotifications: null == emailNotifications
          ? _value.emailNotifications
          : emailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      smsNotifications: null == smsNotifications
          ? _value.smsNotifications
          : smsNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      appointmentReminders: null == appointmentReminders
          ? _value.appointmentReminders
          : appointmentReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionalMessages: null == promotionalMessages
          ? _value.promotionalMessages
          : promotionalMessages // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: freezed == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredContactMethod: freezed == preferredContactMethod
          ? _value.preferredContactMethod
          : preferredContactMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerPreferencesImplCopyWith<$Res>
    implements $CustomerPreferencesCopyWith<$Res> {
  factory _$$CustomerPreferencesImplCopyWith(_$CustomerPreferencesImpl value,
          $Res Function(_$CustomerPreferencesImpl) then) =
      __$$CustomerPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool emailNotifications,
      bool smsNotifications,
      bool appointmentReminders,
      bool promotionalMessages,
      String? preferredLanguage,
      String? preferredContactMethod});
}

/// @nodoc
class __$$CustomerPreferencesImplCopyWithImpl<$Res>
    extends _$CustomerPreferencesCopyWithImpl<$Res, _$CustomerPreferencesImpl>
    implements _$$CustomerPreferencesImplCopyWith<$Res> {
  __$$CustomerPreferencesImplCopyWithImpl(_$CustomerPreferencesImpl _value,
      $Res Function(_$CustomerPreferencesImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailNotifications = null,
    Object? smsNotifications = null,
    Object? appointmentReminders = null,
    Object? promotionalMessages = null,
    Object? preferredLanguage = freezed,
    Object? preferredContactMethod = freezed,
  }) {
    return _then(_$CustomerPreferencesImpl(
      emailNotifications: null == emailNotifications
          ? _value.emailNotifications
          : emailNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      smsNotifications: null == smsNotifications
          ? _value.smsNotifications
          : smsNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      appointmentReminders: null == appointmentReminders
          ? _value.appointmentReminders
          : appointmentReminders // ignore: cast_nullable_to_non_nullable
              as bool,
      promotionalMessages: null == promotionalMessages
          ? _value.promotionalMessages
          : promotionalMessages // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: freezed == preferredLanguage
          ? _value.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredContactMethod: freezed == preferredContactMethod
          ? _value.preferredContactMethod
          : preferredContactMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerPreferencesImpl implements _CustomerPreferences {
  const _$CustomerPreferencesImpl(
      {this.emailNotifications = true,
      this.smsNotifications = true,
      this.appointmentReminders = true,
      this.promotionalMessages = true,
      this.preferredLanguage,
      this.preferredContactMethod});

  factory _$CustomerPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final bool emailNotifications;
  @override
  @JsonKey()
  final bool smsNotifications;
  @override
  @JsonKey()
  final bool appointmentReminders;
  @override
  @JsonKey()
  final bool promotionalMessages;
  @override
  final String? preferredLanguage;
  @override
  final String? preferredContactMethod;

  @override
  String toString() {
    return 'CustomerPreferences(emailNotifications: $emailNotifications, smsNotifications: $smsNotifications, appointmentReminders: $appointmentReminders, promotionalMessages: $promotionalMessages, preferredLanguage: $preferredLanguage, preferredContactMethod: $preferredContactMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerPreferencesImpl &&
            (identical(other.emailNotifications, emailNotifications) ||
                other.emailNotifications == emailNotifications) &&
            (identical(other.smsNotifications, smsNotifications) ||
                other.smsNotifications == smsNotifications) &&
            (identical(other.appointmentReminders, appointmentReminders) ||
                other.appointmentReminders == appointmentReminders) &&
            (identical(other.promotionalMessages, promotionalMessages) ||
                other.promotionalMessages == promotionalMessages) &&
            (identical(other.preferredLanguage, preferredLanguage) ||
                other.preferredLanguage == preferredLanguage) &&
            (identical(other.preferredContactMethod, preferredContactMethod) ||
                other.preferredContactMethod == preferredContactMethod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      emailNotifications,
      smsNotifications,
      appointmentReminders,
      promotionalMessages,
      preferredLanguage,
      preferredContactMethod);

  /// Create a copy of CustomerPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerPreferencesImplCopyWith<_$CustomerPreferencesImpl> get copyWith =>
      __$$CustomerPreferencesImplCopyWithImpl<_$CustomerPreferencesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerPreferencesImplToJson(
      this,
    );
  }
}

abstract class _CustomerPreferences implements CustomerPreferences {
  const factory _CustomerPreferences(
      {final bool emailNotifications,
      final bool smsNotifications,
      final bool appointmentReminders,
      final bool promotionalMessages,
      final String? preferredLanguage,
      final String? preferredContactMethod}) = _$CustomerPreferencesImpl;

  factory _CustomerPreferences.fromJson(Map<String, dynamic> json) =
      _$CustomerPreferencesImpl.fromJson;

  @override
  bool get emailNotifications;
  @override
  bool get smsNotifications;
  @override
  bool get appointmentReminders;
  @override
  bool get promotionalMessages;
  @override
  String? get preferredLanguage;
  @override
  String? get preferredContactMethod;

  /// Create a copy of CustomerPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerPreferencesImplCopyWith<_$CustomerPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerAppointment _$CustomerAppointmentFromJson(Map<String, dynamic> json) {
  return _CustomerAppointment.fromJson(json);
}

/// @nodoc
mixin _$CustomerAppointment {
  String get id => throw _privateConstructorUsedError;
  String get customerId => throw _privateConstructorUsedError;
  String get serviceId => throw _privateConstructorUsedError;
  String get serviceName => throw _privateConstructorUsedError;
  DateTime get scheduledTime => throw _privateConstructorUsedError;
  AppointmentStatus get status => throw _privateConstructorUsedError;
  String? get locationId => throw _privateConstructorUsedError;
  String? get locationName => throw _privateConstructorUsedError;
  String? get providerId => throw _privateConstructorUsedError;
  String? get providerName => throw _privateConstructorUsedError;
  double? get price => throw _privateConstructorUsedError;
  int? get duration => throw _privateConstructorUsedError; // in minutes
  String? get notes => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CustomerAppointment to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerAppointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerAppointmentCopyWith<CustomerAppointment> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerAppointmentCopyWith<$Res> {
  factory $CustomerAppointmentCopyWith(
          CustomerAppointment value, $Res Function(CustomerAppointment) then) =
      _$CustomerAppointmentCopyWithImpl<$Res, CustomerAppointment>;
  @useResult
  $Res call(
      {String id,
      String customerId,
      String serviceId,
      String serviceName,
      DateTime scheduledTime,
      AppointmentStatus status,
      String? locationId,
      String? locationName,
      String? providerId,
      String? providerName,
      double? price,
      int? duration,
      String? notes,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$CustomerAppointmentCopyWithImpl<$Res, $Val extends CustomerAppointment>
    implements $CustomerAppointmentCopyWith<$Res> {
  _$CustomerAppointmentCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerAppointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerId = null,
    Object? serviceId = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? status = null,
    Object? locationId = freezed,
    Object? locationName = freezed,
    Object? providerId = freezed,
    Object? providerName = freezed,
    Object? price = freezed,
    Object? duration = freezed,
    Object? notes = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerName: freezed == providerName
          ? _value.providerName
          : providerName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerAppointmentImplCopyWith<$Res>
    implements $CustomerAppointmentCopyWith<$Res> {
  factory _$$CustomerAppointmentImplCopyWith(_$CustomerAppointmentImpl value,
          $Res Function(_$CustomerAppointmentImpl) then) =
      __$$CustomerAppointmentImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String customerId,
      String serviceId,
      String serviceName,
      DateTime scheduledTime,
      AppointmentStatus status,
      String? locationId,
      String? locationName,
      String? providerId,
      String? providerName,
      double? price,
      int? duration,
      String? notes,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$CustomerAppointmentImplCopyWithImpl<$Res>
    extends _$CustomerAppointmentCopyWithImpl<$Res, _$CustomerAppointmentImpl>
    implements _$$CustomerAppointmentImplCopyWith<$Res> {
  __$$CustomerAppointmentImplCopyWithImpl(_$CustomerAppointmentImpl _value,
      $Res Function(_$CustomerAppointmentImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerAppointment
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? customerId = null,
    Object? serviceId = null,
    Object? serviceName = null,
    Object? scheduledTime = null,
    Object? status = null,
    Object? locationId = freezed,
    Object? locationName = freezed,
    Object? providerId = freezed,
    Object? providerName = freezed,
    Object? price = freezed,
    Object? duration = freezed,
    Object? notes = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$CustomerAppointmentImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      customerId: null == customerId
          ? _value.customerId
          : customerId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceId: null == serviceId
          ? _value.serviceId
          : serviceId // ignore: cast_nullable_to_non_nullable
              as String,
      serviceName: null == serviceName
          ? _value.serviceName
          : serviceName // ignore: cast_nullable_to_non_nullable
              as String,
      scheduledTime: null == scheduledTime
          ? _value.scheduledTime
          : scheduledTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as AppointmentStatus,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      locationName: freezed == locationName
          ? _value.locationName
          : locationName // ignore: cast_nullable_to_non_nullable
              as String?,
      providerId: freezed == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String?,
      providerName: freezed == providerName
          ? _value.providerName
          : providerName // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerAppointmentImpl implements _CustomerAppointment {
  const _$CustomerAppointmentImpl(
      {required this.id,
      required this.customerId,
      required this.serviceId,
      required this.serviceName,
      required this.scheduledTime,
      required this.status,
      this.locationId,
      this.locationName,
      this.providerId,
      this.providerName,
      this.price,
      this.duration,
      this.notes,
      this.createdAt,
      this.updatedAt});

  factory _$CustomerAppointmentImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerAppointmentImplFromJson(json);

  @override
  final String id;
  @override
  final String customerId;
  @override
  final String serviceId;
  @override
  final String serviceName;
  @override
  final DateTime scheduledTime;
  @override
  final AppointmentStatus status;
  @override
  final String? locationId;
  @override
  final String? locationName;
  @override
  final String? providerId;
  @override
  final String? providerName;
  @override
  final double? price;
  @override
  final int? duration;
// in minutes
  @override
  final String? notes;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CustomerAppointment(id: $id, customerId: $customerId, serviceId: $serviceId, serviceName: $serviceName, scheduledTime: $scheduledTime, status: $status, locationId: $locationId, locationName: $locationName, providerId: $providerId, providerName: $providerName, price: $price, duration: $duration, notes: $notes, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerAppointmentImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.customerId, customerId) ||
                other.customerId == customerId) &&
            (identical(other.serviceId, serviceId) ||
                other.serviceId == serviceId) &&
            (identical(other.serviceName, serviceName) ||
                other.serviceName == serviceName) &&
            (identical(other.scheduledTime, scheduledTime) ||
                other.scheduledTime == scheduledTime) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.locationName, locationName) ||
                other.locationName == locationName) &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      customerId,
      serviceId,
      serviceName,
      scheduledTime,
      status,
      locationId,
      locationName,
      providerId,
      providerName,
      price,
      duration,
      notes,
      createdAt,
      updatedAt);

  /// Create a copy of CustomerAppointment
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerAppointmentImplCopyWith<_$CustomerAppointmentImpl> get copyWith =>
      __$$CustomerAppointmentImplCopyWithImpl<_$CustomerAppointmentImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerAppointmentImplToJson(
      this,
    );
  }
}

abstract class _CustomerAppointment implements CustomerAppointment {
  const factory _CustomerAppointment(
      {required final String id,
      required final String customerId,
      required final String serviceId,
      required final String serviceName,
      required final DateTime scheduledTime,
      required final AppointmentStatus status,
      final String? locationId,
      final String? locationName,
      final String? providerId,
      final String? providerName,
      final double? price,
      final int? duration,
      final String? notes,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$CustomerAppointmentImpl;

  factory _CustomerAppointment.fromJson(Map<String, dynamic> json) =
      _$CustomerAppointmentImpl.fromJson;

  @override
  String get id;
  @override
  String get customerId;
  @override
  String get serviceId;
  @override
  String get serviceName;
  @override
  DateTime get scheduledTime;
  @override
  AppointmentStatus get status;
  @override
  String? get locationId;
  @override
  String? get locationName;
  @override
  String? get providerId;
  @override
  String? get providerName;
  @override
  double? get price;
  @override
  int? get duration; // in minutes
  @override
  String? get notes;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of CustomerAppointment
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerAppointmentImplCopyWith<_$CustomerAppointmentImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerSearchFilters _$CustomerSearchFiltersFromJson(
    Map<String, dynamic> json) {
  return _CustomerSearchFilters.fromJson(json);
}

/// @nodoc
mixin _$CustomerSearchFilters {
  String? get searchQuery => throw _privateConstructorUsedError;
  CustomerStatus? get status => throw _privateConstructorUsedError;
  String? get wilaya => throw _privateConstructorUsedError;
  DateTime? get createdAfter => throw _privateConstructorUsedError;
  DateTime? get createdBefore => throw _privateConstructorUsedError;
  int get minAppointments => throw _privateConstructorUsedError;
  double get minSpent => throw _privateConstructorUsedError;
  List<String>? get tags => throw _privateConstructorUsedError;
  String get sortBy => throw _privateConstructorUsedError;
  bool get ascending => throw _privateConstructorUsedError;

  /// Serializes this CustomerSearchFilters to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerSearchFiltersCopyWith<CustomerSearchFilters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerSearchFiltersCopyWith<$Res> {
  factory $CustomerSearchFiltersCopyWith(CustomerSearchFilters value,
          $Res Function(CustomerSearchFilters) then) =
      _$CustomerSearchFiltersCopyWithImpl<$Res, CustomerSearchFilters>;
  @useResult
  $Res call(
      {String? searchQuery,
      CustomerStatus? status,
      String? wilaya,
      DateTime? createdAfter,
      DateTime? createdBefore,
      int minAppointments,
      double minSpent,
      List<String>? tags,
      String sortBy,
      bool ascending});
}

/// @nodoc
class _$CustomerSearchFiltersCopyWithImpl<$Res,
        $Val extends CustomerSearchFilters>
    implements $CustomerSearchFiltersCopyWith<$Res> {
  _$CustomerSearchFiltersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = freezed,
    Object? status = freezed,
    Object? wilaya = freezed,
    Object? createdAfter = freezed,
    Object? createdBefore = freezed,
    Object? minAppointments = null,
    Object? minSpent = null,
    Object? tags = freezed,
    Object? sortBy = null,
    Object? ascending = null,
  }) {
    return _then(_value.copyWith(
      searchQuery: freezed == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CustomerStatus?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAfter: freezed == createdAfter
          ? _value.createdAfter
          : createdAfter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBefore: freezed == createdBefore
          ? _value.createdBefore
          : createdBefore // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      minAppointments: null == minAppointments
          ? _value.minAppointments
          : minAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      minSpent: null == minSpent
          ? _value.minSpent
          : minSpent // ignore: cast_nullable_to_non_nullable
              as double,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      sortBy: null == sortBy
          ? _value.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String,
      ascending: null == ascending
          ? _value.ascending
          : ascending // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerSearchFiltersImplCopyWith<$Res>
    implements $CustomerSearchFiltersCopyWith<$Res> {
  factory _$$CustomerSearchFiltersImplCopyWith(
          _$CustomerSearchFiltersImpl value,
          $Res Function(_$CustomerSearchFiltersImpl) then) =
      __$$CustomerSearchFiltersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? searchQuery,
      CustomerStatus? status,
      String? wilaya,
      DateTime? createdAfter,
      DateTime? createdBefore,
      int minAppointments,
      double minSpent,
      List<String>? tags,
      String sortBy,
      bool ascending});
}

/// @nodoc
class __$$CustomerSearchFiltersImplCopyWithImpl<$Res>
    extends _$CustomerSearchFiltersCopyWithImpl<$Res,
        _$CustomerSearchFiltersImpl>
    implements _$$CustomerSearchFiltersImplCopyWith<$Res> {
  __$$CustomerSearchFiltersImplCopyWithImpl(_$CustomerSearchFiltersImpl _value,
      $Res Function(_$CustomerSearchFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = freezed,
    Object? status = freezed,
    Object? wilaya = freezed,
    Object? createdAfter = freezed,
    Object? createdBefore = freezed,
    Object? minAppointments = null,
    Object? minSpent = null,
    Object? tags = freezed,
    Object? sortBy = null,
    Object? ascending = null,
  }) {
    return _then(_$CustomerSearchFiltersImpl(
      searchQuery: freezed == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CustomerStatus?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAfter: freezed == createdAfter
          ? _value.createdAfter
          : createdAfter // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBefore: freezed == createdBefore
          ? _value.createdBefore
          : createdBefore // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      minAppointments: null == minAppointments
          ? _value.minAppointments
          : minAppointments // ignore: cast_nullable_to_non_nullable
              as int,
      minSpent: null == minSpent
          ? _value.minSpent
          : minSpent // ignore: cast_nullable_to_non_nullable
              as double,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      sortBy: null == sortBy
          ? _value.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String,
      ascending: null == ascending
          ? _value.ascending
          : ascending // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerSearchFiltersImpl implements _CustomerSearchFilters {
  const _$CustomerSearchFiltersImpl(
      {this.searchQuery,
      this.status,
      this.wilaya,
      this.createdAfter,
      this.createdBefore,
      this.minAppointments = 0,
      this.minSpent = 0.0,
      final List<String>? tags,
      this.sortBy = 'firstName',
      this.ascending = true})
      : _tags = tags;

  factory _$CustomerSearchFiltersImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerSearchFiltersImplFromJson(json);

  @override
  final String? searchQuery;
  @override
  final CustomerStatus? status;
  @override
  final String? wilaya;
  @override
  final DateTime? createdAfter;
  @override
  final DateTime? createdBefore;
  @override
  @JsonKey()
  final int minAppointments;
  @override
  @JsonKey()
  final double minSpent;
  final List<String>? _tags;
  @override
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey()
  final String sortBy;
  @override
  @JsonKey()
  final bool ascending;

  @override
  String toString() {
    return 'CustomerSearchFilters(searchQuery: $searchQuery, status: $status, wilaya: $wilaya, createdAfter: $createdAfter, createdBefore: $createdBefore, minAppointments: $minAppointments, minSpent: $minSpent, tags: $tags, sortBy: $sortBy, ascending: $ascending)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerSearchFiltersImpl &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.wilaya, wilaya) || other.wilaya == wilaya) &&
            (identical(other.createdAfter, createdAfter) ||
                other.createdAfter == createdAfter) &&
            (identical(other.createdBefore, createdBefore) ||
                other.createdBefore == createdBefore) &&
            (identical(other.minAppointments, minAppointments) ||
                other.minAppointments == minAppointments) &&
            (identical(other.minSpent, minSpent) ||
                other.minSpent == minSpent) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.ascending, ascending) ||
                other.ascending == ascending));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      searchQuery,
      status,
      wilaya,
      createdAfter,
      createdBefore,
      minAppointments,
      minSpent,
      const DeepCollectionEquality().hash(_tags),
      sortBy,
      ascending);

  /// Create a copy of CustomerSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerSearchFiltersImplCopyWith<_$CustomerSearchFiltersImpl>
      get copyWith => __$$CustomerSearchFiltersImplCopyWithImpl<
          _$CustomerSearchFiltersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerSearchFiltersImplToJson(
      this,
    );
  }
}

abstract class _CustomerSearchFilters implements CustomerSearchFilters {
  const factory _CustomerSearchFilters(
      {final String? searchQuery,
      final CustomerStatus? status,
      final String? wilaya,
      final DateTime? createdAfter,
      final DateTime? createdBefore,
      final int minAppointments,
      final double minSpent,
      final List<String>? tags,
      final String sortBy,
      final bool ascending}) = _$CustomerSearchFiltersImpl;

  factory _CustomerSearchFilters.fromJson(Map<String, dynamic> json) =
      _$CustomerSearchFiltersImpl.fromJson;

  @override
  String? get searchQuery;
  @override
  CustomerStatus? get status;
  @override
  String? get wilaya;
  @override
  DateTime? get createdAfter;
  @override
  DateTime? get createdBefore;
  @override
  int get minAppointments;
  @override
  double get minSpent;
  @override
  List<String>? get tags;
  @override
  String get sortBy;
  @override
  bool get ascending;

  /// Create a copy of CustomerSearchFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerSearchFiltersImplCopyWith<_$CustomerSearchFiltersImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CustomersResponse _$CustomersResponseFromJson(Map<String, dynamic> json) {
  return _CustomersResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomersResponse {
  bool get success => throw _privateConstructorUsedError;
  List<Customer> get customers => throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  int get currentPage => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  int get totalPages => throw _privateConstructorUsedError;
  CustomerError? get error => throw _privateConstructorUsedError;

  /// Serializes this CustomersResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomersResponseCopyWith<CustomersResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomersResponseCopyWith<$Res> {
  factory $CustomersResponseCopyWith(
          CustomersResponse value, $Res Function(CustomersResponse) then) =
      _$CustomersResponseCopyWithImpl<$Res, CustomersResponse>;
  @useResult
  $Res call(
      {bool success,
      List<Customer> customers,
      int totalCount,
      int currentPage,
      int pageSize,
      int totalPages,
      CustomerError? error});

  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$CustomersResponseCopyWithImpl<$Res, $Val extends CustomersResponse>
    implements $CustomersResponseCopyWith<$Res> {
  _$CustomersResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? customers = null,
    Object? totalCount = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? totalPages = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      customers: null == customers
          ? _value.customers
          : customers // ignore: cast_nullable_to_non_nullable
              as List<Customer>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ) as $Val);
  }

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $CustomerErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomersResponseImplCopyWith<$Res>
    implements $CustomersResponseCopyWith<$Res> {
  factory _$$CustomersResponseImplCopyWith(_$CustomersResponseImpl value,
          $Res Function(_$CustomersResponseImpl) then) =
      __$$CustomersResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      List<Customer> customers,
      int totalCount,
      int currentPage,
      int pageSize,
      int totalPages,
      CustomerError? error});

  @override
  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$CustomersResponseImplCopyWithImpl<$Res>
    extends _$CustomersResponseCopyWithImpl<$Res, _$CustomersResponseImpl>
    implements _$$CustomersResponseImplCopyWith<$Res> {
  __$$CustomersResponseImplCopyWithImpl(_$CustomersResponseImpl _value,
      $Res Function(_$CustomersResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? customers = null,
    Object? totalCount = null,
    Object? currentPage = null,
    Object? pageSize = null,
    Object? totalPages = null,
    Object? error = freezed,
  }) {
    return _then(_$CustomersResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      customers: null == customers
          ? _value._customers
          : customers // ignore: cast_nullable_to_non_nullable
              as List<Customer>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomersResponseImpl implements _CustomersResponse {
  const _$CustomersResponseImpl(
      {required this.success,
      final List<Customer> customers = const [],
      this.totalCount = 0,
      this.currentPage = 1,
      this.pageSize = 20,
      this.totalPages = 0,
      this.error})
      : _customers = customers;

  factory _$CustomersResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomersResponseImplFromJson(json);

  @override
  final bool success;
  final List<Customer> _customers;
  @override
  @JsonKey()
  List<Customer> get customers {
    if (_customers is EqualUnmodifiableListView) return _customers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customers);
  }

  @override
  @JsonKey()
  final int totalCount;
  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int pageSize;
  @override
  @JsonKey()
  final int totalPages;
  @override
  final CustomerError? error;

  @override
  String toString() {
    return 'CustomersResponse(success: $success, customers: $customers, totalCount: $totalCount, currentPage: $currentPage, pageSize: $pageSize, totalPages: $totalPages, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomersResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality()
                .equals(other._customers, _customers) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      const DeepCollectionEquality().hash(_customers),
      totalCount,
      currentPage,
      pageSize,
      totalPages,
      error);

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomersResponseImplCopyWith<_$CustomersResponseImpl> get copyWith =>
      __$$CustomersResponseImplCopyWithImpl<_$CustomersResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomersResponseImplToJson(
      this,
    );
  }
}

abstract class _CustomersResponse implements CustomersResponse {
  const factory _CustomersResponse(
      {required final bool success,
      final List<Customer> customers,
      final int totalCount,
      final int currentPage,
      final int pageSize,
      final int totalPages,
      final CustomerError? error}) = _$CustomersResponseImpl;

  factory _CustomersResponse.fromJson(Map<String, dynamic> json) =
      _$CustomersResponseImpl.fromJson;

  @override
  bool get success;
  @override
  List<Customer> get customers;
  @override
  int get totalCount;
  @override
  int get currentPage;
  @override
  int get pageSize;
  @override
  int get totalPages;
  @override
  CustomerError? get error;

  /// Create a copy of CustomersResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomersResponseImplCopyWith<_$CustomersResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerResponse _$CustomerResponseFromJson(Map<String, dynamic> json) {
  return _CustomerResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomerResponse {
  bool get success => throw _privateConstructorUsedError;
  Customer? get customer => throw _privateConstructorUsedError;
  CustomerError? get error => throw _privateConstructorUsedError;

  /// Serializes this CustomerResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerResponseCopyWith<CustomerResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerResponseCopyWith<$Res> {
  factory $CustomerResponseCopyWith(
          CustomerResponse value, $Res Function(CustomerResponse) then) =
      _$CustomerResponseCopyWithImpl<$Res, CustomerResponse>;
  @useResult
  $Res call({bool success, Customer? customer, CustomerError? error});

  $CustomerCopyWith<$Res>? get customer;
  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$CustomerResponseCopyWithImpl<$Res, $Val extends CustomerResponse>
    implements $CustomerResponseCopyWith<$Res> {
  _$CustomerResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? customer = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      customer: freezed == customer
          ? _value.customer
          : customer // ignore: cast_nullable_to_non_nullable
              as Customer?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ) as $Val);
  }

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerCopyWith<$Res>? get customer {
    if (_value.customer == null) {
      return null;
    }

    return $CustomerCopyWith<$Res>(_value.customer!, (value) {
      return _then(_value.copyWith(customer: value) as $Val);
    });
  }

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $CustomerErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomerResponseImplCopyWith<$Res>
    implements $CustomerResponseCopyWith<$Res> {
  factory _$$CustomerResponseImplCopyWith(_$CustomerResponseImpl value,
          $Res Function(_$CustomerResponseImpl) then) =
      __$$CustomerResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool success, Customer? customer, CustomerError? error});

  @override
  $CustomerCopyWith<$Res>? get customer;
  @override
  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$CustomerResponseImplCopyWithImpl<$Res>
    extends _$CustomerResponseCopyWithImpl<$Res, _$CustomerResponseImpl>
    implements _$$CustomerResponseImplCopyWith<$Res> {
  __$$CustomerResponseImplCopyWithImpl(_$CustomerResponseImpl _value,
      $Res Function(_$CustomerResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? customer = freezed,
    Object? error = freezed,
  }) {
    return _then(_$CustomerResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      customer: freezed == customer
          ? _value.customer
          : customer // ignore: cast_nullable_to_non_nullable
              as Customer?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerResponseImpl implements _CustomerResponse {
  const _$CustomerResponseImpl(
      {required this.success, this.customer, this.error});

  factory _$CustomerResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerResponseImplFromJson(json);

  @override
  final bool success;
  @override
  final Customer? customer;
  @override
  final CustomerError? error;

  @override
  String toString() {
    return 'CustomerResponse(success: $success, customer: $customer, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.customer, customer) ||
                other.customer == customer) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, customer, error);

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerResponseImplCopyWith<_$CustomerResponseImpl> get copyWith =>
      __$$CustomerResponseImplCopyWithImpl<_$CustomerResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerResponseImplToJson(
      this,
    );
  }
}

abstract class _CustomerResponse implements CustomerResponse {
  const factory _CustomerResponse(
      {required final bool success,
      final Customer? customer,
      final CustomerError? error}) = _$CustomerResponseImpl;

  factory _CustomerResponse.fromJson(Map<String, dynamic> json) =
      _$CustomerResponseImpl.fromJson;

  @override
  bool get success;
  @override
  Customer? get customer;
  @override
  CustomerError? get error;

  /// Create a copy of CustomerResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerResponseImplCopyWith<_$CustomerResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerAppointmentsResponse _$CustomerAppointmentsResponseFromJson(
    Map<String, dynamic> json) {
  return _CustomerAppointmentsResponse.fromJson(json);
}

/// @nodoc
mixin _$CustomerAppointmentsResponse {
  bool get success => throw _privateConstructorUsedError;
  List<CustomerAppointment> get appointments =>
      throw _privateConstructorUsedError;
  int get totalCount => throw _privateConstructorUsedError;
  CustomerError? get error => throw _privateConstructorUsedError;

  /// Serializes this CustomerAppointmentsResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerAppointmentsResponseCopyWith<CustomerAppointmentsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerAppointmentsResponseCopyWith<$Res> {
  factory $CustomerAppointmentsResponseCopyWith(
          CustomerAppointmentsResponse value,
          $Res Function(CustomerAppointmentsResponse) then) =
      _$CustomerAppointmentsResponseCopyWithImpl<$Res,
          CustomerAppointmentsResponse>;
  @useResult
  $Res call(
      {bool success,
      List<CustomerAppointment> appointments,
      int totalCount,
      CustomerError? error});

  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$CustomerAppointmentsResponseCopyWithImpl<$Res,
        $Val extends CustomerAppointmentsResponse>
    implements $CustomerAppointmentsResponseCopyWith<$Res> {
  _$CustomerAppointmentsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointments = null,
    Object? totalCount = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointments: null == appointments
          ? _value.appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<CustomerAppointment>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ) as $Val);
  }

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $CustomerErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomerAppointmentsResponseImplCopyWith<$Res>
    implements $CustomerAppointmentsResponseCopyWith<$Res> {
  factory _$$CustomerAppointmentsResponseImplCopyWith(
          _$CustomerAppointmentsResponseImpl value,
          $Res Function(_$CustomerAppointmentsResponseImpl) then) =
      __$$CustomerAppointmentsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool success,
      List<CustomerAppointment> appointments,
      int totalCount,
      CustomerError? error});

  @override
  $CustomerErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$CustomerAppointmentsResponseImplCopyWithImpl<$Res>
    extends _$CustomerAppointmentsResponseCopyWithImpl<$Res,
        _$CustomerAppointmentsResponseImpl>
    implements _$$CustomerAppointmentsResponseImplCopyWith<$Res> {
  __$$CustomerAppointmentsResponseImplCopyWithImpl(
      _$CustomerAppointmentsResponseImpl _value,
      $Res Function(_$CustomerAppointmentsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? appointments = null,
    Object? totalCount = null,
    Object? error = freezed,
  }) {
    return _then(_$CustomerAppointmentsResponseImpl(
      success: null == success
          ? _value.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      appointments: null == appointments
          ? _value._appointments
          : appointments // ignore: cast_nullable_to_non_nullable
              as List<CustomerAppointment>,
      totalCount: null == totalCount
          ? _value.totalCount
          : totalCount // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as CustomerError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerAppointmentsResponseImpl
    implements _CustomerAppointmentsResponse {
  const _$CustomerAppointmentsResponseImpl(
      {required this.success,
      final List<CustomerAppointment> appointments = const [],
      this.totalCount = 0,
      this.error})
      : _appointments = appointments;

  factory _$CustomerAppointmentsResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CustomerAppointmentsResponseImplFromJson(json);

  @override
  final bool success;
  final List<CustomerAppointment> _appointments;
  @override
  @JsonKey()
  List<CustomerAppointment> get appointments {
    if (_appointments is EqualUnmodifiableListView) return _appointments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_appointments);
  }

  @override
  @JsonKey()
  final int totalCount;
  @override
  final CustomerError? error;

  @override
  String toString() {
    return 'CustomerAppointmentsResponse(success: $success, appointments: $appointments, totalCount: $totalCount, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerAppointmentsResponseImpl &&
            (identical(other.success, success) || other.success == success) &&
            const DeepCollectionEquality()
                .equals(other._appointments, _appointments) &&
            (identical(other.totalCount, totalCount) ||
                other.totalCount == totalCount) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success,
      const DeepCollectionEquality().hash(_appointments), totalCount, error);

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerAppointmentsResponseImplCopyWith<
          _$CustomerAppointmentsResponseImpl>
      get copyWith => __$$CustomerAppointmentsResponseImplCopyWithImpl<
          _$CustomerAppointmentsResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerAppointmentsResponseImplToJson(
      this,
    );
  }
}

abstract class _CustomerAppointmentsResponse
    implements CustomerAppointmentsResponse {
  const factory _CustomerAppointmentsResponse(
      {required final bool success,
      final List<CustomerAppointment> appointments,
      final int totalCount,
      final CustomerError? error}) = _$CustomerAppointmentsResponseImpl;

  factory _CustomerAppointmentsResponse.fromJson(Map<String, dynamic> json) =
      _$CustomerAppointmentsResponseImpl.fromJson;

  @override
  bool get success;
  @override
  List<CustomerAppointment> get appointments;
  @override
  int get totalCount;
  @override
  CustomerError? get error;

  /// Create a copy of CustomerAppointmentsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerAppointmentsResponseImplCopyWith<
          _$CustomerAppointmentsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CustomerError _$CustomerErrorFromJson(Map<String, dynamic> json) {
  return _CustomerError.fromJson(json);
}

/// @nodoc
mixin _$CustomerError {
  String get code => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;

  /// Serializes this CustomerError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerErrorCopyWith<CustomerError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerErrorCopyWith<$Res> {
  factory $CustomerErrorCopyWith(
          CustomerError value, $Res Function(CustomerError) then) =
      _$CustomerErrorCopyWithImpl<$Res, CustomerError>;
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class _$CustomerErrorCopyWithImpl<$Res, $Val extends CustomerError>
    implements $CustomerErrorCopyWith<$Res> {
  _$CustomerErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerErrorImplCopyWith<$Res>
    implements $CustomerErrorCopyWith<$Res> {
  factory _$$CustomerErrorImplCopyWith(
          _$CustomerErrorImpl value, $Res Function(_$CustomerErrorImpl) then) =
      __$$CustomerErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String code, String message, String? details});
}

/// @nodoc
class __$$CustomerErrorImplCopyWithImpl<$Res>
    extends _$CustomerErrorCopyWithImpl<$Res, _$CustomerErrorImpl>
    implements _$$CustomerErrorImplCopyWith<$Res> {
  __$$CustomerErrorImplCopyWithImpl(
      _$CustomerErrorImpl _value, $Res Function(_$CustomerErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? details = freezed,
  }) {
    return _then(_$CustomerErrorImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerErrorImpl implements _CustomerError {
  const _$CustomerErrorImpl(
      {required this.code, required this.message, this.details});

  factory _$CustomerErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerErrorImplFromJson(json);

  @override
  final String code;
  @override
  final String message;
  @override
  final String? details;

  @override
  String toString() {
    return 'CustomerError(code: $code, message: $message, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerErrorImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.details, details) || other.details == details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message, details);

  /// Create a copy of CustomerError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerErrorImplCopyWith<_$CustomerErrorImpl> get copyWith =>
      __$$CustomerErrorImplCopyWithImpl<_$CustomerErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerErrorImplToJson(
      this,
    );
  }
}

abstract class _CustomerError implements CustomerError {
  const factory _CustomerError(
      {required final String code,
      required final String message,
      final String? details}) = _$CustomerErrorImpl;

  factory _CustomerError.fromJson(Map<String, dynamic> json) =
      _$CustomerErrorImpl.fromJson;

  @override
  String get code;
  @override
  String get message;
  @override
  String? get details;

  /// Create a copy of CustomerError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerErrorImplCopyWith<_$CustomerErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerRequest _$CustomerRequestFromJson(Map<String, dynamic> json) {
  return _CustomerRequest.fromJson(json);
}

/// @nodoc
mixin _$CustomerRequest {
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String? get mobileNumber =>
      throw _privateConstructorUsedError; // Made optional to match backend
  String? get email => throw _privateConstructorUsedError;
  String? get nationalId => throw _privateConstructorUsedError;
  String? get notes =>
      throw _privateConstructorUsedError; // Additional fields for internal use (not sent to backend)
  DateTime? get dateOfBirth => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get wilaya => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  CustomerPreferences? get preferences => throw _privateConstructorUsedError;

  /// Serializes this CustomerRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerRequestCopyWith<CustomerRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerRequestCopyWith<$Res> {
  factory $CustomerRequestCopyWith(
          CustomerRequest value, $Res Function(CustomerRequest) then) =
      _$CustomerRequestCopyWithImpl<$Res, CustomerRequest>;
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String? mobileNumber,
      String? email,
      String? nationalId,
      String? notes,
      DateTime? dateOfBirth,
      String? address,
      String? city,
      String? wilaya,
      List<String> tags,
      CustomerPreferences? preferences});

  $CustomerPreferencesCopyWith<$Res>? get preferences;
}

/// @nodoc
class _$CustomerRequestCopyWithImpl<$Res, $Val extends CustomerRequest>
    implements $CustomerRequestCopyWith<$Res> {
  _$CustomerRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? mobileNumber = freezed,
    Object? email = freezed,
    Object? nationalId = freezed,
    Object? notes = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? wilaya = freezed,
    Object? tags = null,
    Object? preferences = freezed,
  }) {
    return _then(_value.copyWith(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      nationalId: freezed == nationalId
          ? _value.nationalId
          : nationalId // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferences: freezed == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as CustomerPreferences?,
    ) as $Val);
  }

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CustomerPreferencesCopyWith<$Res>? get preferences {
    if (_value.preferences == null) {
      return null;
    }

    return $CustomerPreferencesCopyWith<$Res>(_value.preferences!, (value) {
      return _then(_value.copyWith(preferences: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CustomerRequestImplCopyWith<$Res>
    implements $CustomerRequestCopyWith<$Res> {
  factory _$$CustomerRequestImplCopyWith(_$CustomerRequestImpl value,
          $Res Function(_$CustomerRequestImpl) then) =
      __$$CustomerRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String firstName,
      String lastName,
      String? mobileNumber,
      String? email,
      String? nationalId,
      String? notes,
      DateTime? dateOfBirth,
      String? address,
      String? city,
      String? wilaya,
      List<String> tags,
      CustomerPreferences? preferences});

  @override
  $CustomerPreferencesCopyWith<$Res>? get preferences;
}

/// @nodoc
class __$$CustomerRequestImplCopyWithImpl<$Res>
    extends _$CustomerRequestCopyWithImpl<$Res, _$CustomerRequestImpl>
    implements _$$CustomerRequestImplCopyWith<$Res> {
  __$$CustomerRequestImplCopyWithImpl(
      _$CustomerRequestImpl _value, $Res Function(_$CustomerRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = null,
    Object? lastName = null,
    Object? mobileNumber = freezed,
    Object? email = freezed,
    Object? nationalId = freezed,
    Object? notes = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? wilaya = freezed,
    Object? tags = null,
    Object? preferences = freezed,
  }) {
    return _then(_$CustomerRequestImpl(
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      mobileNumber: freezed == mobileNumber
          ? _value.mobileNumber
          : mobileNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      nationalId: freezed == nationalId
          ? _value.nationalId
          : nationalId // ignore: cast_nullable_to_non_nullable
              as String?,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      wilaya: freezed == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String?,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferences: freezed == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as CustomerPreferences?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerRequestImpl implements _CustomerRequest {
  const _$CustomerRequestImpl(
      {required this.firstName,
      required this.lastName,
      this.mobileNumber,
      this.email,
      this.nationalId,
      this.notes,
      this.dateOfBirth,
      this.address,
      this.city,
      this.wilaya,
      final List<String> tags = const [],
      this.preferences})
      : _tags = tags;

  factory _$CustomerRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerRequestImplFromJson(json);

  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String? mobileNumber;
// Made optional to match backend
  @override
  final String? email;
  @override
  final String? nationalId;
  @override
  final String? notes;
// Additional fields for internal use (not sent to backend)
  @override
  final DateTime? dateOfBirth;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? wilaya;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final CustomerPreferences? preferences;

  @override
  String toString() {
    return 'CustomerRequest(firstName: $firstName, lastName: $lastName, mobileNumber: $mobileNumber, email: $email, nationalId: $nationalId, notes: $notes, dateOfBirth: $dateOfBirth, address: $address, city: $city, wilaya: $wilaya, tags: $tags, preferences: $preferences)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerRequestImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.mobileNumber, mobileNumber) ||
                other.mobileNumber == mobileNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.nationalId, nationalId) ||
                other.nationalId == nationalId) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.wilaya, wilaya) || other.wilaya == wilaya) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.preferences, preferences) ||
                other.preferences == preferences));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      firstName,
      lastName,
      mobileNumber,
      email,
      nationalId,
      notes,
      dateOfBirth,
      address,
      city,
      wilaya,
      const DeepCollectionEquality().hash(_tags),
      preferences);

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerRequestImplCopyWith<_$CustomerRequestImpl> get copyWith =>
      __$$CustomerRequestImplCopyWithImpl<_$CustomerRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerRequestImplToJson(
      this,
    );
  }
}

abstract class _CustomerRequest implements CustomerRequest {
  const factory _CustomerRequest(
      {required final String firstName,
      required final String lastName,
      final String? mobileNumber,
      final String? email,
      final String? nationalId,
      final String? notes,
      final DateTime? dateOfBirth,
      final String? address,
      final String? city,
      final String? wilaya,
      final List<String> tags,
      final CustomerPreferences? preferences}) = _$CustomerRequestImpl;

  factory _CustomerRequest.fromJson(Map<String, dynamic> json) =
      _$CustomerRequestImpl.fromJson;

  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String? get mobileNumber; // Made optional to match backend
  @override
  String? get email;
  @override
  String? get nationalId;
  @override
  String? get notes; // Additional fields for internal use (not sent to backend)
  @override
  DateTime? get dateOfBirth;
  @override
  String? get address;
  @override
  String? get city;
  @override
  String? get wilaya;
  @override
  List<String> get tags;
  @override
  CustomerPreferences? get preferences;

  /// Create a copy of CustomerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerRequestImplCopyWith<_$CustomerRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomerStats _$CustomerStatsFromJson(Map<String, dynamic> json) {
  return _CustomerStats.fromJson(json);
}

/// @nodoc
mixin _$CustomerStats {
  int get totalCustomers => throw _privateConstructorUsedError;
  int get newCustomersThisMonth => throw _privateConstructorUsedError;
  int get activeCustomers => throw _privateConstructorUsedError;
  double get averageSpentPerCustomer => throw _privateConstructorUsedError;
  double get customerRetentionRate => throw _privateConstructorUsedError;
  List<CustomersByWilaya> get customersByWilaya =>
      throw _privateConstructorUsedError;

  /// Serializes this CustomerStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomerStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomerStatsCopyWith<CustomerStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomerStatsCopyWith<$Res> {
  factory $CustomerStatsCopyWith(
          CustomerStats value, $Res Function(CustomerStats) then) =
      _$CustomerStatsCopyWithImpl<$Res, CustomerStats>;
  @useResult
  $Res call(
      {int totalCustomers,
      int newCustomersThisMonth,
      int activeCustomers,
      double averageSpentPerCustomer,
      double customerRetentionRate,
      List<CustomersByWilaya> customersByWilaya});
}

/// @nodoc
class _$CustomerStatsCopyWithImpl<$Res, $Val extends CustomerStats>
    implements $CustomerStatsCopyWith<$Res> {
  _$CustomerStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomerStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalCustomers = null,
    Object? newCustomersThisMonth = null,
    Object? activeCustomers = null,
    Object? averageSpentPerCustomer = null,
    Object? customerRetentionRate = null,
    Object? customersByWilaya = null,
  }) {
    return _then(_value.copyWith(
      totalCustomers: null == totalCustomers
          ? _value.totalCustomers
          : totalCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      newCustomersThisMonth: null == newCustomersThisMonth
          ? _value.newCustomersThisMonth
          : newCustomersThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      activeCustomers: null == activeCustomers
          ? _value.activeCustomers
          : activeCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      averageSpentPerCustomer: null == averageSpentPerCustomer
          ? _value.averageSpentPerCustomer
          : averageSpentPerCustomer // ignore: cast_nullable_to_non_nullable
              as double,
      customerRetentionRate: null == customerRetentionRate
          ? _value.customerRetentionRate
          : customerRetentionRate // ignore: cast_nullable_to_non_nullable
              as double,
      customersByWilaya: null == customersByWilaya
          ? _value.customersByWilaya
          : customersByWilaya // ignore: cast_nullable_to_non_nullable
              as List<CustomersByWilaya>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomerStatsImplCopyWith<$Res>
    implements $CustomerStatsCopyWith<$Res> {
  factory _$$CustomerStatsImplCopyWith(
          _$CustomerStatsImpl value, $Res Function(_$CustomerStatsImpl) then) =
      __$$CustomerStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalCustomers,
      int newCustomersThisMonth,
      int activeCustomers,
      double averageSpentPerCustomer,
      double customerRetentionRate,
      List<CustomersByWilaya> customersByWilaya});
}

/// @nodoc
class __$$CustomerStatsImplCopyWithImpl<$Res>
    extends _$CustomerStatsCopyWithImpl<$Res, _$CustomerStatsImpl>
    implements _$$CustomerStatsImplCopyWith<$Res> {
  __$$CustomerStatsImplCopyWithImpl(
      _$CustomerStatsImpl _value, $Res Function(_$CustomerStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomerStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalCustomers = null,
    Object? newCustomersThisMonth = null,
    Object? activeCustomers = null,
    Object? averageSpentPerCustomer = null,
    Object? customerRetentionRate = null,
    Object? customersByWilaya = null,
  }) {
    return _then(_$CustomerStatsImpl(
      totalCustomers: null == totalCustomers
          ? _value.totalCustomers
          : totalCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      newCustomersThisMonth: null == newCustomersThisMonth
          ? _value.newCustomersThisMonth
          : newCustomersThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
      activeCustomers: null == activeCustomers
          ? _value.activeCustomers
          : activeCustomers // ignore: cast_nullable_to_non_nullable
              as int,
      averageSpentPerCustomer: null == averageSpentPerCustomer
          ? _value.averageSpentPerCustomer
          : averageSpentPerCustomer // ignore: cast_nullable_to_non_nullable
              as double,
      customerRetentionRate: null == customerRetentionRate
          ? _value.customerRetentionRate
          : customerRetentionRate // ignore: cast_nullable_to_non_nullable
              as double,
      customersByWilaya: null == customersByWilaya
          ? _value._customersByWilaya
          : customersByWilaya // ignore: cast_nullable_to_non_nullable
              as List<CustomersByWilaya>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomerStatsImpl implements _CustomerStats {
  const _$CustomerStatsImpl(
      {this.totalCustomers = 0,
      this.newCustomersThisMonth = 0,
      this.activeCustomers = 0,
      this.averageSpentPerCustomer = 0.0,
      this.customerRetentionRate = 0.0,
      final List<CustomersByWilaya> customersByWilaya = const []})
      : _customersByWilaya = customersByWilaya;

  factory _$CustomerStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomerStatsImplFromJson(json);

  @override
  @JsonKey()
  final int totalCustomers;
  @override
  @JsonKey()
  final int newCustomersThisMonth;
  @override
  @JsonKey()
  final int activeCustomers;
  @override
  @JsonKey()
  final double averageSpentPerCustomer;
  @override
  @JsonKey()
  final double customerRetentionRate;
  final List<CustomersByWilaya> _customersByWilaya;
  @override
  @JsonKey()
  List<CustomersByWilaya> get customersByWilaya {
    if (_customersByWilaya is EqualUnmodifiableListView)
      return _customersByWilaya;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_customersByWilaya);
  }

  @override
  String toString() {
    return 'CustomerStats(totalCustomers: $totalCustomers, newCustomersThisMonth: $newCustomersThisMonth, activeCustomers: $activeCustomers, averageSpentPerCustomer: $averageSpentPerCustomer, customerRetentionRate: $customerRetentionRate, customersByWilaya: $customersByWilaya)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomerStatsImpl &&
            (identical(other.totalCustomers, totalCustomers) ||
                other.totalCustomers == totalCustomers) &&
            (identical(other.newCustomersThisMonth, newCustomersThisMonth) ||
                other.newCustomersThisMonth == newCustomersThisMonth) &&
            (identical(other.activeCustomers, activeCustomers) ||
                other.activeCustomers == activeCustomers) &&
            (identical(
                    other.averageSpentPerCustomer, averageSpentPerCustomer) ||
                other.averageSpentPerCustomer == averageSpentPerCustomer) &&
            (identical(other.customerRetentionRate, customerRetentionRate) ||
                other.customerRetentionRate == customerRetentionRate) &&
            const DeepCollectionEquality()
                .equals(other._customersByWilaya, _customersByWilaya));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalCustomers,
      newCustomersThisMonth,
      activeCustomers,
      averageSpentPerCustomer,
      customerRetentionRate,
      const DeepCollectionEquality().hash(_customersByWilaya));

  /// Create a copy of CustomerStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomerStatsImplCopyWith<_$CustomerStatsImpl> get copyWith =>
      __$$CustomerStatsImplCopyWithImpl<_$CustomerStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomerStatsImplToJson(
      this,
    );
  }
}

abstract class _CustomerStats implements CustomerStats {
  const factory _CustomerStats(
      {final int totalCustomers,
      final int newCustomersThisMonth,
      final int activeCustomers,
      final double averageSpentPerCustomer,
      final double customerRetentionRate,
      final List<CustomersByWilaya> customersByWilaya}) = _$CustomerStatsImpl;

  factory _CustomerStats.fromJson(Map<String, dynamic> json) =
      _$CustomerStatsImpl.fromJson;

  @override
  int get totalCustomers;
  @override
  int get newCustomersThisMonth;
  @override
  int get activeCustomers;
  @override
  double get averageSpentPerCustomer;
  @override
  double get customerRetentionRate;
  @override
  List<CustomersByWilaya> get customersByWilaya;

  /// Create a copy of CustomerStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomerStatsImplCopyWith<_$CustomerStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CustomersByWilaya _$CustomersByWilayaFromJson(Map<String, dynamic> json) {
  return _CustomersByWilaya.fromJson(json);
}

/// @nodoc
mixin _$CustomersByWilaya {
  String get wilaya => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  double get percentage => throw _privateConstructorUsedError;

  /// Serializes this CustomersByWilaya to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CustomersByWilaya
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CustomersByWilayaCopyWith<CustomersByWilaya> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CustomersByWilayaCopyWith<$Res> {
  factory $CustomersByWilayaCopyWith(
          CustomersByWilaya value, $Res Function(CustomersByWilaya) then) =
      _$CustomersByWilayaCopyWithImpl<$Res, CustomersByWilaya>;
  @useResult
  $Res call({String wilaya, int count, double percentage});
}

/// @nodoc
class _$CustomersByWilayaCopyWithImpl<$Res, $Val extends CustomersByWilaya>
    implements $CustomersByWilayaCopyWith<$Res> {
  _$CustomersByWilayaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CustomersByWilaya
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wilaya = null,
    Object? count = null,
    Object? percentage = null,
  }) {
    return _then(_value.copyWith(
      wilaya: null == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CustomersByWilayaImplCopyWith<$Res>
    implements $CustomersByWilayaCopyWith<$Res> {
  factory _$$CustomersByWilayaImplCopyWith(_$CustomersByWilayaImpl value,
          $Res Function(_$CustomersByWilayaImpl) then) =
      __$$CustomersByWilayaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String wilaya, int count, double percentage});
}

/// @nodoc
class __$$CustomersByWilayaImplCopyWithImpl<$Res>
    extends _$CustomersByWilayaCopyWithImpl<$Res, _$CustomersByWilayaImpl>
    implements _$$CustomersByWilayaImplCopyWith<$Res> {
  __$$CustomersByWilayaImplCopyWithImpl(_$CustomersByWilayaImpl _value,
      $Res Function(_$CustomersByWilayaImpl) _then)
      : super(_value, _then);

  /// Create a copy of CustomersByWilaya
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? wilaya = null,
    Object? count = null,
    Object? percentage = null,
  }) {
    return _then(_$CustomersByWilayaImpl(
      wilaya: null == wilaya
          ? _value.wilaya
          : wilaya // ignore: cast_nullable_to_non_nullable
              as String,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      percentage: null == percentage
          ? _value.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CustomersByWilayaImpl implements _CustomersByWilaya {
  const _$CustomersByWilayaImpl(
      {required this.wilaya, required this.count, this.percentage = 0.0});

  factory _$CustomersByWilayaImpl.fromJson(Map<String, dynamic> json) =>
      _$$CustomersByWilayaImplFromJson(json);

  @override
  final String wilaya;
  @override
  final int count;
  @override
  @JsonKey()
  final double percentage;

  @override
  String toString() {
    return 'CustomersByWilaya(wilaya: $wilaya, count: $count, percentage: $percentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CustomersByWilayaImpl &&
            (identical(other.wilaya, wilaya) || other.wilaya == wilaya) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, wilaya, count, percentage);

  /// Create a copy of CustomersByWilaya
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CustomersByWilayaImplCopyWith<_$CustomersByWilayaImpl> get copyWith =>
      __$$CustomersByWilayaImplCopyWithImpl<_$CustomersByWilayaImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CustomersByWilayaImplToJson(
      this,
    );
  }
}

abstract class _CustomersByWilaya implements CustomersByWilaya {
  const factory _CustomersByWilaya(
      {required final String wilaya,
      required final int count,
      final double percentage}) = _$CustomersByWilayaImpl;

  factory _CustomersByWilaya.fromJson(Map<String, dynamic> json) =
      _$CustomersByWilayaImpl.fromJson;

  @override
  String get wilaya;
  @override
  int get count;
  @override
  double get percentage;

  /// Create a copy of CustomersByWilaya
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CustomersByWilayaImplCopyWith<_$CustomersByWilayaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
