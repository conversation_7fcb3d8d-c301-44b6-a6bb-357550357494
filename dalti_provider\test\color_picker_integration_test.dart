import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dalti_provider/features/services/widgets/color_picker_widget.dart';

void main() {
  group('Color Picker Integration Tests', () {
    testWidgets('Complete color picker workflow should work correctly', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      String? lastColorChanged;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                lastColorChanged = color;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test 1: Verify initial state
      final textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);
      
      final textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('FF5722')); // No # in input
      
      // Test 2: Verify prefix text is shown
      expect(find.text('#'), findsOneWidget);
      
      // Test 3: Verify character counter
      expect(find.text('6/6'), findsOneWidget);
      
      // Test 4: Test typing new color
      await tester.tap(textField);
      await tester.pumpAndSettle();
      
      // Clear and enter new color
      await tester.enterText(textField, '00FF00'); // Green
      await tester.pumpAndSettle();
      
      // Should call onColorChanged with # prefix
      expect(lastColorChanged, equals('#00FF00'));
      
      // Test 5: Verify input field was updated
      final updatedTextField = tester.widget<TextFormField>(textField);
      expect(updatedTextField.controller?.text, equals('00FF00'));
    });

    testWidgets('Color picker should handle color changes from parent', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: Column(
                  children: [
                    ColorPickerWidget(
                      selectedColor: selectedColor,
                      onColorChanged: (color) {
                        setState(() {
                          selectedColor = color;
                        });
                      },
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          selectedColor = '#2196F3'; // Change to blue
                        });
                      },
                      child: const Text('Change Color'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Initial state
      final textField = find.byType(TextFormField);
      TextFormField textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('FF5722'));

      // Change color from parent
      await tester.tap(find.text('Change Color'));
      await tester.pumpAndSettle();

      // Should update input field
      textFormField = tester.widget<TextFormField>(textField);
      expect(textFormField.controller?.text, equals('2196F3'));
    });

    test('Color format handling should work correctly', () {
      // Test various color format scenarios
      const testCases = [
        // Input, Expected Output
        ('#FF5722', '#FF5722'),
        ('FF5722', '#FF5722'),
        ('#2196F3', '#2196F3'),
        ('2196F3', '#2196F3'),
        ('#000000', '#000000'),
        ('FFFFFF', '#FFFFFF'),
      ];

      for (final testCase in testCases) {
        final input = testCase.$1;
        final expected = testCase.$2;
        
        // Simulate the logic from ColorPickerWidget
        final hexValue = input.startsWith('#') ? input.substring(1) : input;
        final result = '#$hexValue';
        
        expect(result, equals(expected), 
            reason: 'Color format handling failed for input: $input');
      }
    });

    test('Hex validation should be robust', () {
      final hexPattern = RegExp(r'^[0-9A-Fa-f]{6}$');
      
      // Valid hex codes (without #)
      const validCodes = [
        'FF5722',
        '2196F3',
        '000000',
        'FFFFFF',
        'abcdef',
        'ABCDEF',
        '123456',
        '789ABC',
        'DEF012',
      ];

      // Invalid hex codes
      const invalidCodes = [
        'GGGGGG', // Invalid characters
        '12345',  // Too short
        '1234567', // Too long
        'XYZ123', // Invalid characters
        '',       // Empty
        'FF55ZZ', // Invalid characters
        'GG5722', // Invalid characters
        '#FF5722', // Should not include #
      ];

      for (final code in validCodes) {
        expect(hexPattern.hasMatch(code), isTrue,
            reason: 'Valid hex code should pass: $code');
      }

      for (final code in invalidCodes) {
        expect(hexPattern.hasMatch(code), isFalse,
            reason: 'Invalid hex code should fail: $code');
      }
    });

    test('Color parsing should handle edge cases', () {
      // Test color parsing with different inputs
      final testCases = [
        ('FF5722', 0xFFFF5722),
        ('2196F3', 0xFF2196F3),
        ('000000', 0xFF000000),
        ('FFFFFF', 0xFFFFFFFF),
        ('abcdef', 0xFFABCDEF),
        ('ABCDEF', 0xFFABCDEF),
      ];

      for (final testCase in testCases) {
        final hexInput = testCase.$1;
        final expectedValue = testCase.$2;
        
        final colorValue = int.tryParse(hexInput, radix: 16);
        expect(colorValue, isNotNull, reason: 'Should parse hex: $hexInput');
        
        final fullColorValue = 0xFF000000 | colorValue!;
        expect(fullColorValue, equals(expectedValue),
            reason: 'Color value mismatch for: $hexInput');
      }
    });

    testWidgets('Color picker should maintain state correctly', (WidgetTester tester) async {
      String selectedColor = '#FF5722';
      final colorChanges = <String>[];
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ColorPickerWidget(
              selectedColor: selectedColor,
              onColorChanged: (color) {
                colorChanges.add(color);
                selectedColor = color;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test multiple color changes
      final textField = find.byType(TextFormField);
      
      // Change 1: Enter blue
      await tester.tap(textField);
      await tester.enterText(textField, '2196F3');
      await tester.pumpAndSettle();
      
      // Change 2: Enter green
      await tester.enterText(textField, '4CAF50');
      await tester.pumpAndSettle();
      
      // Change 3: Enter red
      await tester.enterText(textField, 'F44336');
      await tester.pumpAndSettle();
      
      // Verify all changes were captured with # prefix
      expect(colorChanges, contains('#2196F3'));
      expect(colorChanges, contains('#4CAF50'));
      expect(colorChanges, contains('#F44336'));
      
      // Verify final state
      final finalTextField = tester.widget<TextFormField>(textField);
      expect(finalTextField.controller?.text, equals('F44336'));
    });
  });
}
